#!/bin/bash

# Color migration script
# This script helps migrate hardcoded Tailwind colors to semantic variables

migrate_file() {
    local file="$1"
    echo "Migrating: $file"
    
    # Create a backup
    cp "$file" "$file.bak"
    
    # Remove dark: variants (CSS variables handle dark mode)
    sed -i '' 's/ dark:bg-[a-zA-Z0-9-]*//g' "$file"
    sed -i '' 's/ dark:text-[a-zA-Z0-9-]*//g' "$file"
    sed -i '' 's/ dark:border-[a-zA-Z0-9-]*//g' "$file"
    
    # Replace hardcoded colors with semantic variables
    # Green colors -> success
    sed -i '' 's/text-green-[0-9][0-9][0-9]/text-success/g' "$file"
    sed -i '' 's/bg-green-[0-9][0-9][0-9]/bg-success/g' "$file"
    sed -i '' 's/border-green-[0-9][0-9][0-9]/border-success/g' "$file"
    
    # Red colors -> error/accent
    sed -i '' 's/text-red-[0-9][0-9][0-9]/text-error/g' "$file"
    sed -i '' 's/bg-red-[0-9][0-9][0-9]/bg-error/g' "$file"
    sed -i '' 's/border-red-[0-9][0-9][0-9]/border-error/g' "$file"
    
    # Blue colors -> primary
    sed -i '' 's/text-blue-[0-9][0-9][0-9]/text-primary/g' "$file"
    sed -i '' 's/bg-blue-[0-9][0-9][0-9]/bg-primary/g' "$file"
    sed -i '' 's/border-blue-[0-9][0-9][0-9]/border-primary/g' "$file"
    
    # Yellow colors -> warning
    sed -i '' 's/text-yellow-[0-9][0-9][0-9]/text-warning/g' "$file"
    sed -i '' 's/bg-yellow-[0-9][0-9][0-9]/bg-warning/g' "$file"
    sed -i '' 's/border-yellow-[0-9][0-9][0-9]/border-warning/g' "$file"
    
    # Gray colors -> surface/muted/subtle based on context
    # Light grays (100-400) -> subtle/muted
    sed -i '' 's/text-gray-[1-4][0-9][0-9]/text-muted/g' "$file"
    sed -i '' 's/bg-gray-[1-4][0-9][0-9]/bg-surface-page/g' "$file"
    sed -i '' 's/border-gray-[1-4][0-9][0-9]/border-subtle/g' "$file"
    
    # Medium grays (500-600) -> secondary
    sed -i '' 's/text-gray-[56][0-9][0-9]/text-secondary/g' "$file"
    sed -i '' 's/bg-gray-[56][0-9][0-9]/bg-surface-alt/g' "$file"
    sed -i '' 's/border-gray-[56][0-9][0-9]/border-default/g' "$file"
    
    # Dark grays (700-900) -> primary/strong
    sed -i '' 's/text-gray-[789][0-9][0-9]/text-primary/g' "$file"
    sed -i '' 's/bg-gray-[789][0-9][0-9]/bg-surface-card/g' "$file"
    sed -i '' 's/border-gray-[789][0-9][0-9]/border-strong/g' "$file"
    
    # Purple colors -> accent
    sed -i '' 's/text-purple-[0-9][0-9][0-9]/text-accent/g' "$file"
    sed -i '' 's/bg-purple-[0-9][0-9][0-9]/bg-accent/g' "$file"
    sed -i '' 's/border-purple-[0-9][0-9][0-9]/border-accent/g' "$file"
    
    # Orange colors -> warning
    sed -i '' 's/text-orange-[0-9][0-9][0-9]/text-warning/g' "$file"
    sed -i '' 's/bg-orange-[0-9][0-9][0-9]/bg-warning/g' "$file"
    sed -i '' 's/border-orange-[0-9][0-9][0-9]/border-warning/g' "$file"
    
    # Pink/Indigo colors -> accent
    sed -i '' 's/text-pink-[0-9][0-9][0-9]/text-accent/g' "$file"
    sed -i '' 's/bg-pink-[0-9][0-9][0-9]/bg-accent/g' "$file"
    sed -i '' 's/border-pink-[0-9][0-9][0-9]/border-accent/g' "$file"
    sed -i '' 's/text-indigo-[0-9][0-9][0-9]/text-primary/g' "$file"
    sed -i '' 's/bg-indigo-[0-9][0-9][0-9]/bg-primary/g' "$file"
    sed -i '' 's/border-indigo-[0-9][0-9][0-9]/border-primary/g' "$file"
    
    # Check if file changed
    if ! diff -q "$file" "$file.bak" > /dev/null; then
        echo "  ✓ Migrated colors in $file"
        rm "$file.bak"
    else
        echo "  - No changes needed in $file"
        rm "$file.bak"
    fi
}

# Process files passed as arguments or from stdin
if [ $# -gt 0 ]; then
    for file in "$@"; do
        migrate_file "$file"
    done
else
    echo "Usage: $0 <file1> <file2> ..."
    echo "Or: find src -name '*.tsx' | xargs $0"
fi