import { defineConfig, devices } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Check if auth.json exists
const authFile = path.join(__dirname, 'auth.json');
const hasAuthFile = fs.existsSync(authFile);

if (!hasAuthFile) {
  console.warn(`
⚠️  Auth file not found: ${authFile}
    Tests requiring authentication may fail.
    Run auth setup first:
      npx playwright test auth-setup.ts --headed
  `);
}

/**
 * See https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [['html', { outputFolder: 'playwright-report' }]],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Use saved authentication state from auth-setup.ts */
    storageState: hasAuthFile ? authFile : undefined,
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:5173',
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    /* Take screenshot on test failure */
    screenshot: 'only-on-failure',
    /* Visual testing settings */
    ignoreHTTPSErrors: true,
    video: 'retain-on-failure',
  },
  /* Configure projects for major browsers */
  projects: [
    {
      name: 'e2e',
      testDir: './tests/e2e',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'visual',
      testDir: './tests/visual',
      use: { 
        ...devices['Desktop Chrome'],
        // Visual test specific settings
        viewport: { width: 1280, height: 720 },
      },
    },
  ],
  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: true,
    timeout: 120000,
  },
});