#!/bin/bash

# Safe CSS-only color migration script
# This script ONLY processes CSS files to avoid damaging TypeScript/React files

echo "=== Safe CSS-Only Color Migration Script ==="
echo "This script will ONLY process .css files"
echo ""

# Create backup directory
BACKUP_DIR="color-migration-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Function to backup a file
backup_file() {
    local file=$1
    local rel_path=${file#./}
    local backup_path="$BACKUP_DIR/$rel_path"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
}

# Count CSS files with color patterns
echo "Analyzing CSS files..."
CSS_FILES=$(find . -name "*.css" -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./dist/*" -not -path "./$BACKUP_DIR/*")
TOTAL_CSS_FILES=$(echo "$CSS_FILES" | wc -l | tr -d ' ')

echo "Found $TOTAL_CSS_FILES CSS files to check"
echo ""

# Create a dry-run log
DRY_RUN_LOG="migration-dry-run.log"
echo "=== CSS Color Migration Dry Run - $(date) ===" > "$DRY_RUN_LOG"

# First, do a dry run to show what would be changed
echo "Running dry-run analysis..."
PATTERNS_FOUND=0

for file in $CSS_FILES; do
    # Check if file contains any Tailwind color patterns
    if grep -E "(bg|text|border|ring|outline|fill|stroke)-(gray|slate|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(50|100|200|300|400|500|600|700|800|900|950)" "$file" > /dev/null 2>&1; then
        echo "Would process: $file" | tee -a "$DRY_RUN_LOG"
        MATCHES=$(grep -E "(bg|text|border|ring|outline|fill|stroke)-(gray|slate|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(50|100|200|300|400|500|600|700|800|900|950)" "$file" | wc -l | tr -d ' ')
        echo "  Found $MATCHES color patterns" | tee -a "$DRY_RUN_LOG"
        ((PATTERNS_FOUND += MATCHES))
    fi
done

echo ""
echo "Dry run complete. Would modify files with $PATTERNS_FOUND total patterns."
echo "Full analysis saved to: $DRY_RUN_LOG"
echo ""

# Ask for confirmation
read -p "Do you want to proceed with the migration? (y/N) " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled."
    exit 0
fi

# Proceed with actual migration
echo ""
echo "Starting migration..."
PROCESSED=0
MODIFIED=0

for file in $CSS_FILES; do
    # Check if file contains any Tailwind color patterns
    if grep -E "(bg|text|border|ring|outline|fill|stroke)-(gray|slate|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(50|100|200|300|400|500|600|700|800|900|950)" "$file" > /dev/null 2>&1; then
        echo "Processing: $file"
        
        # Backup the file first
        backup_file "$file"
        
        # Apply safe replacements - only exact pattern matches
        # Gray scale to neutral
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-50\b/\1-surface-alt/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-100\b/\1-surface-alt/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-200\b/\1-default/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-300\b/\1-strong/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-400\b/\1-subtle/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-500\b/\1-muted/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-600\b/\1-secondary/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-700\b/\1-primary/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-800\b/\1-primary/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-gray-900\b/\1-primary/g' "$file"
        
        # Semantic colors
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(red|rose)-(500|600|700)\b/\1-error/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(red|rose)-(50|100)\b/\1-error-light/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(green|emerald)-(500|600|700)\b/\1-success/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(green|emerald)-(50|100)\b/\1-success-light/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(yellow|amber|orange)-(500|600|700)\b/\1-warning/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(yellow|amber|orange)-(50|100)\b/\1-warning-light/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(blue|sky|cyan)-(500|600|700)\b/\1-info/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(blue|sky|cyan)-(50|100)\b/\1-info-light/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(indigo|violet|purple)-(500|600|700)\b/\1-primary/g' "$file"
        sed -i '' -E 's/\b(bg|text|border|ring|outline|fill|stroke)-(indigo|violet|purple)-(50|100)\b/\1-primary-light/g' "$file"
        
        ((PROCESSED++))
        ((MODIFIED++))
    fi
done

echo ""
echo "=== Migration Complete ==="
echo "Files processed: $PROCESSED"
echo "Files modified: $MODIFIED"
echo "Backup created at: $BACKUP_DIR"
echo ""
echo "Next steps:"
echo "1. Review the changes in your CSS files"
echo "2. Test your build: npm run build"
echo "3. If issues occur, restore from backup: cp -r $BACKUP_DIR/* ."