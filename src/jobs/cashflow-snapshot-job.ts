import { <PERSON><PERSON><PERSON><PERSON> } from "cron";
import { CashflowService } from "../services/cashflow";
import { CashflowSnapshotService } from "../services/cashflow/snapshot-service";
import { XeroService, getXeroService } from "../services/xero";

/**
 * Job to create daily snapshots of cashflow projections
 * Runs automatically at a scheduled time each day
 */
export class CashflowSnapshotJob {
  private cashflowService: CashflowService;
  private snapshotService: CashflowSnapshotService;
  private xeroService: XeroService;
  private job: CronJob;

  constructor() {
    this.cashflowService = new CashflowService();
    this.snapshotService = new CashflowSnapshotService();
    this.xeroService = getXeroService(); // Use the singleton instance

    // Run at 1:00 AM every day
    this.job = new CronJob('0 1 * * *', this.createDailySnapshots.bind(this));
  }

  /**
   * Start the scheduled job
   */
  start(): void {
    this.job.start();
    console.log('Cashflow snapshot job scheduled to run at 1:00 AM daily');
  }

  /**
   * Stop the scheduled job
   */
  stop(): void {
    this.job.stop();
    console.log('Cashflow snapshot job stopped');
  }

  /**
   * Create snapshots for all active tenants
   * This is the main job function that runs on schedule
   */
  async createDailySnapshots(): Promise<void> {
    try {
      console.log('Creating daily cashflow snapshots...');

      // Get all active Xero tenants
      const tenants = this.xeroService.getTenants();
      console.log(`Found ${tenants.length} active tenants`);

      // Create snapshots for each tenant with default timeframe (90 days)
      for (const tenant of tenants) {
        try {
          console.log(`Generating snapshot for tenant ${tenant.tenantId}`);

          // Generate projection
          const forecast = await this.cashflowService.generateForwardProjection(
            tenant.tenantId,
            90, // Default to 90 days
            [] // No custom expenses for the default snapshot
          );

          // Save snapshot
          await this.snapshotService.createSnapshot(
            tenant.tenantId,
            forecast,
            90
          );

          console.log(`Successfully created snapshot for tenant ${tenant.tenantId}`);
        } catch (tenantError) {
          // Log error but continue with other tenants
          console.error(`Error creating snapshot for tenant ${tenant.tenantId}:`, tenantError);
        }
      }

      console.log('Daily cashflow snapshots job completed');
    } catch (error) {
      console.error('Error in daily cashflow snapshots job:", error);
    }
  }
}
