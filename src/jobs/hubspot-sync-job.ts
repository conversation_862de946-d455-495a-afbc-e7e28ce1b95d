import { <PERSON><PERSON><PERSON><PERSON> } from "cron";
import { hubspotService } from "../api/services/hubspot-service";

/**
 * Job to run daily HubSpot full sync
 * Runs automatically at 3:00 AM each day
 */
export class HubSpotSyncJob {
  private job: <PERSON>ronJob;

  constructor() {
    // Run at 3:00 AM every day
    this.job = new CronJob('0 3 * * *', this.runFullSync.bind(this));
  }

  /**
   * Start the scheduled job
   */
  start(): void {
    this.job.start();
    console.log('HubSpot sync job scheduled to run at 3:00 AM daily');
  }

  /**
   * Stop the scheduled job
   */
  stop(): void {
    this.job.stop();
    console.log('HubSpot sync job stopped');
  }

  /**
   * Run a full HubSpot sync
   * This is the main job function that runs on schedule
   */
  async runFullSync(): Promise<void> {
    try {
      console.log('Starting scheduled HubSpot full sync...');

      // Check if HubSpot is configured
      const accessToken = hubspotService.getAccessToken();
      if (!accessToken) {
        console.log('HubSpot not configured, skipping scheduled sync');
        return;
      }

      // Initialize HubSpot if needed
      if (!hubspotService.isInitialized()) {
        const initSuccess = hubspotService.initialize(accessToken);
        if (!initSuccess) {
          console.error('Failed to initialize HubSpot for scheduled sync');
          return;
        }
      }

      // Run the full sync
      const result = await hubspotService.importAllWithProgress();

      if (result.success) {
        const totalCount = result.companies.count + result.deals.count + result.contacts.count + 
                          (result.notes?.count || 0) + (result.associations?.count || 0);
        console.log(`Scheduled HubSpot sync completed successfully. Imported ${totalCount} total records`);
      } else {
        console.error('Scheduled HubSpot sync failed:', result.error);
      }

    } catch (error) {
      console.error('Error in scheduled HubSpot sync job:", error);
    }
  }

  /**
   * Run sync immediately (for testing or manual trigger)
   */
  async runNow(): Promise<void> {
    console.log('Manually triggering HubSpot sync...');
    await this.runFullSync();
  }
}