import { DealRepository } from "../api/repositories/deal-repository";
import { DealEstimateRepository } from "../api/repositories/relationships/deal-estimate-repository";
import { EstimateDraftsRepository } from "../api/repositories/estimate-drafts-repository";
import { DataSource } from "../frontend/types/crm-types";

/**
 * Update deals with estimate data
 *
 * This function finds all deals linked to a specific estimate and updates them
 * with the latest data from the estimate.
 *
 * @param estimateId The ID of the estimate
 * @param estimateType The type of estimate (only 'internal' supported)
 */
export async function updateDealsFromEstimate(estimateId: string, estimateType: "internal"): Promise<void> {
  try {
    // Get the repositories
    const dealRepository = new DealRepository();
    const dealEstimateRepository = new DealEstimateRepository();

    // Find deals linked to this estimate
    const dealRelationships = dealEstimateRepository.getDealsForEstimate(estimateId);
    const dealIds = dealRelationships.map(relationship => relationship.dealId);
    if (dealIds.length === 0) return;

    console.log(`Updating ${dealIds.length} deals linked to draft estimate ${estimateId}`);

    // Get estimate data
    const estimateDraftsRepository = new EstimateDraftsRepository();
    const estimateData = estimateDraftsRepository.getDraftEstimateById(estimateId);

    if (!estimateData) {
      console.warn(`No draft estimate found with ID: ${estimateId}`);
      return;
    }

    // Update each linked deal
    for (const dealId of dealIds) {
      const deal = dealRepository.getDealById(dealId);
      if (!deal) {
        console.warn(`Deal ${dealId} not found, skipping update`);
        continue;
      }

      // Prepare deal update data
      const dealUpdateData: any = {};

      // Update fields from draft estimate
      if (estimateData.startDate) {
        dealUpdateData.startDate = estimateData.startDate;
      }

      if (estimateData.endDate) {
        dealUpdateData.endDate = estimateData.endDate;
      }

      if (estimateData.invoiceFrequency) {
        dealUpdateData.invoiceFrequency = estimateData.invoiceFrequency;
      }

      if (estimateData.paymentTerms) {
        dealUpdateData.paymentTerms = estimateData.paymentTerms;
      }

      // Update value from totalFees
      if (estimateData.totalFees) {
        dealUpdateData.value = estimateData.totalFees;
        dealUpdateData.currency = 'AUD'; // Default to AUD
      }

      // Update deal name from project name (only if not owned by another system)
      if (estimateData.projectName) {
        // Import field ownership check
        const { getFieldOwner } = require('./deal-tracking');
        const nameOwner = getFieldOwner(dealId, 'name');
        
        // Only update if field is not owned or owned by Estimate
        // The database stores lowercase values, so check both cases
        if (!nameOwner || nameOwner === 'Estimate' || nameOwner === 'estimate') {
          dealUpdateData.name = estimateData.projectName;
        } else {
          console.log(`Skipping deal name update - field owned by ${nameOwner}`);
        }
      }

      // Apply the updates if there are any
      if (Object.keys(dealUpdateData).length > 0) {
        console.log(`Updating deal ${dealId} with draft estimate data:`, dealUpdateData);
        dealRepository.updateDeal(dealId, dealUpdateData, 'Estimate' as DataSource);
      } else {
        console.log(`No updates needed for deal ${dealId}`);
      }
    }
  } catch (error) {
    console.error('Error updating deals from estimate:', error);
  }
}

/**
 * Update estimate with deal data
 *
 * This function updates an estimate with the latest data from its linked deal.
 * Currently only syncs the project name from the deal name.
 *
 * @param dealId The ID of the deal
 * @param estimateId The ID of the estimate
 */
export async function updateEstimateFromDeal(dealId: string, estimateId: string): Promise<void> {
  try {
    // Get the repositories
    const dealRepository = new DealRepository();
    const estimateDraftsRepository = new EstimateDraftsRepository();

    // Get deal data
    const dealData = dealRepository.getDealById(dealId);
    if (!dealData) {
      console.warn(`No deal found with ID: ${dealId}`);
      return;
    }

    // Get estimate data
    const estimateData = estimateDraftsRepository.getDraftEstimateById(estimateId);
    if (!estimateData) {
      console.warn(`No estimate found with ID: ${estimateId}`);
      return;
    }

    // Update estimate project name from deal name (only if owned by Deal)
    if (dealData.name && dealData.name !== estimateData.projectName) {
      // Import field ownership check
      const { getFieldOwnerForEntity } = require('./deal-tracking');
      const projectNameOwner = getFieldOwnerForEntity('estimate', estimateId, 'project_name');
      
      // Only update if field is owned by Deal or not owned
      // The database stores lowercase values, so check both cases
      if (!projectNameOwner || projectNameOwner === 'Deal' || projectNameOwner === 'deal') {
        console.log(`Updating estimate ${estimateId} project name from deal ${dealId}`);
        estimateDraftsRepository.updateDraftEstimate(estimateId, {
          projectName: dealData.name,
          userId: "system" // System update
        });
      } else {
        console.log(`Skipping estimate project name update - field owned by ${projectNameOwner}`);
      }
    }
  } catch (error) {
    console.error('Error updating estimate from deal:', error);
  }
}
