/**
 * Activity Logger Utility
 *
 * This utility provides convenient functions for logging activities throughout the application.
 * It integrates with the ActivityService to create activity records.
 */

import { getActivityService } from "../api/services/activity-service";
import {
  Activity,
  ActivityCreate,
  ActivityType,
  ActivitySource,
  ActivityEntityType
} from "../frontend/types/activity-types";

/**
 * Interface for entity change data
 */
interface EntityChanges {
  [key: string]: string | number | boolean | Date | null | undefined;
}

/**
 * Interface for sync error data
 */
interface SyncError {
  message: string;
  code?: string;
  details?: string;
  timestamp?: string;
}

/**
 * Activity logger class
 */
class ActivityLogger {
  private activityService = getActivityService();

  /**
   * Log a user action
   */
  async logUserAction(
    type: ActivityType,
    subject: string,
    details: Partial<ActivityCreate> = {}
  ): Promise<Activity> {
    try {
      return await this.activityService.logUserAction(type, subject, {
        ...details,
        createdBy: details.createdBy || "user"
      });
    } catch (error) {
      // Log to stderr in development, suppress in production
      if (process.env.NODE_ENV !== 'production') {
        process.stderr.write(`Error logging user action: ${error instanceof Error ? error.message : error}\n`);
      }
      throw error;
    }
  }

  /**
   * Log a system event
   */
  async logSystemEvent(
    type: ActivityType,
    subject: string,
    details: Partial<ActivityCreate> = {}
  ): Promise<Activity> {
    try {
      return await this.activityService.logSystemEvent(type, subject, details);
    } catch (error) {
      // Log to stderr in development, suppress in production
      if (process.env.NODE_ENV !== 'production') {
        process.stderr.write(`Error logging system event: ${error instanceof Error ? error.message : error}\n`);
      }
      throw error;
    }
  }

  /**
   * Log an integration event
   */
  async logIntegrationEvent(
    source: ActivitySource,
    type: ActivityType,
    details: Partial<ActivityCreate> = {}
  ): Promise<Activity> {
    try {
      return await this.activityService.logIntegrationEvent(source, type, details);
    } catch (error) {
      // Log to stderr in development, suppress in production
      if (process.env.NODE_ENV !== 'production') {
        process.stderr.write(`Error logging integration event: ${error instanceof Error ? error.message : error}\n`);
      }
      throw error;
    }
  }

  /**
   * Log entity changes
   */
  async logEntityChange(
    entityType: ActivityEntityType,
    entityId: string,
    changes: EntityChanges
  ): Promise<Activity> {
    try {
      return await this.activityService.logEntityChange(entityType, entityId, changes);
    } catch (error) {
      // Log to stderr in development, suppress in production
      if (process.env.NODE_ENV !== 'production') {
        process.stderr.write(`Error logging entity change: ${error instanceof Error ? error.message : error}\n`);
      }
      throw error;
    }
  }

  /**
   * Log deal creation
   */
  async logDealCreated(dealId: string, dealName: string, createdBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('deal_created', `Deal "${dealName}' created`, {
      entityType: "deal",
      entityId: dealId,
      dealId,
      createdBy
    });
  }

  /**
   * Log deal update
   */
  async logDealUpdated(dealId: string, dealName: string, changes: EntityChanges, updatedBy: string = 'user'): Promise<Activity> {
    const changesList = Object.keys(changes).join(', ');
    return this.logUserAction('deal_updated', `Deal "${dealName}' updated`, {
      description: `Updated fields: ${changesList}`,
      entityType: "deal",
      entityId: dealId,
      dealId,
      createdBy: updatedBy,
      metadata: { changes }
    });
  }

  /**
   * Log deal stage change
   */
  async logDealStageChanged(dealId: string, dealName: string, oldStage: string, newStage: string, changedBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('deal_stage_changed', `Deal "${dealName}' moved to ${newStage}`, {
      description: `Stage changed from "${oldStage}" to "${newStage}"`,
      entityType: "deal",
      entityId: dealId,
      dealId,
      createdBy: changedBy,
      metadata: { oldStage, newStage }
    });
  }

  /**
   * Log estimate creation
   */
  async logEstimateCreated(estimateId: string, projectName: string, createdBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('estimate_created', `Estimate for "${projectName}' created`, {
      entityType: "estimate",
      entityId: estimateId,
      createdBy
    });
  }

  /**
   * Log estimate publication
   */
  async logEstimatePublished(estimateId: string, projectName: string, publishedBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('estimate_published', `Estimate for "${projectName}' published`, {
      entityType: "estimate",
      entityId: estimateId,
      createdBy: publishedBy,
      importance: "high"
    });
  }

  /**
   * Log company creation
   */
  async logCompanyCreated(companyId: string, companyName: string, createdBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('company_created', `Company "${companyName}' created`, {
      entityType: "company",
      entityId: companyId,
      companyId,
      createdBy
    });
  }

  /**
   * Log company update
   */
  async logCompanyUpdated(companyId: string, companyName: string, changes: EntityChanges, updatedBy: string = 'user'): Promise<Activity> {
    const changesList = Object.keys(changes).filter(key => key !== 'updatedBy').join(', ');
    return this.logUserAction('company_updated', `Company "${companyName}' updated`, {
      description: `Updated fields: ${changesList}`,
      entityType: "company",
      entityId: companyId,
      companyId,
      createdBy: updatedBy,
      metadata: { changes }
    });
  }

  /**
   * Log company radar changes
   */
  async logCompanyRadarChanged(companyId: string, companyName: string, changes: EntityChanges, changedBy: string = 'user'): Promise<Activity> {
    const changesList = Object.keys(changes).filter(key => !['updatedBy', 'updated_at'].includes(key));

    if (changesList.length === 0) {
      return Promise.resolve({} as Activity); // Skip if no meaningful changes
    }

    let description = "";
    if (changes.radar_priority) {
      description = `Priority changed to ${changes.radar_priority}`;
    } else if (changes.radar_status) {
      description = `Status changed to ${changes.radar_status}`;
    } else if (changes.radar_notes) {
      description = 'Radar notes updated";
    } else {
      description = `Updated radar fields: ${changesList.join(', ')}`;
    }

    return this.logUserAction('company_radar_updated', `Company "${companyName}' radar updated`, {
      description,
      entityType: "company",
      entityId: companyId,
      companyId,
      createdBy: changedBy,
      metadata: { changes, radarUpdate: true }
    });
  }

  /**
   * Log company linking
   */
  async logCompanyLinked(companyId: string, companyName: string, externalSystem: string, linkedBy: string = 'system'): Promise<Activity> {
    return this.logSystemEvent('company_linked', `Company "${companyName}' linked to ${externalSystem}`, {
      entityType: "company",
      entityId: companyId,
      companyId,
      createdBy: linkedBy,
      metadata: { externalSystem }
    });
  }

  /**
   * Log contact creation
   */
  async logContactCreated(contactId: string, contactName: string, createdBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('contact_created', `Contact "${contactName}' created`, {
      entityType: "contact",
      entityId: contactId,
      contactId,
      createdBy
    });
  }

  /**
   * Log note addition
   */
  async logNoteAdded(dealId: string, dealName: string, noteContent: string, addedBy: string = 'user'): Promise<Activity> {
    const preview = noteContent.length > 100 ? noteContent.substring(0, 100) + '...' : noteContent;
    return this.logUserAction('note_added', `Note added to "${dealName}'`, {
      description: `Note: ${preview}`,
      entityType: "deal",
      entityId: dealId,
      dealId,
      createdBy: addedBy,
      metadata: { noteContent }
    });
  }

  /**
   * Log HubSpot sync events
   */
  async logHubSpotSyncStarted(): Promise<Activity> {
    return this.logIntegrationEvent('hubspot', 'hubspot_sync_started', {
      subject: "HubSpot synchronization started"
    });
  }

  async logHubSpotSyncCompleted(importedCount: number, errors: SyncError[] = []): Promise<Activity> {
    const hasErrors = errors.length > 0;
    return this.logIntegrationEvent('hubspot', 'hubspot_sync_completed', {
      subject: `HubSpot sync completed - ${importedCount} items imported`,
      description: hasErrors ? `${errors.length} errors occurred` : "Sync completed successfully",
      importance: hasErrors ? 'high' : "normal",
      metadata: { importedCount, errors: errors.slice(0, 10) } // Limit error details
    });
  }

  async logHubSpotSyncFailed(error: string): Promise<Activity> {
    return this.logIntegrationEvent('hubspot', 'hubspot_sync_failed', {
      subject: "HubSpot synchronization failed",
      description: error,
      importance: "high",
      metadata: { error }
    });
  }

  /**
   * Log Xero sync events
   */
  async logXeroSyncStarted(syncType: string): Promise<Activity> {
    return this.logIntegrationEvent('xero', 'xero_sync_started', {
      subject: `Xero ${syncType} synchronization started`,
      metadata: { syncType }
    });
  }

  async logXeroSyncCompleted(syncType: string, importedCount: number): Promise<Activity> {
    return this.logIntegrationEvent('xero', 'xero_sync_completed', {
      subject: `Xero ${syncType} sync completed - ${importedCount} items imported`,
      metadata: { syncType, importedCount }
    });
  }

  async logXeroSyncFailed(syncType: string, error: string): Promise<Activity> {
    return this.logIntegrationEvent('xero', 'xero_sync_failed', {
      subject: `Xero ${syncType} synchronization failed`,
      description: error,
      importance: "high",
      metadata: { syncType, error }
    });
  }

  /**
   * Log Harvest sync events
   */
  async logHarvestSyncStarted(syncType: string): Promise<Activity> {
    return this.logIntegrationEvent('harvest', 'harvest_sync_started', {
      subject: `Harvest ${syncType} synchronization started`,
      metadata: { syncType }
    });
  }

  async logHarvestSyncCompleted(syncType: string, importedCount: number): Promise<Activity> {
    return this.logIntegrationEvent('harvest', 'harvest_sync_completed', {
      subject: `Harvest ${syncType} sync completed - ${importedCount} items imported`,
      metadata: { syncType, importedCount }
    });
  }

  async logHarvestSyncFailed(syncType: string, error: string): Promise<Activity> {
    return this.logIntegrationEvent('harvest', 'harvest_sync_failed', {
      subject: `Harvest ${syncType} synchronization failed`,
      description: error,
      importance: "high",
      metadata: { syncType, error }
    });
  }

  /**
   * Log cashflow projection generation
   */
  async logCashflowProjectionGenerated(projectionDate: string): Promise<Activity> {
    return this.logSystemEvent('cashflow_projection_generated', 'Cashflow projection generated', {
      description: `Projection generated for ${projectionDate}`,
      metadata: { projectionDate }
    });
  }

  /**
   * Log estimate-deal linking
   */
  async logEstimateDealLinked(estimateId: string, dealId: string, projectName: string, linkedBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('estimate_deal_linked', `Estimate linked to deal for "${projectName}'`, {
      entityType: "estimate",
      entityId: estimateId,
      dealId,
      createdBy: linkedBy,
      metadata: { estimateId, dealId }
    });
  }

  /**
   * Log expense operations
   */
  async logExpenseCreated(expenseId: string, expenseName: string, amount: number, createdBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('expense_created', `Expense "${expenseName}' created`, {
      description: `Amount: $${amount.toFixed(2)}`,
      entityType: "expense",
      entityId: expenseId,
      createdBy,
      metadata: { expenseId, amount }
    });
  }

  async logExpenseUpdated(expenseId: string, expenseName: string, changes: EntityChanges, updatedBy: string = 'user'): Promise<Activity> {
    const changesList = Object.keys(changes).filter(key => !['updatedBy', 'updated_at'].includes(key)).join(', ');
    return this.logUserAction('expense_updated', `Expense "${expenseName}' updated`, {
      description: `Updated fields: ${changesList}`,
      entityType: "expense",
      entityId: expenseId,
      createdBy: updatedBy,
      metadata: { changes }
    });
  }

  async logExpenseDeleted(expenseId: string, expenseName: string, amount: number, deletedBy: string = 'user'): Promise<Activity> {
    return this.logUserAction('expense_deleted', `Expense "${expenseName}' deleted`, {
      description: `Amount: $${amount.toFixed(2)}`,
      entityType: "expense",
      entityId: expenseId,
      createdBy: deletedBy,
      importance: "normal",
      metadata: { expenseId, amount }
    });
  }

  /**
   * Log bulk operations
   */
  async logBulkOperationStarted(operationType: string, itemCount: number): Promise<Activity> {
    return this.logSystemEvent('bulk_operation_started', `Bulk ${operationType} started`, {
      description: `Processing ${itemCount} items`,
      metadata: { operationType, itemCount }
    });
  }

  async logBulkOperationCompleted(operationType: string, successCount: number, errorCount: number = 0): Promise<Activity> {
    const hasErrors = errorCount > 0;
    return this.logSystemEvent('bulk_operation_completed", `Bulk ${operationType} completed`, {
      description: `${successCount} items processed successfully${hasErrors ? `, ${errorCount} errors` : "}`,
      importance: hasErrors ? 'high' : "normal",
      metadata: { operationType, successCount, errorCount }
    });
  }
}

// Export singleton instance
const activityLogger = new ActivityLogger();
export default activityLogger;

// Export individual functions for convenience
export const {
  logUserAction,
  logSystemEvent,
  logIntegrationEvent,
  logEntityChange,
  logDealCreated,
  logDealUpdated,
  logDealStageChanged,
  logEstimateCreated,
  logEstimatePublished,
  logCompanyCreated,
  logCompanyUpdated,
  logCompanyRadarChanged,
  logCompanyLinked,
  logContactCreated,
  logNoteAdded,
  logExpenseCreated,
  logExpenseUpdated,
  logExpenseDeleted,
  logHubSpotSyncStarted,
  logHubSpotSyncCompleted,
  logHubSpotSyncFailed,
  logXeroSyncStarted,
  logXeroSyncCompleted,
  logXeroSyncFailed,
  logHarvestSyncStarted,
  logHarvestSyncCompleted,
  logHarvestSyncFailed,
  logCashflowProjectionGenerated,
  logEstimateDealLinked,
  logBulkOperationStarted,
  logBulkOperationCompleted
} = activityLogger;
