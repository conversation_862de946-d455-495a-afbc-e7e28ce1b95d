/**
 * Deal tracking utilities
 *
 * This module provides utilities for tracking deal field ownership and changes.
 */

import { v4 as uuidv4 } from "uuid";
import db from "../api/services/db-service";
import {
  Deal,
  DealUpdate,
  DataSource,
  FieldOwnership,
  ChangeLogEntry
} from "../frontend/types/crm-types";
import activityLogger from "./activity-logger";

// Re-export DataSource type
export { DataSource };

/**
 * Get the owner source for a field (generic for any entity)
 *
 * @param entityType Entity type (e.g., 'deal', 'estimate')
 * @param entityId Entity ID
 * @param fieldName Field name
 * @returns Owner source or null if not found
 */
export function getEntityFieldOwner(entityType: string, entityId: string, fieldName: string): DataSource | null {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'').get();
    if (!tableExists) {
      // Table doesn't exist yet, return null
      return null;
    }

    const result = db.prepare(`
      SELECT owner_system as ownerSource
      FROM field_ownership
      WHERE entity_type = ? AND entity_id = ? AND field_name = ?
    `).get(entityType, entityId, fieldName) as { ownerSource: string } | undefined;

    // Return the value as-is from the database (it's already lowercase)
    // The calling code can handle the comparison with normalization
    return result ? result.ownerSource as DataSource : null;
  } catch (error) {
    console.error(`Error getting field owner for ${fieldName} of ${entityType} ${entityId}:`, error);
    return null;
  }
}

/**
 * Get the owner source for a field (backward compatibility for deals)
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @returns Owner source or null if not found
 */
export function getFieldOwner(dealId: string, fieldName: string): DataSource | null {
  return getEntityFieldOwner('deal', dealId, fieldName);
}

/**
 * Get all field ownership records for a deal
 *
 * @param dealId Deal ID
 * @returns Array of field ownership records
 */
export function getDealFieldOwnership(dealId: string): FieldOwnership[] {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'').get();
    if (!tableExists) {
      // Table doesn't exist yet, return empty array
      return [];
    }

    return db.prepare(`
      SELECT
        entity_id as dealId,
        field_name as fieldName,
        owner_system as ownerSource,
        set_at as lastUpdated
      FROM field_ownership
      WHERE entity_type = 'deal' AND entity_id = ?
    `).all(dealId) as FieldOwnership[];
  } catch (error) {
    console.error(`Error getting field ownership for deal ${dealId}:`, error);
    return [];
  }
}

/**
 * Convert DataSource to lowercase for database storage
 */
function normalizeOwnerSystem(source: DataSource | string): string {
  // Map TypeScript DataSource values to database values
  const mapping: Record<string, string> = {
    'HubSpot': 'hubspot',
    'Estimate': 'estimate',
    'Manual': 'manual',
    'System': 'system',
    'Harvest': 'harvest',
    'Xero': 'xero',
    'Deal': 'deal',
    'deal': 'deal',
    'estimate': 'estimate',
    'manual': 'manual',
    'hubspot': 'hubspot',
    'harvest': 'harvest',
    'xero': 'xero',
    'system': 'system'
  };
  
  return mapping[source] || source.toLowerCase();
}

/**
 * Set the owner source for a field (generic for any entity type)
 *
 * @param entityType Entity type (e.g., 'deal', 'estimate')
 * @param entityId Entity ID
 * @param fieldName Field name
 * @param ownerSource Owner source
 * @returns True if successful
 */
export function setFieldOwnerForEntity(entityType: string, entityId: string, fieldName: string, ownerSource: DataSource | string): boolean {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'').get();
    if (!tableExists) {
      // Table doesn't exist yet, create it
      db.prepare(`
        CREATE TABLE field_ownership (
          id TEXT PRIMARY KEY,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          field_name TEXT NOT NULL,
          owner_system TEXT NOT NULL,
          set_at TEXT NOT NULL,
          set_by TEXT,
          UNIQUE(entity_type, entity_id, field_name)
        )
      `).run();

      // Create indexes
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_field_ownership_entity ON field_ownership(entity_type, entity_id)`).run();
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_field_ownership_owner ON field_ownership(owner_system)`).run();
    }

    const id = uuidv4();
    const now = new Date().toISOString();
    
    // Normalize the owner source to lowercase for database storage
    const normalizedOwner = normalizeOwnerSystem(ownerSource);

    // Check if the field ownership record already exists
    const existingOwner = db.prepare(`
      SELECT owner_system as ownerSource
      FROM field_ownership
      WHERE entity_type = ? AND entity_id = ? AND field_name = ?
    `).get(entityType, entityId, fieldName) as { ownerSource: string } | undefined;

    if (existingOwner) {
      // Update existing record
      db.prepare(`
        UPDATE field_ownership
        SET owner_system = ?, set_at = ?
        WHERE entity_type = ? AND entity_id = ? AND field_name = ?
      `).run(normalizedOwner, now, entityType, entityId, fieldName);
    } else {
      // Insert new record
      db.prepare(`
        INSERT INTO field_ownership (
          id, entity_type, entity_id, field_name, owner_system, set_at, set_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(id, entityType, entityId, fieldName, normalizedOwner, now, 'system');
    }

    return true;
  } catch (error) {
    console.error(`Error setting field owner for ${fieldName} of ${entityType} ${entityId}:`, error);
    return false;
  }
}

/**
 * Set the owner source for a deal field (backward compatibility wrapper)
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @param ownerSource Owner source
 * @returns True if successful
 */
export function setFieldOwner(dealId: string, fieldName: string, ownerSource: DataSource): boolean {
  return setFieldOwnerForEntity('deal', dealId, fieldName, ownerSource);
}

/**
 * Clear field ownership for specific fields (generic for any entity type)
 *
 * @param entityType Entity type (e.g., 'deal', 'estimate')
 * @param entityId Entity ID
 * @param fieldNames Array of field names to clear ownership for
 * @returns True if successful
 */
export function clearFieldOwnershipForEntity(entityType: string, entityId: string, fieldNames: string[]): boolean {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='field_ownership'').get();
    if (!tableExists) {
      // Table doesn't exist yet, nothing to clear
      return true;
    }

    const placeholders = fieldNames.map(() => '?').join(', ');
    db.prepare(`
      DELETE FROM field_ownership
      WHERE entity_type = ? AND entity_id = ? AND field_name IN (${placeholders})
    `).run(entityType, entityId, ...fieldNames);

    return true;
  } catch (error) {
    console.error(`Error clearing field ownership for ${entityType} ${entityId}:`, error);
    return false;
  }
}

/**
 * Clear field ownership for deal fields (backward compatibility wrapper)
 *
 * @param dealId Deal ID
 * @param fieldNames Array of field names to clear ownership for
 * @returns True if successful
 */
export function clearFieldOwnership(dealId: string, fieldNames: string[]): boolean {
  return clearFieldOwnershipForEntity('deal', dealId, fieldNames);
}

/**
 * Log a change to a deal field
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @param oldValue Old value
 * @param newValue New value
 * @param changeSource Source of the change
 * @param changedBy User or system that made the change
 * @returns True if successful
 */
export function logFieldChange(
  dealId: string,
  fieldName: string,
  oldValue: any,
  newValue: any,
  changeSource: DataSource,
  changedBy?: string
): boolean {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='change_log'').get();
    if (!tableExists) {
      // Table doesn't exist yet, create it
      db.prepare(`
        CREATE TABLE change_log (
          id TEXT PRIMARY KEY,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          field_name TEXT NOT NULL,
          old_value TEXT,
          new_value TEXT,
          change_type TEXT NOT NULL CHECK(change_type IN ('create', 'update', 'delete', 'restore')),
          change_source TEXT CHECK(change_source IN ('ui', 'api', 'sync', 'import', 'system')),
          changed_at TEXT NOT NULL,
          changed_by TEXT
        )
      `).run();

      // Create indexes
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_entity ON change_log(entity_type, entity_id)`).run();
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_field ON change_log(field_name)`).run();
      db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_date ON change_log(changed_at)`).run();
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    // Convert values to strings for storage
    const oldValueStr = oldValue !== undefined && oldValue !== null ?
      (typeof oldValue === 'object' ? JSON.stringify(oldValue) : String(oldValue)) :
      null;

    const newValueStr = newValue !== undefined && newValue !== null ?
      (typeof newValue === 'object' ? JSON.stringify(newValue) : String(newValue)) :
      null;

    // Map the changeSource to valid values for change_log table
    let mappedSource: string;
    switch (changeSource) {
      case 'HubSpot':
      case 'Harvest':
      case 'Xero':
        mappedSource = 'sync';  // External system syncs
        break;
      case 'Manual':
      case 'User':
        mappedSource = 'ui';    // User interface changes
        break;
      case 'System':
        mappedSource = 'system';
        break;
      case 'Import':
        mappedSource = 'import';
        break;
      case 'API':
        mappedSource = 'api';
        break;
      default:
        mappedSource = 'system'; // Default fallback
    }

    // Insert change log entry
    db.prepare(`
      INSERT INTO change_log (
        id, entity_type, entity_id, field_name, old_value, new_value,
        change_type, change_source, changed_at, changed_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id,
      'deal',
      dealId,
      fieldName,
      oldValueStr,
      newValueStr,
      'update',  // Since this is a field change, it's always an update
      mappedSource,
      now,
      changedBy || null
    );

    return true;
  } catch (error) {
    console.error(`Error logging field change for ${fieldName} of deal ${dealId}:`, error);
    return false;
  }
}

/**
 * Get change history for a deal field
 *
 * @param dealId Deal ID
 * @param fieldName Field name (optional - if not provided, returns changes for all fields)
 * @param limit Maximum number of changes to return (default: 10)
 * @returns Array of change log entries
 */
export function getFieldChangeHistory(
  dealId: string,
  fieldName?: string,
  limit: number = 10
): ChangeLogEntry[] {
  try {
    // Check if the table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='change_log'').get();
    if (!tableExists) {
      // Table doesn't exist yet, return empty array
      return [];
    }

    let query = `
      SELECT
        id, entity_id as dealId, field_name as fieldName,
        old_value as oldValue, new_value as newValue,
        change_source as changeSource, changed_at as changedAt,
        changed_by as changedBy
      FROM change_log
      WHERE entity_type = 'deal' AND entity_id = ?
    `;

    const params: any[] = [dealId];

    if (fieldName) {
      query += ' AND field_name = ?';
      params.push(fieldName);
    }

    query += ' ORDER BY changed_at DESC LIMIT ?';
    params.push(limit);

    return db.prepare(query).all(...params) as ChangeLogEntry[];
  } catch (error) {
    console.error(`Error getting change history for deal ${dealId}:`, error);
    return [];
  }
}

/**
 * Track changes to a deal
 *
 * @param dealId Deal ID
 * @param oldDeal Original deal data
 * @param newData New deal data
 * @param source Source of the changes
 * @param changedBy User or system that made the changes
 * @returns True if successful
 */
export function trackDealChanges(
  dealId: string,
  oldDeal: Deal,
  newData: DealUpdate,
  source: DataSource,
  changedBy?: string
): boolean {
  try {
    // In the fresh unified data model, we don't use field_ownership table anymore
    // Instead, we track ownership through the source field and audit fields

    const changes: Record<string, any> = {};
    let hasChanges = false;

    // Track changes for audit purposes and activity logging
    for (const [key, newValue] of Object.entries(newData)) {
      // Skip undefined values (no change)
      if (newValue === undefined) continue;

      const oldValue = (oldDeal as any)[key];

      // Skip if values are the same
      if (oldValue === newValue) continue;

      // Log the change to the console
      console.log(`Deal ${dealId} field ${key} changed from ${oldValue} to ${newValue} by ${source}`);

      // Track the change for activity logging
      changes[key] = { oldValue, newValue };
      hasChanges = true;

      // Log field change to change_log table
      logFieldChange(dealId, key, oldValue, newValue, source, changedBy);
    }

    // Log activity for deal updates if there are changes
    if (hasChanges) {
      const dealName = newData.name || oldDeal.name || 'Unknown Deal';

      // Check for stage changes specifically
      if (changes.stage) {
        activityLogger.logDealStageChanged(
          dealId,
          dealName,
          changes.stage.oldValue,
          changes.stage.newValue,
          changedBy || source
        ).catch(error => {
          console.error('Error logging deal stage change activity:', error);
        });
      } else {
        // Log general deal update
        activityLogger.logDealUpdated(
          dealId,
          dealName,
          changes,
          changedBy || source
        ).catch(error => {
          console.error('Error logging deal update activity:', error);
        });
      }
    }

    return true;
  } catch (error) {
    console.error(`Error tracking changes for deal ${dealId}:`, error);
    return false;
  }
}

/**
 * Check if a source is allowed to update a field
 *
 * @param dealId Deal ID
 * @param fieldName Field name
 * @param source Source attempting the update
 * @returns True if the source is allowed to update the field
 */
export function canSourceUpdateField(
  dealId: string,
  fieldName: string,
  source: DataSource
): boolean {
  // System source can update any field
  if (source === 'System') return true;

  // Check the actual field ownership
  const fieldOwner = getFieldOwner(dealId, fieldName);
  console.log(`canSourceUpdateField - dealId: ${dealId}, field: ${fieldName}, source: ${source}, owner: ${fieldOwner}`);
  
  // Normalize both values for comparison
  const normalizedSource = normalizeOwnerSystem(source);
  const normalizedOwner = fieldOwner ? normalizeOwnerSystem(fieldOwner) : null;
  
  // If there's a field owner, check if the source matches or has permission
  if (normalizedOwner) {
    // The owner can always update their own field
    if (normalizedOwner === normalizedSource) return true;
    
    // If field is owned by estimate, only estimate or system can update it
    if (normalizedOwner === 'estimate') {
      return normalizedSource === 'estimate' || source === 'System';
    }
    
    // If field is owned by HubSpot, only HubSpot or System can update it
    if (normalizedOwner === 'hubspot') {
      return normalizedSource === 'hubspot' || source === 'System';
    }
    
    // For other owners, respect the ownership
    return false;
  }
  
  // No field owner set, use default rules
  
  // Manual source can update any unowned field
  if (source === 'Manual') {
    return true;
  }

  // HubSpot source can update any unowned field
  if (source === 'HubSpot') {
    return true;
  }

  // Estimate source can only update estimate-related fields
  if (source === 'Estimate') {
    const estimateFields = ['startDate', 'endDate', 'invoiceFrequency', 'paymentTerms', 'value'];
    return estimateFields.includes(fieldName);
  }

  // Default to allowing the update for unowned fields
  return true;
}

/**
 * Log deal creation activity
 *
 * @param dealId Deal ID
 * @param dealName Deal name
 * @param createdBy User or system that created the deal
 */
export function logDealCreation(dealId: string, dealName: string, createdBy: string = 'user'): void {
  activityLogger.logDealCreated(dealId, dealName, createdBy).catch(error => {
    console.error('Error logging deal creation activity:', error);
  });
}

/**
 * Initialize field ownership for a new deal
 *
 * @param dealId Deal ID
 * @param hasHubspotId Whether the deal has a HubSpot ID
 * @returns True if successful
 */
export function initializeFieldOwnership(dealId: string, hasHubspotId: boolean): boolean {
  try {
    // In the fresh unified data model, we don't use field_ownership table anymore
    // Instead, we track ownership through the source field and audit fields

    const now = new Date().toISOString();
    const source = hasHubspotId ? 'HubSpot' : 'Manual';

    // Update the deal with the source
    db.prepare(`
      UPDATE deal
      SET source = ?,
          updated_at = ?,
          updated_by = 'system'
      WHERE id = ?
    `).run(source, now, dealId);

    return true;
  } catch (error) {
    console.error(`Error initializing field ownership for deal ${dealId}:`, error);
    return false;
  }
}
