/**
 * Authentication utilities
 * 
 * This module provides utilities for authentication-related operations.
 */

import { Request } from "express";
import { getXeroService } from "../services/xero";

/**
 * Get the tenant ID from the authenticated user's request
 * 
 * @param req Express request object
 * @returns Tenant ID or null if not found
 */
export function getUserTenantId(req: Request): string | null {
  // First check if there's an active tenant in the session
  if (req.session?.activeTenant?.tenantId) {
    return req.session.activeTenant.tenantId;
  }
  
  // If not in session, try to get from the Xero service
  const xeroService = getXeroService();
  return xeroService.getActiveTenantId();
}
