/**
 * Comprehensive logging service for the financial dashboard
 * Replaces scattered console.log statements with structured, secure logging
 */

import { v4 as uuidv4 } from "uuid";

/**
 * Log levels in order of severity
 */
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

/**
 * Core context interface for all log entries
 */
export interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component?: string;
  operation?: string;
  correlationId?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Specialized context interfaces
 */
export interface ApiContext extends LogContext {
  method?: string;
  url?: string;
  statusCode?: number;
  duration?: number;
  responseSize?: number;
  rateLimitRemaining?: number;
}

export interface UserContext extends LogContext {
  action: string;
  target?: string;
  timestamp?: string;
}

export interface BusinessContext extends LogContext {
  entityType?: string;
  entityId?: string;
  previousValue?: unknown;
  newValue?: unknown;
  changeSource?: string;
}

export interface PerformanceContext extends LogContext {
  operation: string;
  duration?: number;
  memoryUsage?: number;
  timestamp?: string;
}

/**
 * Log entry structure
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  environment: string;
  source: 'frontend' | 'backend';
}

/**
 * Sensitive fields that should be masked in logs
 */
const SENSITIVE_FIELDS = [
  'password',
  'token',
  'secret',
  'key',
  'authorization',
  'cookie',
  'session',
  'access_token',
  'refresh_token',
  'api_key',
  'private_key',
  'client_secret',
  'oauth_token',
  'bearer',
  'credit_card',
  'ssn',
  'tax_id'
];

/**
 * Configuration for the logging service
 */
export interface LoggerConfig {
  level: LogLevel;
  environment: 'development' | 'production' | 'test';
  enableConsole: boolean;
  enableStructuredLogging: boolean;
  enableSanitization: boolean;
  enablePerformanceLogging: boolean;
  maxContextSize: number;
  enableAsyncLogging: boolean;
  logRetentionHours?: number;
}

/**
 * Default configuration based on environment
 */
function getDefaultConfig(): LoggerConfig {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV === 'test';
  
  return {
    level: isDevelopment ? LogLevel.TRACE : LogLevel.INFO,
    environment: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
    enableConsole: isDevelopment || isTest,
    enableStructuredLogging: !isDevelopment,
    enableSanitization: true,
    enablePerformanceLogging: isDevelopment,
    maxContextSize: 10000, // 10KB max context size
    enableAsyncLogging: !isDevelopment,
    logRetentionHours: 24
  };
}

/**
 * Core Logger class
 */
export class Logger {
  private config: LoggerConfig;
  private sessionId: string;
  private performanceTimers: Map<string, number> = new Map();
  private logQueue: LogEntry[] = [];
  private flushInterval?: NodeJS.Timeout;

  constructor(config?: Partial<LoggerConfig>) {
    this.config = { ...getDefaultConfig(), ...config };
    this.sessionId = uuidv4();
    
    if (this.config.enableAsyncLogging) {
      this.startAsyncLogging();
    }
  }

  /**
   * Check if a log level should be logged
   */
  private shouldLog(level: LogLevel): boolean {
    return level <= this.config.level;
  }

  /**
   * Sanitize sensitive data from context and messages
   */
  private sanitize(data: unknown): unknown {
    if (!this.config.enableSanitization) {
      return data;
    }

    if (typeof data === 'string') {
      // Mask potential tokens in strings
      return data.replace(/(token|key|secret|password)[\s=:]+[^\s&]+/gi, '$1=***MASKED***');
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitize(item));
    }

    if (data && typeof data === 'object') {
      const sanitized: Record<string, unknown> = {};
      
      for (const [key, value] of Object.entries(data)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = SENSITIVE_FIELDS.some(field => lowerKey.includes(field));
        
        if (isSensitive) {
          sanitized[key] = '***MASKED***';
        } else if (typeof value === 'object') {
          sanitized[key] = this.sanitize(value);
        } else {
          sanitized[key] = value;
        }
      }
      
      return sanitized;
    }

    return data;
  }

  /**
   * Limit context size to prevent memory issues
   */
  private limitContextSize(context: unknown): unknown {
    const json = JSON.stringify(context);
    if (json.length > this.config.maxContextSize) {
      return {
        ...context,
        _truncated: true,
        _originalSize: json.length,
        _maxSize: this.config.maxContextSize
      };
    }
    return context;
  }

  /**
   * Create a standardized log entry
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: LogContext,
    error?: Error
  ): LogEntry {
    // Add session context
    const enrichedContext: LogContext = {
      sessionId: this.sessionId,
      requestId: context?.requestId || uuidv4(),
      ...context
    };

    // Sanitize and limit context
    const sanitizedContext = this.sanitize(this.limitContextSize(enrichedContext));

    const entry: LogEntry = {
      level,
      message: this.sanitize(message),
      timestamp: new Date().toISOString(),
      context: sanitizedContext,
      environment: this.config.environment,
      source: typeof window !== 'undefined' ? 'frontend' : 'backend'
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: this.sanitize(error.message),
        stack: this.config.environment === 'development' ? error.stack : undefined,
        code: (error as Error & { code?: string }).code
      };
    }

    return entry;
  }

  /**
   * Output log entry to appropriate destinations
   */
  private output(entry: LogEntry): void {
    if (this.config.enableAsyncLogging) {
      this.logQueue.push(entry);
      return;
    }

    this.writeLog(entry);
  }

  /**
   * Write log entry to console or structured output
   */
  private writeLog(entry: LogEntry): void {
    if (this.config.enableConsole) {
      this.writeToConsole(entry);
    }

    if (this.config.enableStructuredLogging) {
      this.writeStructured(entry);
    }
  }

  /**
   * Write to console with appropriate formatting
   */
  private writeToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const prefix = `[${timestamp}] [${LogLevel[entry.level]}]`;
    const contextStr = entry.context ? ` (${entry.context.component || entry.context.operation || ''})` : '';
    const message = `${prefix}${contextStr} ${entry.message}`;

    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(message, entry.error || '', entry.context || '');
        break;
      case LogLevel.WARN:
        console.warn(message, entry.context || '');
        break;
      case LogLevel.INFO:
        console.info(message, entry.context || '');
        break;
      default:
        console.log(message, entry.context || '');
        break;
    }
  }

  /**
   * Write structured log (JSON format for production)
   */
  private writeStructured(entry: LogEntry): void {
    // In a real application, this would send to a logging service
    // For now, we'll write structured JSON to console
    const structuredLog = JSON.stringify(entry);
    
    // Could integrate with external services like:
    // - Winston for Node.js
    // - LogRocket for frontend
    // - DataDog, New Relic, etc.
    if (typeof window === 'undefined') {
      // Backend - could write to file or send to logging service
      process.stdout.write(structuredLog + '\n');
    } else {
      // Frontend - could send to analytics service
      if (this.config.environment === 'development') {
        console.log('STRUCTURED:', structuredLog);
      }
    }
  }

  /**
   * Start async logging with periodic flushing
   */
  private startAsyncLogging(): void {
    this.flushInterval = setInterval(() => {
      this.flush();
    }, 1000); // Flush every second
  }

  /**
   * Flush queued logs
   */
  private flush(): void {
    if (this.logQueue.length === 0) return;

    const logs = [...this.logQueue];
    this.logQueue = [];

    logs.forEach(log => this.writeLog(log));
  }

  /**
   * Core logging methods
   */
  error(message: string, error?: Error, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error);
    this.output(entry);
  }

  warn(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    
    const entry = this.createLogEntry(LogLevel.WARN, message, context);
    this.output(entry);
  }

  info(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    
    const entry = this.createLogEntry(LogLevel.INFO, message, context);
    this.output(entry);
  }

  debug(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
    this.output(entry);
  }

  trace(message: string, context?: LogContext): void {
    if (!this.shouldLog(LogLevel.TRACE)) return;
    
    const entry = this.createLogEntry(LogLevel.TRACE, message, context);
    this.output(entry);
  }

  /**
   * Domain-specific logging methods
   */
  apiCall(operation: string, context: ApiContext): void {
    const message = `API Call: ${context.method} ${context.url}`;
    const enrichedContext = {
      ...context,
      operation: 'api_call',
      component: 'api'
    };

    if (context.statusCode && context.statusCode >= 400) {
      this.error(message, undefined, enrichedContext);
    } else {
      this.info(message, enrichedContext);
    }
  }

  userAction(action: string, context: UserContext): void {
    const message = `User Action: ${action}`;
    const enrichedContext = {
      ...context,
      operation: 'user_action',
      component: 'user_interface'
    };

    this.info(message, enrichedContext);
  }

  businessEvent(event: string, context: BusinessContext): void {
    const message = `Business Event: ${event}`;
    const enrichedContext = {
      ...context,
      operation: 'business_event',
      component: 'business_logic'
    };

    this.info(message, enrichedContext);
  }

  /**
   * Performance logging
   */
  time(label: string, context?: PerformanceContext): void {
    if (!this.config.enablePerformanceLogging) return;
    
    this.performanceTimers.set(label, performance.now());
    this.debug(`Timer started: ${label}`, { 
      ...context, 
      operation: 'performance_start',
      component: 'performance'
    });
  }

  timeEnd(label: string, context?: PerformanceContext): void {
    if (!this.config.enablePerformanceLogging) return;
    
    const startTime = this.performanceTimers.get(label);
    if (!startTime) {
      this.warn(`Timer '${label}' was not started`, { component: 'performance' });
      return;
    }

    const duration = performance.now() - startTime;
    this.performanceTimers.delete(label);

    const perfContext: PerformanceContext = {
      ...context,
      operation: 'performance_end',
      component: 'performance',
      duration: Math.round(duration * 100) / 100 // Round to 2 decimals
    };

    this.info(`Timer completed: ${label} (${perfContext.duration}ms)`, perfContext);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    this.flush(); // Final flush
  }
}

/**
 * Global logger instance
 */
let globalLogger: Logger;

/**
 * Get or create the global logger instance
 */
export function getLogger(config?: Partial<LoggerConfig>): Logger {
  if (!globalLogger) {
    globalLogger = new Logger(config);
  }
  return globalLogger;
}

/**
 * Initialize logger with specific configuration
 */
export function initializeLogger(config: Partial<LoggerConfig>): Logger {
  globalLogger = new Logger(config);
  return globalLogger;
}

/**
 * Convenience functions for global logger
 */
export const logger = {
  error: (message: string, error?: Error, context?: LogContext) => 
    getLogger().error(message, error, context),
  
  warn: (message: string, context?: LogContext) => 
    getLogger().warn(message, context),
  
  info: (message: string, context?: LogContext) => 
    getLogger().info(message, context),
  
  debug: (message: string, context?: LogContext) => 
    getLogger().debug(message, context),
  
  trace: (message: string, context?: LogContext) => 
    getLogger().trace(message, context),
  
  apiCall: (operation: string, context: ApiContext) => 
    getLogger().apiCall(operation, context),
  
  userAction: (action: string, context: UserContext) => 
    getLogger().userAction(action, context),
  
  businessEvent: (event: string, context: BusinessContext) => 
    getLogger().businessEvent(event, context),
  
  time: (label: string, context?: PerformanceContext) => 
    getLogger().time(label, context),
  
  timeEnd: (label: string, context?: PerformanceContext) => 
    getLogger().timeEnd(label, context)
};

export default logger;