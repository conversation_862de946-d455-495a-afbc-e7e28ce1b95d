/**
 * Circuit Breaker Pattern Implementation
 *
 * Prevents cascading failures by monitoring external service calls
 * and temporarily blocking requests when failure threshold is reached.
 */

export interface CircuitBreakerOptions {
  failureThreshold: number; // Number of failures before opening circuit
  successThreshold: number; // Number of successes before closing circuit
  timeout: number; // Timeout for each request in ms
  resetTimeout: number; // Time to wait before attempting to close circuit in ms
  monitoringPeriod: number; // Time window for counting failures in ms
  name: string; // Name for logging
}

export interface CircuitBreakerState {
  failures: number;
  successes: number;
  lastFailureTime: number;
  state: "CLOSED" | "OPEN" | "HALF_OPEN";
  nextAttempt: number;
}

export class CircuitBreaker<T = unknown> {
  private options: CircuitBreakerOptions;
  private state: CircuitBreakerState;
  private stateChangeListeners: Array<(state: CircuitBreakerState) => void> =
    [];

  constructor(options: Partial<CircuitBreakerOptions> = {}) {
    this.options = {
      failureThreshold: options.failureThreshold || 5,
      successThreshold: options.successThreshold || 2,
      timeout: options.timeout || 30000, // 30 seconds
      resetTimeout: options.resetTimeout || 60000, // 1 minute
      monitoringPeriod: options.monitoringPeriod || 60000, // 1 minute
      name: options.name || "CircuitBreaker",
    };

    this.state = {
      failures: 0,
      successes: 0,
      lastFailureTime: 0,
      state: "CLOSED",
      nextAttempt: 0,
    };
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<R>(fn: () => Promise<R>): Promise<R> {
    if (this.state.state === "OPEN") {
      if (Date.now() < this.state.nextAttempt) {
        throw new CircuitBreakerError(
          `Circuit breaker is OPEN for ${this.options.name}. Retry after ${new Date(this.state.nextAttempt).toISOString()}`,
          "CIRCUIT_OPEN",
        );
      }
      // Move to half-open state
      this.setState("HALF_OPEN");
    }

    try {
      // Execute with timeout
      const result = await this.executeWithTimeout(fn);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<R>(fn: () => Promise<R>): Promise<R> {
    return new Promise<R>((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(
          new CircuitBreakerError(
            `Request timeout after ${this.options.timeout}ms`,
            "TIMEOUT",
          ),
        );
      }, this.options.timeout);

      fn()
        .then((result) => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Handle successful execution
   */
  private onSuccess(): void {
    this.resetFailuresIfNeeded();

    if (this.state.state === "HALF_OPEN") {
      this.state.successes++;
      if (this.state.successes >= this.options.successThreshold) {
        this.setState("CLOSED");
        this.state.failures = 0;
        this.state.successes = 0;
      }
    }
  }

  /**
   * Handle failed execution
   */
  private onFailure(): void {
    this.state.failures++;
    this.state.lastFailureTime = Date.now();

    if (this.state.state === "HALF_OPEN") {
      this.setState("OPEN");
      this.state.nextAttempt = Date.now() + this.options.resetTimeout;
    } else if (this.state.failures >= this.options.failureThreshold) {
      this.setState("OPEN");
      this.state.nextAttempt = Date.now() + this.options.resetTimeout;
    }
  }

  /**
   * Reset failures if outside monitoring period
   */
  private resetFailuresIfNeeded(): void {
    const now = Date.now();
    if (now - this.state.lastFailureTime > this.options.monitoringPeriod) {
      this.state.failures = 0;
    }
  }

  /**
   * Set circuit breaker state
   */
  private setState(newState: "CLOSED" | "OPEN" | "HALF_OPEN"): void {
    const oldState = this.state.state;
    this.state.state = newState;

    if (oldState !== newState) {
      // Log state changes in development only
      if (process.env.NODE_ENV !== "production") {
        process.stderr.write(
          `[${this.options.name}] Circuit breaker state changed: ${oldState} → ${newState}\n`,
        );
      }
      this.notifyStateChange();
    }
  }

  /**
   * Get current state
   */
  getState(): CircuitBreakerState {
    return { ...this.state };
  }

  /**
   * Subscribe to state changes
   */
  onStateChange(listener: (state: CircuitBreakerState) => void): () => void {
    this.stateChangeListeners.push(listener);
    return () => {
      this.stateChangeListeners = this.stateChangeListeners.filter(
        (l) => l !== listener,
      );
    };
  }

  /**
   * Notify listeners of state change
   */
  private notifyStateChange(): void {
    const currentState = this.getState();
    this.stateChangeListeners.forEach((listener) => {
      try {
        listener(currentState);
      } catch (error) {
        // Log listener errors in development only
        if (process.env.NODE_ENV !== "production") {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          process.stderr.write(
            `Error in state change listener: ${errorMessage}\n`,
          );
        }
      }
    });
  }

  /**
   * Force circuit to close (for testing/manual override)
   */
  close(): void {
    this.setState("CLOSED");
    this.state.failures = 0;
    this.state.successes = 0;
    this.state.nextAttempt = 0;
  }

  /**
   * Force circuit to open (for testing/manual override)
   */
  open(): void {
    this.setState("OPEN");
    this.state.nextAttempt = Date.now() + this.options.resetTimeout;
  }
}

/**
 * Circuit Breaker Error
 */
export class CircuitBreakerError extends Error {
  constructor(
    message: string,
    public code: "CIRCUIT_OPEN" | "TIMEOUT" | "UNKNOWN" = "UNKNOWN",
  ) {
    super(message);
    this.name = "CircuitBreakerError";
  }
}

/**
 * Factory function to create typed circuit breakers
 */
export function createCircuitBreaker<T>(
  options: Partial<CircuitBreakerOptions>,
): CircuitBreaker<T> {
  return new CircuitBreaker<T>(options);
}

/**
 * Global circuit breaker registry
 */
class CircuitBreakerRegistry {
  private breakers: Map<string, CircuitBreaker> = new Map();

  /**
   * Get or create a circuit breaker
   */
  getBreaker(
    name: string,
    options?: Partial<CircuitBreakerOptions>,
  ): CircuitBreaker {
    if (!this.breakers.has(name)) {
      this.breakers.set(
        name,
        new CircuitBreaker({
          ...options,
          name,
        }),
      );
    }
    return this.breakers.get(name)!;
  }

  /**
   * Get all circuit breakers
   */
  getAllBreakers(): Map<string, CircuitBreaker> {
    return new Map(this.breakers);
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    this.breakers.forEach((breaker) => breaker.close());
  }

  /**
   * Get status of all breakers
   */
  getStatus(): Record<string, CircuitBreakerState> {
    const status: Record<string, CircuitBreakerState> = {};
    this.breakers.forEach((breaker, name) => {
      status[name] = breaker.getState();
    });
    return status;
  }
}

// Export singleton registry
export const circuitBreakerRegistry = new CircuitBreakerRegistry();
