/**
 * Utilities to help migrate from console.log statements to structured logging
 * Provides backwards compatibility and gradual migration tools
 */

import { logger } from "./logger";
import { backendLogger } from "./backend-logger";
import { frontendLogger } from "./frontend-logger";

// TypeScript type definitions for console arguments and logging
type ConsoleArgument = string | number | boolean | Error | Record<string, unknown> | null | undefined;
type ConsoleArguments = ConsoleArgument[];
type LogMetadata = Record<string, unknown>;
type ServiceName = 'xero' | "harvest" | "hubspot" | "database" | "api" | "unknown";
type LogPattern = 'error' | "api" | "repository" | "user_action" | "business_event" | "performance" | "config" | "general";
type LogPriority = 'high' | "medium" | "low";

interface AnalysisResult {
  pattern: string;
  suggestion: string;
  priority: LogPriority;
}

interface MigrationSuggestion {
  line: number;
  original: string;
  suggestion: string;
  priority: string;
}

interface ConsoleStatement {
  line: number;
  statement: string;
  args: ConsoleArguments;
}

interface MigrationReport {
  file: string;
  totalStatements: number;
  patterns: Record<string, number>;
  highPriorityCount: number;
  suggestions: MigrationSuggestion[];
}

/**
 * Environment detection
 */
const isBrowser = typeof window !== 'undefined';
const isProduction = process.env.NODE_ENV === 'production';

/**
 * Legacy console replacement that provides structured logging
 */
export const console = {
  /**
   * Enhanced error logging
   */
  error: (...args: ConsoleArguments) => {
    const [message, ...rest] = args;
    const error = rest.find(arg => arg instanceof Error);
    const context = rest.find(arg => arg && typeof arg === 'object' && !(arg instanceof Error));
    
    if (isBrowser) {
      frontendLogger.error(String(message), error, context);
    } else {
      backendLogger.error(String(message), error, context);
    }
    
    // Still output to original console in development
    if (!isProduction) {
      globalThis.console.error(...args);
    }
  },

  /**
   * Enhanced warning logging
   */
  warn: (...args: ConsoleArguments) => {
    const [message, ...rest] = args;
    const context = rest.find(arg => arg && typeof arg === 'object');
    
    if (isBrowser) {
      frontendLogger.warn(String(message), context);
    } else {
      backendLogger.warn(String(message), context);
    }
    
    if (!isProduction) {
      globalThis.console.warn(...args);
    }
  },

  /**
   * Enhanced info logging
   */
  info: (...args: ConsoleArguments) => {
    const [message, ...rest] = args;
    const context = rest.find(arg => arg && typeof arg === 'object');
    
    if (isBrowser) {
      frontendLogger.info(String(message), context);
    } else {
      backendLogger.info(String(message), context);
    }
    
    if (!isProduction) {
      globalThis.console.info(...args);
    }
  },

  /**
   * Enhanced debug logging
   */
  log: (...args: ConsoleArguments) => {
    const [message, ...rest] = args;
    const context = rest.find(arg => arg && typeof arg === 'object');
    
    if (isBrowser) {
      frontendLogger.debug(String(message), context);
    } else {
      backendLogger.debug(String(message), context);
    }
    
    if (!isProduction) {
      globalThis.console.log(...args);
    }
  },

  /**
   * Debug logging (same as log but explicit)
   */
  debug: (...args: ConsoleArguments) => {
    const [message, ...rest] = args;
    const context = rest.find(arg => arg && typeof arg === 'object');
    
    if (isBrowser) {
      frontendLogger.debug(String(message), context);
    } else {
      backendLogger.debug(String(message), context);
    }
    
    if (!isProduction) {
      globalThis.console.debug(...args);
    }
  },

  /**
   * Passthrough methods for non-logging console functions
   */
  table: globalThis.console.table.bind(globalThis.console),
  group: globalThis.console.group.bind(globalThis.console),
  groupEnd: globalThis.console.groupEnd.bind(globalThis.console),
  time: globalThis.console.time.bind(globalThis.console),
  timeEnd: globalThis.console.timeEnd.bind(globalThis.console),
  clear: globalThis.console.clear.bind(globalThis.console),
  trace: globalThis.console.trace.bind(globalThis.console)
};

/**
 * Migration helper functions for common logging patterns
 */
export const logPatterns = {
  /**
   * API call logging pattern
   */
  apiCall: (method: string, url: string, status?: number, duration?: number, error?: Error) => {
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    
    if (isBrowser) {
      frontendLogger.logApiCall(url, method, { status, duration, error });
    } else {
      backendLogger.logIntegration(`${method} ${url}`, {
        service: "unknown" as ServiceName,
        url,
        method,
        statusCode: status,
        duration
      });
    }
  },

  /**
   * Repository operation logging pattern
   */
  repository: (repository: string, method: string, entityId?: string, duration?: number, error?: Error) => {
    if (!isBrowser) {
      backendLogger.logRepositoryOperation(repository, method, entityId, duration, error);
    }
  },

  /**
   * User action logging pattern  
   */
  userAction: (action: string, target?: string, userId?: string) => {
    if (isBrowser) {
      frontendLogger.logComponentInteraction('unknown', action, { 
        metadata: { target, userId }
      });
    } else {
      backendLogger.logAuthEvent('login', userId, {
        metadata: { action, target }
      });
    }
  },

  /**
   * Business event logging pattern
   */
  businessEvent: (event: string, entityType?: string, entityId?: string, changes?: LogMetadata) => {
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    
    if (isBrowser) {
      frontendLogger.logBusinessAction(event, entityType, entityId, {
        metadata: { changes }
      });
    } else {
      backendLogger.logBusinessEvent(event, entityType || "unknown", entityId || "unknown", undefined, changes);
    }
  },

  /**
   * Error handling pattern
   */
  error: (context: string, error: Error, additionalInfo?: LogMetadata) => {
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    activeLogger.error(`${context}: ${error.message}`, error, {
      metadata: additionalInfo
    });
  },

  /**
   * Performance logging pattern
   */
  performance: (operation: string, duration: number, additionalInfo?: LogMetadata) => {
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    activeLogger.info(`Performance: ${operation} completed in ${duration}ms`, {
      component: "performance",
      metadata: { operation, duration, ...additionalInfo }
    });
  },

  /**
   * Configuration/startup logging pattern
   */
  config: (message: string, config?: LogMetadata) => {
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    activeLogger.info(`Configuration: ${message}`, {
      component: "configuration",
      metadata: config
    });
  }
};

/**
 * Function to help identify and migrate specific console.log patterns
 */
export function migrateConsoleStatement(
  originalStatement: string,
  suggestedReplacement: string,
  context?: {
    file: string;
    line: number;
    pattern: LogPattern;
  }
): void {
  if (process.env.NODE_ENV === 'development') {
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    activeLogger.debug(`Migration suggestion: ${originalStatement} → ${suggestedReplacement}`, {
      component: "migration",
      metadata: context
    });
  }
}

/**
 * Utility to analyze and categorize console statements for migration
 */
export const migrationAnalyzer = {
  /**
   * Analyze a console statement and suggest appropriate migration
   */
  analyzeStatement: (statement: string, args: ConsoleArguments): AnalysisResult => {
    const message = String(statement).toLowerCase();
    
    // API call patterns
    if (message.includes('api') || message.includes('request') || message.includes('response')) {
      return {
        pattern: "api",
        suggestion: "Use logPatterns.apiCall() or frontendLogger.logApiCall()",
        priority: "high"
      };
    }
    
    // Error patterns
    if (message.includes('error') || args.some(arg => arg instanceof Error)) {
      return {
        pattern: "error",
        suggestion: "Use logger.error() with proper error object and context",
        priority: "high"
      };
    }
    
    // Repository patterns
    if (message.includes('repository') || message.includes('database') || message.includes('query')) {
      return {
        pattern: "repository",
        suggestion: "Use backendLogger.logRepositoryOperation()",
        priority: "medium"
      };
    }
    
    // User action patterns
    if (message.includes('user') || message.includes('click') || message.includes('submit')) {
      return {
        pattern: "user_action",
        suggestion: "Use frontendLogger.logComponentInteraction()",
        priority: "medium"
      };
    }
    
    // Business event patterns
    if (message.includes('created') || message.includes('updated') || message.includes('deleted')) {
      return {
        pattern: "business_event",
        suggestion: "Use logPatterns.businessEvent()",
        priority: "medium"
      };
    }
    
    // Performance patterns
    if (message.includes('performance') || message.includes('duration') || message.includes('ms')) {
      return {
        pattern: "performance",
        suggestion: "Use logger.time()/timeEnd() or logPatterns.performance()",
        priority: "low"
      };
    }
    
    // Configuration patterns
    if (message.includes('config') || message.includes('setting') || message.includes('environment')) {
      return {
        pattern: "config",
        suggestion: "Use logPatterns.config()",
        priority: "low"
      };
    }
    
    return {
      pattern: "general",
      suggestion: "Use logger.info() or logger.debug() with appropriate context",
      priority: "low"
    };
  },

  /**
   * Generate migration report for a file
   */
  generateMigrationReport: (filePath: string, consoleStatements: ConsoleStatement[]): MigrationReport => {
    const patterns: Record<string, number> = {};
    const suggestions: MigrationSuggestion[] = [];
    let highPriorityCount = 0;
    
    consoleStatements.forEach(({ line, statement, args }) => {
      const analysis = migrationAnalyzer.analyzeStatement(statement, args);
      
      patterns[analysis.pattern] = (patterns[analysis.pattern] || 0) + 1;
      
      if (analysis.priority === 'high') {
        highPriorityCount++;
      }
      
      suggestions.push({
        line,
        original: statement,
        suggestion: analysis.suggestion,
        priority: analysis.priority
      });
    });
    
    return {
      file: filePath,
      totalStatements: consoleStatements.length,
      patterns,
      highPriorityCount,
      suggestions
    };
  }
};

/**
 * Development helper to override global console in development for migration assistance
 */
export function enableMigrationMode(): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  const originalConsole = globalThis.console;
  
  // Override global console methods to provide migration hints
  globalThis.console = {
    ...originalConsole,
    log: (...args: ConsoleArguments) => {
      const analysis = migrationAnalyzer.analyzeStatement(String(args[0]), args);
      if (analysis.priority === 'high') {
        originalConsole.warn(`🔄 MIGRATION NEEDED: ${analysis.suggestion}`);
      }
      console.log(...args);
    },
    error: (...args: ConsoleArguments) => {
      originalConsole.warn(`🔄 MIGRATION NEEDED: Use logger.error() with proper error object and context`);
      console.error(...args);
    },
    warn: (...args: ConsoleArguments) => {
      console.warn(...args);
    },
    info: (...args: ConsoleArguments) => {
      console.info(...args);
    }
  };
}

export default console;