/**
 * Standardized error handling utilities
 * 
 * This module provides a consistent approach to error handling across the application:
 * - Hierarchy of error types with proper inheritance
 * - Utility functions for common error handling patterns
 * - Retry mechanism with exponential backoff
 * - Request deduplication
 */

// Base error class for all application errors
export class AppError extends Error {
  constructor(message: string, public readonly context?: Record<string, unknown>) {
    super(message);
    this.name = this.constructor.name;
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, AppError.prototype);
  }
}

// API-related errors
export class ApiError extends AppError {
  constructor(
    message: string, 
    public readonly status?: number,
    public readonly code?: string,
    public readonly data?: unknown,
    context?: Record<string, unknown>
  ) {
    super(message, context);
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, ApiError.prototype);
  }

  // Helper to determine if this is a rate limit error
  isRateLimit(): boolean {
    return this.status === 429;
  }

  // Helper to determine if this is a client error (4xx)
  isClientError(): boolean {
    return !!this.status && this.status >= 400 && this.status < 500;
  }

  // Helper to determine if this is a server error (5xx)
  isServerError(): boolean {
    return !!this.status && this.status >= 500;
  }
}

// Validation errors
export class ValidationError extends AppError {
  constructor(message: string, public readonly errors?: Record<string, string[]>, context?: Record<string, unknown>) {
    super(message, context);
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, ValidationError.prototype);
  }
}

// Timeout errors
export class TimeoutError extends AppError {
  constructor(message: string, public readonly operationName: string, context?: Record<string, unknown>) {
    super(message, context);
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, TimeoutError.prototype);
  }
}

/**
 * Options for withErrorHandling function
 */
export interface ErrorHandlingOptions<T> {
  // Name of the operation (for logging)
  operationName: string;
  
  // Maximum number of retry attempts
  maxRetries?: number;
  
  // Function to determine if an error is retryable
  retryableErrors?: (error: unknown) => boolean;
  
  // Value to return if all retries fail
  fallbackValue?: T;
  
  // Custom error handler
  onError?: (error: unknown) => void;
  
  // Log function (defaults to console.error)
  logError?: (message: string, error: unknown) => void;
}

/**
 * Execute an operation with retry logic and error handling
 * 
 * This is the core utility function for standardized error handling:
 * - Attempts the operation multiple times based on maxRetries
 * - Only retries errors that match the retryableErrors function
 * - Implements exponential backoff with jitter
 * - Provides a fallback value if all retries fail
 * - Consistently logs errors with context
 * 
 * @param operation Function to execute that returns a Promise
 * @param options Configuration options
 * @returns Result of the operation or fallback value
 * @throws Error if all retry attempts fail and no fallback is provided
 * 
 * @example
 * // Simple usage with default options
 * const result = await withErrorHandling(
 *   () => api.fetchData(),
 *   { operationName: "fetchData", fallbackValue: [] }
 * );
 * 
 * @example
 * // Advanced usage with custom retry logic
 * const result = await withErrorHandling(
 *   () => api.fetchData(),
 *   {
 *     operationName: "fetchData",
 *     maxRetries: 5,
 *     retryableErrors: (error) => error instanceof NetworkError,
 *     fallbackValue: [],
 *     onError: (error) => metrics.recordError('api_fetch", error)
 *   }
 * );
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlingOptions<T>
): Promise<T> {
  const { 
    operationName, 
    maxRetries = 3, 
    retryableErrors = isRetryableError,
    fallbackValue,
    onError,
    logError = console.error
  } = options;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: unknown) {
      // Log error with context
      logError(`Error in ${operationName} (attempt ${attempt + 1}/${maxRetries}):`, error);
      
      // Call custom error handler if provided
      if (onError) {
        onError(error);
      }
      
      // Only retry if this is a retryable error and we have attempts left
      if (retryableErrors(error) && attempt < maxRetries - 1) {
        const backoffTime = calculateBackoff(attempt, error);
        console.log(`Retrying ${operationName} in ${Math.round(backoffTime/1000)}s...`);
        await delay(backoffTime);
      } else if (fallbackValue !== undefined) {
        // Return fallback value if provided
        console.log(`Returning fallback value for ${operationName} after ${attempt + 1} attempts`);
        return fallbackValue;
      } else {
        // No fallback, rethrow the error
        throw error;
      }
    }
  }
  
  // This should never be reached due to the loop structure
  throw new AppError(`Failed ${operationName} after ${maxRetries} attempts`);
}

/**
 * Determine if an error is retryable
 * 
 * Default implementation that handles common retryable errors:
 * - Network errors (connection reset, timeout, refused)
 * - Server errors (5xx)
 * - Rate limit errors (429)
 * - Timeout errors
 * 
 * @param error The error to check
 * @returns True if the error should be retried
 */
export function isRetryableError(error: unknown): boolean {
  // Retry on API server errors and rate limits
  if (error instanceof ApiError) {
    return error.isServerError() || error.isRateLimit();
  }
  
  // Always retry timeout errors
  if (error instanceof TimeoutError) {
    return true;
  }
  
  // Check for common network error messages
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    return (
      message.includes('econnreset') ||
      message.includes('etimedout') ||
      message.includes('econnrefused') ||
      message.includes('network') ||
      message.includes('connection') ||
      message.includes('socket')
    );
  }
  
  return false;
}

/**
 * Calculate backoff time with exponential backoff and jitter
 * 
 * - Uses retry-after header if available (for rate limits)
 * - Otherwise implements exponential backoff with jitter
 * 
 * @param attempt Current attempt number (0-based)
 * @param error The error that triggered the retry
 * @returns Milliseconds to wait before retrying
 */
function calculateBackoff(attempt: number, error: unknown): number {
  // Use retry-after header for rate limit errors if available
  if (error instanceof ApiError && 
      error.isRateLimit() && 
      error.data && 
      typeof error.data === 'object' && 
      'retryAfter' in error.data && 
      typeof error.data.retryAfter === 'number') {
    // Convert seconds to milliseconds and add a small buffer
    return (error.data.retryAfter + 1) * 1000;
  }
  
  // Exponential backoff with jitter
  const baseDelay = 1000; // 1 second
  const maxDelay = 30000; // 30 seconds
  const exponentialDelay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
  const jitter = Math.random() * 1000; // Random delay between 0-1000ms
  return exponentialDelay + jitter;
}

/**
 * Simple delay function 
 * @param ms Milliseconds to delay
 * @returns Promise that resolves after the delay
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Request deduplication utility
 * 
 * Prevents redundant in-flight API requests by:
 * - Tracking requests in flight using a Map keyed by request ID
 * - Returning the existing Promise for duplicate requests
 * - Automatically cleaning up completed requests
 */
export class RequestDeduplicator {
  private static pendingRequests: Map<string, Promise<any>> = new Map();
  
  /**
   * Execute a request with deduplication
   * 
   * If an identical request is already in flight, returns the existing promise
   * instead of creating a new one.
   * 
   * @param requestKey Unique key identifying the request
   * @param requestFn Function that executes the API request and returns a promise
   * @returns Result of the request
   * 
   * @example
   * const data = await RequestDeduplicator.execute(
   *   `fetch-users-${userId}`,
   *   () => api.getUser(userId)
   * );
   */
  static async execute<T>(
    requestKey: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    // Return existing promise if the request is already in flight
    if (this.pendingRequests.has(requestKey)) {
      console.log(`Deduplicating request: ${requestKey}`);
      return this.pendingRequests.get(requestKey) as Promise<T>;
    }
    
    // Create new request promise that cleans up after itself
    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(requestKey);
    });
    
    // Store promise for potential reuse
    this.pendingRequests.set(requestKey, promise);
    
    return promise;
  }
  
  /**
   * Check if a request with the given key is currently in progress
   * @param requestKey Request key to check
   * @returns True if the request is in progress
   */
  static isRequestInProgress(requestKey: string): boolean {
    return this.pendingRequests.has(requestKey);
  }
  
  /**
   * Clear all pending request records
   * Note: This doesn't cancel the requests, just removes them from tracking
   */
  static clearAll(): void {
    this.pendingRequests.clear();
  }
}

/**
 * Create a timeout promise that rejects after the specified time
 * 
 * Useful for implementing timeouts on operations that might hang
 * 
 * @param ms Milliseconds before timeout
 * @param operationName Name of the operation (for the error message)
 * @returns Promise that rejects with a TimeoutError after the specified time
 * 
 * @example
 * // Use with Promise.race to implement timeouts
 * const result = await Promise.race([
 *   api.fetchData(),
 *   createTimeout(5000, 'fetchData')
 * ]);
 */
export function createTimeout(ms: number, operationName: string): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new TimeoutError(`Operation ${operationName} timed out after ${ms}ms`, operationName));
    }, ms);
  });
}

/**
 * Utility for wrapping an operation with a timeout
 * 
 * @param operation Function to execute that returns a Promise
 * @param ms Milliseconds before timeout
 * @param operationName Name of the operation (for error message)
 * @returns Promise with the operation result or timeout error
 * 
 * @example
 * const data = await withTimeout(
 *   () => api.fetchData(), 
 *   5000,
 *   'fetchData"
 * );
 */
export async function withTimeout<T>(
  operation: () => Promise<T>,
  ms: number,
  operationName: string
): Promise<T> {
  return Promise.race([
    operation(),
    createTimeout(ms, operationName)
  ]);
}

/**
 * Generate user-friendly error messages with retry guidance
 * 
 * @param error The error that occurred
 * @param context Optional context about the operation
 * @returns User-friendly error message with retry guidance
 */
export function getRetryableErrorMessage(error: unknown, context?: { operationName?: string }): string {
  const operationName = context?.operationName || "operation";
  
  // Handle specific error types
  if (error instanceof TimeoutError) {
    return `The ${operationName} timed out. Please check your connection and try again - it usually works on the second try.`;
  }
  
  if (error instanceof ApiError) {
    // Rate limit errors
    if (error.isRateLimit()) {
      return `Too many requests. Please wait a moment and try again.`;
    }
    
    // Server errors (5xx)
    if (error.isServerError()) {
      return `Server temporarily unavailable. Don't worry - please try again in a moment as it typically works on retry.`;
    }
    
    // Client errors that aren't retryable
    if (error.status === 401) {
      return `Authentication required. Please refresh the page and sign in again.`;
    }
    
    if (error.status === 403) {
      return `Access denied. Please check your permissions.`;
    }
    
    if (error.status === 404) {
      return `Resource not found. Please refresh the page and try again.`;
    }
    
    if (error.isClientError()) {
      return `Invalid request. Please check your input and try again.`;
    }
  }
  
  // Handle network errors
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('fetch') || message.includes('network') || message.includes('connection')) {
      return `Connection issue detected. Please check your internet and try again - it usually works on the second attempt.`;
    }
    
    if (message.includes('timeout') || message.includes('etimedout')) {
      return `Request timed out. Please try again - timeout errors often resolve on retry.`;
    }
    
    if (message.includes('econnreset') || message.includes('econnrefused')) {
      return `Connection was reset. Please try again in a moment - this is usually temporary.`;
    }
  }
  
  // Default retryable message for unknown errors
  return `Something went wrong with the ${operationName}. Don't worry - please try again as it often works on the second try.`;
}
