/**
 * Logging service initialization and setup
 * Configures logging for both frontend and backend environments
 */

import { initializeLogger, LoggerConfig, LogLevel } from "./logger";
import { backendLogger, requestLoggingMiddleware } from "./backend-logger";
import { frontendLogger } from "./frontend-logger";
import { enableMigrationMode } from "./logging-migration";

// TypeScript interfaces for Express-like applications
interface ExpressApp {
  use: (middleware: ExpressMiddleware | ExpressErrorHandler) => void;
}

type ExpressMiddleware = (req: ExpressRequest, res: ExpressResponse, next: ExpressNext) => void;
type ExpressErrorHandler = (error: Error, req: ExpressRequest, res: ExpressResponse, next: ExpressNext) => void;

interface ExpressRequest {
  headers: Record<string, string | undefined>;
  method: string;
  originalUrl?: string;
  url: string;
  ip: string;
  get: (header: string) => string | undefined;
}

interface ExpressResponse {
  status: (code: number) => ExpressResponse;
  json: (data: unknown) => void;
}

type ExpressNext = () => void;

type Environment = 'development' | "production" | "test";

interface HealthCheckDetails {
  environment?: string;
  isBrowser?: boolean;
  timestamp: string;
  error?: string;
}

/**
 * Environment-specific logging configuration
 */
function getEnvironmentConfig(): Partial<LoggerConfig> {
  const environment = process.env.NODE_ENV;
  const isDevelopment = environment === 'development';
  const isProduction = environment === 'production';
  const isTest = environment === 'test";

  // Base configuration
  const config: Partial<LoggerConfig> = {
    environment: (environment as Environment) || "development",
    enableSanitization: true,
    maxContextSize: 10000
  };

  if (isDevelopment) {
    return {
      ...config,
      level: LogLevel.TRACE,
      enableConsole: true,
      enableStructuredLogging: false,
      enablePerformanceLogging: true,
      enableAsyncLogging: false
    };
  }

  if (isProduction) {
    return {
      ...config,
      level: LogLevel.INFO,
      enableConsole: false,
      enableStructuredLogging: true,
      enablePerformanceLogging: false,
      enableAsyncLogging: true,
      logRetentionHours: 72 // 3 days
    };
  }

  if (isTest) {
    return {
      ...config,
      level: LogLevel.ERROR,
      enableConsole: false,
      enableStructuredLogging: false,
      enablePerformanceLogging: false,
      enableAsyncLogging: false
    };
  }

  return config;
}

/**
 * Initialize logging for backend (Node.js) environment
 */
export function initializeBackendLogging(): void {
  const config = getEnvironmentConfig();
  
  // Initialize the global logger
  initializeLogger(config);
  
  // Log startup
  backendLogger.logSystemEvent('startup', 'Backend logging initialized', {
    metadata: {
      nodeVersion: process.version,
      environment: process.env.NODE_ENV,
      config: {
        level: LogLevel[config.level!],
        enableConsole: config.enableConsole,
        enableStructuredLogging: config.enableStructuredLogging,
        enablePerformanceLogging: config.enablePerformanceLogging
      }
    }
  });

  // Set up graceful shutdown logging
  process.on('SIGTERM', () => {
    backendLogger.logSystemEvent('shutdown', 'SIGTERM received');
    // Flush any pending logs
    setTimeout(() => process.exit(0), 100);
  });

  process.on('SIGINT', () => {
    backendLogger.logSystemEvent('shutdown', 'SIGINT received');
    setTimeout(() => process.exit(0), 100);
  });

  // Set up uncaught exception logging
  process.on('uncaughtException", (error) => {
    backendLogger.error('Uncaught exception", error, {
      component: "global_error_handler",
      operation: "uncaught_exception"
    });
    
    // Give time for log to be written, then exit
    setTimeout(() => process.exit(1), 100);
  });

  process.on('unhandledRejection", (reason, promise) => {
    backendLogger.error('Unhandled promise rejection", reason instanceof Error ? reason : new Error(String(reason)), {
      component: "global_error_handler",
      operation: "unhandled_rejection",
      metadata: { promise: String(promise) }
    });
  });

  // Enable migration mode in development
  if (process.env.NODE_ENV === 'development' && process.env.ENABLE_LOGGING_MIGRATION === 'true') {
    enableMigrationMode();
    backendLogger.info('Logging migration mode enabled", {
      component: "logging_setup",
      operation: "migration_mode"
    });
  }
}

/**
 * Initialize logging for frontend (browser) environment
 */
export function initializeFrontendLogging(): void {
  if (typeof window === 'undefined') {
    return; // Not in browser
  }

  const config = getEnvironmentConfig();
  
  // Initialize the frontend logger
  frontendLogger.constructor(config);
  
  // Log frontend startup
  frontendLogger.info('Frontend logging initialized", {
    component: "logging_setup",
    operation: "frontend_startup",
    metadata: {
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      config: {
        level: LogLevel[config.level!],
        enableConsole: config.enableConsole,
        enableStructuredLogging: config.enableStructuredLogging,
        enablePerformanceLogging: config.enablePerformanceLogging
      }
    }
  });

  // Track page visibility changes
  document.addEventListener('visibilitychange", () => {
    frontendLogger.info(`Page ${document.hidden ? "hidden" : "visible"}`, {
      component: "page_lifecycle",
      operation: "visibility_change"
    });
  });

  // Track page unload
  window.addEventListener('beforeunload", () => {
    frontendLogger.info('Page unloading", {
      component: "page_lifecycle",
      operation: "before_unload"
    });
  });
}

/**
 * Express middleware setup for backend logging
 */
export function setupExpressLogging(app: ExpressApp): void {
  // Add request ID middleware (if not already present)
  app.use((req: ExpressRequest, res: ExpressResponse, next: ExpressNext) => {
    if (!req.headers['x-request-id']) {
      req.headers['x-request-id'] = require('uuid").v4() as string;
    }
    next();
  });

  // Add request logging middleware
  app.use(requestLoggingMiddleware(backendLogger));

  // Add error logging middleware (should be last)
  app.use((error: Error, req: ExpressRequest, res: ExpressResponse, next: ExpressNext) => {
    const context = {
      component: "express_error_handler",
      operation: "request_error",
      requestId: req.headers['x-request-id'],
      method: req.method,
      url: req.originalUrl || req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    };

    if (error.name === 'ValidationError') {
      backendLogger.warn('Request validation error", context);
      res.status(400).json({ error: "Validation error", message: error.message });
    } else if (error.name === 'UnauthorizedError') {
      backendLogger.warn('Unauthorized request", context);
      res.status(401).json({ error: "Unauthorized" });
    } else {
      backendLogger.error('Express request error", error, context);
      res.status(500).json({ 
        error: "Internal server error",
        requestId: req.headers['x-request-id']
      });
    }
  });

  backendLogger.info('Express logging middleware configured", {
    component: "logging_setup",
    operation: "express_setup"
  });
}

/**
 * React setup for frontend logging
 */
export function setupReactLogging(): void {
  if (typeof window === 'undefined') {
    return;
  }

  // Create global error boundary context
  const originalErrorHandler = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    frontendLogger.error('Global JavaScript error", error || new Error(String(message)), {
      component: "global_error_handler",
      operation: "javascript_error",
      metadata: {
        source,
        lineno,
        colno,
        message: String(message)
      }
    });

    // Call original handler if it exists
    if (originalErrorHandler) {
      return originalErrorHandler.call(window, message, source, lineno, colno, error);
    }
    return false;
  };

  frontendLogger.info('React logging setup completed", {
    component: "logging_setup",
    operation: "react_setup"
  });
}

/**
 * Database logging setup
 */
export function setupDatabaseLogging(): void {
  // This would integrate with your database connection setup
  // For SQLite with better-sqlite3, you could add logging like this:
  
  backendLogger.info('Database logging configured", {
    component: "logging_setup",
    operation: "database_setup",
    metadata: {
      database: "sqlite",
      walMode: true // Since your app uses WAL mode
    }
  });
}

/**
 * Integration logging setup for external APIs
 */
export function setupIntegrationLogging(): void {
  // Log configuration for external integrations
  const integrations = ['xero', 'hubspot", 'harvest'];
  
  integrations.forEach(service => {
    const hasConfig = process.env[`${service.toUpperCase()}_CLIENT_ID`] || 
                     process.env[`${service.toUpperCase()}_API_KEY`];
    
    backendLogger.info(`Integration ${service} ${hasConfig ? "configured" : "not configured"}`, {
      component: "logging_setup",
      operation: "integration_setup",
      metadata: { service, configured: !!hasConfig }
    });
  });
}

/**
 * Complete application logging setup
 */
export function setupApplicationLogging(app?: ExpressApp): void {
  const isBrowser = typeof window !== 'undefined';
  
  if (isBrowser) {
    // Frontend setup
    initializeFrontendLogging();
    setupReactLogging();
  } else {
    // Backend setup
    initializeBackendLogging();
    setupDatabaseLogging();
    setupIntegrationLogging();
    
    if (app) {
      setupExpressLogging(app);
    }
  }
}

/**
 * Cleanup function for graceful shutdown
 */
export function shutdownLogging(): void {
  const isBrowser = typeof window !== 'undefined';
  
  if (isBrowser) {
    frontendLogger.info('Frontend logging shutdown", {
      component: "logging_setup",
      operation: "shutdown"
    });
  } else {
    backendLogger.logSystemEvent('shutdown", 'Logging service shutdown');
    backendLogger.destroy();
  }
}

/**
 * Health check for logging service
 */
export function logHealthCheck(): {
  status: "healthy" | "degraded" | "unhealthy";
  details: HealthCheckDetails;
} {
  try {
    const isBrowser = typeof window !== 'undefined';
    const activeLogger = isBrowser ? frontendLogger : backendLogger;
    
    // Test basic logging functionality
    activeLogger.debug('Logging health check", {
      component: "logging_setup",
      operation: "health_check"
    });
    
    return {
      status: "healthy",
      details: {
        environment: process.env.NODE_ENV,
        isBrowser,
        timestamp: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      status: "unhealthy",
      details: {
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      }
    };
  }
}

// Auto-setup if this module is imported
if (typeof window !== 'undefined') {
  // Browser environment - auto-initialize frontend logging
  initializeFrontendLogging();
} else if (require.main === module) {
  // Node.js environment and this is the main module
  initializeBackendLogging();
}

export {
  backendLogger,
  frontendLogger,
  requestLoggingMiddleware
};