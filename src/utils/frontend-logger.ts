/**
 * Frontend-specific logger that integrates with existing LoggingContext
 * and provides browser-optimized logging
 */

import { Logger, LoggerConfig, Log<PERSON>ontext, UserContext } from "./logger";

/**
 * Frontend-specific context
 */
export interface FrontendContext extends LogContext {
  page?: string;
  route?: string;
  userAgent?: string;
  viewportSize?: string;
  timestamp?: string;
}

/**
 * Component interaction context
 */
export interface ComponentContext extends LogContext {
  componentName: string;
  action: string;
  props?: Record<string, any>;
  state?: Record<string, any>;
}

/**
 * Navigation context
 */
export interface NavigationContext extends LogContext {
  from?: string;
  to: string;
  trigger: "user" | "programmatic" | "redirect";
}

/**
 * API call context for frontend
 */
export interface FrontendApiContext extends LogContext {
  endpoint: string;
  method: string;
  status?: number;
  duration?: number;
  cached?: boolean;
  retryCount?: number;
}

/**
 * Frontend-specific logger class
 */
export class FrontendLogger extends Logger {
  private pageLoadTime: number;
  private currentPage: string = '';
  private userId?: string;

  constructor(config?: Partial<LoggerConfig>) {
    const frontendConfig: Partial<LoggerConfig> = {
      environment: process.env.NODE_ENV as any || "development",
      enableConsole: process.env.NODE_ENV === 'development',
      enableStructuredLogging: true,
      enablePerformanceLogging: true,
      enableAsyncLogging: false, // Sync logging for better debugging in frontend
      ...config
    };

    super(frontendConfig);
    this.pageLoadTime = Date.now();

    // Initialize page tracking
    if (typeof window !== 'undefined') {
      this.currentPage = window.location.pathname;
      this.setupGlobalErrorHandling();
      this.setupPerformanceTracking();
    }
  }

  /**
   * Set current user for context
   */
  setUser(userId: string, additionalContext?: Record<string, any>): void {
    this.userId = userId;
    this.info('User context set', {
      userId,
      component: "authentication",
      metadata: additionalContext
    });
  }

  /**
   * Clear user context (logout)
   */
  clearUser(): void {
    const previousUserId = this.userId;
    this.userId = undefined;
    this.info('User context cleared', {
      component: "authentication",
      metadata: { previousUserId }
    });
  }

  /**
   * Get common frontend context
   */
  private getFrontendContext(): FrontendContext {
    if (typeof window === 'undefined') {
      return {};
    }

    return {
      page: this.currentPage,
      route: window.location.pathname + window.location.search,
      userAgent: navigator.userAgent,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      userId: this.userId
    };
  }

  /**
   * Log page navigation
   */
  logNavigation(to: string, from?: string, trigger: NavigationContext['trigger'] = 'user'): void {
    const context: NavigationContext = {
      ...this.getFrontendContext(),
      from: from || this.currentPage,
      to,
      trigger,
      component: "navigation",
      operation: "page_change"
    };

    this.currentPage = to;
    this.info(`Navigation: ${from || "unknown"} → ${to}`, context);
  }

  /**
   * Log component interactions
   */
  logComponentInteraction(
    componentName: string,
    action: string,
    additionalContext?: Partial<ComponentContext>
  ): void {
    const context: ComponentContext = {
      ...this.getFrontendContext(),
      componentName,
      action,
      component: "ui_component",
      operation: "user_interaction",
      ...additionalContext
    };

    this.info(`Component interaction: ${componentName}.${action}`, context);
  }

  /**
   * Log form submissions
   */
  logFormSubmission(
    formName: string,
    isValid: boolean,
    validationErrors?: Record<string, string[]>,
    additionalContext?: LogContext
  ): void {
    const context: LogContext = {
      ...this.getFrontendContext(),
      ...additionalContext,
      component: "form",
      operation: "form_submission",
      metadata: {
        formName,
        isValid,
        errorCount: validationErrors ? Object.keys(validationErrors).length : 0,
        validationErrors: validationErrors ? Object.keys(validationErrors) : []
      }
    };

    if (isValid) {
      this.info(`Form submitted: ${formName}`, context);
    } else {
      this.warn(`Form submission failed: ${formName}`, context);
    }
  }

  /**
   * Log API calls from frontend
   */
  logApiCall(
    endpoint: string,
    method: string,
    options?: {
      status?: number;
      duration?: number;
      cached?: boolean;
      retryCount?: number;
      error?: Error;
    }
  ): void {
    const context: FrontendApiContext = {
      ...this.getFrontendContext(),
      endpoint,
      method,
      status: options?.status,
      duration: options?.duration,
      cached: options?.cached,
      retryCount: options?.retryCount,
      component: "api_client",
      operation: "api_call"
    };

    const message = `API ${method} ${endpoint}${options?.status ? ` - ${options.status}` : ""}`;

    if (options?.error || (options?.status && options.status >= 400)) {
      this.error(message, options?.error, context);
    } else if (options?.retryCount && options.retryCount > 0) {
      this.warn(`${message} - Retry ${options.retryCount}`, context);
    } else {
      this.debug(message, context);
    }
  }

  /**
   * Log business actions from UI
   */
  logBusinessAction(
    action: string,
    entityType?: string,
    entityId?: string,
    additionalContext?: LogContext
  ): void {
    const context: LogContext = {
      ...this.getFrontendContext(),
      ...additionalContext,
      component: "business_action",
      operation: action,
      metadata: {
        entityType,
        entityId
      }
    };

    this.info(`Business action: ${action}`, context);
  }

  /**
   * Log React errors
   */
  logReactError(
    error: Error,
    errorInfo: { componentStack: string },
    componentName?: string
  ): void {
    const context: LogContext = {
      ...this.getFrontendContext(),
      component: "react_error_boundary",
      operation: "component_error",
      metadata: {
        componentName,
        componentStack: errorInfo.componentStack
      }
    };

    this.error(`React component error${componentName ? ` in ${componentName}` : ""}`, error, context);
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetric(
    metric: string,
    value: number,
    unit: "ms" | "bytes" | "count" = 'ms',
    additionalContext?: LogContext
  ): void {
    const context: LogContext = {
      ...this.getFrontendContext(),
      ...additionalContext,
      component: "performance",
      operation: "performance_metric",
      metadata: {
        metric,
        value,
        unit
      }
    };

    this.info(`Performance: ${metric} = ${value}${unit}`, context);
  }

  /**
   * Log search actions
   */
  logSearch(
    query: string,
    resultsCount: number,
    searchType: string = 'general',
    additionalContext?: LogContext
  ): void {
    const context: LogContext = {
      ...this.getFrontendContext(),
      ...additionalContext,
      component: "search",
      operation: "search_executed",
      metadata: {
        query: query.length > 100 ? `${query.substring(0, 100)}...` : query,
        queryLength: query.length,
        resultsCount,
        searchType
      }
    };

    this.info(`Search executed: ${searchType} (${resultsCount} results)`, context);
  }

  /**
   * Setup global error handling
   */
  private setupGlobalErrorHandling(): void {
    if (typeof window === 'undefined') return;

    // Catch unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.error('Unhandled JavaScript error', event.error, {
        ...this.getFrontendContext(),
        component: "global_error_handler",
        operation: "unhandled_error",
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          message: event.message
        }
      });
    });

    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled promise rejection', undefined, {
        ...this.getFrontendContext(),
        component: "global_error_handler",
        operation: "unhandled_promise_rejection",
        metadata: {
          reason: event.reason
        }
      });
    });
  }

  /**
   * Setup performance tracking
   */
  private setupPerformanceTracking(): void {
    if (typeof window === 'undefined' || !window.performance) return;

    // Log page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (perfData) {
          this.logPerformanceMetric('page_load_time', perfData.loadEventEnd - perfData.fetchStart, 'ms', {
            operation: "page_load",
            metadata: {
              dns_lookup: perfData.domainLookupEnd - perfData.domainLookupStart,
              tcp_connection: perfData.connectEnd - perfData.connectStart,
              request_response: perfData.responseEnd - perfData.requestStart,
              dom_processing: perfData.domContentLoadedEventEnd - perfData.responseEnd
            }
          });
        }
      }, 0);
    });

    // Track Core Web Vitals if available
    if ('PerformanceObserver' in window) {
      try {
        // Track Largest Contentful Paint (LCP)
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.logPerformanceMetric('largest_contentful_paint', lastEntry.startTime, 'ms');
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // Track First Input Delay (FID)
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          for (const entry of entries) {
            this.logPerformanceMetric('first_input_delay', (entry as any).processingStart - entry.startTime, 'ms');
          }
        }).observe({ entryTypes: ['first-input'] });

        // Track Cumulative Layout Shift (CLS)
        new PerformanceObserver((list) => {
          let clsValue = 0;
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          if (clsValue > 0) {
            this.logPerformanceMetric('cumulative_layout_shift', clsValue, 'count');
          }
        }).observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        // PerformanceObserver not fully supported
        this.debug('Performance Observer not fully supported', {
          component: "performance",
          metadata: { error: e }
        });
      }
    }
  }
}

/**
 * Create and configure frontend logger instance
 */
export function createFrontendLogger(config?: Partial<LoggerConfig>): FrontendLogger {
  return new FrontendLogger(config);
}

/**
 * Default frontend logger instance
 */
export const frontendLogger = new FrontendLogger();

/**
 * React Hook for component-specific logging
 */
export function useLogger(componentName: string): {
  logInteraction: (action: string, context?: Partial<ComponentContext>) => void;
  logError: (error: Error, context?: LogContext) => void;
  logPerformance: (metric: string, value: number, unit?: "ms" | "bytes" | "count") => void;
} {
  return {
    logInteraction: (action: string, context?: Partial<ComponentContext>) => {
      frontendLogger.logComponentInteraction(componentName, action, context);
    },
    
    logError: (error: Error, context?: LogContext) => {
      frontendLogger.error(`Component error in ${componentName}`, error, {
        ...context,
        component: componentName
      });
    },
    
    logPerformance: (metric: string, value: number, unit: "ms" | "bytes" | "count" = 'ms') => {
      frontendLogger.logPerformanceMetric(metric, value, unit, {
        component: componentName
      });
    }
  };
}

/**
 * HOC for automatic component logging
 */
export function withLogging<T extends {}>(
  WrappedComponent: React.ComponentType<T>,
  componentName?: string
): React.ComponentType<T> {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || "Component";
  
  const LoggingWrapper: React.ComponentType<T> = (props) => {
    const { logInteraction, logError } = useLogger(displayName);

    React.useEffect(() => {
      logInteraction('component_mounted');
      
      return () => {
        logInteraction('component_unmounted');
      };
    }, [logInteraction]);

    try {
      return React.createElement(WrappedComponent, props);
    } catch (error) {
      logError(error as Error);
      throw error;
    }
  };

  LoggingWrapper.displayName = `withLogging(${displayName})`;
  return LoggingWrapper;
}

export default frontendLogger;