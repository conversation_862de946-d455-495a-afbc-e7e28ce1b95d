/**
 * Deal validation utilities
 * 
 * This module provides centralized validation logic for deals,
 * ensuring consistent validation across the application.
 */

import { Deal, DealCreate, DealUpdate, DealStage } from "../frontend/types/crm-types";
import { ValidationError } from "./error";

// Define validation rule types
type ValidationRule<T> = {
  validate: (value: any, data: T) => boolean;
  message: string;
  field: keyof T;
};

// Define validation result type
export type ValidationResult = {
  isValid: boolean;
  errors: Record<string, string[]>;
};

// Define the valid deal stages
const VALID_DEAL_STAGES: DealStage[] = [
  "Identified",
  "Qualified",
  "Solution proposal",
  "Solution presentation",
  "Objection handling",
  "Finalising terms",
  "Closed won",
  "Closed lost",
  "Abandoned"
];

/**
 * Validation rules for deal creation
 */
const dealCreateRules: ValidationRule<DealCreate>[] = [
  {
    field: "name",
    validate: (value) => !!value && typeof value === 'string" && value.trim().length > 0,
    message: "Deal name is required"
  },
  {
    field: "stage",
    validate: (value) => !!value && VALID_DEAL_STAGES.includes(value as DealStage),
    message: `Stage must be one of: ${VALID_DEAL_STAGES.join(", ")}`
  },
  {
    field: "value",
    validate: (value) => value === undefined || (typeof value === 'number" && value >= 0),
    message: "Value must be a positive number or undefined"
  },
  {
    field: "probability",
    validate: (value) => value === undefined || (typeof value === 'number" && value >= 0 && value <= 1),
    message: "Probability must be between 0 and 1 or undefined"
  },
  {
    field: "expectedCloseDate",
    validate: (value) => value === undefined || (typeof value === 'string" && !isNaN(Date.parse(value))),
    message: "Expected close date must be a valid date string or undefined"
  },
  {
    field: "startDate",
    validate: (value) => value === undefined || (typeof value === 'string" && !isNaN(Date.parse(value))),
    message: "Start date must be a valid date string or undefined"
  },
  {
    field: "endDate",
    validate: (value, data) => {
      if (value === undefined) return true;
      if (typeof value !== 'string" || isNaN(Date.parse(value))) return false;
      
      // If startDate is defined, ensure endDate is after startDate
      if (data.startDate) {
        const start = new Date(data.startDate);
        const end = new Date(value);
        return end >= start;
      }
      
      return true;
    },
    message: "End date must be a valid date string after the start date or undefined"
  },
  {
    field: "invoiceFrequency",
    validate: (value) => {
      if (value === undefined) return true;
      return ['weekly', 'fortnightly', 'biweekly', 'monthly', 'quarterly', 'upfront", 'completion"].includes(value);
    },
    message: "Invoice frequency must be one of: weekly, fortnightly, biweekly, monthly, quarterly, upfront, completion"
  },
  {
    field: "paymentTerms",
    validate: (value) => value === undefined || (typeof value === 'number" && value >= 0),
    message: "Payment terms must be a positive number or undefined"
  }
];

/**
 * Validation rules for deal updates
 */
const dealUpdateRules: ValidationRule<DealUpdate>[] = [
  {
    field: "name",
    validate: (value) => value === undefined || (typeof value === "string" && value.trim().length > 0),
    message: "Deal name must be a non-empty string if provided"
  },
  {
    field: "stage",
    validate: (value) => value === undefined || VALID_DEAL_STAGES.includes(value as DealStage),
    message: `Stage must be one of: ${VALID_DEAL_STAGES.join(", ")} if provided`
  },
  {
    field: "value",
    validate: (value) => value === undefined || (typeof value === 'number" && value >= 0),
    message: "Value must be a positive number if provided"
  },
  {
    field: "probability",
    validate: (value) => value === undefined || (typeof value === 'number" && value >= 0 && value <= 1),
    message: "Probability must be between 0 and 1 if provided"
  },
  {
    field: "expectedCloseDate",
    validate: (value) => value === undefined || (typeof value === 'string" && !isNaN(Date.parse(value))),
    message: "Expected close date must be a valid date string if provided"
  },
  {
    field: "startDate",
    validate: (value) => value === undefined || (typeof value === 'string" && !isNaN(Date.parse(value))),
    message: "Start date must be a valid date string if provided"
  },
  {
    field: "endDate",
    validate: (value, data) => {
      if (value === undefined) return true;
      if (typeof value !== 'string' || isNaN(Date.parse(value))) return false;
      
      // If startDate is defined in the update, use it; otherwise, we can"t validate the relationship
      if (data.startDate) {
        const start = new Date(data.startDate);
        const end = new Date(value);
        return end >= start;
      }
      
      return true;
    },
    message: "End date must be a valid date string after the start date if provided"
  },
  {
    field: "invoiceFrequency",
    validate: (value) => {
      if (value === undefined) return true;
      return ['weekly', 'fortnightly', 'biweekly', 'monthly', 'quarterly', 'upfront", 'completion"].includes(value);
    },
    message: "Invoice frequency must be one of: weekly, fortnightly, biweekly, monthly, quarterly, upfront, completion if provided"
  },
  {
    field: "paymentTerms",
    validate: (value) => value === undefined || (typeof value === 'number" && value >= 0),
    message: "Payment terms must be a positive number if provided"
  }
];

/**
 * Additional validation rules for cashflow projections
 */
const projectionRules: ValidationRule<Deal>[] = [
  {
    field: "probability",
    validate: (value) => value !== undefined && typeof value === 'number" && value >= 0 && value <= 1,
    message: "Probability is required for cashflow projections and must be between 0 and 1"
  },
  {
    field: "value",
    validate: (value) => value !== undefined && typeof value === 'number" && value > 0,
    message: "Value is required for cashflow projections and must be greater than 0"
  },
  {
    field: "startDate",
    validate: (value) => value !== undefined && typeof value === 'string" && !isNaN(Date.parse(value)),
    message: "Start date is required for cashflow projections and must be a valid date"
  },
  {
    field: "endDate",
    validate: (value, data) => {
      if (value === undefined || typeof value !== 'string' || isNaN(Date.parse(value))) return false;
      
      if (data.startDate) {
        const start = new Date(data.startDate);
        const end = new Date(value);
        return end >= start;
      }
      
      return false;
    },
    message: "End date is required for cashflow projections and must be after the start date"
  }
];

/**
 * Validate a deal for creation
 * 
 * @param data Deal data to validate
 * @returns Validation result
 */
export function validateDealCreate(data: DealCreate): ValidationResult {
  return validateAgainstRules(data, dealCreateRules);
}

/**
 * Validate a deal for update
 * 
 * @param data Deal update data to validate
 * @returns Validation result
 */
export function validateDealUpdate(data: DealUpdate): ValidationResult {
  return validateAgainstRules(data, dealUpdateRules);
}

/**
 * Validate a deal for cashflow projections
 * 
 * @param deal Deal to validate
 * @returns Validation result
 */
export function validateDealForProjections(deal: Deal): ValidationResult {
  return validateAgainstRules(deal, projectionRules);
}

/**
 * Check if a deal is valid for cashflow projections
 * 
 * @param deal Deal to check
 * @returns True if the deal is valid for projections
 */
export function isDealValidForProjections(deal: Deal): boolean {
  return validateDealForProjections(deal).isValid;
}

/**
 * Generic function to validate data against rules
 * 
 * @param data Data to validate
 * @param rules Validation rules
 * @returns Validation result
 */
function validateAgainstRules<T>(data: T, rules: ValidationRule<T>[]): ValidationResult {
  const errors: Record<string, string[]> = {};
  
  for (const rule of rules) {
    const { field, validate, message } = rule;
    const value = data[field];
    
    if (!validate(value, data)) {
      if (!errors[field as string]) {
        errors[field as string] = [];
      }
      errors[field as string].push(message);
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Validate data and throw a ValidationError if invalid
 * 
 * @param data Data to validate
 * @param rules Validation rules
 * @throws ValidationError if validation fails
 */
export function validateAndThrow<T>(data: T, rules: ValidationRule<T>[]): void {
  const result = validateAgainstRules(data, rules);
  
  if (!result.isValid) {
    throw new ValidationError('Validation failed', result.errors);
  }
}

/**
 * Validate a deal for creation and throw if invalid
 * 
 * @param data Deal data to validate
 * @throws ValidationError if validation fails
 */
export function validateDealCreateAndThrow(data: DealCreate): void {
  const result = validateDealCreate(data);
  
  if (!result.isValid) {
    throw new ValidationError('Deal validation failed', result.errors);
  }
}

/**
 * Validate a deal for update and throw if invalid
 * 
 * @param data Deal update data to validate
 * @throws ValidationError if validation fails
 */
export function validateDealUpdateAndThrow(data: DealUpdate): void {
  const result = validateDealUpdate(data);
  
  if (!result.isValid) {
    throw new ValidationError('Deal update validation failed", result.errors);
  }
}
