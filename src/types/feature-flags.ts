/**
 * Feature flag names - shared between frontend and backend
 * This file should not import any backend-specific code
 */
export enum FeatureFlag {
  KNOWLEDGE_GRAPH = "feature_knowledge_graph",
  OPPORTUNITY_INTELLIGENCE = "feature_opportunity_intelligence",
  DATA_ENRICHMENT = "feature_data_enrichment",
  ACTIVITY_FEED = "feature_activity_feed",
  TENDER_MANAGEMENT = "feature_tender_management",
  XERO_CHAT = "feature_xero_chat",
  HUBSPOT_MCP = "feature_hubspot_mcp",
}

/**
 * Feature flag configuration interface
 */
export interface FeatureFlagConfig {
  name: string;
  description: string;
  defaultValue: boolean;
  category: "experimental" | "beta" | "stable";
}
