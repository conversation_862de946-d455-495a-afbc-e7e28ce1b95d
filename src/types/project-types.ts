/**
 * Project-related type definitions
 */

export interface Project {
  id: string;
  name: string;
  description?: string;
  status: "active" | "on_hold" | "completed" | "cancelled";
  project_type?: string;

  // Timing
  start_date?: string;
  end_date?: string;

  // Financial
  budget?: number;
  spent?: number;
  currency?: string;

  // External IDs
  harvest_project_id?: string;

  // Relationships
  company_id: string;
  deal_id?: string;

  // Metadata
  tags?: string[];
  custom_fields?: Record<string, any>;

  // Audit
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  deleted_at?: string;
}

export interface ProjectContact {
  project_id: string;
  contact_id: string;
  role: string;
  allocation_percentage?: number;
  start_date?: string;
  end_date?: string;
  created_at: string;
  created_by?: string;
  updated_at?: string;
  updated_by?: string;
  deleted_at?: string;
}

export interface ProjectDependency {
  id: string;
  predecessor_project_id: string;
  successor_project_id: string;
  dependency_type: "blocks" | "requires" | "related_to";
  lag_days?: number;
  created_at: string;
  created_by?: string;
  deleted_at?: string;
}

export type ProjectRole =
  | "project_manager"
  | "developer"
  | "consultant"
  | "stakeholder"
  | "designer"
  | "analyst"
  | "tester"
  | "other";

export interface ProjectWithRelations extends Project {
  company?: {
    id: string;
    name: string;
  };
  deal?: {
    id: string;
    name: string;
  };
  contacts?: Array<{
    contact: {
      id: string;
      first_name: string;
      last_name: string;
      email?: string;
    };
    role: string;
    allocation_percentage?: number;
  }>;
}

export interface ProjectSummary {
  id: string;
  name: string;
  status: Project["status"];
  company_name: string;
  start_date?: string;
  end_date?: string;
  progress_percentage?: number;
  team_size: number;
}

export interface CreateProjectInput {
  name: string;
  description?: string;
  status?: Project["status"];
  project_type?: string;
  start_date?: string;
  end_date?: string;
  budget?: number;
  currency?: string;
  company_id: string;
  deal_id?: string;
  tags?: string[];
  custom_fields?: Record<string, any>;
}

export interface UpdateProjectInput extends Partial<CreateProjectInput> {
  updated_by?: string;
}

export interface ProjectFilters {
  status?: Project["status"][];
  company_id?: string;
  project_type?: string;
  has_budget?: boolean;
  start_date_from?: string;
  start_date_to?: string;
  search?: string;
}
