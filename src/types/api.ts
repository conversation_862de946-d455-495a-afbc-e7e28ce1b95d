/**
 * Draft Estimate Type Definitions
 *
 * This file contains types for draft estimates.
 * Common API types are in src/api/types/api.ts
 */

// Re-export common API types from the backend location
export type { 
  RequestOptions, 
  DateRange, 
  ApiError, 
  RateLimitConfig, 
  ApiClientConfig, 
  ApiResponse 
} from "../api/types/api";

/**
 * Draft Estimate Interfaces
 */
export interface DraftEstimateTimeAllocation {
  weekIdentifier: string;
  days: number;
  deletedAt?: string;
}

export interface DraftEstimateAllocation {
  internalId: string;
  harvestUserId: number;
  firstName: string;
  lastName: string | null;
  projectRole: string | null;
  level: string | null;
  onbordTargetRateDaily: number;
  onbordCostRateDaily: number;
  rateProposedDaily: number;
  timeAllocations: DraftEstimateTimeAllocation[];
  deletedAt?: string;
}

export interface DraftEstimate {
  uuid: string;
  companyId: string;
  clientName: string;
  projectName: string | null;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  harvestEstimateId: number | null;
  userId: string;
  notes: string | null;
  status: "draft" | "published" | "archived";
  // Discount fields
  discountType: "percentage" | "amount" | "none";
  discountValue: number;
  // Invoice and payment fields
  invoiceFrequency?: string; // 'weekly', 'biweekly', 'monthly', 'custom', 'upfront', 'completion'
  paymentTerms?: number; // days until payment due
  // Billing type fields
  billingType: "daily" | "hourly";
  hoursPerDay: number;
  allocations: DraftEstimateAllocation[];
  // Optional calculated fields
  totalFees?: number;
  deletedAt?: string;
}

export interface DraftEstimateSummary {
  uuid: string;
  clientName: string;
  projectName: string | null;
  startDate: string;
  endDate: string;
  updatedAt: string;
  status: "draft" | "published" | "archived";
  harvestEstimateId: number | null;
  // Optional fields from the full estimate
  totalFees?: number;
  allocations?: DraftEstimateAllocation[];
  invoiceFrequency?: string;
  paymentTerms?: number;
  billingType?: "daily" | "hourly";
  hoursPerDay?: number;
}

export interface DraftEstimateAllocationInput {
  internalId: string;
  harvestUserId: number;
  firstName: string;
  lastName?: string | null;
  projectRole?: string | null;
  level?: string | null;
  onbordTargetRateDaily: number;
  onbordCostRateDaily: number;
  rateProposedDaily: number;
  weeklyAllocation: Record<string, number>; // Convert to timeAllocations in repository
}

export interface DraftEstimateCreate {
  uuid?: string; // Optional, will be generated if not provided
  companyId: string;
  clientName: string;
  projectName?: string;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  userId: string;
  notes?: string;
  // Discount fields
  discountType?: "percentage" | "amount" | "none";
  discountValue?: number;
  // Invoice and payment fields
  invoiceFrequency?: string; // 'weekly', 'biweekly', 'monthly', 'custom', 'upfront', 'completion'
  paymentTerms?: number; // days until payment due
  // Billing type fields
  billingType?: "daily" | "hourly"; // defaults to 'daily' if not specified
  hoursPerDay?: number; // defaults to 7.5 if not specified
  allocations: DraftEstimateAllocationInput[];
}

export interface DraftEstimateUpdate extends Partial<DraftEstimateCreate> {
  updatedAt?: string; // Will be set automatically in repository
  harvestEstimateId?: number | null;
  status?: "draft" | "published" | "archived";
}

// Re-export the Transaction interface from financial types
// This allows api.ts users to continue importing Transaction from here
export type { Transaction } from "./financial";