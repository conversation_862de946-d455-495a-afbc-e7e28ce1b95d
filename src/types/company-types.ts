/**
 * Unified Company Types
 *
 * This file contains unified type definitions for companies across CRM and Radar features.
 */

import {
  BaseCompany,
  BaseContact,
  BaseDeal,
  CompanyPriority,
  CompanySource,
  RadarState,
  PRIORITY_COLORS,
  PRIORITY_DESCRIPTIONS,
} from "./shared-types";

// Re-export shared types
export { PRIORITY_COLORS, PRIORITY_DESCRIPTIONS };

/**
 * Unified Company interface that extends BaseCompany
 */
export interface Company extends BaseCompany {
  // These fields reference other entities but use type-only references
  // to prevent circular dependencies
  contacts?: Partial<BaseContact>[] | number; // Either Contact objects or count
  deals?: Partial<BaseDeal>[];
}

/**
 * Company create interface
 */
export interface CompanyCreate {
  name: string;
  industry?: string;
  size?: string;
  website?: string;
  address?: string;
  description?: string;
  hubspotId?: string;
  harvestId?: number;
  source?: CompanySource;
  radarState?: RadarState;
  priority?: CompanyPriority;
  notes?: string;
  currentSpend?: number;
  potentialSpend?: number;
  lastInteractionDate?: string;
  createdBy?: string;
  updatedBy?: string;
}

/**
 * Company update interface
 */
export interface CompanyUpdate {
  name?: string;
  industry?: string;
  size?: string;
  website?: string;
  address?: string;
  description?: string;
  // Radar-specific fields
  priority?: CompanyPriority | null;
  radarState?: RadarState | null;
  currentSpend?: number;
  potentialSpend?: number;
  lastInteractionDate?: string;
  notes?: string;
  harvestId?: number;
  hubspotId?: string;
  source?: CompanySource;
  updatedBy?: string;
}

/**
 * Radar-specific company update interface
 */
export interface RadarCompanyUpdate {
  priority?: CompanyPriority | null;
  radarState?: RadarState | null;
  potentialSpend?: number;
  notes?: string;
  lastInteractionDate?: string;
  updatedBy?: string;
}
