/**
 * Shared Types
 *
 * This file contains shared base types that are common across multiple features
 * to break circular dependencies between company-types.ts and crm-types.ts.
 */

/**
 * Company priority types
 */
export type CompanyPriority = "High" | "Medium" | "Low" | "Qualified out";

/**
 * Radar state types
 */
export type RadarState =
  | "Strategy"
  | "Transformation"
  | "BAU"
  | "Transition out";

/**
 * Company source types
 */
export type CompanySource = "HubSpot" | "Harvest" | "Manual";

/**
 * Deal stage types
 */
export type DealStage =
  | "Identified"
  | "Qualified"
  | "Solution proposal"
  | "Solution presentation"
  | "Objection handling"
  | "Finalising terms"
  | "Closed won"
  | "Closed lost"
  | "Abandoned";

/**
 * Deal priority types
 */
export type DealPriority = "Low" | "Medium" | "High";

/**
 * Tender qualification status types
 */
export type TenderQualificationStatus =
  | "new"
  | "reviewing"
  | "not_interested"
  | "interested";

/**
 * Tender status types (from TendersWA)
 */
export type TenderStatus = "Current" | "Closed" | "Awarded";

/**
 * Data source types for field ownership
 */
export type DataSource = "HubSpot" | "Estimate" | "Manual" | "System";

/**
 * Base company interface with minimal properties
 * (to avoid circular dependencies)
 */
export interface BaseCompany {
  id: string;
  name: string;
  industry?: string;
  size?: string;
  website?: string;
  address?: string;
  description?: string;
  hubspotId?: string;
  harvestId?: number;
  source?: CompanySource;
  priority?: CompanyPriority | null;
  radarState?: RadarState | null;
  currentSpend?: number;
  potentialSpend?: number;
  lastInteractionDate?: string;
  notes?: string;
  // Deal statistics (for radar view)
  activeDealsCount?: number;
  totalDealValue?: number;
  // Contact statistics
  contactCount?: number;
  // Spend breakdown (manual + Harvest)
  totalSpend?: number;
  manualSpend?: number;
  harvestSpend?: number;
  // Linking status
  linkedStatus?: "both" | "hubspot_only" | "harvest_only" | "none";
  // Enrichment fields
  enrichmentStatus?: any; // JSON object with enrichment source status
  lastEnrichedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: string;
}

/**
 * Contact role types in companies
 */
export type ContactRole =
  | "employee"
  | "contractor"
  | "consultant"
  | "manager"
  | "executive"
  | "owner"
  | "other";

/**
 * Contact source types
 */
export type ContactSource = "HubSpot" | "Harvest" | "Manual";

/**
 * Base contact interface with minimal properties
 * (to avoid circular dependencies)
 */
export interface BaseContact {
  id: string;
  firstName: string;
  lastName: string;
  name?: string; // Full name for convenience
  email?: string;
  phone?: string;
  jobTitle?: string;
  companyId?: string; // Maintained for backward compatibility
  company?: string; // Company name for display
  notes?: string;
  hubspotId?: string;
  harvestUserId?: string;
  source?: ContactSource;
  roles?: string[]; // Array of roles across companies
  // Enrichment fields
  enrichmentStatus?: any; // JSON object with enrichment source status
  lastEnrichedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: string;
}

/**
 * Deal source types
 */
export type DealSource = "HubSpot" | "Manual" | "System";

/**
 * Base deal interface with minimal properties
 * (to avoid circular dependencies)
 */
export interface BaseDeal {
  id: string;
  name: string;
  stage: DealStage;
  value?: number;
  amount?: number; // Alternative field name for value
  currency?: string;
  probability?: number;
  expectedCloseDate?: string;
  closeDate?: string; // Alternative field name for expectedCloseDate
  startDate?: string;
  endDate?: string;
  invoiceFrequency?: string;
  paymentTerms?: number;
  description?: string;
  source?: DealSource;
  priority?: DealPriority;
  owner?: string;
  hubspotId?: string;
  harvestProjectId?: string;
  companyId?: string; // Direct link to primary company
  hubspot_name?: string; // Preserved HubSpot name
  hubspot_value?: number; // Preserved HubSpot value
  company?: string; // Company name for display
  hasEstimates?: boolean; // Whether deal has linked estimates
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  lastUpdatedBy?: string;
  lastUpdateSource?: string;
  includeInProjections?: boolean;
  validForProjections?: boolean;
  deletedAt?: string;
}

/**
 * Priority color mapping
 */
export const PRIORITY_COLORS = {
  High: "bg-red-500",
  Medium: "bg-yellow-500",
  Low: "bg-blue-500",
  "Qualified out": "bg-gray-500",
};

/**
 * Priority description mapping
 */
export const PRIORITY_DESCRIPTIONS = {
  High: "Pursue with strategic and proactive investment",
  Medium: "Pursue with moderate investment",
  Low: "Reactive only",
  "Qualified out": "Maintain relationships and monitor client this FY",
};

/**
 * Base tender interface
 */
export interface BaseTender {
  id: string;
  requestNo: string;
  status: TenderStatus;
  type: string;
  summary: string;
  issuedBy: string;
  unspsc?: string;
  closingDate: string;
  sourceEmail?: string;
  tenderUrl?: string;
  additionalInfo?: string;
  qualificationStatus: TenderQualificationStatus;
  qualificationReason?: string;
  qualifiedBy?: string;
  qualifiedAt?: string;
  companyId?: string;
  company?: BaseCompany;
  dealId?: string;
  tags?: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  deletedAt?: string;
}
