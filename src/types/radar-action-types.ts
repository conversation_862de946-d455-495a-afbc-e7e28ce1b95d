/**
 * Radar Action Types
 *
 * Types for the "Question Marks" action tracking system
 */

/**
 * Action types for radar action items
 */
export type RadarActionType =
  | "research"
  | "contact"
  | "qualify"
  | "analyze"
  | "follow_up"
  | "other";

/**
 * Status types for radar actions
 */
export type RadarActionStatus =
  | "pending"
  | "in_progress"
  | "completed"
  | "cancelled";

/**
 * Priority levels for radar actions
 */
export type RadarActionPriority = "low" | "normal" | "high" | "urgent";

/**
 * Radar action item interface
 */
export interface RadarAction {
  id: string;
  companyId: string;
  actionType: RadarActionType;
  title: string;
  description?: string;
  assignedTo: string;
  status: RadarActionStatus;
  priority: RadarActionPriority;
  dueDate?: string;
  startedAt?: string;
  completedAt?: string;
  notes?: string;
  completionNotes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  deletedAt?: string;

  // Joined data (optional, populated by queries)
  company?: {
    id: string;
    name: string;
    industry?: string;
    website?: string;
  };
}

/**
 * Create radar action DTO
 */
export interface CreateRadarAction {
  companyId: string;
  actionType: RadarActionType;
  title: string;
  description?: string;
  assignedTo: string;
  priority?: RadarActionPriority;
  dueDate?: string;
  notes?: string;
  createdBy: string;
}

/**
 * Update radar action DTO
 */
export interface UpdateRadarAction {
  title?: string;
  description?: string;
  assignedTo?: string;
  status?: RadarActionStatus;
  priority?: RadarActionPriority;
  dueDate?: string;
  notes?: string;
  startedAt?: string;
  completedAt?: string;
  completionNotes?: string;
  updatedBy?: string;
}

/**
 * Radar action filters
 */
export interface RadarActionFilters {
  companyId?: string;
  assignedTo?: string;
  status?: RadarActionStatus | RadarActionStatus[];
  priority?: RadarActionPriority | RadarActionPriority[];
  actionType?: RadarActionType | RadarActionType[];
  dueBefore?: string;
  dueAfter?: string;
  includeCompleted?: boolean;
}

/**
 * Action type metadata
 */
export const RADAR_ACTION_TYPE_META = {
  research: {
    label: "Research",
    icon: "🔍",
    description: "Investigate company background",
    color: "blue",
  },
  contact: {
    label: "Contact",
    icon: "📞",
    description: "Reach out to stakeholders",
    color: "green",
  },
  qualify: {
    label: "Qualify",
    icon: "✅",
    description: "Assess opportunity fit",
    color: "purple",
  },
  analyze: {
    label: "Analyze",
    icon: "📊",
    description: "Deep dive into metrics",
    color: "indigo",
  },
  follow_up: {
    label: "Follow Up",
    icon: "🔄",
    description: "Continue previous conversation",
    color: "orange",
  },
  other: {
    label: "Other",
    icon: "📝",
    description: "Custom action",
    color: "gray",
  },
} as const;

/**
 * Priority metadata
 */
export const RADAR_ACTION_PRIORITY_META = {
  urgent: {
    label: "Urgent",
    color: "red",
    bgColor: "bg-red-100 dark:bg-red-900/20",
    textColor: "text-red-700 dark:text-red-300",
  },
  high: {
    label: "High",
    color: "orange",
    bgColor: "bg-orange-100 dark:bg-orange-900/20",
    textColor: "text-orange-700 dark:text-orange-300",
  },
  normal: {
    label: "Normal",
    color: "blue",
    bgColor: "bg-blue-100 dark:bg-blue-900/20",
    textColor: "text-blue-700 dark:text-blue-300",
  },
  low: {
    label: "Low",
    color: "gray",
    bgColor: "bg-gray-100 dark:bg-gray-900/20",
    textColor: "text-gray-700 dark:text-gray-300",
  },
} as const;
