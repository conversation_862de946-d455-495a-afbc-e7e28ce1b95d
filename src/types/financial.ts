/**
 * Core financial type definitions
 *
 * This file contains consolidated type definitions for financial data
 * used throughout the application. It serves as the single source of truth
 * for these interfaces.
 */

// Import FilterDecision type
import { FilterDecision } from "../frontend/components/ForwardProjection/types/projection-audit-types";

/**
 * Represents a financial transaction in the system.
 * This is the single source of truth, designed to accommodate data
 * from various sources (Xero, Harvest, Manual) after transformation.
 */
export interface Transaction {
  /** Unique identifier for the transaction */
  id: string;

  /** The date the transaction occurred or is scheduled */
  date: Date; // Assume transformation logic converts string dates to Date objects

  /** The monetary value of the transaction (positive for inflows, negative for outflows) */
  amount: number;

  /** A clear description of the transaction */
  description: string; // Standardize on 'description', make required

  /** The category or nature of the transaction */
  type: 'invoice' | 'payment' | 'bill' | 'expense' | 'payroll' | 'transfer' | 'other'; // Use a specific union covering known types

  /** The lifecycle status, primarily for invoices/bills (optional) */
  status?: 'draft' | 'submitted' | 'approved' | 'paid' | 'void'; // Keep optional as not all types have this status

  /** The system or origin of the transaction data */
  source: 'xero' | 'harvest' | 'manual' | 'system'; // Make required for traceability, use specific union

  /** Optional field for additional source-specific or contextual data */
  metadata?: Record<string, any>; // Keep optional for flexibility
}

/**
 * Interface for custom expense
 */
export interface CustomExpense {
  id: string;
  name: string;
  type: 'Monthly Payroll' | 'Superannuation' | 'Insurances' | 'Taxes' | 'Subcontractor Fees' | 'Rent' | 'Reimbursements' | 'Professional Fees' | 'General Expenses' | 'Director Distributions' | 'Hardware' | 'Subscriptions' | 'Other Fees' | 'Other';
  amount: number;
  date: Date;
  frequency: 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'one-off';
  repeatCount?: number; // Number of times to repeat the expense (for recurring expenses)
  description?: string;
  source?: string;
  metadata?: Record<string, unknown>;
  _source?: {
    type: string;
    importedAt: Date;
    [key: string]: unknown;
  };
}

/**
 * Represents the cashflow for a specific day
 */
export interface DailyCashflow {
  date: Date; // Use Date object consistently
  inflows: number;
  outflows: number;
  netFlow: number;
  balance: number;
  transactions: Transaction[];
}

/**
 * Interface for bank account information
 */
export interface BankAccount {
  id: string;
  name: string;
  openingBalance?: number;
  closingBalance: number;
}

/**
 * Interface for bank balances summary
 */
export interface BankBalances {
  totalOpeningBalance?: number;
  totalClosingBalance: number;
  accounts: BankAccount[];
}

/**
 * Comprehensive cashflow forecast
 * Contains both input data and calculated projections
 */
export interface CashflowForecast {
  // Core date range
  startDate: Date; // Use Date object consistently
  endDate: Date; // Use Date object consistently

  // Balance information
  startingBalance: number;
  endingBalance: number;
  currentBalance?: number;
  projectedBalance?: number;
  projectedChange?: number;

  // Account details
  accounts: BankAccount[]; // Make required, aligns with default initialization

  // Transaction data
  historicalTransactions?: Transaction[];  // Used in service context
  projectedTransactions: Transaction[]; // Make required, aligns with default initialization
  transactions?: Transaction[];           // Used in UI context

  // Calculated daily projections
  dailyCashflow: DailyCashflow[]; // Make required, aligns with default initialization

  // Custom expenses data
  customExpenses: CustomExpense[]; // Make required, aligns with default initialization

  // Filter decisions from projection generation (optional)
  filterDecisions?: FilterDecision[];
}

/**
 * Type guard functions to check transaction types
 */

/**
 * Checks if a transaction is an invoice
 */
export function isInvoiceTransaction(transaction: Transaction): boolean {
  return transaction.type === 'invoice';
}

/**
 * Checks if a transaction is a payment
 */
export function isPaymentTransaction(transaction: Transaction): boolean {
  return transaction.type === 'payment';
}

/**
 * Checks if a transaction is an expense
 */
export function isExpenseTransaction(transaction: Transaction): boolean {
  return transaction.type === 'expense';
}

/**
 * Represents a potential transaction derived from a CRM deal
 * Extends the base Transaction with probability information
 */
export interface PotentialTransaction extends Transaction {
  dealId: string;              // Reference to source deal
  probability: number;         // Probability (0-100)
  scenarioType: ScenarioType;  // Which scenarios include this
}

export type ScenarioType = 'best-case' | 'expected-case' | 'worst-case';

/**
 * Enhanced cashflow forecast with scenario data
 */
export interface ScenarioCashflowForecast extends CashflowForecast {
  scenarios: {
    bestCase: {
      dailyCashflow: DailyCashflow[];
      endingBalance: number;
    };
    expectedCase: {
      dailyCashflow: DailyCashflow[];
      endingBalance: number;
    };
  };
  dealContributions: {
    dealId: string;
    name: string;
    expectedRevenue: number;
    probability: number;
    expectedCloseDate: Date;
    weightedContribution: number;
  }[];
}
