/**
 * Constants for expense types used throughout the application
 */

import { CustomExpense } from "../types";

/**
 * Array of all valid expense types
 */
export const EXPENSE_TYPES: CustomExpense["type"][] = [
  "Monthly Payroll",
  "Superannuation",
  "Insurances",
  "Taxes",
  "Subcontractor Fees",
  "Rent",
  "Reimbursements",
  "Professional Fees",
  "General Expenses",
  "Director Distributions",
  "Hardware",
  "Subscriptions",
  "Other Fees",
  "Other",
];

/**
 * Get expense type options for dropdowns
 * Returns an array of option elements for use in select components
 */
export const getExpenseTypeOptions = () => {
  return EXPENSE_TYPES.map((type) => ({
    value: type,
    label: type,
  }));
};
