/**
 * Constants for Xero integration
 */

/**
 * Xero source values used throughout the application
 */
export const XERO_SOURCES = {
  BASE: "xero",
  PAYROLL: "xero-payroll",
  PAYROLL_TEMPLATE: "xero-payroll-template",
  NET_PAY: "xero-netpay",
  NET_PAY_EXPENSE: "xero-netpay-expense",
  TAX: "xero-tax",
  TAX_EXPENSE: "xero-tax-expense",
  TAX_STATEMENT: "xero-tax-statement",
  SUPERANNUATION: "xero-superannuation",
  SUPERANNUATION_EXPENSE: "xero-superannuation-expense",
  GST: "xero-gst",
  BILL: "xero-bill",
};

/**
 * Check if a source string indicates it originated from Xero
 * @param source The source string to check
 * @returns True if the source indicates a Xero origin
 */
export const isXeroSource = (source: string | undefined): boolean => {
  if (!source) return false;

  // Check for exact match with base Xero source
  if (source === XERO_SOURCES.BASE) return true;

  // Check for Xero prefix
  return source.toLowerCase().startsWith("xero-");
};
