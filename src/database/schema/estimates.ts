/**
 * Estimates domain schema
 *
 * This file contains the schema for the estimates domain tables.
 */

import BetterSqlite3 from "better-sqlite3";

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the estimates domain tables
 * @param db Database instance
 */
export function createEstimateTables(db: Database): void {
  console.log('Creating estimates domain tables...');

  // Create estimate table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS estimate (
      id TEXT PRIMARY KEY,
      company_id TEXT NOT NULL,
      client_name TEXT NOT NULL,
      project_name TEXT,
      project_code TEXT,
      estimate_number TEXT,
      date_sent TEXT,
      valid_until TEXT,
      start_date TEXT,
      end_date TEXT,
      discount_type TEXT DEFAULT 'none',
      discount_value REAL DEFAULT 0,
      invoice_frequency TEXT,
      total_consultancy REAL DEFAULT 0,
      total_expenses REAL DEFAULT 0,
      total REAL DEFAULT 0,
      tax REAL DEFAULT 0,
      grand_total REAL DEFAULT 0,
      payment_schedule TEXT,
      payment_terms TEXT,
      payment_percentage REAL,
      staff_allocations TEXT,
      status TEXT DEFAULT 'draft",
      version INTEGER DEFAULT 1,
      notes TEXT,
      harvest_estimate_id TEXT UNIQUE,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      created_by TEXT,
      updated_by TEXT,
      deleted_at TEXT,
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for estimate table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_company_id ON estimate(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_dates ON estimate(start_date, end_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_status ON estimate(status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_harvest_id ON estimate(harvest_estimate_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_source ON estimate(source)`).run();

  // Create estimate_allocation table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS estimate_allocation (
      id TEXT PRIMARY KEY,
      estimate_id TEXT NOT NULL,
      harvest_user_id INTEGER NOT NULL,
      first_name TEXT NOT NULL,
      last_name TEXT,
      project_role TEXT,
      level TEXT,
      target_rate_daily REAL NOT NULL,
      cost_rate_daily REAL NOT NULL,
      proposed_rate_daily REAL NOT NULL,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for estimate_allocation table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_allocation_estimate_id ON estimate_allocation(estimate_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_allocation_harvest_user_id ON estimate_allocation(harvest_user_id)`).run();

  // Create estimate_time_allocation table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS estimate_time_allocation (
      id TEXT PRIMARY KEY,
      allocation_id TEXT NOT NULL,
      week_identifier TEXT NOT NULL, -- ISO week identifier
      days REAL NOT NULL,
      FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE CASCADE,
      UNIQUE(allocation_id, week_identifier)
    )
  `).run();

  // Create indexes for estimate_time_allocation table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_time_allocation_allocation_id ON estimate_time_allocation(allocation_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_estimate_time_allocation_week ON estimate_time_allocation(week_identifier)`).run();

  console.log('Estimates domain tables created successfully');
}
