/**
 * Integration domain schema
 *
 * This file contains the schema for the integration domain tables.
 */

import BetterSqlite3 from "better-sqlite3";

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the integration domain tables
 * @param db Database instance
 */
export function createIntegrationTables(db: Database): void {
  console.log('Creating integration domain tables...');

  // Create hubspot_import table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS hubspot_import (
      id TEXT PRIMARY KEY,
      import_date TEXT NOT NULL,
      status TEXT NOT NULL, -- 'pending', 'completed', 'failed'
      deals_count INTEGER,
      contacts_count INTEGER,
      companies_count INTEGER,
      error_message TEXT,
      created_at TEXT NOT NULL
    )
  `).run();

  // Create indexes for hubspot_import table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_hubspot_import_date ON hubspot_import(import_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_hubspot_import_status ON hubspot_import(status)`).run();

  // Create hubspot_settings table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS hubspot_settings (
      id TEXT PRIMARY KEY,
      key TEXT NOT NULL UNIQUE,
      value TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `).run();

  // Create settings table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      updated_by TEXT
    )
  `).run();

  // Create harvest_invoice_cache table for performance
  db.prepare(`
    CREATE TABLE IF NOT EXISTS harvest_invoice_cache (
      harvest_client_id INTEGER PRIMARY KEY,
      total_invoiced REAL NOT NULL DEFAULT 0,
      invoice_count INTEGER NOT NULL DEFAULT 0,
      last_updated TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT (datetime('now'))
    )
  `).run();

  // Create indexes for harvest_invoice_cache table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_harvest_invoice_cache_updated ON harvest_invoice_cache(last_updated)`).run();

  // Create company_enrichment table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS company_enrichment (
      id TEXT PRIMARY KEY,
      company_id TEXT NOT NULL,
      source TEXT NOT NULL, -- 'abn_lookup', 'clearbit', 'apollo', etc.
      source_id TEXT, -- External ID from the source (e.g., ABN number)
      data JSON NOT NULL, -- Full enriched data as JSON
      confidence_score REAL DEFAULT 1.0, -- 0.0 to 1.0
      enriched_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP, -- When to re-enrich
      created_by TEXT DEFAULT 'system',
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for company_enrichment table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_enrichment_company_id ON company_enrichment(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_enrichment_source ON company_enrichment(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_enrichment_expires_at ON company_enrichment(expires_at)`).run();

  // Create contact_enrichment table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact_enrichment (
      id TEXT PRIMARY KEY,
      contact_id TEXT NOT NULL,
      source TEXT NOT NULL, -- 'clearbit', 'apollo', 'linkedin', etc.
      source_id TEXT, -- External ID from the source
      data JSON NOT NULL, -- Full enriched data as JSON
      confidence_score REAL DEFAULT 1.0, -- 0.0 to 1.0
      enriched_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP, -- When to re-enrich
      created_by TEXT DEFAULT 'system',
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for contact_enrichment table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_enrichment_contact_id ON contact_enrichment(contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_enrichment_source ON contact_enrichment(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_enrichment_expires_at ON contact_enrichment(expires_at)`).run();

  // Create enrichment_log table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS enrichment_log (
      id TEXT PRIMARY KEY,
      entity_type TEXT NOT NULL, -- 'company' or 'contact'
      entity_id TEXT NOT NULL,
      source TEXT NOT NULL,
      status TEXT NOT NULL, -- 'success', 'failed', 'no_match", 'rate_limited'
      error_message TEXT,
      attempted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      response_time_ms INTEGER,
      api_credits_used INTEGER DEFAULT 0
    )
  `).run();

  // Create indexes for enrichment_log table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_enrichment_log_entity ON enrichment_log(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_enrichment_log_attempted_at ON enrichment_log(attempted_at)`).run();

  console.log('Integration domain tables created successfully');
}
