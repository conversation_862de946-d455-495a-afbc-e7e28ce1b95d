/**
 * Financial domain schema
 *
 * This file contains the schema for the financial domain tables.
 */

import BetterSqlite3 from "better-sqlite3";

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the financial domain tables
 * @param db Database instance
 */
export function createFinancialTables(db: Database): void {
  console.log('Creating financial domain tables...');

  // Create expense table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS expense (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,

      /* Source and Type Information */
      source TEXT NOT NULL, -- 'manual', 'xero_bill', 'xero_payroll', 'xero_tax', etc.
      type TEXT NOT NULL, -- 'Monthly Payroll', 'Superannuation', 'Insurances', 'Tax', etc.
      category TEXT, -- Additional categorization

      /* Financial Information */
      amount REAL NOT NULL,
      currency TEXT DEFAULT 'AUD',
      tax_inclusive INTEGER DEFAULT 1, -- Boolean: 1 = tax inclusive, 0 = tax exclusive
      tax_amount REAL, -- For Xero expenses that include tax details

      /* Timing Information */
      date TEXT NOT NULL, -- ISO date string for the expense date
      due_date TEXT, -- For bills and other expenses with due dates
      frequency TEXT, -- 'one-off', 'weekly', 'fortnightly', 'monthly', 'quarterly', 'yearly'
      repeat_count INTEGER, -- Number of times to repeat (for recurring expenses)
      end_date TEXT, -- Optional end date for recurring expenses

      /* Additional Information */
      description TEXT,
      reference TEXT, -- Reference number, invoice number, etc.
      status TEXT, -- 'draft', 'approved', 'paid', etc. (primarily for Xero-synced expenses)

      /* External System Information */
      external_id TEXT, -- ID in external system (e.g., Xero bill ID)
      external_url TEXT, -- URL to view in external system
      metadata TEXT, -- JSON string for additional data from external systems

      /* Audit Information */
      editable INTEGER DEFAULT 1, -- Boolean: 1 = can be edited, 0 = read-only (for synced expenses)
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      last_synced_at TEXT, -- For expenses synced from external systems
      deleted_at TEXT
    )
  `).run();

  // Create indexes for expense table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_date ON expense(date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_due_date ON expense(due_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_type ON expense(type)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_source ON expense(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_status ON expense(status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_external_id ON expense(external_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_expense_editable ON expense(editable)`).run();

  // Create cashflow_snapshot table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS cashflow_snapshot (
      id TEXT PRIMARY KEY,
      date TEXT NOT NULL, -- ISO date string
      tenant_id TEXT NOT NULL, -- Xero tenant ID
      days_ahead INTEGER NOT NULL DEFAULT 90,
      snapshot_data TEXT NOT NULL, -- JSON string of snapshot data
      created_at TEXT NOT NULL,
      created_by TEXT,
      UNIQUE(date, tenant_id, days_ahead)
    )
  `).run();

  // Create indexes for cashflow_snapshot table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_cashflow_snapshot_date ON cashflow_snapshot(date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_cashflow_snapshot_tenant ON cashflow_snapshot(tenant_id)`).run();

  console.log('Financial domain tables created successfully');
}
