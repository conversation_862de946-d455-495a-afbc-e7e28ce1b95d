/**
 * Audit domain schema
 *
 * This file contains the schema for the audit domain tables.
 */

import BetterSqlite3 from "better-sqlite3";

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the audit domain tables
 * @param db Database instance
 */
export function createAuditTables(db: Database): void {
  console.log('Creating audit domain tables...');

  // Create field_ownership table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS field_ownership (
      id TEXT PRIMARY KEY,
      entity_type TEXT NOT NULL, -- 'deal', 'estimate', etc.
      entity_id TEXT NOT NULL,
      field_name TEXT NOT NULL,
      owner TEXT NOT NULL, -- 'HubSpot', 'Harvest', 'Manual', 'System', 'Estimate'
      set_at TEXT NOT NULL,
      set_by TEXT,
      UNIQUE(entity_type, entity_id, field_name)
    )
  `).run();

  // Create indexes for field_ownership table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_field_ownership_entity ON field_ownership(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_field_ownership_owner ON field_ownership(owner)`).run();

  // Create change_log table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS change_log (
      id TEXT PRIMARY KEY,
      entity_type TEXT NOT NULL, -- 'deal', 'estimate', etc.
      entity_id TEXT NOT NULL,
      field_name TEXT NOT NULL,
      old_value TEXT,
      new_value TEXT,
      change_source TEXT NOT NULL,
      changed_at TEXT NOT NULL,
      changed_by TEXT
    )
  `).run();

  // Create indexes for change_log table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_entity ON change_log(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_field ON change_log(field_name)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_change_log_date ON change_log(changed_at)`).run();

  // Create activity_feed table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS activity_feed (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL, -- Activity type (see types below)
      subject TEXT NOT NULL, -- Brief description
      description TEXT, -- Detailed description
      status TEXT, -- 'planned', 'completed', 'canceled', 'in_progress'

      /* Entity Relationships */
      entity_type TEXT, -- 'deal', 'company', 'estimate', 'expense', 'contact'
      entity_id TEXT, -- ID of the related entity

      /* Timing Information */
      due_date TEXT,
      completed_date TEXT,

      /* Legacy Relationships (for backward compatibility) */
      company_id TEXT,
      contact_id TEXT,
      deal_id TEXT,

      /* Metadata */
      metadata TEXT, -- JSON for additional context and structured data
      is_read INTEGER DEFAULT 0, -- Track read status per activity
      importance TEXT DEFAULT 'normal', -- 'low', 'normal', 'high'

      /* User and System Information */
      created_by TEXT NOT NULL, -- User ID or 'system'
      source TEXT, -- 'user', 'hubspot', 'xero', 'harvest", 'system'

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,

      /* Foreign Key Constraints */
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for activity_feed table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_activity_feed_created_at ON activity_feed(created_at DESC)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_activity_feed_type ON activity_feed(type)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_activity_feed_entity ON activity_feed(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_activity_feed_source ON activity_feed(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_activity_feed_created_by ON activity_feed(created_by)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_activity_feed_unread ON activity_feed(is_read)`).run();

  console.log('Audit domain tables created successfully');
}
