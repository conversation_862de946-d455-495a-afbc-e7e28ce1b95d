/**
 * Database module
 *
 * This is the main entry point for the database module.
 * It exports the database instance and initialization functions.
 */

import BetterSqlite3 from "better-sqlite3";
import path from "path";
import fs from "fs";
import { execSync } from "child_process";

// Use proper type for Database
type Database = BetterSqlite3.Database | null;

// Database filename - consistent across environments
const DB_FILENAME = "upstream.db";

/**
 * Get the database path based on the environment
 * @returns The path to the database file
 */
export function getDatabasePath(): string {
  let dataDir = "";

  if (process.env.NODE_ENV === "production") {
    // In production (Render), use the mounted persistent disk
    dataDir = "/data";
    console.log("Running in production mode, using data directory:", dataDir);
  } else {
    // In development, use local directory
    dataDir = path.resolve(__dirname, "../../data");
  }

  // Ensure the data directory exists
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // Return the full path to the database file
  return path.join(dataDir, DB_FILENAME);
}

// Database instance
let db: Database = null;

/**
 * Get the database instance
 * @returns The database instance
 */
export function getDatabase(): BetterSqlite3.Database {
  if (!db) {
    const dbPath = getDatabasePath();
    console.log("Using database path:", dbPath);

    try {
      // Check if we can write to the directory
      const dataDir = path.dirname(dbPath);
      const testFile = path.join(dataDir, ".write-test");
      fs.writeFileSync(testFile, "test");

      // Add a small delay to ensure file system operations complete
      // This prevents race conditions during hot reloads
      setTimeout(() => {
        try {
          if (fs.existsSync(testFile)) {
            fs.unlinkSync(testFile);
          }
        } catch (cleanupError) {
          // Ignore cleanup errors - the important thing is that we can write
          console.log(
            "Write test cleanup warning (non-critical):",
            cleanupError.message,
          );
        }
      }, 10);

      console.log(`Successfully verified write access to ${dataDir}`);
    } catch (error) {
      console.error(
        `ERROR: Cannot write to data directory ${path.dirname(dbPath)}:`,
        error,
      );
      throw error;
    }

    try {
      // Create database connection with timeout configuration
      db = new BetterSqlite3(dbPath, {
        // Set busy timeout to 30 seconds (30000ms)
        // This prevents "database is locked" errors when multiple queries compete
        timeout: 30000,
        // Enable verbose mode in development for debugging
        verbose:
          process.env.NODE_ENV === "development" ? console.log : undefined,
      });

      // Configure additional pragmas for performance and safety
      db.pragma("journal_mode = WAL"); // Write-Ahead Logging for better concurrency
      db.pragma("busy_timeout = 30000"); // Same as connection timeout
      db.pragma("foreign_keys = ON"); // Enable foreign key constraints

      // Set statement timeout for individual queries (5 seconds)
      // This prevents runaway queries from blocking the database
      db.defaultSafeIntegers(false); // Use JavaScript numbers for integers

      console.log(
        "Successfully connected to SQLite database with timeouts configured",
      );
    } catch (error) {
      console.error(
        `ERROR: Failed to initialize SQLite database at ${dbPath}:`,
        error,
      );
      throw error;
    }
  }

  return db;
}

/**
 * Get the current schema version
 * @param db Database instance
 * @returns The current schema version or null if not initialized
 */
export function getSchemaVersion(db: Database): number | null {
  try {
    // Check if the schema_version table exists
    const tableExists = db
      .prepare(
        `
      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    `,
      )
      .get();

    if (!tableExists) {
      return null;
    }

    // Get the highest version
    const result = db
      .prepare(
        `
      SELECT MAX(version) as version FROM schema_version
    `,
      )
      .get() as { version: number } | undefined;

    return result?.version || null;
  } catch (error) {
    console.error("Error getting schema version:", error);
    return null;
  }
}

/**
 * Initialize the database
 * This function should be called once at application startup
 * @returns The initialized database instance
 */
export function initializeDatabase(): BetterSqlite3.Database {
  const database = getDatabase();

  // Add process ID to help debug concurrent migration attempts
  const processId = process.pid;
  console.log(`[Process ${processId}] Starting database initialization...`);

  // Get the current schema version
  const currentVersion = getSchemaVersion(database);
  console.log(
    `[Process ${processId}] Current database schema version: ${currentVersion || "none"}`,
  );

  // Check if we need to initialize the schema
  if (!currentVersion) {
    console.log("Database schema not found. Running simple migration...");

    try {
      // Use simple migration system - try multiple possible locations
      const migrationPaths = [
        path.join(__dirname, "../../migrations/000_unified_schema.sql"), // Development
        path.join(process.cwd(), "migrations/000_unified_schema.sql"), // Production
        path.join(__dirname, "../../../migrations/000_unified_schema.sql"), // Alternative
      ];

      let migrationFound = false;

      for (const migrationPath of migrationPaths) {
        if (fs.existsSync(migrationPath)) {
          console.log("Found migration file:", migrationPath);
          const migrationSQL = fs.readFileSync(migrationPath, "utf8");

          // Execute the migration in a transaction for atomicity
          console.log("Executing migration in transaction...");

          try {
            // Begin exclusive transaction to prevent concurrent migrations
            database.exec("BEGIN EXCLUSIVE");

            // Check again if schema exists (in case another process just created it)
            const versionCheck = database
              .prepare(
                `
              SELECT 1 FROM sqlite_master WHERE type='table' AND name='schema_version'
            `,
              )
              .get();

            if (versionCheck) {
              console.log(
                "Schema already exists (created by another process), skipping migration",
              );
              database.exec("ROLLBACK");
            } else {
              // Execute the migration
              database.exec(migrationSQL);
              database.exec("COMMIT");
              console.log(
                "Database schema initialized successfully via migration",
              );
            }
          } catch (error) {
            console.error("Migration failed, rolling back:", error);
            try {
              database.exec("ROLLBACK");
            } catch (rollbackError) {
              console.error("Rollback failed:", rollbackError);
            }
            throw error;
          }

          migrationFound = true;
          break;
        } else {
          console.log("Migration file not found at:", migrationPath);
        }
      }

      if (!migrationFound) {
        console.warn(
          "No migration file found at any location, trying alternative initialization...",
        );

        // Fallback to initialization script - try multiple locations
        const scriptPaths = [
          path.join(__dirname, "../../scripts/initialize-fresh-database.js"), // Development
          path.join(process.cwd(), "scripts/initialize-fresh-database.js"), // Production
          path.join(__dirname, "../../../scripts/initialize-fresh-database.js"), // Alternative
        ];

        let scriptFound = false;

        for (const scriptPath of scriptPaths) {
          if (fs.existsSync(scriptPath)) {
            console.log(
              `Found initialization script, but skipping automatic execution for safety`,
            );
            console.log(
              `To initialize database, run manually: node ${scriptPath} --force`,
            );

            // Instead of running the destructive script, just execute the schema
            const schemaPath = path.join(
              path.dirname(scriptPath),
              "../migrations/000_unified_schema.sql",
            );
            if (fs.existsSync(schemaPath)) {
              console.log("Applying schema without dropping tables...");
              const schema = fs.readFileSync(schemaPath, "utf8");
              database.exec(schema);
              console.log("Schema applied successfully");
              scriptFound = true;
            }
            break;
          } else {
            console.log("Initialization script not found at:", scriptPath);
          }
        }

        if (!scriptFound) {
          console.error(
            "No migration file or initialization script found at any location",
          );
          throw new Error(
            "Cannot initialize database - no migration or initialization method available",
          );
        }
      }
    } catch (error) {
      console.error("Error initializing database schema:", error);
      throw error;
    }
  } else {
    // Database exists, check for missing tables
    console.log("Checking for missing tables...");

    // Check for note table specifically (common issue after refactor)
    const noteTableExists = database
      .prepare(
        `
      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    `,
      )
      .get();

    if (!noteTableExists) {
      console.log("Note table missing. Creating it...");
      try {
        database
          .prepare(
            `
          CREATE TABLE note (
            id TEXT PRIMARY KEY,
            deal_id TEXT,
            content TEXT NOT NULL,
            created_at TEXT NOT NULL,
            created_by TEXT,
            FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
          )
        `,
          )
          .run();

        database
          .prepare(
            `
          CREATE INDEX idx_note_deal_id ON note(deal_id)
        `,
          )
          .run();

        console.log("Note table created successfully");
      } catch (error) {
        console.error("Error creating note table:", error);
      }
    }

    // Check for radar_action_items table
    const radarTableExists = database
      .prepare(
        `
      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    `,
      )
      .get();

    if (!radarTableExists) {
      console.log("radar_action_items table missing. Creating it...");
      try {
        database.exec(`
          -- Radar Action Items - For tracking action items on companies needing investigation
          CREATE TABLE IF NOT EXISTS radar_action_items (
            id TEXT PRIMARY KEY,
            company_id TEXT NOT NULL,
            
            -- Action details
            action_type TEXT NOT NULL CHECK(action_type IN ('research', 'contact', 'qualify', 'analyze', 'follow_up', 'other')),
            title TEXT NOT NULL,
            description TEXT,
            
            -- Assignment and status
            assigned_to TEXT NOT NULL, -- User email or ID
            status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'in_progress', 'completed', 'cancelled')),
            priority TEXT DEFAULT 'normal' CHECK(priority IN ('low', 'normal', 'high', 'urgent')),
            
            -- Timing
            due_date TEXT,
            started_at TEXT,
            completed_at TEXT,
            
            -- Additional context
            notes TEXT,
            completion_notes TEXT,
            
            -- Audit fields
            created_at TEXT NOT NULL DEFAULT (datetime('now')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now')),
            created_by TEXT NOT NULL,
            updated_by TEXT,
            deleted_at TEXT,
            
            -- Foreign keys
            FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
          );

          -- Indexes for performance
          CREATE INDEX IF NOT EXISTS idx_radar_action_items_company_id ON radar_action_items(company_id);
          CREATE INDEX IF NOT EXISTS idx_radar_action_items_assigned_to ON radar_action_items(assigned_to);
          CREATE INDEX IF NOT EXISTS idx_radar_action_items_status ON radar_action_items(status);
          CREATE INDEX IF NOT EXISTS idx_radar_action_items_due_date ON radar_action_items(due_date);
          CREATE INDEX IF NOT EXISTS idx_radar_action_items_deleted_at ON radar_action_items(deleted_at);
        `);

        console.log("radar_action_items table created successfully");
      } catch (error) {
        console.error("Error creating radar_action_items table:", error);
      }
    }
  }

  // Return the database instance
  return database;
}

/**
 * Check if the database has been initialized with the complete schema
 * @returns True if the database has been initialized, false otherwise
 */
export function isDatabaseInitialized(): boolean {
  const database = getDatabase();
  return !!getSchemaVersion(database);
}

// Export a getter that always returns the current database instance
// This ensures we always get the latest connection even after re-initialization
const databaseGetter = {
  get prepare() {
    return getDatabase().prepare.bind(getDatabase());
  },
  get exec() {
    return getDatabase().exec.bind(getDatabase());
  },
  get pragma() {
    return getDatabase().pragma.bind(getDatabase());
  },
  get transaction() {
    return getDatabase().transaction.bind(getDatabase());
  },
  get close() {
    return getDatabase().close.bind(getDatabase());
  },
  get defaultSafeIntegers() {
    return getDatabase().defaultSafeIntegers.bind(getDatabase());
  },
};

// Export the getter as default
export default databaseGetter;
