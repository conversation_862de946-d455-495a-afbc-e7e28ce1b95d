/**
 * Navigation Configuration
 *
 * This file defines all navigation tabs in a single location to ensure consistency
 * throughout the application. It serves as the single source of truth for navigation items.
 */

import React from "react";
import { TabId } from "../types/navigation";

/**
 * Structure of a navigation tab
 */
export interface NavigationTab {
  id: TabId;
  label: string;
  icon?: React.ReactNode;
  newBadge?: boolean;
  color?: string;
  showInNavigation: boolean;
}

/**
 * All navigation tabs in the application
 */
export const NAVIGATION_TABS: NavigationTab[] = [
  {
    id: "projection",
    label: "Cashflow",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "secondary",
    showInNavigation: true,
  },
  {
    id: "forecast",
    label: "Smart Forecast",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "amber",
    showInNavigation: true,
  },
  {
    id: "expenses",
    label: "Expenses",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "secondary",
    showInNavigation: true,
  },
  {
    id: "crm/deals",
    label: "CRM",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
          clipRule="evenodd"
        />
        <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
      </svg>
    ),
    color: "blue",
    showInNavigation: true,
  },
  {
    id: "activity",
    label: "Activity",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "purple",
    showInNavigation: true,
  },
  {
    id: "estimates",
    label: "Estimates",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "secondary",
    showInNavigation: true,
  },
  {
    id: "reports",
    label: "Reports",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "green",
    showInNavigation: true,
  },
  {
    id: "help",
    label: "Help",
    icon: (
      <svg
        className="nav-icon"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
      >
        <path
          d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    color: "gray",
    showInNavigation: false, // Not shown in main navigation, handled separately in header
  },
  {
    id: "version-history",
    label: "Version History",
    icon: (
      <svg className="nav-icon" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
          clipRule="evenodd"
        />
      </svg>
    ),
    color: "blue",
    showInNavigation: false, // Not shown in main navigation, but accessible via other means
  },
];

/**
 * Filter to get only tabs that should be shown in navigation
 */
export const VISIBLE_NAVIGATION_TABS = NAVIGATION_TABS.filter(
  (tab) => tab.showInNavigation,
);

/**
 * Helper function to get a tab by ID
 */
export function getTabById(id: TabId): NavigationTab | undefined {
  return NAVIGATION_TABS.find((tab) => tab.id === id);
}
