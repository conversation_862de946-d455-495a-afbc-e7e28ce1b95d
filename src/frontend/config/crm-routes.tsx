import React, { lazy } from "react";
import { Routes, Route, Navigate } from "react-router-dom";

// Lazy load CRM components
const CRMLayout = lazy(() => import('../components/CRM/layouts/CRMLayout'));
const EnhancedDealBoard = lazy(() => import('../components/CRM/pipeline/EnhancedDealBoard'));
const DirectoryPage = lazy(() => import('../components/CRM/directory/DirectoryPage'));
const HubSpotIntegration = lazy(() => import('../components/CRM/HubSpot/HubSpotIntegration'));
const DataManagementPage = lazy(() => import('../components/CRM/DataManagement/DataManagementPage'));
const DealEditPage = lazy(() => import('../components/CRM/DealEdit/DealEditPage'));
const ContactsList = lazy(() => import('../components/CRM/Contacts/ContactsList'));
const CompaniesList = lazy(() => import('../components/CRM/Companies/CompaniesList'));

// Lazy load the Intelligence page with sub-tabs
const IntelligencePage = lazy(() => import('../components/CRM/Intelligence/IntelligencePage'));

// Lazy load Tender components
const TenderPipeline = lazy(() => import('../components/CRM/tenders/TenderPipeline'));

export const CRMRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<CRMLayout />}>
        <Route index element={<Navigate to="pipeline" replace />} />
        <Route path="pipeline" element={<EnhancedDealBoard />} />
        <Route path="tenders" element={<TenderPipeline />} />
        <Route path="directory" element={<DirectoryPage />} />
        <Route path="intelligence" element={<IntelligencePage />} />
        <Route path="intelligence/knowledge-graph" element={<IntelligencePage />} />
        <Route path="hubspot" element={<HubSpotIntegration />} />
        <Route path="data-management" element={<DataManagementPage />} />
        <Route path="deals/:id" element={<DealEditPage />} />
        
        {/* Contact and Company pages for detail views */}
        <Route path="contacts" element={<ContactsList />} />
        <Route path="companies" element={<CompaniesList />} />
        
        {/* Legacy redirects */}
        <Route path="deals" element={<Navigate to="/crm/pipeline" replace />} />
      </Route>
    </Routes>
  );
};

export default CRMRoutes;