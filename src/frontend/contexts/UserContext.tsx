import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";

interface UserInfo {
  name: string;
  email: string;
  id: string;
}

interface OrganizationInfo {
  name: string;
  id: string;
}

interface UserContextType {
  user: UserInfo | null;
  organization: OrganizationInfo | null;
  isLoading: boolean;
  error: string | null;
  refreshUserData: () => Promise<void>;
}

const UserContext = createContext<UserContextType>({
  user: null,
  organization: null,
  isLoading: false,
  error: null,
  refreshUserData: async () => {},
});

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [organization, setOrganization] = useState<OrganizationInfo | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUserData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get("/api/xero/account", {
        withCredentials: true,
      });

      if (response.data.success) {
        setUser(response.data.user);
        setOrganization(response.data.organization);
      } else {
        setError("Failed to load user data");
      }
    } catch (err) {
      setError("Error fetching user data");
      console.error("Error fetching user data:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch on mount
  useEffect(() => {
    fetchUserData();
  }, []);

  return (
    <UserContext.Provider
      value={{
        user,
        organization,
        isLoading,
        error,
        refreshUserData: fetchUserData,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => useContext(UserContext);
