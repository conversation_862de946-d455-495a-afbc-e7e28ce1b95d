import React, { createContext, useContext, useState, ReactNode } from "react";

interface FloatingPanelsContextType {
  feedbackExpanded: boolean;
  updateNotificationExpanded: boolean;
  xeroExpanded: boolean;
  aiChatExpanded: boolean;
  aiChatProvider: "xero" | "hubspot";
  setFeedbackExpanded: (expanded: boolean) => void;
  setUpdateNotificationExpanded: (expanded: boolean) => void;
  setXeroExpanded: (expanded: boolean) => void;
  setAiChatExpanded: (expanded: boolean) => void;
  setAiChatProvider: (provider: "xero" | "hubspot") => void;
  expandFeedback: () => void;
  expandUpdateNotification: () => void;
  expandUpdate: () => void;
  expandXero: () => void;
  expandAiChat: () => void;
  collapseAll: () => void;
}

const FloatingPanelsContext = createContext<
  FloatingPanelsContextType | undefined
>(undefined);

interface FloatingPanelsProviderProps {
  children: ReactNode;
}

export const FloatingPanelsProvider: React.FC<FloatingPanelsProviderProps> = ({
  children,
}) => {
  const [feedbackExpanded, setFeedbackExpanded] = useState(false);
  const [updateNotificationExpanded, setUpdateNotificationExpanded] =
    useState(false);
  const [xeroExpanded, setXeroExpanded] = useState(false);
  const [aiChatExpanded, setAiChatExpanded] = useState(false);
  const [aiChatProvider, setAiChatProvider] = useState<"xero" | "hubspot">(
    "xero",
  );

  const expandFeedback = () => {
    setFeedbackExpanded(true);
    setUpdateNotificationExpanded(false);
    setXeroExpanded(false);
    setAiChatExpanded(false);
  };

  const expandUpdateNotification = () => {
    setUpdateNotificationExpanded(true);
    setFeedbackExpanded(false);
    setXeroExpanded(false);
    setAiChatExpanded(false);
  };

  const expandUpdate = () => {
    // Alias for expandUpdateNotification
    expandUpdateNotification();
  };

  const expandXero = () => {
    setXeroExpanded(true);
    setFeedbackExpanded(false);
    setUpdateNotificationExpanded(false);
    setAiChatExpanded(false);
  };

  const expandAiChat = () => {
    setAiChatExpanded(true);
    setFeedbackExpanded(false);
    setUpdateNotificationExpanded(false);
    setXeroExpanded(false);
  };

  const collapseAll = () => {
    setFeedbackExpanded(false);
    setUpdateNotificationExpanded(false);
    setXeroExpanded(false);
    setAiChatExpanded(false);
  };

  const value: FloatingPanelsContextType = {
    feedbackExpanded,
    updateNotificationExpanded,
    xeroExpanded,
    aiChatExpanded,
    aiChatProvider,
    setFeedbackExpanded,
    setUpdateNotificationExpanded,
    setXeroExpanded,
    setAiChatExpanded,
    setAiChatProvider,
    expandFeedback,
    expandUpdateNotification,
    expandUpdate,
    expandXero,
    expandAiChat,
    collapseAll,
  };

  return (
    <FloatingPanelsContext.Provider value={value}>
      {children}
    </FloatingPanelsContext.Provider>
  );
};

export const useFloatingPanels = (): FloatingPanelsContextType => {
  const context = useContext(FloatingPanelsContext);
  if (context === undefined) {
    throw new Error(
      "useFloatingPanels must be used within a FloatingPanelsProvider",
    );
  }
  return context;
};
