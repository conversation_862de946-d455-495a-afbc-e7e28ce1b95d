import React, { createContext, useContext, useCallback, useState, useEffect, useRef } from "react";
import { useEvents, LogEntry } from "./EventContext";

interface LoggingContextType {
  enableConsoleInterceptor: () => () => void; // Returns a cleanup function
  disableConsoleInterceptor: () => void;
  isInterceptorEnabled: boolean;
}

const LoggingContext = createContext<LoggingContextType | null>(null);

interface LoggingProviderProps {
  children: React.ReactNode;
  interceptConsoleByDefault?: boolean;
}

/**
 * Helper function to safely stringify objects with possible circular references
 */
const safeStringify = (obj: any): string => {
  if (obj === null || obj === undefined) {
    return String(obj);
  }
  
  if (typeof obj !== 'object') {
    return String(obj);
  }
  
  // Handle special objects
  if (obj instanceof Error) {
    return `Error: ${obj.message}${obj.stack ? `\n${obj.stack}` : ""}`;
  }
  
  try {
    // Use a set to track objects we've seen
    const seen = new WeakSet();
    
    // Custom replacer function to handle circular references
    const replacer = (key: string, value: any) => {
      // Skip non-object values
      if (typeof value !== 'object' || value === null) {
        return value;
      }
      
      // Handle DOM nodes
      if (value instanceof Node) {
        return `[DOM Node: ${value.nodeName}]`;
      }
      
      // Check for circular reference
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      
      // Add this object to the set of seen objects
      seen.add(value);
      
      return value;
    };
    
    return JSON.stringify(obj, replacer, 2);
  } catch (err) {
    return `[Object - Failed to stringify: ${err instanceof Error ? err.message : String(err)}]`;
  }
};

/**
 * Provider component for logging functionality
 * Manages console interceptor state and provides methods to enable/disable it
 */
export const LoggingProvider: React.FC<LoggingProviderProps> = ({ 
  children, 
  interceptConsoleByDefault = false 
}) => {
  const events = useEvents();
  const [isInterceptorEnabled, setIsInterceptorEnabled] = useState(interceptConsoleByDefault);
  
  // Store original console methods
  const originalConsoleMethods = useRef({
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug
  });
  
  // Enable console interceptor
  const enableConsoleInterceptor = useCallback(() => {
    // Skip if already enabled to prevent unnecessary re-renders
    if (isInterceptorEnabled) {
      return () => {}; // Empty cleanup function
    }
    
    const { log, error, warn, info, debug } = originalConsoleMethods.current;
    
    // Override console methods to intercept logs
    console.log = (...args: any[]) => {
      // Call the original method
      log(...args);
      
      // Add to our logs
      events.addLog({
        timestamp: new Date().toISOString(),
        message: args.map(arg => safeStringify(arg)).join(' '),
        type: "info",
        source: "frontend"
      });
    };
    
    console.error = (...args: any[]) => {
      // Call the original method
      error(...args);
      
      // Add to our logs
      events.addLog({
        timestamp: new Date().toISOString(),
        message: args.map(arg => safeStringify(arg)).join(' '),
        type: "error",
        source: "frontend"
      });
    };
    
    console.warn = (...args: any[]) => {
      // Call the original method
      warn(...args);
      
      // Add to our logs
      events.addLog({
        timestamp: new Date().toISOString(),
        message: args.map(arg => safeStringify(arg)).join(' '),
        type: "warning",
        source: "frontend"
      });
    };
    
    console.info = (...args: any[]) => {
      // Call the original method
      info(...args);
      
      // Add to our logs
      events.addLog({
        timestamp: new Date().toISOString(),
        message: args.map(arg => safeStringify(arg)).join(' '),
        type: "info",
        source: "frontend"
      });
    };
    
    console.debug = (...args: any[]) => {
      // Call the original method
      debug(...args);
      
      // Add to our logs
      events.addLog({
        timestamp: new Date().toISOString(),
        message: args.map(arg => safeStringify(arg)).join(' '),
        type: "debug",
        source: "frontend"
      });
    };
    
    // Update state only once
    setIsInterceptorEnabled(true);
    
    // Return a function to restore the original methods
    return () => {
      console.log = log;
      console.error = error;
      console.warn = warn;
      console.info = info;
      console.debug = debug;
      setIsInterceptorEnabled(false);
    };
  }, [events, isInterceptorEnabled]);
  
  // Disable console interceptor
  const disableConsoleInterceptor = useCallback(() => {
    const { log, error, warn, info, debug } = originalConsoleMethods.current;
    console.log = log;
    console.error = error;
    console.warn = warn;
    console.info = info;
    console.debug = debug;
    setIsInterceptorEnabled(false);
  }, []);
  
  // Track if we've already initialized
  const hasInitializedRef = useRef(false);

  // Enable interceptor on mount if requested, but only once
  useEffect(() => {
    // Skip if already initialized to prevent loops
    if (hasInitializedRef.current) {
      return undefined;
    }

    if (interceptConsoleByDefault) {
      // Mark as initialized
      hasInitializedRef.current = true;
      
      const cleanup = enableConsoleInterceptor();
      return cleanup;
    }
    return undefined;
  }, [interceptConsoleByDefault, enableConsoleInterceptor]);
  
  // The value object that will be provided to consumers
  const value = {
    enableConsoleInterceptor,
    disableConsoleInterceptor,
    isInterceptorEnabled
  };
  
  return (
    <LoggingContext.Provider value={value}>
      {children}
    </LoggingContext.Provider>
  );
};

/**
 * Custom hook for accessing the logging functionality
 * Provides a type-safe interface to the logging context
 */
export const useLogging = () => {
  const context = useContext(LoggingContext);
  
  if (context === null) {
    throw new Error('useLogging must be used within a LoggingProvider');
  }
  
  return context;
};
