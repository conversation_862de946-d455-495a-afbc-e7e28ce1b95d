import React, { createContext, useContext, useState, useEffect } from "react";
import axios, { InternalAxiosRequestConfig } from "axios";

// Extend axios config to include metadata
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  metadata?: {
    startTime?: number;
    loadingType?: string;
    [key: string]: unknown;
  };
}

// Define loading types
export type LoadingType = "generic" | "xero" | "harvest" | "projection";

interface LoadingContextType {
  isLoading: boolean;
  loadingType: LoadingType;
  pendingRequests: Record<LoadingType, number>;
  setLoading: (loading: boolean, type?: LoadingType) => void;
  incrementPendingRequests: (type: LoadingType) => void;
  decrementPendingRequests: (type: LoadingType) => void;
}

const LoadingContext = createContext<LoadingContextType | null>(null);

interface LoadingProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component for global loading state
 * Tracks loading state and pending API requests
 */
export const LoadingProvider: React.FC<LoadingProviderProps> = ({
  children,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingType, setLoadingType] = useState("generic" as LoadingType);
  const [pendingRequests, setPendingRequests] = useState({
    generic: 0,
    xero: 0,
    harvest: 0,
    projection: 0,
  });

  // Direct setter for loading state
  const setLoading = (loading: boolean, type: LoadingType = "generic") => {
    setIsLoading(loading);
    if (loading) {
      setLoadingType(type);
    }
  };

  // Increment pending requests counter
  const incrementPendingRequests = (type: LoadingType) => {
    setPendingRequests((prev: Record<LoadingType, number>) => ({
      ...prev,
      [type]: prev[type] + 1,
    }));
  };

  // Decrement pending requests counter
  const decrementPendingRequests = (type: LoadingType) => {
    setPendingRequests((prev: Record<LoadingType, number>) => ({
      ...prev,
      [type]: Math.max(0, prev[type] - 1),
    }));
  };

  // Update loading state based on pending requests
  useEffect(() => {
    const totalRequests =
      pendingRequests.generic +
      pendingRequests.xero +
      pendingRequests.harvest +
      pendingRequests.projection;

    if (totalRequests > 0) {
      setIsLoading(true);

      // Set the loading type based on which type has the most pending requests
      if (pendingRequests.projection > 0) {
        setLoadingType("projection");
      } else if (pendingRequests.xero > 0) {
        setLoadingType("xero");
      } else if (pendingRequests.harvest > 0) {
        setLoadingType("harvest");
      } else {
        setLoadingType("generic");
      }
    } else {
      setIsLoading(false);
    }
  }, [pendingRequests]);

  // Set up axios interceptors to track API requests
  useEffect(() => {
    // Request interceptor
    const requestInterceptor = axios.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Determine the loading type based on the URL
        if (config.url?.includes("/api/xero/")) {
          incrementPendingRequests("xero");

          // Add a timestamp to the request for debugging
          (config as ExtendedAxiosRequestConfig).metadata = {
            ...(config as ExtendedAxiosRequestConfig).metadata,
            startTime: new Date().getTime(),
            loadingType: "xero",
          };
        } else if (config.url?.includes("/api/harvest/")) {
          incrementPendingRequests("harvest");

          // Add a timestamp to the request for debugging
          (config as ExtendedAxiosRequestConfig).metadata = {
            ...(config as ExtendedAxiosRequestConfig).metadata,
            startTime: new Date().getTime(),
            loadingType: "harvest",
          };
        } else if (config.url?.includes("/api/cashflow/")) {
          incrementPendingRequests("projection");

          // Add a timestamp to the request for debugging
          (config as ExtendedAxiosRequestConfig).metadata = {
            ...(config as ExtendedAxiosRequestConfig).metadata,
            startTime: new Date().getTime(),
            loadingType: "projection",
          };
        } else if (config.url?.includes("/api/")) {
          incrementPendingRequests("generic");

          // Add a timestamp to the request for debugging
          (config as ExtendedAxiosRequestConfig).metadata = {
            ...(config as ExtendedAxiosRequestConfig).metadata,
            startTime: new Date().getTime(),
            loadingType: "generic",
          };
        }
        return config;
      },
      (error) => {
        // If request fails, decrement counter based on URL
        if (error.config?.url?.includes("/api/xero/")) {
          decrementPendingRequests("xero");
        } else if (error.config?.url?.includes("/api/harvest/")) {
          decrementPendingRequests("harvest");
        } else if (error.config?.url?.includes("/api/cashflow/")) {
          decrementPendingRequests("projection");
        } else if (error.config?.url?.includes("/api/")) {
          decrementPendingRequests("generic");
        }
        return Promise.reject(error);
      },
    );

    // Response interceptor
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        // Determine the loading type based on the URL
        if (response.config.url?.includes("/api/xero/")) {
          decrementPendingRequests("xero");

          // Log request duration for debugging
          const startTime = (response.config as ExtendedAxiosRequestConfig)
            .metadata?.startTime;
          if (startTime) {
            const duration = new Date().getTime() - startTime;
            console.log(
              `Xero request to ${response.config.url} took ${duration}ms`,
            );
          }
        } else if (response.config.url?.includes("/api/harvest/")) {
          decrementPendingRequests("harvest");

          // Log request duration for debugging
          const startTime = (response.config as ExtendedAxiosRequestConfig)
            .metadata?.startTime;
          if (startTime) {
            const duration = new Date().getTime() - startTime;
            console.log(
              `Harvest request to ${response.config.url} took ${duration}ms`,
            );
          }
        } else if (response.config.url?.includes("/api/cashflow/")) {
          decrementPendingRequests("projection");

          // Log request duration for debugging
          const startTime = (response.config as ExtendedAxiosRequestConfig)
            .metadata?.startTime;
          if (startTime) {
            const duration = new Date().getTime() - startTime;
            console.log(
              `Projection request to ${response.config.url} took ${duration}ms`,
            );
          }
        } else if (response.config.url?.includes("/api/")) {
          decrementPendingRequests("generic");

          // Log request duration for debugging
          const startTime = (response.config as ExtendedAxiosRequestConfig)
            .metadata?.startTime;
          if (startTime) {
            const duration = new Date().getTime() - startTime;
            console.log(
              `API request to ${response.config.url} took ${duration}ms`,
            );
          }
        }
        return response;
      },
      (error) => {
        // If response fails, decrement counter based on URL
        if (error.config?.url?.includes("/api/xero/")) {
          decrementPendingRequests("xero");
        } else if (error.config?.url?.includes("/api/harvest/")) {
          decrementPendingRequests("harvest");
        } else if (error.config?.url?.includes("/api/cashflow/")) {
          decrementPendingRequests("projection");
        } else if (error.config?.url?.includes("/api/")) {
          decrementPendingRequests("generic");
        }
        return Promise.reject(error);
      },
    );

    // Clean up interceptors on unmount
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  const value = {
    isLoading,
    loadingType,
    pendingRequests,
    setLoading,
    incrementPendingRequests,
    decrementPendingRequests,
  };

  return (
    <LoadingContext.Provider value={value}>{children}</LoadingContext.Provider>
  );
};

/**
 * Custom hook for accessing the loading context
 */
export const useLoading = () => {
  const context = useContext(LoadingContext);

  if (context === null) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }

  return context;
};
