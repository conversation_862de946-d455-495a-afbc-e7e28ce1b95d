import React, {
  createContext,
  useContext,
  useCallback,
  useState,
  useRef,
} from "react";

export type EventCallback = (data?: unknown) => void;
export type LogEventCallback = (log: LogEntry) => void;

export interface LogEntry {
  timestamp: string;
  message: string;
  type: "info" | "warning" | "error" | "debug";
  source: "frontend" | "backend" | "system";
  details?: unknown;
}

export const EVENTS = {
  EXPENSES_UPDATED: "EXPENSES_UPDATED",
  DEV_CONSOLE_TOGGLE: "DEV_CONSOLE_TOGGLE",
  PROJECTION_FILTERED: "PROJECTION_FILTERED",
  DEAL_ESTIMATES_UPDATED: "DEAL_ESTIMATES_UPDATED",
};

interface EventContextType {
  subscribe: (event: string, callback: EventCallback) => void;
  unsubscribe: (event: string, callback: EventCallback) => void;
  publish: (event: string, data?: unknown) => void;
  subscribeToLogs: (callback: LogEventCallback) => void;
  unsubscribeFromLogs: (callback: LogEventCallback) => void;
  addLog: (log: LogEntry) => void;
  getLogs: () => LogEntry[];
  clearLogs: () => void;
}

const EventContext = createContext<EventContextType | null>(null);

interface EventProviderProps {
  children: React.ReactNode;
  maxLogs?: number;
}

/**
 * Provider component for the event system
 * Replaces the global eventBus with a React Context-based approach
 */
export const EventProvider: React.FC<EventProviderProps> = ({
  children,
  maxLogs = 500,
}) => {
  // Using refs to store listeners to avoid unnecessary re-renders
  const listenersRef = useRef<{ [event: string]: EventCallback[] }>({});
  const logListenersRef = useRef<LogEventCallback[]>([]);
  const [logs, setLogs] = useState<LogEntry[]>([]);

  // Expose event system to window for debugging and cross-component communication
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      // Expose EVENTS constants
      (window as any).__EVENTS = EVENTS;

      // Provide publish function for external components
      (window as any).__publishEvent = (event: string, data?: unknown) => {
        console.log(`Publishing event ${event} via window.__publishEvent`);
        publish(event, data);
      };

      // Provide dispatchProjectionEvent helper specifically for projection events
      (window as any).__dispatchProjectionEvent = (data: unknown) => {
        console.log(
          "Dispatching projection event via window.__dispatchProjectionEvent",
        );
        publish(EVENTS.PROJECTION_FILTERED, data);
      };
    }

    return () => {
      // Clean up when unmounted
      if (typeof window !== "undefined") {
        delete (window as any).__EVENTS;
        delete (window as any).__publishEvent;
        delete (window as any).__dispatchProjectionEvent;
      }
    };
  }, []);

  // Subscribe to an event
  const subscribe = useCallback((event: string, callback: EventCallback) => {
    if (!listenersRef.current[event]) {
      listenersRef.current[event] = [];
    }
    listenersRef.current[event].push(callback);
  }, []);

  // Unsubscribe from an event
  const unsubscribe = useCallback((event: string, callback: EventCallback) => {
    if (listenersRef.current[event]) {
      listenersRef.current[event] = listenersRef.current[event].filter(
        (cb) => cb !== callback,
      );
    }
  }, []);

  // Publish an event
  const publish = useCallback((event: string, data?: unknown) => {
    const eventListeners = listenersRef.current[event];
    if (eventListeners) {
      eventListeners.forEach((callback) => callback(data));
    }
  }, []);

  // Subscribe to log events
  const subscribeToLogs = useCallback((callback: LogEventCallback) => {
    logListenersRef.current.push(callback);
  }, []);

  // Unsubscribe from log events
  const unsubscribeFromLogs = useCallback((callback: LogEventCallback) => {
    logListenersRef.current = logListenersRef.current.filter(
      (cb) => cb !== callback,
    );
  }, []);

  // Add a log entry
  const addLog = useCallback(
    (log: LogEntry) => {
      setLogs((prevLogs) => {
        const newLogs = [...prevLogs, log];
        // Trim logs if we have too many
        return newLogs.length > maxLogs ? newLogs.slice(-maxLogs) : newLogs;
      });

      // Notify listeners
      logListenersRef.current.forEach((callback) => callback(log));
    },
    [maxLogs],
  );

  // Get all logs
  const getLogs = useCallback(() => {
    return [...logs];
  }, [logs]);

  // Clear all logs
  const clearLogs = useCallback(() => {
    setLogs([]);
    // Notify with an empty log to trigger a refresh
    logListenersRef.current.forEach((callback) =>
      callback({
        timestamp: new Date().toISOString(),
        message: "Logs cleared",
        type: "info",
        source: "system",
      }),
    );
  }, []);

  // The value object that will be provided to consumers
  const value = {
    subscribe,
    unsubscribe,
    publish,
    subscribeToLogs,
    unsubscribeFromLogs,
    addLog,
    getLogs,
    clearLogs,
  };

  return (
    <EventContext.Provider value={value}>{children}</EventContext.Provider>
  );
};

/**
 * Custom hook for accessing the event system
 * Provides a type-safe interface to the event context
 */
export const useEvents = () => {
  const context = useContext(EventContext);

  if (context === null) {
    throw new Error("useEvents must be used within an EventProvider");
  }

  return context;
};
