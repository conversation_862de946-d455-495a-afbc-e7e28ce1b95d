import React from "react";

interface AdaptiveGridProps {
  children: React.ReactNode;
  minItemWidth?: string;
  gap?: string;
}

/**
 * A layout component that arranges children in a grid that automatically
 * adjusts the number of columns based on the available container width
 * and the minimum item width specified.
 */
export function AdaptiveGrid({
  children,
  minItemWidth = '300px", // Default minimum width for each grid item
  gap = 'var(--space-md)" // Default gap using fluid spacing variable
}: AdaptiveGridProps) {
  return (
    <div
      style={{
        display: "grid",
        // Creates as many columns as fit, each at least minItemWidth wide,
        // distributing extra space equally.
        gridTemplateColumns: `repeat(auto-fill, minmax(${minItemWidth}, 1fr))`,
        gap // Uses the provided gap value
      }}
    >
      {children}
    </div>
  );
}
