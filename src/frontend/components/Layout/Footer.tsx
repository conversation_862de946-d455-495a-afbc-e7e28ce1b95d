import React, { useState, useEffect } from "react";
import { versionHistoryService, type VersionUpdate } from "../../../services/version-history-service";
import { formatDistanceToNow } from "date-fns";
import { Button } from "@/frontend/components/ui/Button";

interface FooterProps {
  onViewVersionHistory?: () => void;
}

export const Footer: React.FC<FooterProps> = ({ onViewVersionHistory }) => {
  const [latestUpdate, setLatestUpdate] = useState<VersionUpdate | null>(null);

  useEffect(() => {
    let mounted = true;
    const loadLatestUpdate = async () => {
      try {
        const latest = await versionHistoryService.getLatestVersion();
        if (mounted) {
          setLatestUpdate(latest);
        }
      } catch (error) {
        console.error('Failed to load latest update:', error);
      }
    };
    loadLatestUpdate();

    return () => {
      mounted = false;
    };
  }, []);

  if (!latestUpdate || !latestUpdate.date || !latestUpdate.version) {
    return (
      <footer className="max-w-7xl mx-auto text-center text-muted dark:text-subtle text-xs pb-20 sm:pb-6">
        <div className="flex flex-col sm:flex-row items-center justify-center space-y-1 sm:space-y-0 sm:space-x-2">
          <p>Upstream &copy; {new Date().getFullYear()}</p>
        </div>
      </footer>);

  }

  const version = latestUpdate.version;
  let timeAgo: string;
  try {
    timeAgo = formatDistanceToNow(new Date(latestUpdate.date), { addSuffix: true });
  } catch (error) {
    timeAgo = 'recently';
  }

  return (
    <footer className="max-w-7xl mx-auto text-center text-muted dark:text-subtle text-xs pb-20 sm:pb-6">
      <div className="flex flex-col sm:flex-row items-center justify-center space-y-1 sm:space-y-0 sm:space-x-2">
        <p>
          Upstream v{version} &copy; {new Date().getFullYear()}
        </p>
        {onViewVersionHistory &&
        <Button variant="ghost"
        onClick={onViewVersionHistory}
        className="underline">

            Updated {timeAgo} - View changelog
          </Button>
        }
      </div>
    </footer>);

};