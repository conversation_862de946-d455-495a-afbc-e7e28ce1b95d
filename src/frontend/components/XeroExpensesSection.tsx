import React, { useEffect, useState } from "react";
import { useAuthStatus } from "../hooks/useAuthStatus";
import { useEvents } from "../contexts";
import { useLoading } from "../contexts/LoadingContext";
import { getXeroExpenseBreakdown, syncWagesExpense, syncSuperExpense, debouncedPublishExpenseUpdated } from "../api/xero";
import { getExpenses } from "../api/expenses";
import { XeroExpenseBreakdown } from "../../api/types/xero";
import XeroExpenseCard from "./XeroExpenseCard";
import { formatDate } from "../utils/format";
import { LoadingIndicator } from "./ForwardProjection";
import { Card } from "./ui";
import XeroBadge from "./shared/XeroBadge";
import { isXeroExpense } from "./Expense/utils";
import { Button } from "@/frontend/components/ui/Button";

const XeroExpensesSection: React.FC = () => {
  const { isAuthenticated, isLoading: isAuthLoading } = useAuthStatus();
  const { setLoading } = useLoading(); // Use global loading context
  const events = useEvents();

  const [expenseBreakdown, setExpenseBreakdown] = useState<XeroExpenseBreakdown | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [error, setError] = useState('');

  // Sync status for payroll expenses
  const [syncingWages, setSyncingWages] = useState(false);
  const [syncingSuper, setSyncingSuper] = useState(false);

  // Load expense breakdown on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadExpenseBreakdown();
    }
  }, [isAuthenticated]);

  const loadExpenseBreakdown = async () => {
    setLocalLoading(true);
    setLoading(true, 'xero'); // Set global loading state with Xero type
    setError('');

    try {
      // Get expense breakdown from Xero
      const data = await getXeroExpenseBreakdown();

      if (Array.isArray(data) && data.length > 0) {
        const breakdown = data[0];

        // Check for existing expenses
        try {
          const existingExpenses = await getExpenses();

          // Check for net pay expense (previously called wages) - only consider expenses that are genuinely from Xero
          const existingWages = existingExpenses.find((exp) =>
          exp.frequency === 'monthly' &&
          exp.type === 'Monthly Payroll' &&
          isXeroExpense(exp) && (// First check if it's from Xero
          exp.source?.includes('xero-wages') || exp.source?.includes('xero-netpay'))
          );

          // Check for superannuation expense - only consider expenses that are genuinely from Xero
          const existingSuper = existingExpenses.find((exp) =>
          exp.frequency === 'monthly' &&
          exp.type === 'Superannuation' &&
          isXeroExpense(exp) && // First check if it's from Xero
          exp.source?.includes('xero-superannuation')
          );

          // Update the breakdown with existing expense info
          breakdown.wages.hasRecurringExpense = !!existingWages;
          breakdown.superannuation.hasRecurringExpense = !!existingSuper;

          console.log('Expense breakdown with existing expense checks:', {
            wages: breakdown.wages.hasRecurringExpense,
            superannuation: breakdown.superannuation.hasRecurringExpense
          });

          // Log more details for debugging
          console.log('Existing expenses found:', {
            wages: existingWages ? { id: existingWages.id, name: existingWages.name, source: existingWages.source } : null,
            super: existingSuper ? { id: existingSuper.id, name: existingSuper.name, source: existingSuper.source } : null
          });
        } catch (expError) {
          console.error('Error checking for existing expenses:', expError);
          // Continue without checking for existing expenses
        }

        setExpenseBreakdown(breakdown);
      } else {
        setExpenseBreakdown(null);
      }
    } catch (err: any) {
      console.error('Error loading Xero expense breakdown:", err);
      setError(err.message || "Failed to load Xero expense data");
    } finally {
      setLocalLoading(false);
      setLoading(false); // Clear global loading state
    }
  };

  const handleRefresh = () => {
    loadExpenseBreakdown();
  };

  const handleSyncWages = async () => {
    if (!expenseBreakdown) return;

    setSyncingWages(true);
    try {
      await syncWagesExpense(expenseBreakdown);

      // Update the local state to show the expense as added
      setExpenseBreakdown((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          wages: {
            ...prev.wages,
            isAdded: true,
            hasRecurringExpense: true
          }
        };
      });

      // Notify the app that expenses have been updated
      debouncedPublishExpenseUpdated(events);
    } catch (err: any) {
      console.error('Error syncing wages expense:", err);
      setError(err.message || "Failed to sync wages expense");
    } finally {
      setSyncingWages(false);
    }
  };

  const handleSyncSuper = async () => {
    if (!expenseBreakdown) return;

    setSyncingSuper(true);
    try {
      await syncSuperExpense(expenseBreakdown);

      // Update the local state to show the expense as added
      setExpenseBreakdown((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          superannuation: {
            ...prev.superannuation,
            isAdded: true,
            hasRecurringExpense: true
          }
        };
      });

      // Notify the app that expenses have been updated
      debouncedPublishExpenseUpdated(events);
    } catch (err: any) {
      console.error('Error syncing superannuation expense:", err);
      setError(err.message || "Failed to sync superannuation expense");
    } finally {
      setSyncingSuper(false);
    }
  };

  if (!isAuthenticated) {
    // Show a different message when authentication is still being checked
    if (isAuthLoading) {
      return (
        <Card className="text-center py-8 bg-primary-light/50/20 border-primary">
          <div className="flex items-center justify-center mb-4">
            <svg className="animate-spin h-8 w-8 text-primary-color mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h3 className="text-lg font-semibold text-primary-color">Checking Authentication Status</h3>
          </div>
          <p className="text-primary-color">
            Please wait while we verify your Xero connection...
          </p>
        </Card>);

    }

    // Show the standard authentication required message
    return (
      <Card className="text-center py-8 bg-warning-light/50/20 border-warning">
        <div className="flex items-center justify-center mb-2">
          <svg className="w-6 h-6 text-warning mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m0 0v2m0-2h2m-2 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold text-warning">Authentication Required</h3>
        </div>
        <p className="text-warning">
          You need to connect to Xero to access payroll data.
        </p>
      </Card>);

  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <h2 className="text-xl font-semibold text-primary">Xero Payroll</h2>
          <XeroBadge className="ml-2" />
        </div>

        <Button variant="primary"
        onClick={handleRefresh}
        disabled={localLoading}>


          {localLoading ?
          <>
              <LoadingIndicator size="small" className="mr-2" />
              Loading...
            </> :

          <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh Data
            </>
          }
        </Button>
      </div>

      {error &&
      <Card className="bg-error-light/80/20 border-error">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-error mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="font-medium text-error">Error loading data</p>
              <p className="text-sm text-error">{error}</p>
            </div>
          </div>
        </Card>
      }

      {localLoading ?
      <Card className="flex items-center justify-center py-12">
          <div className="py-8 text-center text-muted">
            <p>Loading expense data...</p>
          </div>
        </Card> :
      !expenseBreakdown ?
      <Card className="text-center py-8 bg-surface-page/50/50 border-default/70/70">
          <svg className="mx-auto h-12 w-12 text-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-primary">No expense data found</h3>
          <p className="mt-1 text-sm text-muted">
            Please make sure you have processed at least one payroll run in Xero.
          </p>
        </Card> :

      <>
          <div className="xero-info-panel">
            <div className="flex items-center mb-1">
              <svg className="w-5 h-5 mr-2 text-primary-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="font-medium text-primary">Latest Payroll Information</h3>
            </div>
            <p className="text-sm text-primary-color ml-7">
              Based on payroll from {formatDate(expenseBreakdown.paymentDate)}
              ({expenseBreakdown.employeeCount} {expenseBreakdown.employeeCount === 1 ? 'employee' : "employees"})
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Net Pay Card */}
            <XeroExpenseCard
            title="Net Pay"
            amount={expenseBreakdown.wages.amount}
            date={expenseBreakdown.wages.paymentDate}
            isAdded={expenseBreakdown.wages.isAdded || false}
            hasRecurringExpense={expenseBreakdown.wages.hasRecurringExpense || false}
            onSync={handleSyncWages}
            isLoading={syncingWages}
            description="Monthly net pay expense based on your latest payroll"
            type="net-pay" />


            {/* Super Card */}
            <XeroExpenseCard
            title="Superannuation"
            amount={expenseBreakdown.superannuation.amount}
            date={expenseBreakdown.superannuation.paymentDate}
            isAdded={expenseBreakdown.superannuation.isAdded || false}
            hasRecurringExpense={expenseBreakdown.superannuation.hasRecurringExpense || false}
            onSync={handleSyncSuper}
            isLoading={syncingSuper}
            description="Monthly superannuation payment"
            type="super" />

          </div>
        </>
      }
    </div>);

};

export default XeroExpensesSection;