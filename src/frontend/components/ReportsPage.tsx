import React, { useState } from "react";
import UtilizationReport from "./Reports/UtilizationReport";
import XeroReportsPage from "./Reports/XeroReportsPage";
import { LoadingProvider } from "../contexts/LoadingContext";

/**
 * Reports page component that will display various reports
 */import { Button } from "@/frontend/components/ui/Button";
const ReportsPage: React.FC = () => {
  // State for active sub-tab (can be expanded later)
  const [activeSubTab, setActiveSubTab] = useState<"utilization" |"harvest" |"xero">("utilization");

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-primary dark:text-primary">
          Reports
        </h1>
      </div>

      {/* Sub-tabs for different report types */}
      <div className="border-b border-default dark:border-default">
        <nav className="flex space-x-8" aria-label="Report Types">
          <Button variant="secondary"
          onClick={() => setActiveSubTab("utilization")}
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
          activeSubTab ==="utilization" ?"border-secondary text-secondary dark:border-secondary-light dark:text-secondary-light" :"border-transparent text-muted hover:text-primary hover:border-strong dark:text-subtle dark:hover:text-subtle dark:hover:border-strong"}`
          }>

            Staff Utilisation
          </Button>
          <Button variant="secondary"
          onClick={() => setActiveSubTab("harvest")}
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
          activeSubTab ==="harvest" ?"border-secondary text-secondary dark:border-secondary-light dark:text-secondary-light" :"border-transparent text-muted hover:text-primary hover:border-strong dark:text-subtle dark:hover:text-subtle dark:hover:border-strong"}`
          }>

            Harvest Reports
          </Button>
          <Button variant="secondary"
          onClick={() => setActiveSubTab("xero")}
          className={`py-4 px-1 border-b-2 font-medium text-sm ${
          activeSubTab ==="xero" ?"border-secondary text-secondary dark:border-secondary-light dark:text-secondary-light" :"border-transparent text-muted hover:text-primary hover:border-strong dark:text-subtle dark:hover:text-subtle dark:hover:border-strong"}`
          }>

            Xero Reports
          </Button>
        </nav>
      </div>

      {/* Content area for reports */}
      <div className="bg-surface-card dark:bg-surface-card shadow rounded-lg p-6">
        {activeSubTab ==="utilization" ?
        <UtilizationReport /> :
        activeSubTab ==="harvest" ?
        <div>
            <h2 className="text-lg font-medium text-primary dark:text-primary mb-4">
              Harvest Reports
            </h2>
            <p className="text-secondary dark:text-subtle">
              This section will display reports from Harvest including time
              tracking, uninvoiced work, and expense reports.
            </p>
          </div> :

        <XeroReportsPage />
        }
      </div>
    </div>);

};

export default ReportsPage;