import React, { useState, useEffect, useCallback } from "react";
import { CustomExpense } from "../../types";
import {
  getExpenses,
  createExpense,
  updateExpense,
  deleteExpense } from "../api/expenses";
import { format } from "date-fns";
import { useEvents, EVENTS } from "../contexts";
import { LoadingIndicator } from "./ForwardProjection";
import XeroBillsSection from "./XeroBillsSection";
import XeroExpensesSection from "./XeroExpensesSection";
import XeroActivityStatementsSection from "./XeroActivityStatementsSection";
import { ExpenseSummary, ExpenseList } from "./Expense";

/**
 * Component for managing custom expenses
 */import { Button } from "@/frontend/components/ui/Button";
export const CustomExpensesTab: React.FC = () => {
  const events = useEvents();
  const [expenses, setExpenses] = useState<CustomExpense[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<{
    name?: string;
    amount?: string;
    date?: string;
    general?: string;
  }>({});

  // Form state
  const [editingId, setEditingId] = useState<string | null>(null);
  const [name, setName] = useState<string>("");
  const [type, setType] = useState<CustomExpense["type"]>("Other");
  const [amount, setAmount] = useState<string>("");
  const [date, setDate] = useState<string>(format(new Date(),"yyyy-MM-dd"));
  const [frequency, setFrequency] =
  useState<CustomExpense["frequency"]>("monthly");
  const [repeatCount, setRepeatCount] = useState<number | undefined>(undefined);
  const [isAddingNewRow, setIsAddingNewRow] = useState<boolean>(false);

  // Loading states for individual actions
  const [savingId, setSavingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Tab state for switching between custom expenses and different Xero integrations
  const [activeSubTab, setActiveSubTab] = useState<"expenses" |"xero-bills" |"xero-payroll" |"xero-activity-statements">("expenses");

  // Handle tab change
  const handleTabChange = (
  tab:"expenses" |"xero-bills" |"xero-payroll" |"xero-activity-statements") =>
  {
    setActiveSubTab(tab);
    // Refresh data when switching to expenses tab to ensure we have the latest data
    if (tab ==="expenses") {
      loadExpenses();
    }
  };

  // Load expenses on component mount
  useEffect(() => {
    loadExpenses();
  }, []);

  /**
   * Load expenses from API
   */
  const loadExpenses = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await getExpenses();

      // Sort by date (soonest first)
      data.sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      setExpenses(data);
    } catch (error: any) {
      setError(error.message ||"Failed to load expenses");
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset form fields
   */
  const resetForm = () => {
    setEditingId(null);
    setName("");
    setType("Other");
    setAmount("");
    setDate(format(new Date(),"yyyy-MM-dd"));
    setFrequency("monthly");
    setRepeatCount(undefined);
    setIsAddingNewRow(false);
    setFieldErrors({});
    setError(null);
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    // Reset previous errors
    setError(null);
    setFieldErrors({});

    // Validate form - collect all errors
    const newFieldErrors: {
      name?: string;
      amount?: string;
      date?: string;
      general?: string;
    } = {};

    let hasErrors = false;

    if (!name) {
      newFieldErrors.name ="Name is required";
      hasErrors = true;
    }

    if (!amount) {
      newFieldErrors.amount ="Amount is required";
      hasErrors = true;
    } else {
      const parsedAmount = parseFloat(amount);
      if (isNaN(parsedAmount) || parsedAmount <= 0) {
        newFieldErrors.amount ="Amount must be a positive number";
        hasErrors = true;
      }
    }

    if (!date) {
      newFieldErrors.date ="Date is required";
      hasErrors = true;
    } else {
      // Validate date is forward-looking
      const parsedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day

      if (parsedDate < today) {
        newFieldErrors.date ="Date must be today or later";
        hasErrors = true;
      }
    }

    // If there are any errors, set them and return
    if (hasErrors) {
      setFieldErrors(newFieldErrors);
      setError("Please fix the errors in the form");
      return;
    }

    const parsedAmount = parseFloat(amount);
    const parsedDate = new Date(date);

    // Set loading states
    setLoading(true);
    setError(null);

    // Set saving state for specific row
    if (editingId) {
      setSavingId(editingId);
    } else if (isAddingNewRow) {
      setSavingId("new");
    }

    try {
      // Only include repeatCount if it's a recurring expense and has a value
      const expenseData = {
        name,
        type,
        amount: parsedAmount,
        date: parsedDate,
        frequency,
        ...(frequency !=="one-off" && repeatCount ? { repeatCount } : {})
      };

      if (editingId) {
        // Update existing expense
        await updateExpense(editingId, expenseData);
      } else {
        // Create new expense
        await createExpense(expenseData);
      }

      // Reload expenses
      await loadExpenses();

      // Reset form
      resetForm();

      // Notify other components that expenses have been updated
      events.publish(EVENTS.EXPENSES_UPDATED);
    } catch (error: any) {
      // Set general error
      const errorMessage = error.message ||"Failed to save expense";
      setError(errorMessage);
      setFieldErrors((prev) => ({ ...prev, general: errorMessage }));
    } finally {
      setLoading(false);
      setSavingId(null);
    }
  };

  /**
   * Handle delete expense
   */
  const handleDelete = async (id: string) => {
    if (!window.confirm("Are you sure you want to delete this expense?")) {
      return;
    }

    setLoading(true);
    setError(null);
    setDeletingId(id);

    try {
      await deleteExpense(id);

      // Reload expenses
      await loadExpenses();

      // Reset form if we were editing the deleted expense
      if (editingId === id) {
        resetForm();
      }

      // Notify other components that expenses have been updated
      events.publish(EVENTS.EXPENSES_UPDATED);
    } catch (error: any) {
      setError(error.message ||"Failed to delete expense");
    } finally {
      setLoading(false);
      setDeletingId(null);
    }
  };

  /**
   * Handle edit expense
   */
  const handleEdit = (expense: CustomExpense) => {
    setEditingId(expense.id);
    setName(expense.name);
    setType(expense.type);
    setAmount(expense.amount.toString());
    setDate(format(new Date(expense.date),"yyyy-MM-dd"));
    setFrequency(expense.frequency);
    setRepeatCount(expense.repeatCount);
  };

  // Prepare form state and handlers for child components
  const formState = { name, type, amount, date, frequency, repeatCount };
  const formHandlers = {
    setName,
    setType,
    setAmount,
    setDate,
    setFrequency,
    setRepeatCount
  };

  return (
    <div>
      {error &&
      <div className="bg-error-light border border-error text-accent p-4 rounded-lg mb-6 animate-fadeIn">
          <div className="flex items-center">
            <svg
            className="w-5 h-5 mr-2 text-accent"
            fill="currentColor"
            viewBox="0 0 20 20">

              <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd" />

            </svg>
            <p>{error}</p>
          </div>
        </div>
      }

      {/* Sub-tabs navigation */}
      <div className="mb-6 border-b border-default overflow-x-auto">
        <nav className="flex flex-nowrap -mb-px space-x-1" aria-label="Tabs">
          <Button variant="secondary"
          onClick={() => handleTabChange("expenses")}
          className={`py-3 px-4 font-medium text-sm whitespace-nowrap flex items-center border-b-2 transition-colors ${
          activeSubTab ==="expenses" ?"border-secondary text-secondary" :"border-transparent text-muted hover:text-primary dark:hover:text-muted hover:border-strong dark:hover:border-strong"}`
          }
          aria-current={activeSubTab ==="expenses" ?"page" : undefined}>

            <svg
              className="w-4 h-4 mr-2"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />

            </svg>
            All Expenses
          </Button>
          <Button variant="secondary"
          onClick={() => handleTabChange("xero-bills")}
          className={`py-3 px-4 font-medium text-sm whitespace-nowrap flex items-center border-b-2 transition-colors ${
          activeSubTab ==="xero-bills" ?"border-secondary text-secondary" :"border-transparent text-muted hover:text-primary dark:hover:text-muted hover:border-strong dark:hover:border-strong"}`
          }
          aria-current={activeSubTab ==="xero-bills" ?"page" : undefined}>

            <svg
              className="w-4 h-4 mr-2"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />

            </svg>
            <span className="flex items-center">Xero Bills</span>
          </Button>
          <Button variant="secondary"
          onClick={() => handleTabChange("xero-payroll")}
          className={`py-3 px-4 font-medium text-sm whitespace-nowrap flex items-center border-b-2 transition-colors ${
          activeSubTab ==="xero-payroll" ?"border-secondary text-secondary" :"border-transparent text-muted hover:text-primary dark:hover:text-muted hover:border-strong dark:hover:border-strong"}`
          }
          aria-current={activeSubTab ==="xero-payroll" ?"page" : undefined}>

            <svg
              className="w-4 h-4 mr-2"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />

            </svg>
            <span className="flex items-center">Xero Payroll</span>
          </Button>
          <Button variant="secondary"
          onClick={() => handleTabChange("xero-activity-statements")}
          className={`py-3 px-4 font-medium text-sm whitespace-nowrap flex items-center border-b-2 transition-colors ${
          activeSubTab ==="xero-activity-statements" ?"border-secondary text-secondary" :"border-transparent text-muted hover:text-primary dark:hover:text-muted hover:border-strong dark:hover:border-strong"}`
          }
          aria-current={
          activeSubTab ==="xero-activity-statements" ?"page" : undefined
          }>

            <svg
              className="w-4 h-4 mr-2"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />

            </svg>
            <span className="flex items-center">Xero Tax</span>
          </Button>
        </nav>
      </div>

      {/* Content based on active sub-tab */}
      {activeSubTab ==="expenses" ?
      <>
          <div className="w-full px-2 md:px-4 mb-6">
            <h2 className="text-lg font-semibold text-primary mb-3">
              Expense Summary
            </h2>
            <ExpenseSummary expenses={expenses} />
          </div>

          <div className="w-full px-2 md:px-4">
            <ExpenseList
            expenses={expenses}
            loading={loading}
            onEdit={handleEdit}
            onDelete={handleDelete}
            isAddingNewRow={isAddingNewRow}
            editingId={editingId}
            setIsAddingNewRow={setIsAddingNewRow}
            formState={formState}
            formHandlers={formHandlers}
            resetForm={resetForm}
            handleSubmit={handleSubmit}
            savingId={savingId}
            deletingId={deletingId}
            fieldErrors={fieldErrors} />

          </div>
        </> :
      activeSubTab ==="xero-bills" ?
      <XeroBillsSection /> :
      activeSubTab ==="xero-payroll" ?
      <XeroExpensesSection /> :

      <XeroActivityStatementsSection />
      }
    </div>);

};

// Export is already handled with named export above