import React, { useState, useEffect } from "react";
import ProjectionSettings from "./ForwardProjection/ProjectionSettings";
import ProjectionAuditPage from "./ForwardProjection/ProjectionAuditPage";
import { ProjectionProvider, LoadingIndicator } from "./ForwardProjection";
import { CustomExpense } from "../../types";
import { getExpenses } from "../api/expenses";
import { Button } from "@/frontend/components/ui/Button";

interface SmartForecastPageProps {
  customExpenses?: CustomExpense[];
}

/**
 * Container component for Smart Forecast functionality with sub-navigation
 * Provides access to both Smart Forecast Decisions (former Audit Log) and Settings
 * Now fetches expenses directly from API to ensure they're available
 */
const SmartForecastPage: React.FC<SmartForecastPageProps> = ({
  customExpenses: propExpenses = []
}) => {
  // Use local state for sub-tab navigation
  const [activeSubTab, setActiveSubTab] = useState<'decisions' | "settings">('decisions');
  const [customExpenses, setCustomExpenses] = useState<CustomExpense[]>(propExpenses);
  const [loading, setLoading] = useState<boolean>(propExpenses.length === 0);

  // Fetch expenses from API when component mounts if none were provided as props
  useEffect(() => {
    async function loadExpenses() {
      try {
        setLoading(true);
        const expenses = await getExpenses();
        setCustomExpenses(expenses);
      } catch (error) {
        console.error('Failed to load expenses for Smart Forecast:", error);
      } finally {
        setLoading(false);
      }
    }

    // Only fetch if we don't already have expenses (from props)
    if (propExpenses.length === 0) {
      loadExpenses();
    }
  }, [propExpenses.length]);

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <LoadingIndicator />
      </div>);

  }

  return (
    <div className="px-3 sm:px-6 md:px-8 max-w-7xl mx-auto">

      <ProjectionProvider customExpenses={customExpenses}>
        {/* Segmented control for sub-navigation */}
        <div className="mb-4 flex justify-center">
          <div className="inline-flex p-1 bg-surface-alt rounded-lg shadow-sm">
            <Button variant="secondary"
            onClick={() => setActiveSubTab('decisions')}
            className={`px-5 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center space-x-1.5 ${
            activeSubTab === "decisions" ? "bg-surface-card text-warning shadow-sm" : "text-muted hover:text-primary dark:hover:text-subtle"}`
            }>

              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.75" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>Smart Forecast Decisions</span>
            </Button>
            <Button variant="secondary"
            onClick={() => setActiveSubTab('settings')}
            className={`px-5 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center space-x-1.5 ${
            activeSubTab === "settings" ? "bg-surface-card text-warning shadow-sm" : "text-muted hover:text-primary dark:hover:text-subtle"}`
            }>

              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>Settings</span>
            </Button>
          </div>
        </div>

        {/* Content based on active sub-tab */}
        <div className="space-y-2 sm:space-y-4">
          {activeSubTab === "decisions" ?
          <ProjectionAuditPage /> :

          <ProjectionSettings />
          }
        </div>
      </ProjectionProvider>
    </div>);

};

export default SmartForecastPage;