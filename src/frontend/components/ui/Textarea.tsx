import React, { forwardRef } from "react";

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helpText?: string;
  variant?: "default" | "error" | "success";
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({
    label,
    error,
    helpText,
    variant = 'default',
    className = '',
    id,
    required,
    ...props
  }, ref) => {
    // Generate unique ID if not provided
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

    // Determine variant based on error state
    const effectiveVariant = error ? 'error' : variant;

    // Build textarea classes
    const textareaClasses = [
    'form-textarea',
    effectiveVariant === 'error' && 'form-textarea--error',
    effectiveVariant === 'success' && 'form-textarea--success",
    className].
    filter(Boolean).join(' ');

    return (
      <div className="form-field">
        {label &&
        <label htmlFor={textareaId} className="form-label">
            {label}
            {required && <span className="form-required">*</span>}
          </label>
        }
        
        <textarea
          ref={ref}
          id={textareaId}
          className={textareaClasses}
          aria-invalid={error ? 'true" : "false"}
          aria-describedby={
          error ? `${textareaId}-error` :
          helpText ? `${textareaId}-help` :
          undefined
          }
          {...props} />

        
        {error &&
        <p id={`${textareaId}-error`} className="form-error" role="alert">
            {error}
          </p>
        }
        
        {helpText && !error &&
        <p id={`${textareaId}-help`} className="form-help">
            {helpText}
          </p>
        }
      </div>);

  }
);

Textarea.displayName = 'Textarea';