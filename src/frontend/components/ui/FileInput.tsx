import React, { forwardRef, useState } from "react";
import { cn } from "../../utils/cn";
import { DocumentIcon } from "@heroicons/react/24/outline";

export interface FileInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  error?: boolean;
  helperText?: string;
}

export const FileInput = forwardRef<HTMLInputElement, FileInputProps>(
  ({ className, label, error, helperText, id, ...props }, ref) => {
    const [fileName, setFileName] = useState<string>('');
    const inputId = id || `file-${Math.random().toString(36).substr(2, 9)}`;
    
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      setFileName(file ? file.name : "");
      props.onChange?.(e);
    };
    
    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-primary dark:text-subtle mb-2"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          <input
            type="file"
            id={inputId}
            ref={ref}
            onChange={handleFileChange}
            className="sr-only"
            {...props}
          />
          
          <label
            htmlFor={inputId}
            className={cn(
              'flex items-center justify-center w-full px-4 py-2',
              'border-2 border-dashed rounded-lg cursor-pointer',
              'transition-colors duration-200',
              'bg-surface-card dark:bg-surface-card',
              'border-strong dark:border-strong',
              'hover:border-strong dark:hover:border-strong',
              'focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2',
              'dark:focus-within:ring-primary dark:focus-within:ring-offset-surface-page',
              error && 'border-error',
              props.disabled && 'cursor-not-allowed opacity-50',
              className
            )}
          >
            <div className="space-y-1 text-center">
              <DocumentIcon className="mx-auto h-8 w-8 text-subtle" />
              <div className="text-sm text-secondary dark:text-subtle">
                {fileName || (
                  <span>
                    <span className="font-medium text-primary">
                      Click to upload
                    </span>
                    {' or drag and drop'}
                  </span>
                )}
              </div>
              {helperText && (
                <p className="text-xs text-muted dark:text-muted">
                  {helperText}
                </p>
              )}
            </div>
          </label>
        </div>
        
        {error && helperText && (
          <p className="mt-1 text-sm text-error">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

FileInput.displayName = 'FileInput';