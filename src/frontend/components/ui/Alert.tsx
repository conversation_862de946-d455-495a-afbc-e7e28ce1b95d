import React from "react";

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'info' | "success" | "warning" | 'error';
  icon?: React.ReactNode;
  title?: string;
}

export const Alert: React.FC<AlertProps> = ({ 
  variant = 'info',
  icon,
  title,
  className = '', 
  children, 
  ...props 
}) => {
  // Map to semantic alert classes from foundation.css
  const variantClasses = {
    info: 'alert alert-info',
    success: 'alert alert-success',
    warning: 'alert alert-warning',
    error: 'alert alert-error',
  };
  
  const classes = [
    variantClasses[variant],
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={classes} {...props}>
      <div className="flex">
        {icon && (
          <div className="alert-icon h-5 w-5 flex-shrink-0">
            {icon}
          </div>
        )}
        <div className={icon ? 'ml-3' : ''}>
          {title && (
            <h3 className="alert-title text-sm font-medium">
              {title}
            </h3>
          )}
          {children && (
            <div className={`alert-body ${title ? 'mt-1' : ''} text-sm`}>
              {children}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};