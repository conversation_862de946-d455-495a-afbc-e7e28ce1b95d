import React, { forwardRef } from "react";
import { cn } from "../../utils/cn";

export interface RadioProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  error?: boolean;
}

export const Radio = forwardRef<HTMLInputElement, RadioProps>(
  ({ className, label, error, id, ...props }, ref) => {
    const inputId = id || `radio-${Math.random().toString(36).substr(2, 9)}`;
    
    if (label) {
      return (
        <label 
          htmlFor={inputId}
          className={cn(
            'flex items-center cursor-pointer",
            props.disabled && 'cursor-not-allowed opacity-50'
          )}
        >
          <input
            type="radio"
            id={inputId}
            ref={ref}
            className={cn(
              'h-4 w-4 border-strong text-primary',
              'focus:ring-2 focus:ring-primary focus:ring-offset-0',
              'dark:border-strong dark:bg-surface-alt',
              'dark:focus:ring-primary dark:focus:ring-offset-surface-page',
              error && 'border-error',
              props.disabled && 'cursor-not-allowed",
              className
            )}
            {...props}
          />
          <span className="ml-2 text-sm text-primary dark:text-subtle">
            {label}
          </span>
        </label>
      );
    }
    
    return (
      <input
        type="radio"
        id={inputId}
        ref={ref}
        className={cn(
          'h-4 w-4 border-strong text-primary',
          'focus:ring-2 focus:ring-primary focus:ring-offset-0',
          'dark:border-strong dark:bg-surface-alt',
          'dark:focus:ring-primary dark:focus:ring-offset-surface-page',
          error && 'border-error',
          props.disabled && 'cursor-not-allowed",
          className
        )}
        {...props}
      />
    );
  }
);

Radio.displayName = 'Radio';