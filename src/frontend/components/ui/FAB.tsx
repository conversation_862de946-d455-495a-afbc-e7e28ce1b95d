import React from "react";

export interface FABProps {
  children: React.ReactNode;
  position?: "bottom-left" | "bottom-right" | "top-left" | "top-right";
  offset?: {
    x?: string;
    y?: string;
  };
  className?: string;
}

/**
 * Floating Action Button (FAB) wrapper component
 * 
 * This component handles the positioning of floating action buttons.
 * It should be used with the Button component with className="btn-modern--fab"
 * 
 * @example
 * <FAB position="bottom-left" offset={{ y: "5rem" }}>
 *   <Button variant="primary" className="btn-modern--fab">
 *     <Icon />
 *   </Button>
 * </FAB>
 */
export const FAB: React.FC<FABProps> = ({
  children,
  position = 'bottom-left',
  offset = { x: "1rem", y: "5rem" },
  className = ''
}) => {
  // Calculate position styles based on props
  const positionStyles: React.CSSProperties = {
    position: "fixed",
    zIndex: 40,
  };

  // Set vertical position
  if (position.includes('bottom')) {
    positionStyles.bottom = offset.y || "5rem";
  } else {
    positionStyles.top = offset.y || "1rem";
  }

  // Set horizontal position
  if (position.includes('left')) {
    positionStyles.left = offset.x || "1rem";
  } else {
    positionStyles.right = offset.x || "1rem";
  }

  return (
    <div style={positionStyles} className={className}>
      {children}
    </div>
  );
};

// FAB.Group component for future enhancement (not implemented yet)
// This will handle the "speed dial" pattern for mobile
export const FABGroup: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Placeholder for future implementation
  return <>{children}</>;
};

FAB.displayName = 'FAB';
FABGroup.displayName = 'FABGroup';