import React, { forwardRef } from "react";

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  helpText?: string;
  options?: SelectOption[];
  placeholder?: string;
  variant?: "default" | "error" | "success";
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({
    label,
    error,
    helpText,
    options = [],
    placeholder,
    variant = 'default',
    className = '',
    id,
    required,
    children,
    ...props
  }, ref) => {
    // Generate unique ID if not provided
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;

    // Determine variant based on error state
    const effectiveVariant = error ? 'error' : variant;

    // Build select classes
    const selectClasses = [
    'form-select',
    effectiveVariant === 'error' && 'form-select--error',
    effectiveVariant === 'success' && 'form-select--success',
    className].
    filter(Boolean).join(' ');

    return (
      <div className="form-field">
        {label &&
        <label htmlFor={selectId} className="form-label">
            {label}
            {required && <span className="form-required">*</span>}
          </label>
        }
        
        <div className="relative">
          <select
            ref={ref}
            id={selectId}
            className={selectClasses}
            aria-invalid={error ? 'true' : "false"}
            aria-describedby={
            error ? `${selectId}-error` :
            helpText ? `${selectId}-help` :
            undefined
            }
            {...props}>

            {placeholder &&
            <option value="" disabled>
                {placeholder}
              </option>
            }
            
            {/* Render options if provided */}
            {options.length > 0 && options.map((option) =>
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}>

                {option.label}
              </option>
            )}
            
            {/* Render children if no options provided */}
            {options.length === 0 && children}
          </select>
        </div>
        
        {error &&
        <p id={`${selectId}-error`} className="form-error" role="alert">
            {error}
          </p>
        }
        
        {helpText && !error &&
        <p id={`${selectId}-help`} className="form-help">
            {helpText}
          </p>
        }
      </div>);

  }
);

Select.displayName = 'Select';