import React from "react";

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: "primary" | "secondary" | "success" | "warning" | "error" | "info";
}

export const Badge: React.FC<BadgeProps> = ({
  variant = "primary",
  className = "",
  children,
  ...props
}) => {
  // Map to existing badge classes from foundation.css
  const variantClasses = {
    primary: "badge badge-primary",
    secondary: "badge badge-secondary",
    success: "badge badge-success",
    warning: "badge badge-warning",
    error: "badge badge-error",
    info: "badge badge-info",
  };

  const classes = [variantClasses[variant], className]
    .filter(Boolean)
    .join(" ");

  return (
    <span className={classes} {...props}>
      {children}
    </span>
  );
};
