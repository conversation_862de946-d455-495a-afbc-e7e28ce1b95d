import React from "react";

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'hover' | 'info' | "success" | "warning" | 'error';
  hover?: boolean;
}

export const Card: React.FC<CardProps> = ({ 
  variant = 'default',
  hover = false,
  className = '', 
  children, 
  ...props 
}) => {
  // Map to existing card classes
  const baseClass = 'card';
  const variantClasses = {
    default: '',
    hover: 'card-hover',
    info: 'card-info',
    success: 'card-success',
    warning: 'card-warning',
    error: 'card-error',
  };
  
  const classes = [
    baseClass,
    variantClasses[variant],
    hover && variant === 'default' ? 'card-hover' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};