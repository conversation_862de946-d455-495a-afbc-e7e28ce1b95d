import React from "react";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "success" | "danger" | "warning" | "ghost" | "outline" | "harvest" | "xero" | "tab" | "toggle";
  size?: "default" | "sm" | "lg";
  isActive?: boolean; // For tab and toggle variants
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'default', isActive = false, className = '', children, ...props }, ref) => {
    // Map variants to existing CSS classes
    const variantClasses = {
      primary: "btn-modern btn-modern--primary",
      secondary: "btn-modern btn-modern--secondary",
      success: "btn-modern btn-modern--success",
      danger: "btn-modern btn-modern--danger",
      warning: "btn-modern btn-modern--warning",
      ghost: "btn-modern btn-modern--ghost",
      outline: "btn-modern btn-modern--outline",
      harvest: "btn-modern btn-modern--harvest",
      xero: "btn-modern btn-modern--xero",
      tab: isActive ? 'btn-modern btn-modern--tab-active' : "btn-modern btn-modern--tab",
      toggle: isActive ? 'btn-modern btn-modern--toggle-active' : "btn-modern btn-modern--toggle"
    };

    // Map sizes to existing CSS classes
    const sizeClasses = {
      default: "",
      sm: "btn-modern--sm",
      lg: "btn-modern--lg"
    };

    // Combine classes
    const classes = [
    variantClasses[variant],
    sizeClasses[size],
    className].
    filter(Boolean).join(' ');

    return (
      <button
        ref={ref}
        className={classes}
        {...props}>

        {children}
      </button>);

  }
);

Button.displayName = 'Button';