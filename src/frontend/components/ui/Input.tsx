import React, { forwardRef } from "react";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helpText?: string;
  icon?: React.ReactNode;
  variant?: "default" | "error" | "success";
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({
    label,
    error,
    helpText,
    icon,
    variant = 'default',
    className = '',
    id,
    required,
    ...props
  }, ref) => {
    // Generate unique ID if not provided
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    // Determine variant based on error state
    const effectiveVariant = error ? 'error' : variant;

    // Build input classes
    const inputClasses = [
    'form-input',
    effectiveVariant === 'error' && 'form-input--error',
    effectiveVariant === 'success' && 'form-input--success',
    icon && 'pl-10',
    className].
    filter(Boolean).join(' ');

    return (
      <div className="form-field">
        {label &&
        <label htmlFor={inputId} className="form-label">
            {label}
            {required && <span className="form-required">*</span>}
          </label>
        }
        
        <div className="relative">
          {icon &&
          <div className="form-input-icon">
              {icon}
            </div>
          }
          
          <input
            ref={ref}
            id={inputId}
            className={inputClasses}
            aria-invalid={error ? 'true' : "false"}
            aria-describedby={
            error ? `${inputId}-error` :
            helpText ? `${inputId}-help` :
            undefined
            }
            {...props} />

          
          {error &&
          <div className="form-error-icon">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          }
        </div>
        
        {error &&
        <p id={`${inputId}-error`} className="form-error" role="alert">
            {error}
          </p>
        }
        
        {helpText && !error &&
        <p id={`${inputId}-help`} className="form-help">
            {helpText}
          </p>
        }
      </div>);

  }
);

Input.displayName = 'Input';