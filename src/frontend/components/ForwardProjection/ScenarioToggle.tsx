import React, { useCallback } from "react";
import { Button } from "@/frontend/components/ui/Button";

interface ScenarioToggleProps {
  showScenarios: boolean;
  onToggle: () => void;
}

const ScenarioToggle: React.FC<ScenarioToggleProps> = ({
  showScenarios,
  onToggle
}) => {
  // Wrap the onToggle callback to ensure we don"t miss any clicks
  const handleToggle = useCallback(() => {
    console.log("Scenario toggle clicked, current state:", showScenarios);
    // Add a small delay to ensure the state update is processed
    setTimeout(() => {
      onToggle();
    }, 0);
  }, [onToggle, showScenarios]);

  return (
    <div className="flex items-center space-x-2">
      <span className="hidden sm:inline text-xs sm:text-sm text-secondary dark:text-subtle whitespace-nowrap">
        Deal scenarios:
      </span>
      <Button variant="secondary"
      onClick={handleToggle}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
      showScenarios ?"bg-primary" :"bg-surface-muted"}`
      }
      title="Toggle deal scenarios">

        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-surface-card transition-transform ${
          showScenarios ?"translate-x-6" :"translate-x-1"}`
          } />

      </Button>
    </div>);

};

export default ScenarioToggle;