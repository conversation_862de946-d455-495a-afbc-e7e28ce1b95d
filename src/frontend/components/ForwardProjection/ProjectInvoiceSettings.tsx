import React, { useState, useEffect, useMemo } from "react";
import { formatCurrency } from "./utils";
import { Project, ProjectSetting } from "../../api/harvest";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Checkbox } from "@/frontend/components/ui/Checkbox";

interface ProjectSettingsProps {
  projects: Project[];
  settings: ProjectSetting[];
  onSettingsChange: (settings: ProjectSetting[]) => void;
}

export interface ProjectSettingsFilters {
  searchText: string;
  clients: string[];
  frequencies: string[];
  statuses: string[];
  budgetRange: [number | null, number | null];
}

const frequencyOptions = [
{ value: "weekly", label: "Weekly" },
{ value: "biweekly", label: "Every 2 Weeks" },
{ value: "monthly", label: "Monthly" },
{ value: "custom", label: "Custom" }];


const paymentTermsOptions = [
{ value: 7, label: "Net 7" },
{ value: 14, label: "Net 14" },
{ value: 20, label: "Net 20" },
{ value: 30, label: "Net 30" }];


const ProjectInvoiceSettings: React.FC<ProjectSettingsProps> = ({
  projects,
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<ProjectSetting[]>(settings);
  const [filters, setFilters] = useState<ProjectSettingsFilters>({
    searchText: "",
    clients: [],
    frequencies: [],
    statuses: ['Active'], // Default to showing only active projects
    budgetRange: [null, null]
  });

  // Check for projects without start or end dates
  const projectsWithoutDates = useMemo(() => {
    return projects.filter((project) => !project.startDate || !project.endDate);
  }, [projects]);

  // State for filter accordion
  const [filtersExpanded, setFiltersExpanded] = useState<boolean>(false);

  // Toggle filters visibility
  const toggleFilters = () => {
    setFiltersExpanded(!filtersExpanded);
  };

  // Update local settings when props change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // Extract unique client names and project statuses for filter options
  const clientNames = useMemo(() => {
    const clientsSet = new Set<string>();
    projects.forEach((project) => {
      clientsSet.add(project.client);
    });
    return Array.from(clientsSet);
  }, [projects]);

  // Extract unique project statuses
  const projectStatuses = useMemo(() => {
    const statusesSet = new Set<string>();
    projects.forEach((project) => {
      if (project.status) {
        statusesSet.add(project.status);
      }
    });
    return Array.from(statusesSet);
  }, [projects]);

  // Filter projects based on current filters
  const filteredProjects = useMemo(() => {
    return projects.filter((project) => {
      // Filter by search text (project name or client name)
      if (filters.searchText &&
      !project.name.toLowerCase().includes(filters.searchText.toLowerCase()) &&
      !project.client.toLowerCase().includes(filters.searchText.toLowerCase())) {
        return false;
      }

      // Filter by client
      if (filters.clients.length > 0 && !filters.clients.includes(project.client)) {
        return false;
      }

      // Filter by status
      if (filters.statuses.length > 0 && !filters.statuses.includes(project.status)) {
        return false;
      }

      // Filter by budget range
      if (filters.budgetRange[0] !== null && project.budgetRemaining < filters.budgetRange[0]) {
        return false;
      }
      if (filters.budgetRange[1] !== null && project.budgetRemaining > filters.budgetRange[1]) {
        return false;
      }

      // Filter by frequency
      if (filters.frequencies.length > 0) {
        const projectSetting = localSettings.find((s) => s.projectId === project.id);
        if (!projectSetting || !filters.frequencies.includes(projectSetting.invoiceFrequency)) {
          return false;
        }
      }

      return true;
    });
  }, [projects, filters, localSettings]);

  // Handle changes to project settings
  const handleSettingChange = (projectId: string, field: keyof ProjectSetting, value: any) => {
    console.log(`Changing setting for project ${projectId}, field: ${field}, value: ${value}`);

    // Check if settings exist for this project
    const existingSetting = localSettings.find((s) => s.projectId === projectId);

    let newSettings;
    if (!existingSetting) {
      // If no settings exist for this project, create new one
      console.log(`Creating new setting for project ${projectId}`);
      const newSetting: ProjectSetting = {
        projectId,
        invoiceFrequency: field ==="invoiceFrequency' ? value : "biweekly",
        invoiceIntervalDays: field ==="invoiceFrequency' ?
        value ==="weekly' ? 7 : value ==='monthly' ? 30 : 14 : 14,
        paymentTerms: field ==="paymentTerms' ? value : 14
      };
      newSettings = [...localSettings, newSetting];
    } else {
      // Otherwise update existing settings
      newSettings = localSettings.map((setting) => {
        if (setting.projectId === projectId) {
          const updatedSetting = { ...setting, [field]: value };
          // Update invoiceIntervalDays if frequency changes
          if (field ==="invoiceFrequency') {
            updatedSetting.invoiceIntervalDays =
            value ==="weekly' ? 7 :
            value ==="monthly' ? 30 :
            value ==="biweekly' ? 14 :
            setting.invoiceIntervalDays;
          }
          return updatedSetting;
        }
        return setting;
      });
    }

    // Log the before and after state for debugging
    console.log('Previous settings:', localSettings);
    console.log('New settings:', newSettings);

    setLocalSettings(newSettings);
    onSettingsChange(newSettings);
  };

  // Handle filter changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({
      ...filters,
      searchText: e.target.value
    });
  };

  const handleClientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const client = e.target.value;
    const newClients = e.target.checked ?
    [...filters.clients, client] :
    filters.clients.filter((c) => c !== client);

    setFilters({
      ...filters,
      clients: newClients
    });
  };

  const handleFrequencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const frequency = e.target.value;
    const newFrequencies = e.target.checked ?
    [...filters.frequencies, frequency] :
    filters.frequencies.filter((f) => f !== frequency);

    setFilters({
      ...filters,
      frequencies: newFrequencies
    });
  };

  const handleBudgetMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const min = e.target.value ==="' ? null : parseFloat(e.target.value);
    setFilters({
      ...filters,
      budgetRange: [min, filters.budgetRange[1]]
    });
  };

  const handleBudgetMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const max = e.target.value ==="' ? null : parseFloat(e.target.value);
    setFilters({
      ...filters,
      budgetRange: [filters.budgetRange[0], max]
    });
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const status = e.target.value;
    const newStatuses = e.target.checked ?
    [...filters.statuses, status] :
    filters.statuses.filter((s) => s !== status);

    setFilters({
      ...filters,
      statuses: newStatuses
    });
  };

  const handleClearFilters = () => {
    setFilters({
      searchText: "",
      clients: [],
      frequencies: [],
      statuses: ['Active'], // Keep the default to show active projects
      budgetRange: [null, null]
    });
  };

  // Helper function to get label for frequency option
  const getFrequencyLabel = (value: string) => {
    const option = frequencyOptions.find((opt) => opt.value === value);
    return option ? option.label : value;
  };

  // Helper function to get label for payment terms option
  const getPaymentTermsLabel = (value: number) => {
    const option = paymentTermsOptions.find((opt) => opt.value === value);
    return option ? option.label : `Net ${value}`;
  };

  return (
    <div className="py-2 md:py-4 px-2 md:px-4 onboarding-project-settings">
      <h2 className="text-lg font-medium text-primary mb-3">
        Project Invoice Settings
        {filters.statuses.length === 1 && filters.statuses[0] ==="Active' &&
        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded border border-success/50 text-xs font-medium bg-success-light text-success/30">
            Active Projects Only
          </span>
        }
      </h2>
      
      {/* Warning message for projects without dates */}
      {projectsWithoutDates.length > 0 &&
      <div className="mb-4 p-4 border border-error bg-error-light/20/30 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-error" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-error">
                Missing Project Dates
              </h3>
              <div className="mt-2 text-sm text-error">
                <p>
                  {projectsWithoutDates.length === 1 ?"1 project is missing" :
                `${projectsWithoutDates.length} projects are missing`} start or end dates. 
                  These projects will not be included in cashflow projections. 
                  Please go to Harvest and add missing dates to your projects.
                </p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  {projectsWithoutDates.slice(0, 3).map((project) =>
                <li key={project.id}>
                      <strong>{project.name}</strong> ({project.client})
                      {!project.startDate && !project.endDate ?" - missing both start and end dates" :
                  !project.startDate ?" - missing start date" :" - missing end date"}
                    </li>
                )}
                  {projectsWithoutDates.length > 3 &&
                <li>...and {projectsWithoutDates.length - 3} more</li>
                }
                </ul>
              </div>
            </div>
          </div>
        </div>
      }
      
      {/* Filter accordion */}
      <div className="mb-4">
        <div className="border border-subtle rounded-lg overflow-hidden shadow-sm">
          {/* Accordion header */}
          <div className="flex items-center justify-between w-full">
            <Button variant="secondary"
            type="button"
            onClick={toggleFilters}
            className="flex items-center grow justify-between text-left"
            aria-expanded={filtersExpanded}
            aria-controls="project-filters-panel">

              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                <span className="font-medium">Filter Projects</span>
                {(filters.clients.length > 0 || filters.frequencies.length > 0 ||
                filters.statuses.length > 0 || filters.searchText ||
                filters.budgetRange[0] !== null || filters.budgetRange[1] !== null) &&
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded border border-primary/50 text-xs font-medium bg-primary-light text-primary/40">
                    Active
                  </span>
                }
              </div>
              <svg className={`w-5 h-5 transition-transform duration-200 ${filtersExpanded ? "transform rotate-180" : ""}`} fill="currentColor" viewBox="0 0 20 20'>
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Button>
            {(filters.clients.length > 0 || filters.frequencies.length > 0 ||
            filters.statuses.length > 0 || filters.searchText ||
            filters.budgetRange[0] !== null || filters.budgetRange[1] !== null) &&
            <Button variant="ghost"
            onClick={handleClearFilters}>


                Clear All
              </Button>
            }
          </div>
          
          {/* Filters UI - conditionally rendered based on accordion state */}
          {filtersExpanded &&
          <div id="project-filters-panel" className="bg-surface-card p-4">
          
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Search filter */}
            <div className="lg:col-span-2">
              <label htmlFor="project-search-filter" className="block text-xs font-medium text-primary mb-1">
                Search
              </label>
              <Input
                  type="text"
                  id="project-search-filter"
                  value={filters.searchText}
                  onChange={handleSearchChange}
                  placeholder="Search project or client..."
                  className="w-full" />

            </div>
            
            {/* Budget range */}
            <div className="lg:col-span-2">
              <label className="block text-xs font-medium text-primary mb-1">
                Budget Range
              </label>
              <div className="flex space-x-2">
                <Input
                    type="number"
                    value={filters.budgetRange[0] === null ? "" : filters.budgetRange[0]}
                    onChange={handleBudgetMinChange}
                    placeholder="Min"
                    className="w-full" />

                <Input
                    type="number"
                    value={filters.budgetRange[1] === null ? "" : filters.budgetRange[1]}
                    onChange={handleBudgetMaxChange}
                    placeholder="Max"
                    className="w-full" />

              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            {/* Client filter */}
            {clientNames.length > 0 &&
              <div>
                <label className="block text-xs font-medium text-primary mb-2">
                  Clients
                </label>
                <div className="flex flex-wrap gap-2">
                  {clientNames.map((client) =>
                    <Checkbox
                      key={client}
                      id={`setting-client-${client}`}
                      checked={filters.clients.includes(client)}
                      onChange={handleClientChange}
                      value={client}
                      label={client}
                    />
                  )}
                </div>
              </div>
              }
            
            {/* Project Status filter */}
            <div>
              <label className="block text-xs font-medium text-primary mb-2">
                Project Status
              </label>
              <div className="flex flex-wrap gap-2">
                {projectStatuses.map((status) => {
                  const labelElement = (
                    <span
                      className={`${
                        status ==="Active' ?'bg-success-light text-success/30 border border-success/50' : "bg-surface-alt text-primary border border-default/50"
                      } text-xs px-2 py-1 rounded`}
                    >
                      {status}
                    </span>
                  );
                  return (
                    <Checkbox
                      key={status}
                      id={`status-${status}`}
                      value={status}
                      checked={filters.statuses.includes(status)}
                      onChange={handleStatusChange}
                      label={labelElement}
                    />
                  );
                })}
              </div>
            </div>
            
            {/* Frequency filter */}
            <div>
              <label className="block text-xs font-medium text-primary mb-2">
                Invoice Frequency
              </label>
              <div className="flex flex-wrap gap-2">
                {frequencyOptions.map((option) => {
                    let colorClass ="';
                    let borderClass ="';

                    // Apply color coding based on CLAUDE.md guidelines
                    if (option.value ==="weekly') {
                      colorClass ="bg-primary-light text-primary';
                      borderClass ="border-primary/50';
                    } else if (option.value ==="biweekly') {
                      colorClass ="bg-primary-light text-primary';
                      borderClass ="border-primary/50';
                    } else if (option.value ==="monthly') {
                      colorClass ="bg-accent-light text-accent';
                      borderClass ="border-accent/50';
                    } else {
                      colorClass ="bg-surface-alt text-primary';
                      borderClass ="border-default/50';
                    }

                    const labelElement = (
                      <span className={`${colorClass} border ${borderClass} text-xs px-2 py-1 rounded`}>
                        {option.label}
                      </span>
                    );

                    return (
                      <Checkbox
                        key={option.value}
                        id={`frequency-${option.value}`}
                        value={option.value}
                        checked={filters.frequencies.includes(option.value)}
                        onChange={handleFrequencyChange}
                        label={labelElement}
                      />
                    );
                  })}
              </div>
            </div>
          </div>
            </div>
          }
        </div>
      </div>
      
      {/* Mobile card view - only shown on small screens */}
      <div className="md:hidden space-y-3 touch-pan-y pb-safe">
        {filteredProjects.length > 0 ?
        filteredProjects.map((project) => {
          // Find settings for this project
          let projectSettings = localSettings.find((s) => s.projectId === project.id);

          // If no settings exist, create a default one on the fly (not saved until changed)
          if (!projectSettings) {
            projectSettings = {
              projectId: project.id,
              invoiceFrequency: "biweekly",
              invoiceIntervalDays: 14,
              paymentTerms: 14 // Default to Net 14
            };
          }

          return (
            <div
              key={project.id}
              className={`p-3 rounded-lg border border-subtle ${
              project.status ==="Active' ?'bg-surface-card' : "bg-surface-page/50/50"} shadow-sm`
              }>

                {/* Project name and status */}
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-primary text-sm truncate mr-2 max-w-[60%]">
                    {project.name}
                  </h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded border text-xs font-medium flex-shrink-0 whitespace-nowrap ${
                project.status ==="Active' ?'bg-success-light text-success/30 border-success/50' : "bg-surface-alt text-primary border-default/50"}`
                }>
                    {project.status}
                  </span>
                </div>
                
                {/* Client name */}
                <div className="text-xs text-muted mb-3">
                  {project.client}
                </div>
                
                {/* Budget and uninvoiced */}
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <div className="text-xs font-medium text-muted mb-1">Remaining Budget</div>
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(project.budgetRemaining)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-muted mb-1">Uninvoiced</div>
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(project.uninvoicedAmount)}
                    </div>
                  </div>
                </div>
                
                {/* Project dates */}
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <div className="text-xs font-medium text-muted mb-1">Start Date</div>
                    {project.startDate ?
                  <div className="text-sm text-primary">
                        {new Date(project.startDate).toLocaleDateString()}
                      </div> :

                  <div className="flex items-center text-error text-sm font-medium">
                        <svg className="h-3.5 w-3.5 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        Missing
                      </div>
                  }
                  </div>
                  <div>
                    <div className="text-xs font-medium text-muted mb-1">End Date</div>
                    {project.endDate ?
                  <div className="text-sm text-primary">
                        {new Date(project.endDate).toLocaleDateString()}
                      </div> :

                  <div className="flex items-center text-error text-sm font-medium">
                        <svg className="h-3.5 w-3.5 mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        Missing
                      </div>
                  }
                  </div>
                </div>
                
                {/* Invoice settings */}
                <div className="border-t border-subtle pt-3 mt-3">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label htmlFor={`frequency-mobile-${project.id}`} className="block text-xs font-medium text-muted mb-1">
                        Invoice Frequency
                      </label>
                      <Select
                      id={`frequency-mobile-${project.id}`}
                      className="appearance-none bg-surface-page border border-strong text-primary text-xs rounded-lg focus:ring-secondary focus:border-secondary block w-full p-2"
                      value={projectSettings.invoiceFrequency}
                      onChange={(e) => handleSettingChange(project.id,"invoiceFrequency', e.target.value)}>

                        {frequencyOptions.map((option) =>
                      <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                      )}
                      </Select>
                    </div>
                    <div>
                      <label htmlFor={`payment-terms-mobile-${project.id}`} className="block text-xs font-medium text-muted mb-1">
                        Payment Terms
                      </label>
                      <Select
                      id={`payment-terms-mobile-${project.id}`}
                      className="appearance-none bg-surface-page border border-strong text-primary text-xs rounded-lg focus:ring-secondary focus:border-secondary block w-full p-2"
                      value={projectSettings.paymentTerms}
                      onChange={(e) => handleSettingChange(project.id,"paymentTerms', parseInt(e.target.value))}>

                        {paymentTermsOptions.map((option) =>
                      <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                      )}
                      </Select>
                    </div>
                  </div>
                </div>
              </div>);

        }) :

        <div className="text-center py-8 bg-surface-page rounded-lg border border-default">
            <svg className="mx-auto h-12 w-12 text-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-primary">No projects found</h3>
            <p className="mt-1 text-sm text-muted">
              No projects match the current filters. Try adjusting your filter criteria.
            </p>
          </div>
        }
      </div>
      
      {/* Desktop table view - only shown on medium screens and up */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-surface-page">
            <tr>
              <th className="py-2 px-3 text-left text-xs font-medium text-muted uppercase tracking-wider">
                Project / Client
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-24">
                Status
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-28">
                Start Date
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-28">
                End Date
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-28">
                Remaining Budget
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-28">
                Uninvoiced
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-28">
                Invoice Frequency
              </th>
              <th className="py-2 px-2 text-left text-xs font-medium text-muted uppercase tracking-wider w-24">
                Payment Terms
              </th>
            </tr>
          </thead>
          <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
            {filteredProjects.length > 0 ?
            filteredProjects.map((project) => {
              // Find settings for this project
              let projectSettings = localSettings.find((s) => s.projectId === project.id);

              // If no settings exist, create a default one on the fly (not saved until changed)
              if (!projectSettings) {
                projectSettings = {
                  projectId: project.id,
                  invoiceFrequency: "biweekly",
                  invoiceIntervalDays: 14,
                  paymentTerms: 14 // Default to Net 14
                };
              }

              return (
                <tr key={project.id}>
                    {/* Project and client */}
                    <td className="py-3 px-3">
                      <div className="text-sm font-medium text-primary">{project.name}</div>
                      <div className="text-xs text-muted">{project.client}</div>
                    </td>
                    
                    {/* Status */}
                    <td className="py-3 px-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded border text-xs font-medium ${
                    project.status ==="Active' ?'bg-success-light text-success/30 border-success/50' : "bg-surface-alt text-primary border-default/50"}`
                    }>
                        {project.status}
                      </span>
                    </td>
                    
                    {/* Start date */}
                    <td className="py-3 px-2 text-sm">
                      {project.startDate ?
                    <span className="text-muted">
                          {new Date(project.startDate).toLocaleDateString()}
                        </span> :

                    <span className="flex items-center text-error font-medium">
                          <svg className="h-4 w-4 mr-1 text-error" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          Missing
                        </span>
                    }
                    </td>
                    
                    {/* End date */}
                    <td className="py-3 px-2 text-sm">
                      {project.endDate ?
                    <span className="text-muted">
                          {new Date(project.endDate).toLocaleDateString()}
                        </span> :

                    <span className="flex items-center text-error font-medium">
                          <svg className="h-4 w-4 mr-1 text-error" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          Missing
                        </span>
                    }
                    </td>
                    
                    {/* Budget remaining */}
                    <td className="py-3 px-2 text-sm text-muted">
                      {formatCurrency(project.budgetRemaining)}
                    </td>
                    
                    {/* Uninvoiced amount */}
                    <td className="py-3 px-2 text-sm text-muted">
                      {formatCurrency(project.uninvoicedAmount)}
                    </td>
                    
                    {/* Invoice frequency dropdown */}
                    <td className="py-2 px-2">
                      <Select
                      id={`frequency-${project.id}`}
                      className="appearance-none bg-surface-page border border-strong text-primary text-xs rounded-lg focus:ring-secondary focus:border-secondary block w-full p-1"
                      value={projectSettings.invoiceFrequency}
                      onChange={(e) => handleSettingChange(project.id,"invoiceFrequency', e.target.value)}>

                        {frequencyOptions.map((option) =>
                      <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                      )}
                      </Select>
                    </td>
                    
                    {/* Payment terms dropdown */}
                    <td className="py-2 px-2">
                      <Select
                      id={`payment-terms-${project.id}`}
                      className="appearance-none bg-surface-page border border-strong text-primary text-xs rounded-lg focus:ring-secondary focus:border-secondary block w-full p-1"
                      value={projectSettings.paymentTerms}
                      onChange={(e) => handleSettingChange(project.id,"paymentTerms', parseInt(e.target.value))}>

                        {paymentTermsOptions.map((option) =>
                      <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                      )}
                      </Select>
                    </td>
                  </tr>);

            }) :

            <tr>
                <td colSpan={8} className="py-4 text-sm text-center text-muted">
                  No projects match the current filters. Try adjusting your filter criteria.
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>);

};

export default ProjectInvoiceSettings;