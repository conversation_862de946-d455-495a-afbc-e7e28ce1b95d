import React from "react";
import { useProjection } from ".";

/**
 * Component for displaying errors
 */
const ErrorDisplay: React.FC = () => {
  const { error } = useProjection();

  if (!error) return null;

  return (
    <div className="bg-error-light border border-error text-accent p-4 rounded-lg mb-6 animate-fadeIn">
      <div className="flex items-center">
        <svg
          className="w-5 h-5 mr-2 text-accent"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <p>{error}</p>
      </div>
    </div>
  );
};

export default ErrorDisplay;
