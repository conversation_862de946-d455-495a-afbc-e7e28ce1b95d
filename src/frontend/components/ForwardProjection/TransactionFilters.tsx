import * as React from "react";
import { Transaction } from "../../../types/financial";
import { Input } from "@/frontend/components/ui/Input";
import { Checkbox } from "@/frontend/components/ui/Checkbox";

const { useMemo } = React;

export interface TransactionFilters {
  types: string[];
  sources: string[];
  timeRange: [Date | null, Date | null];
  searchText: string;
  amountRange: [number | null, number | null];
}

interface TransactionFiltersProps {
  transactions: Transaction[];
  filters: TransactionFilters;
  onFilterChange: (filters: TransactionFilters) => void;
}

const TransactionFilters: React.FC<TransactionFiltersProps> = ({
  transactions,
  filters,
  onFilterChange
}) => {
  // Extract unique transaction types and sources for filter options
  const transactionTypes = useMemo(() => {
    const typesSet = new Set<string>();
    transactions.forEach((transaction) => {
      typesSet.add(transaction.type);
    });
    return Array.from(typesSet);
  }, [transactions]);

  // Extract income vs expense transactions for income filter
  const hasIncomeTransactions = useMemo(() => {
    return transactions.some((transaction) => transaction.amount > 0);
  }, [transactions]);

  // Check if we have real invoices and/or projected income
  const hasRealInvoices = useMemo(() => {
    return transactions.some(
      (transaction) =>
      transaction.source === "Harvest" &&
      transaction.type === "invoice" &&
      !transaction.id.startsWith("future-work-") &&
      !transaction.id.startsWith("uninvoiced-")
    );
  }, [transactions]);

  const hasProjectedIncome = useMemo(() => {
    return transactions.some(
      (transaction) =>
      transaction.source === "Harvest" &&
      transaction.type === "invoice" && (
      transaction.id.startsWith("future-work-") ||
      transaction.id.startsWith("uninvoiced-"))
    );
  }, [transactions]);

  const transactionSources = useMemo(() => {
    const sourcesSet = new Set<string>();
    transactions.forEach((transaction) => {
      if (transaction.source) {
        sourcesSet.add(transaction.source);
      }
    });
    return Array.from(sourcesSet);
  }, [transactions]);

  // Handle filter changes
  const handleTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const type = e.target.value;
    const newTypes = e.target.checked ?
    [...filters.types, type] :
    filters.types.filter((t) => t !== type);

    onFilterChange({
      ...filters,
      types: newTypes
    });
  };

  const handleSourceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const source = e.target.value;
    const newSources = e.target.checked ?
    [...filters.sources, source] :
    filters.sources.filter((s) => s !== source);

    onFilterChange({
      ...filters,
      sources: newSources
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange({
      ...filters,
      searchText: e.target.value
    });
  };

  const handleAmountMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const min = e.target.value === "" ? null : parseFloat(e.target.value);
    onFilterChange({
      ...filters,
      amountRange: [min, filters.amountRange[1]]
    });
  };

  const handleAmountMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const max = e.target.value === "" ? null : parseFloat(e.target.value);
    onFilterChange({
      ...filters,
      amountRange: [filters.amountRange[0], max]
    });
  };

  const handleClearFilters = () => {
    onFilterChange({
      types: [],
      sources: [], // Keep this to maintain interface compatibility
      timeRange: [null, null],
      searchText: "",
      amountRange: [null, null]
    });
  };

  return (
    <div className="p-4">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Search filter */}
        <div className="lg:col-span-2">
          <label
            htmlFor="search-filter"
            className="block text-xs font-medium mb-1"
            style={{ color: "var(--color-text)" }}>

            Search
          </label>
          <Input
            type="text"
            id="search-filter"
            value={filters.searchText}
            onChange={handleSearchChange}
            placeholder="Search description..."
          />

        </div>

        {/* Amount range */}
        <div className="lg:col-span-2">
          <label
            className="block text-xs font-medium mb-1"
            style={{ color: "var(--color-text)" }}>

            Amount Range
          </label>
          <div className="flex space-x-2">
            <Input
              type="number"
              value={
              filters.amountRange[0] === null ? "" : filters.amountRange[0]
              }
              onChange={handleAmountMinChange}
              placeholder="Min"
              className="w-full p-2 text-sm border border-strong rounded-md bg-surface-card text-primary" />

            <Input
              type="number"
              value={
              filters.amountRange[1] === null ? "" : filters.amountRange[1]
              }
              onChange={handleAmountMaxChange}
              placeholder="Max"
              className="w-full p-2 text-sm border border-strong rounded-md bg-surface-card text-primary" />

          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {/* Transaction direction filter (Income/Expense) */}
        <div>
          <label
            className="block text-xs font-medium mb-2"
            style={{ color: "var(--color-text)" }}>

            Transaction Direction
          </label>
          <div className="flex flex-wrap gap-2">
            {/* Income filter */}
            {hasIncomeTransactions &&
              <Checkbox
                id="filter-income"
                value="income"
                checked={filters.types.includes("income")}
                onChange={(e) => {
                  const newTypes = e.target.checked ?
                    [...filters.types, "income"] :
                    filters.types.filter((t) => t !== "income");

                  onFilterChange({
                    ...filters,
                    types: newTypes
                  });
                }}
                label={
                  <span className="bg-success-light text-success/30 text-xs px-2 py-1 rounded border border-success/30">
                    Income
                  </span>
                }
              />
            }

            {/* Expense filter */}
            <div className="ml-2">
              <Checkbox
                id="filter-expense"
                value="expense"
                checked={filters.types.includes("expense")}
                onChange={(e) => {
                  // Special handling for expense filter - will filter by amount < 0
                  const newTypes = e.target.checked ?
                    [...filters.types, "expense"] :
                    filters.types.filter((t) => t !== "expense");

                  onFilterChange({
                    ...filters,
                    types: newTypes
                  });
                }}
                label={
                  <span className="bg-error-light text-error/30 text-xs px-2 py-1 rounded border border-error/30">
                    Expense
                  </span>
                }
              />
            </div>
          </div>
        </div>

        {/* Transaction types filter */}
        <div>
          <label
            className="block text-xs font-medium mb-2"
            style={{ color: "var(--color-text)" }}>

            Transaction Types
          </label>
          <div className="flex flex-wrap gap-2">
            {/* Invoice Filter */}
            {hasRealInvoices &&
              <Checkbox
                id="filter-real"
                value="real_invoice"
                checked={filters.types.includes("real_invoice")}
                onChange={(e) => {
                  const newTypes = e.target.checked ?
                    [...filters.types, "real_invoice"] :
                    filters.types.filter((t) => t !== "real_invoice");

                  onFilterChange({
                    ...filters,
                    types: newTypes
                  });
                }}
                label={
                  <span className="bg-success text-success/50 text-xs px-2 py-1 rounded border border-success/50">
                    Invoice
                  </span>
                }
              />
            }

            {/* Projected Income Filter */}
            {hasProjectedIncome &&
              <div className="ml-2">
                <Checkbox
                  id="filter-projected"
                  value="projected_income"
                  checked={filters.types.includes("projected_income")}
                  onChange={(e) => {
                    const newTypes = e.target.checked ?
                      [...filters.types, "projected_income"] :
                      filters.types.filter((t) => t !== "projected_income");

                    onFilterChange({
                      ...filters,
                      types: newTypes
                    });
                  }}
                  label={
                    <span className="bg-success-light text-success/30 text-xs px-2 py-1 rounded border border-success/30">
                      <svg
                        className="w-3 h-3 mr-1 inline-block"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Projected Income
                    </span>
                  }
                />
              </div>
            }

            {/* Custom Expense types */}
            {transactionTypes.map((type) => {
              // Skip non-custom expense types as they are handled separately
              if (
              !type.startsWith("custom_expense_") ||
              type === "income" ||
              type === "expense" ||
              type === "invoice")
              {
                return null;
              }

              // Determine visual appearance based on transaction type
              let typeColor = "bg-surface-alt text-primary";

              if (type.includes("Monthly Payroll")) {
                typeColor = "bg-primary-light text-primary";
              } else if (type.includes("Software")) {
                typeColor = "bg-accent-light text-accent";
              } else if (type.includes("Superannuation")) {
                typeColor = "bg-warning-light text-warning";
              } else if (type.includes("Insurances")) {
                typeColor = "bg-magenta-100 text-magenta-800";
              } else if (type.includes("Taxes")) {
                typeColor = "bg-primary-light text-primary";
              } else if (type.includes("Fees")) {
                typeColor = "bg-error-light text-error";
              }

              // Format display name
              const displayName = type.replace("custom_expense_", "");

              return (
                <div key={type} className="mt-2">
                  <Checkbox
                    id={`type-${type}`}
                    value={type}
                    checked={filters.types.includes(type)}
                    onChange={handleTypeChange}
                    label={
                      <span className={`${typeColor} text-xs px-2 py-1 rounded border border-current border-opacity-20`}>
                        {displayName}
                      </span>
                    }
                  />
                </div>
              );

            })}

            {/* Other transaction types if needed */}
            {transactionTypes.map((type) => {
              // Only include specialized types that aren't already handled
              if (
              type === "income" ||
              type === "expense" ||
              type === "invoice" ||
              type.startsWith("custom_expense_"))
              {
                return null;
              }

              // Determine visual appearance based on transaction type
              let typeColor = "bg-surface-alt text-primary";

              if (type === "repeating_bill") {
                typeColor = "bg-warning-light text-warning";
              } else if (type === "fixed_expense") {
                typeColor = "bg-accent-light text-accent";
              }

              return (
                <div key={type} className="mt-2">
                  <Checkbox
                    id={`type-${type}`}
                    value={type}
                    checked={filters.types.includes(type)}
                    onChange={handleTypeChange}
                    label={
                      <span className={`${typeColor} text-xs px-2 py-1 rounded border border-current border-opacity-20`}>
                        {type}
                      </span>
                    }
                  />
                </div>
              );

            })}
          </div>
        </div>
      </div>
    </div>);

};

export default TransactionFilters;