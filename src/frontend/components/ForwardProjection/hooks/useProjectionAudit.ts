import { useState, useEffect } from 'react'; // Removed useMemo
import { CashflowForecast } from '../../../../types/financial'; // Corrected import path
import { FilterDecision, FilterSummary } from "../types/projection-audit-types";
// Removed unused event imports: useEvents, EVENTS, convertEventToDecision
import { 
  // Removed unused imports: generateSyntheticExclusions
  calculateSummary,
  resolveClientName
} from "../utils/projection-audit-utils";

export function useProjectionAudit(projectionData: CashflowForecast | null) {
  // Removed event handling logic
  const [filterDecisions, setFilterDecisions] = useState<FilterDecision[]>([]); // Explicit type already present
  const [summary, setSummary] = useState<FilterSummary>({ // Explicit type already present
    totalInvoices: 0,
    keptInvoices: 0,
    excludedInvoices: 0,
    byReason: {}
  });
  
  // Removed useEffect hook for PROJECTION_FILTERED event subscription
  
  // Process filter decisions directly from projectionData
  useEffect(() => {
    // Use filterDecisions directly from projectionData if available
    const decisionsFromContext = projectionData?.filterDecisions || [];
    
    if (decisionsFromContext.length > 0) {
      console.log(`Processing ${decisionsFromContext.length} decisions from context`);

      // Create transaction map for enrichment (only if needed)
      const transactionsById = new Map<string, any>();
      if (projectionData?.projectedTransactions) {
        projectionData.projectedTransactions.forEach(transaction => {
          if (transaction.id) {
            transactionsById.set(transaction.id, transaction);
          }
        });
      }
      
      // Create project-client map for enrichment (only if needed)
      const projectClientMap = new Map<string, string>();
      if (projectionData?.projectedTransactions) {
        projectionData.projectedTransactions.forEach(transaction => {
          if (transaction.metadata?.projectId && transaction.metadata?.clientName) {
            projectClientMap.set(transaction.metadata.projectId, transaction.metadata.clientName);
          }
        });
      }

      // First validate each decision and ensure required fields are present
      const validatedDecisions = decisionsFromContext.map(decision => {
        // Get data from invoice object if available, for backward compatibility
        const invoice = decision.invoice || {};
        
        // Determine client name from best available source
        const clientName = 
          decision.clientName || 
          invoice.clientName || 
          invoice.metadata?.clientName || 
          'Unknown Client';
        
        // Determine project name from best available source
        const projectName = 
          decision.projectName || 
          invoice.projectName || 
          invoice.description || 
          invoice.what || 
          'Unknown Project';
          
        // Ensure basics are present
        const validDecision = {
          ...decision,
          // Ensure required properties:
          projectId: decision.projectId || invoice.projectId || "unknown",
          projectName: projectName,
          clientName: clientName,
          invoiceType: decision.invoiceType || invoice.invoiceType || "Uninvoiced Work",
          paymentDate: decision.paymentDate || (invoice.date ? new Date(invoice.date).toISOString() : new Date().toISOString()),
          amount: typeof decision.amount === 'number' && !isNaN(decision.amount) ? 
            decision.amount : 
            (typeof invoice.amount === 'number' && !isNaN(invoice.amount) ? invoice.amount : 0),
          reason: decision.reason || "Unknown reason",
          // Force action to be either 'kept' or 'excluded'
          action: decision.action === 'kept' ? 'kept' : (decision.action === 'excluded' ? 'excluded' : "kept")
        };
        return validDecision;
      });

      // Log validation results
      console.log(`Validated ${validatedDecisions.length} decisions - checking for any invalid ones`);
      
      // Enrich decisions (e.g., resolve client names)
      // Ensure we work with copies to avoid mutating context state
      const enrichedDecisions = validatedDecisions.map(decision => {
        try {
          const resolvedClientName = resolveClientName(decision, transactionsById, projectClientMap);
          // Return a new object, potentially with the resolved client name
          return { 
            ...decision, 
            clientName: resolvedClientName || decision.clientName || "Unknown Client" // Keep original if resolution fails
          };
        } catch (error) {
          console.error('Error enriching decision:', error);
          // Return a safe fallback
          return {
            ...decision,
            clientName: decision.clientName || "Unknown Client"
          };
        }
      });
      
      // Final safety check - ensure relatedInvoice is always a string (if present)
      const sanitizedDecisions = enrichedDecisions.map(d => {
        if (d.relatedInvoice && typeof d.relatedInvoice !== 'string') {
          console.warn('Found non-string relatedInvoice, converting to string:', d.relatedInvoice);
          return {
            ...d,
            relatedInvoice: "Related Invoice" // Replace with a safe string
          };
        }
        return d;
      });
      
      // Set the processed decisions
      // Ensure safe copies are set to state
      setFilterDecisions(sanitizedDecisions.map(d => ({...d}))); 
      
      // Log statistics for debugging
      const keptCount = enrichedDecisions.filter(d => d.action === 'kept').length;
      const excludedCount = enrichedDecisions.filter(d => d.action === 'excluded').length;
      
      console.log(`Set ${enrichedDecisions.length} processed filter decisions (${keptCount} kept, ${excludedCount} excluded)`);
      
    } else {
      // If no decisions in context, reset state
      console.log('No filter decisions found in projection data, resetting state.');
      setFilterDecisions([]);
    }
    
    // Removed all logic related to global variables, synthetic decisions, etc.
    
  }, [projectionData]); // Depend only on projectionData
  
  // Calculate summary whenever filter decisions change (this remains the same)
  useEffect(() => {
    // Calculate summary
    const summaryData = calculateSummary(filterDecisions);
    setSummary(summaryData);
  }, [filterDecisions]);
  
  return {
    filterDecisions,
    summary
  };
}
