import { useState, useMemo } from "react";
import { useProjection } from "../ProjectionContext";
import { Transaction } from "../../../../types/financial";
import {
  TransactionFilters,
  ProcessedTransaction,
  TransactionsHookResult,
} from "../types/transactions-list-types";
import {
  getFormattedDescription,
  getTypeColor,
  isRealInvoice,
  isProjectedInvoice,
} from "../utils/transactions-list-utils";
import { calculatePercentageChange } from "../utils";

/**
 * Custom hook for handling transaction data processing and state
 */
export function useTransactions(): TransactionsHookResult {
  // Removed unused hoveredDate from context destructuring
  const { projectionData } = useProjection();

  // State for filters
  const [filters, setFilters] = useState<TransactionFilters>({
    // Re-apply explicit type
    types: [],
    sources: [],
    timeRange: [null, null],
    searchText: "",
    amountRange: [null, null],
  });

  // State for filter accordion
  const [filtersExpanded, setFiltersExpanded] = useState<boolean>(false); // Re-apply explicit type

  // Sort transactions by date
  const sortedTransactions = useMemo(() => {
    return projectionData?.projectedTransactions
      ? [...projectionData.projectedTransactions].sort(
          (a: Transaction, b: Transaction) =>
            new Date(a.date).getTime() - new Date(b.date).getTime(),
        )
      : [];
  }, [projectionData?.projectedTransactions]);

  // Apply filters to sorted transactions
  const filteredTransactions = useMemo(() => {
    if (!sortedTransactions.length) return [];

    // First filter transactions based on criteria
    const filtered = sortedTransactions.filter((transaction) => {
      // Determine transaction characteristics
      const isExpense = transaction.amount <= 0;
      const isRealInvoiceValue = isRealInvoice(transaction);
      const isProjectedInvoiceValue = isProjectedInvoice(transaction);

      // Type filters logic
      if (filters.types.length > 0) {
        // Create a flag to track if any filter matched
        let typeFilterMatched = false;

        // Check for invoice type filters (real_invoice, projected_income)
        if (filters.types.includes("real_invoice") && isRealInvoiceValue) {
          typeFilterMatched = true;
        }

        if (
          filters.types.includes("projected_income") &&
          isProjectedInvoiceValue
        ) {
          typeFilterMatched = true;
        }

        // Check for income filter type (amount > 0)
        if (filters.types.includes("income") && transaction.amount > 0) {
          typeFilterMatched = true;
        }

        if (filters.types.includes("expense") && isExpense) {
          typeFilterMatched = true;
        }

        // Check for specific transaction type filters
        if (filters.types.includes(transaction.type)) {
          typeFilterMatched = true;
        }

        // If no type filter matched this transaction, filter it out
        if (!typeFilterMatched) {
          return false;
        }
      }

      // Source filter
      if (
        filters.sources.length > 0 &&
        !filters.sources.includes(transaction.source)
      ) {
        return false;
      }

      // Search text filter (case insensitive)
      const searchableDescription = getFormattedDescription(transaction);
      if (
        filters.searchText &&
        !searchableDescription
          .toLowerCase()
          .replace("\n", " ")
          .includes(filters.searchText.toLowerCase())
      ) {
        return false;
      }

      // Amount range filter
      if (
        filters.amountRange[0] !== null &&
        transaction.amount < filters.amountRange[0]
      ) {
        return false;
      }

      if (
        filters.amountRange[1] !== null &&
        transaction.amount > filters.amountRange[1]
      ) {
        return false;
      }

      // All filters passed
      return true;
    });

    // Then process the filtered transactions, including balance calculations
    let previousBalance = projectionData?.startingBalance || 0;
    let runningBalance = previousBalance;

    const processed = filtered.map((transaction) => {
      // Determine transaction characteristics
      const isExpense = transaction.amount < 0;

      // Update running balance
      runningBalance += transaction.amount;

      // Calculate percentage change
      const percentChange = calculatePercentageChange(
        runningBalance,
        previousBalance,
      );

      // Create processed transaction
      const result: ProcessedTransaction = {
        ...transaction,
        formattedDescription: getFormattedDescription(transaction),
        isRealInvoice: isRealInvoice(transaction),
        isProjectedInvoice: isProjectedInvoice(transaction),
        isExpense,
        typeColor: getTypeColor(transaction),
        percentChange,
        runningBalance,
      };

      // Update previous balance for next iteration
      previousBalance = runningBalance;

      return result;
    });

    return processed;
  }, [sortedTransactions, filters, projectionData?.startingBalance]);

  return {
    filteredTransactions,
    filters,
    setFilters,
    filtersExpanded,
    setFiltersExpanded,
    totalTransactions: sortedTransactions.length,
  };
}
