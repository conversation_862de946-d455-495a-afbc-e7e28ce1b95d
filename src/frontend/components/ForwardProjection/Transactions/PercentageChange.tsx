import React from "react";
import { PercentageChangeProps } from "../types/transactions-list-types";

/**
 * Component for displaying percentage change with appropriate styling
 */
const PercentageChange: React.FC<PercentageChangeProps> = ({ percentChange }) => {
  // Show even if percentage change is 0, with a neutral color
  const isZero = percentChange === 0;
  const isPositive = percentChange > 0;
  
  // Use neutral gray for 0% change, green for positive, red for negative
  const bgColorClass = isZero ? 'bg-surface-alt dark:bg-surface-alt/50' : isPositive ? 'bg-success-light dark:bg-success-dark/30' : 'bg-error-light dark:bg-error-dark/30';
  const textColorClass = isZero ? 'text-primary dark:text-subtle' : isPositive ? 'text-green-800 dark:text-green-300' : 'text-red-800 dark:text-red-300';
  const borderColorClass = isZero ? 'border-default dark:border-strong' : isPositive ? 'border-success dark:border-green-800/30' : 'border-error dark:border-red-800/30';
  
  return (
    <span className={`inline-flex items-center px-1 py-0.5 rounded border text-[10px] font-medium whitespace-nowrap ${bgColorClass} ${textColorClass} ${borderColorClass}`}>
      {isZero ? (
        // Horizontal arrow for zero change
        <svg className="w-2.5 h-2.5 mr-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M12 5l7 7-7 7" />
        </svg>
      ) : isPositive ? (
        // Up arrow for positive change
        <svg className="w-2.5 h-2.5 mr-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
        </svg>
      ) : (
        // Down arrow for negative change
        <svg className="w-2.5 h-2.5 mr-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      )}
      {Math.abs(percentChange).toFixed(1)}%
    </span>
  );
};

export default PercentageChange;