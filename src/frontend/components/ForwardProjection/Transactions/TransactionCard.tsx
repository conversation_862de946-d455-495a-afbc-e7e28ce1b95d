import React from "react";
import { TransactionCardProps } from "../types/transactions-list-types"; // Assuming types are defined here
import PercentageChange from "./PercentageChange";
import {
  getBackgroundColorClass,
  extractInvoiceId,
  isGSTTransaction,
} from "../utils/transactions-list-utils";
import { formatDate, formatCurrency } from "../utils";
import { isXeroTransaction } from "../utils/xero-utils";
import XeroBadge from "../../shared/XeroBadge";
import HarvestBadge from "../../shared/HarvestBadge";
import PredictedBadge from "../../shared/PredictedBadge";
import AccruedBadge from "../../shared/AccruedBadge";
import { getCurrentQuarterInfo } from "../../../utils/quarter-utils";
// CSS is now imported centrally in index.tsx

/**
 * Adaptive card view for a transaction, replacing separate mobile/desktop versions.
 * Uses container queries defined in transaction-card.css to adapt layout.
 */
const TransactionCard: React.FC<TransactionCardProps> = ({
  transaction,
  isHighlighted,
  index,
}) => {
  const {
    formattedDescription,
    isExpense,
    isProjectedInvoice,
    // isRealInvoice, // This wasn't used in the proposal example, check if needed
    typeColor,
    percentChange,
    runningBalance,
    id,
    source, // Added source for badge logic
    type, // Added type for badge logic
    metadata, // Added metadata for badge logic
  } = transaction;

  // Background color based on highlight state and index
  const bgColorClass = getBackgroundColorClass(index, isHighlighted);

  // Border color based on transaction type
  const borderClass = isProjectedInvoice ? "border-success" : "border-subtle";

  // Determine badge content and link
  let badgeContent: React.ReactNode;
  let badgeLink: string | null = null;

  if (
    source === "harvest" &&
    type === "invoice" &&
    id.startsWith("future-work-")
  ) {
    badgeContent = (
      <>
        <svg
          className="transaction-card__badge-icon"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
            clipRule="evenodd"
          />
        </svg>
        <span className="transaction-card__badge-text">Projected Income</span>
      </>
    );
  } else if (
    source === "harvest" &&
    type === "invoice" &&
    id.startsWith("uninvoiced-")
  ) {
    badgeContent = (
      <>
        <svg
          className="transaction-card__badge-icon"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
            clipRule="evenodd"
          />
        </svg>
        <span className="transaction-card__badge-text">Uninvoiced Work</span>
      </>
    );
  } else if (source === "harvest" && type === "invoice") {
    const isRealHarvestInvoice =
      id.startsWith("harvest-invoice-") ||
      id.startsWith("outstanding-invoice-");
    if (isRealHarvestInvoice) {
      const invoiceId = extractInvoiceId(id);
      badgeLink = `https://onbord.harvestapp.com/invoices/${invoiceId}`;
      const issueDate = metadata?.issue_date
        ? new Date(metadata.issue_date)
        : null;
      const isRecent =
        issueDate &&
        !isNaN(issueDate.getTime()) &&
        (new Date().getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24) <=
          5;

      badgeContent = (
        <>
          {isRecent && (
            <span
              className="transaction-card__badge-dot"
              title="Recently created invoice"
            ></span>
          )}
          <span className="transaction-card__badge-text">
            Invoice {metadata?.invoiceNumber || "N/A"}
          </span>
          <svg
            className="transaction-card__badge-link-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            ></path>
          </svg>
        </>
      );
    } else {
      badgeContent = (
        <span className="transaction-card__badge-text">Invoice</span>
      );
    }
  } else {
    badgeContent = (
      <span className="transaction-card__badge-text">
        {type.startsWith("custom_expense_")
          ? type.replace("custom_expense_", "")
          : source || "Unknown"}
      </span>
    );
  }

  const BadgeComponent = badgeLink ? "a" : "div";

  return (
    <div
      className={`transaction-card p-fluid-sm ${borderClass} ${bgColorClass} ${
        isHighlighted ? "transaction-card--highlighted" : ""
      }`}
      // Added inline width constraint as failsafe for older browsers that don't support container queries
      style={{ width: "100%", maxWidth: "100%" }}
    >
      {/* Top section with date, description and amount */}
      <div className="transaction-card__header">
        {/* Date and icon */}
        <div className="transaction-card__date-container">
          <svg
            className={`transaction-card__icon ${
              isExpense ? "text-accent" : "text-success"
            }`}
            fill="currentColor"
            viewBox="0 0 100 100"
          >
            {isExpense ? (
              <path d="m91.266 50.781-11.156 31.25c-0.34375 0.9375-1.2344 1.5625-2.2031 1.5625-0.125 0-0.26562-0.015625-0.40625-0.03125-1.125-0.1875-1.9375-1.1719-1.9375-2.3125v-7.6562h-13.062c-1.2969 0-2.3438-1.0469-2.3438-2.3438s1.0469-2.3438 2.3438-2.3438h15.406c0.65625 0 1.25 0.26562 1.6719 0.70312l7-19.609-7-19.609c-0.42188 0.4375-1.0156 0.70312-1.6719 0.70312h-15.406c-1.2969 0-2.3438-1.0469-2.3438-2.3438s1.0469-2.3438 2.3438-2.3438h13.062v-7.6562c0-1.1406 0.8125-2.1094 1.9375-2.3125 1.125-0.1875 2.2188 0.45312 2.6094 1.5156l11.156 31.25c0.1875 0.51562 0.1875 1.0781 0 1.5781zm-30.562 27.234c-5.3125 3.5156-11.672 5.5781-18.516 5.5781-18.547 0-33.594-15.047-33.594-33.594s15.047-33.594 33.594-33.594c6.8438 0 13.203 2.0625 18.516 5.5781-3 0.79688-5.2344 3.5156-5.2344 6.7656 0 3.875 3.1562 7.0312 7.0312 7.0312h10.094c2.0312 4.3281 3.1875 9.125 3.1875 14.219s-1.1719 9.8906-3.1875 14.219h-10.094c-3.875 0-7.0312 3.1562-7.0312 7.0312 0 3.25 2.2344 5.9688 5.2344 6.7656zm-24.812-36.266c0-3.2656 2.6562-5.9062 5.9062-5.9062s5.9062 2.6562 5.9062 5.9062c0 1.2969 1.0469 2.3438 2.3438 2.3438s2.3438-1.0469 2.3438-2.3438c0-5.0312-3.5312-9.2656-8.25-10.328v-2.0469c0-1.2969-1.0469-2.3438-2.3438-2.3438s-2.3438 1.0469-2.3438 2.3438v2.0469c-4.7188 1.0781-8.25 5.2969-8.25 10.328 0 6.3125 8.7188 10.031 9.7188 10.422 2.125 0.85938 6.7969 3.5781 6.7969 6.0781 0 3.2656-2.6562 5.9062-5.9062 5.9062s-5.9062-2.6562-5.9062-5.9062c0-1.2969-1.0469-2.3438-2.3438-2.3438s-2.3438 1.0469-2.3438 2.3438c0 5.0312 3.5312 9.2656 8.25 10.328v2.0469c0 1.2969 1.0469 2.3438 2.3438 2.3438s2.3438-1.0469 2.3438-2.3438v-2.0469c4.7188-1.0781 8.25-5.2969 8.25-10.328 0-6.3125-8.7188-10.031-9.7188-10.422-2.1406-0.85938-6.7969-3.5938-6.7969-6.0781z" />
            ) : (
              <path d="m55.875 47.641 8.2344-23.062c-5.875-5.0938-13.531-8.1719-21.922-8.1719-18.547 0-33.594 15.047-33.594 33.594s15.047 33.594 33.594 33.594c8.3906 0 16.047-3.0781 21.922-8.1719l-8.2344-23.062c-0.54688-1.5156-0.54688-3.2031 0-4.7188zm-13.203 0.1875c1 0.40625 9.7188 4.1094 9.7188 10.422 0 5.0312-3.5312 9.2656-8.25 10.328v2.0469c0 1.2969-1.0469 2.3438-2.3438 2.3438s-2.3438 1.0469-2.3438 2.3438v-2.0469c-4.7188 1.0781-8.25 5.2969-8.25 10.328 0 1.2969 1.0469 2.3438 2.3438 2.3438s2.3438 1.0469 2.3438 2.3438c0 3.2656 2.6562 5.9062 5.9062 5.9062s5.9062-2.6562 5.9062-5.9062c0-2.5-4.6562-5.2188-6.7969-6.0781-1-0.40625-9.7188-4.1094-9.7188-10.422 0-5.0312 3.5312-9.2656 8.25-10.328v-2.0469c0-1.2969 1.0469-2.3438 2.3438-2.3438s2.3438 1.0469 2.3438 2.3438v2.0469c4.7188 1.0781 8.25 5.2969 8.25 10.328 0 1.2969-1.0469 2.3438-2.3438 2.3438s-2.3438 1.0469-2.3438 2.3438c0-3.2656-2.6562-5.9062-5.9062-5.9062s-5.9062 2.6562-5.9062 5.9062c0.015625 2.4844 4.6719 5.2188 6.7969 6.0781zm48.734 23.422c0 1.2969-1.0469 2.3438-2.3438 2.3438h-13.062v7.6562c0 1.1406-0.82812 2.1094-1.9375 2.3125-0.14062 0.015625-0.26562 0.03125-0.40625 0.03125-0.96875 0-1.875-0.60938-2.2031-1.5625l-11.156-31.25c-0.1875-0.51562-0.1875-1.0625 0-1.5781l11.156-31.25c0.39062-1.0781 1.4844-1.7031 2.6094-1.5156s1.9375 1.1719 1.9375 2.3125v7.6562h13.062c1.2969 0 2.3438 1.0469 2.3438 2.3438s-1.0469 2.3438-2.3438 2.3438h-15.406c-0.65625 0-1.25-0.26562-1.6719-0.70312l-7 19.609 7 19.609c0.42188-0.4375 1.0156-0.70312 1.6719-0.70312h15.406c1.2969 0 2.3438 1.0469 2.3438 2.3438z" />
            )}
          </svg>
          <span className="transaction-card__date text-fluid-sm text-secondary dark:text-subtle">
            {formatDate(transaction.date.toString())}
          </span>
        </div>

        {/* Amount with percentage */}
        <div className="transaction-card__amount-container">
          <span
            className={`transaction-card__amount text-fluid-base font-medium ${
              isExpense ? "text-accent" : "text-success"
            }`}
          >
            {formatCurrency(transaction.amount)}
          </span>
          <div className="transaction-card__percent">
            <PercentageChange percentChange={percentChange} />
          </div>
        </div>
      </div>

      {/* Description */}
      <div
        className="transaction-card__description"
        title={formattedDescription.replace("\n", " - ")}
      >
        {formattedDescription.split("\n").map((line, index) => (
          <div
            key={index}
            className={
              index === 0
                ? "transaction-card__desc-primary text-fluid-base font-medium text-primary"
                : "transaction-card__desc-secondary text-fluid-sm text-muted"
            }
          >
            {line}
          </div>
        ))}
      </div>

      {/* Bottom section with badge and running balance */}
      <div className="transaction-card__footer">
        {/* Transaction type badge */}
        <div className="flex items-center">
          {/* Xero badge on the left */}
          {isXeroTransaction(transaction) && <XeroBadge className="mr-1.5" />}

          {/* Harvest badge on the left - only for real invoices */}
          {source === "harvest" &&
            type === "invoice" &&
            (id.startsWith("harvest-invoice-") ||
              id.startsWith("outstanding-invoice-")) && (
              <HarvestBadge className="mr-1.5" />
            )}

          {/* GST/BAS badges */}
          {isGSTTransaction(transaction) &&
            transaction.metadata?.usedProjectedAmount === true && (
              <PredictedBadge
                className="mr-1.5"
                quarterInfo={getCurrentQuarterInfo()}
              />
            )}
          {isGSTTransaction(transaction) &&
            transaction.metadata?.usedProjectedAmount === false && (
              <AccruedBadge
                className="mr-1.5"
                quarterInfo={getCurrentQuarterInfo()}
              />
            )}

          <BadgeComponent
            className={`transaction-card__badge ${typeColor}`}
            href={badgeLink ?? undefined} // Only add href if badgeLink is not null
            target={badgeLink ? "_blank" : undefined}
            rel={badgeLink ? "noopener noreferrer" : undefined}
            title={badgeLink ? "View invoice in Harvest" : undefined}
          >
            {badgeContent}
          </BadgeComponent>
        </div>

        {/* Running balance */}
        <div
          className={`transaction-card__balance text-fluid-sm font-medium ${
            runningBalance >= 0 ? "text-primary" : "text-accent"
          }`}
        >
          {formatCurrency(runningBalance)}
        </div>
      </div>
    </div>
  );
};

export default TransactionCard;
