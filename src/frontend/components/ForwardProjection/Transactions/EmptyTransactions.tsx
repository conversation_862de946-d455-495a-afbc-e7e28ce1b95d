import React from "react";
import { Button } from "@/frontend/components/ui/Button";

interface EmptyTransactionsProps {
  hasTransactions: boolean;
  clearFilters: () => void;
}

/**
 * Component for displaying empty state when no transactions match filters
 */
const EmptyTransactions: React.FC<EmptyTransactionsProps> = ({
  hasTransactions,
  clearFilters,
}) => {
  return (
    <div className="text-center py-8 bg-surface-page dark:bg-surface-alt rounded-lg border border-default dark:border-strong">
      <svg
        className="mx-auto h-12 w-12 text-subtle"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
        />
      </svg>
      <h3 className="mt-2 text-sm font-medium text-primary dark:text-gray-100">
        No transactions found
      </h3>
      <p className="mt-1 text-sm text-muted dark:text-subtle">
        {hasTransactions
          ? "No transactions match your current filter criteria."
          : "No upcoming transactions in the selected timeframe."}
      </p>
      {hasTransactions && (
        <Button variant="primary" onClick={clearFilters} className="mt-3">
          Clear Filters
        </Button>
      )}
    </div>
  );
};

export default EmptyTransactions;
