import React, { useState } from "react";
import { useProjection } from "./ProjectionContext";
import { useProjectionAudit } from "./hooks/useProjectionAudit";

// Import components
import InfoAccordion from "./ProjectionAudit/InfoAccordion";
import FilteringRules from "./ProjectionAudit/FilteringRules";
import DecisionTable from "./ProjectionAudit/DecisionTable";

/**
 * Component to display a detailed breakdown of the projection filtering logic
 */
const ProjectionAuditPage: React.FC = () => {
  const { projectionData } = useProjection();
  const [showDetailedRules, setShowDetailedRules] = useState<boolean>(true);
  const [showInformation, setShowInformation] = useState<boolean>(false);
  // Always initialize to 'all' to make sure we see decisions
  const [activeTab, setActiveTab] = useState<'all' | 'kept' | 'excluded'>('all');
  const [filter, setFilter] = useState<string>('');
  
  // Use the custom hook to handle data loading and processing
  const { filterDecisions, summary } = useProjectionAudit(projectionData);
  
  // Only apply tab filtering
  let filteredDecisions = filterDecisions;
  
  if (activeTab === 'kept') {
    filteredDecisions = filterDecisions.filter(d => d.action === 'kept');
  } else if (activeTab === 'excluded') {
    filteredDecisions = filterDecisions.filter(d => d.action === 'excluded');
  } 
  // All tab - use all decisions
  
  // Calculate counts once from complete unfiltered data
  const decisionCounts = {
    all: filterDecisions.length,
    kept: filterDecisions.filter(d => d.action === 'kept').length,
    excluded: filterDecisions.filter(d => d.action === 'excluded').length,
    uninvoiced: filterDecisions.filter(d => 
      d.invoiceType === 'Uninvoiced Work' || 
      (d.id && d.id.startsWith('uninvoiced-'))
    ).length
  };
  
  return (
    <div className="space-y-6 onboarding-audit-log">
      {/* Information accordion */}
      <InfoAccordion 
        showInformation={showInformation} 
        setShowInformation={setShowInformation} 
      />
      
      {/* Filtering Rules Section */}
      <div className="bg-surface-card dark:bg-surface-card rounded-lg shadow-sm overflow-hidden border border-default dark:border-default">
        <FilteringRules 
          summary={summary} 
          showDetailedRules={showDetailedRules} 
          setShowDetailedRules={setShowDetailedRules} 
        />
      </div>
      
      <DecisionTable 
        decisions={filterDecisions}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        filter={filter}
        setFilter={setFilter}
        totalCount={decisionCounts.all}
        keptCount={decisionCounts.kept}
        excludedCount={decisionCounts.excluded}
      />
      
      <div className="text-center py-2 my-1">
        <div className="text-xs text-muted dark:text-subtle italic">
          Smart Forecast Decisions helps you understand why certain invoices are included or excluded from forecasts
        </div>
      </div>
    </div>
  );
};

export default ProjectionAuditPage;