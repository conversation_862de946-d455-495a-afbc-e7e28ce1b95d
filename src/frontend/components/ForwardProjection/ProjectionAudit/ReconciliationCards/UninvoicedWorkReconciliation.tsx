import React from "react";
import { ProcessedDecision } from "../../types/projection-audit-types";
import { formatDate, formatCurrency } from "../../utils";

interface UninvoicedWorkReconciliationProps {
  decision: ProcessedDecision;
}

const UninvoicedWorkReconciliation: React.FC<UninvoicedWorkReconciliationProps> = ({ decision }) => {
  return (
    <div className="flex flex-col space-y-2 p-2 bg-surface-card border border-default rounded-lg">
      {/* Title and explanation */}
      <div className="flex items-center justify-between border-b border-default pb-2">
        <div className="flex items-center">
          <svg className="w-3.5 h-3.5 text-warning mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <div className="text-[11px] font-medium text-primary">
            Uninvoiced Work Reconciliation
          </div>
        </div>
      </div>
      
      {/* Main content in a very compact layout */}
      <div className="grid grid-cols-12 gap-x-1 items-center">
        {/* Left side - Excluded uninvoiced work - 5 columns */}
        <div className="col-span-5 p-1.5 bg-surface-page rounded border border-default">
          <div className="flex flex-col">
            <div className="flex items-center gap-1 mb-1">
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[9px] font-medium bg-cyan-100 text-cyan-800/30">
                Uninvoiced
              </span>
              <div className="text-[9px] font-medium text-primary truncate">
                {decision.formattedDescription}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1">
              <div className="text-[8px] text-muted">
                <span className="font-bold uppercase tracking-wide">Date</span><br/>
                {formatDate(decision.paymentDate)}
              </div>
              <div className="text-[8px] text-muted text-right">
                <span className="font-bold uppercase tracking-wide">Amount</span><br/>
                {formatCurrency(decision.amount)}
              </div>
            </div>
          </div>
        </div>
        
        {/* Center connector - 2 columns */}
        <div className="col-span-2 flex items-center justify-center">
          <div className="bg-success-light/30 rounded-full p-1">
            <svg className="w-3 h-3 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </div>
        </div>
        
        {/* Right side - Projected Income - 5 columns */}
        <div className="col-span-5 p-1.5 bg-surface-page rounded border border-default">
          <div className="flex flex-col">
            <div className="flex items-center gap-1 mb-1">
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[9px] font-medium bg-success-light text-success/30">
                Projected
              </span>
              <div className="text-[9px] font-medium text-primary truncate">
                {decision.formattedDescription}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1">
              <div className="text-[8px] text-muted">
                <span className="font-bold uppercase tracking-wide">Date</span><br/>
                {formatDate(decision.paymentDate)}
              </div>
              <div className="text-[8px] text-muted text-right">
                <span className="font-bold uppercase tracking-wide">Usage</span><br/>
                <a 
                  href="https://onbord.harvestapp.com/reports/uninvoiced?kind=all_time"
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary-color hover:text-primary-color"
                  onClick={e => e.stopPropagation()}
                >
                  View Report
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Note at the bottom */}
      <div className="mt-1 text-[9px] text-muted pl-2 border-l-2 border-warning/50">
        <span className="font-medium text-warning">Note:</span> 
        This uninvoiced work was excluded because there is already a projected income from the same project scheduled within 3 days.
      </div>
    </div>
  );
};

export default UninvoicedWorkReconciliation;