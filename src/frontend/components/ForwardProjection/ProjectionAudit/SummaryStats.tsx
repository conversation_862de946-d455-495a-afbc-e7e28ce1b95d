import React from "react";
import { FilterSummary } from "../types/projection-audit-types";

interface SummaryStatsProps {
  summary: FilterSummary;
}

const SummaryStats: React.FC<SummaryStatsProps> = ({ summary }) => {
  const inclusionRate = Math.round(
    (summary.keptInvoices / (summary.totalInvoices || 1)) * 100,
  );

  return (
    <div className="bg-surface-card px-4 py-3 rounded-lg shadow-sm border border-default mb-4">
      <div className="flex flex-wrap items-center justify-between">
        <div className="flex items-center mb-2 md:mb-0">
          <svg
            className="w-4 h-4 text-blue-logo mr-1.5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
            />
          </svg>
          <h3 className="text-sm font-medium text-primary">
            Projection Summary
          </h3>
        </div>

        <div className="flex flex-wrap gap-x-3 gap-y-2">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-surface-page0 rounded-full mr-1.5"></div>
            <span className="text-xs text-muted mr-1">Total:</span>
            <span className="text-sm font-semibold text-primary">
              {summary.totalInvoices}
            </span>
          </div>

          <div className="flex items-center">
            <div className="w-2 h-2 bg-success-light0 rounded-full mr-1.5"></div>
            <span className="text-xs text-success mr-1">Included:</span>
            <span className="text-sm font-semibold text-success">
              {summary.keptInvoices}
            </span>
          </div>

          <div className="flex items-center">
            <div className="w-2 h-2 bg-error-light0 rounded-full mr-1.5"></div>
            <span className="text-xs text-error mr-1">Excluded:</span>
            <span className="text-sm font-semibold text-error">
              {summary.excludedInvoices}
            </span>
          </div>

          <div className="flex items-center">
            <div className="w-2 h-2 bg-primary-light0 rounded-full mr-1.5"></div>
            <span className="text-xs text-primary-color mr-1">Rate:</span>
            <span className="text-sm font-semibold text-primary-color">
              {inclusionRate}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SummaryStats;
