import React, { useState, useMemo } from "react";
import {
  FilterDecision,
  ProcessedDecision } from "../types/projection-audit-types";
import { processDecision } from "../utils/projection-audit-utils";
import { useIsDesktop } from "../../../hooks/useMediaQuery";
import DecisionListItem from "./DecisionListItem";
import { DataList } from "../../shared/lists";

/**
 * Consistent component for displaying decision descriptions
 * Exported for use in DecisionCard
 */import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
export const DecisionDescription: React.FC<{decision: ProcessedDecision;}> = ({
  decision
}) => {
  // Added export
  // Create a safe copy to avoid issues with readonly properties
  const formattedTitle = decision.formattedDescription ?
  decision.formattedDescription.replace("\n"," -") :
  `${decision.projectName} - ${decision.clientName ||"Unknown Client"}`;

  // Use the same formatting approach as in TransactionsList
  return (
    <div title={formattedTitle}>
      {""}
      {/* Removed max-w-[150px] */}
      <div className="font-medium">{decision.projectName}</div>
      <div className="text-muted text-[10px]">
        {decision.clientName ||"Unknown Client"}
      </div>
    </div>);

};

interface DecisionTableProps {
  decisions: FilterDecision[];
  activeTab:"all" |"kept" |"excluded";
  setActiveTab: (tab:"all" |"kept" |"excluded") => void;
  filter: string;
  setFilter: (filter: string) => void;
  totalCount: number;
  keptCount: number;
  excludedCount: number;
}

const DecisionTable: React.FC<DecisionTableProps> = ({
  decisions,
  activeTab,
  setActiveTab,
  filter,
  setFilter,
  totalCount,
  keptCount,
  excludedCount
}) => {
  // State for tracking expanded rows - Use string keys
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const isDesktop = useIsDesktop();

  // Toggle expanded state for a row
  const toggleRowExpansion = (index: number) => {
    // Use string index for state
    setExpandedRows((prev) => ({
      ...prev,
      [String(index)]: !prev[String(index)]
    }));
  };

  // Removed unused isRecentInvoice function

  // Process decisions for display
  const processedDecisions = useMemo(() => {
    console.log(
      `DecisionTable received ${decisions.length} decisions to process`
    );

    // CRITICAL DEBUGGING - Count kept vs excluded BEFORE any processing
    const keptBefore = decisions.filter((d) => d.action ==="kept").length;
    const excludedBefore = decisions.filter(
      (d) => d.action ==="excluded"
    ).length;
    console.log(
      `BEFORE ANY PROCESSING: ${keptBefore} kept, ${excludedBefore} excluded`
    );

    // CRITICAL INVOICE DEBUGGING - Check for real invoice decisions
    const realInvoiceDecisions = decisions.filter(
      (d) =>
      d.relatedInvoice && (
      d.reason.includes("Real invoice") ||
      d.reason.includes("direct capture"))
    );
    console.log(`FOUND ${realInvoiceDecisions.length} REAL INVOICE DECISIONS:`);
    realInvoiceDecisions.forEach((d, i) => {
      if (i < 5) {
        // Limit to first 5 to avoid spam
        console.log(`Real Invoice Decision ${i}:`, {
          id: d.id,
          projectName: d.projectName,
          reason: d.reason,
          relatedInvoiceId: d.relatedInvoiceId,
          relatedInvoiceNumber: d.relatedInvoiceNumber
        });
      }
    });

    // Log action values to debug any issues
    const actionValues = new Set(decisions.map((d) => d.action));
    console.log("UNIQUE ACTION VALUES:", Array.from(actionValues));

    // Log a few examples of each type
    const keptExamples = decisions.
    filter((d) => d.action ==="kept").
    slice(0, 2);
    const excludedExamples = decisions.
    filter((d) => d.action ==="excluded").
    slice(0, 2);

    if (keptExamples.length > 0) {
      console.log("KEPT EXAMPLE:", JSON.stringify(keptExamples[0], null, 2));
    } else {
      console.warn("NO KEPT DECISIONS FOUND IN INPUT!");
    }

    if (excludedExamples.length > 0) {
      console.log("EXCLUDED EXAMPLE:",
        JSON.stringify(excludedExamples[0], null, 2)
      );
    }

    // Create a safe copy of decisions to avoid modifying readonly objects
    const safeDecisions = decisions.map((d) => {
      // Ensure action property is correctly set
      // Ensure action property is correctly typed
      const actionValue =
      d.action ==="kept" ?"kept" :
      (d.action ==="excluded" ?"excluded" :"kept") as"kept" |"excluded";
      return {
        ...d,
        action: actionValue
      };
    });

    // Recheck kept count after safe copy
    const keptAfterSafe = safeDecisions.filter(
      (d) => d.action ==="kept"
    ).length;
    console.log(`AFTER SAFE COPY: ${keptAfterSafe} kept decisions`);

    // First apply tab filtering based on activeTab
    let filteredDecisions = safeDecisions;

    if (activeTab ==="kept") {
      filteredDecisions = filteredDecisions.filter((d) => d.action ==="kept");
      console.log(`Filtered to ${filteredDecisions.length} kept decisions`);
    } else if (activeTab ==="excluded") {
      filteredDecisions = filteredDecisions.filter(
        (d) => d.action ==="excluded"
      );
      console.log(`Filtered to ${filteredDecisions.length} excluded decisions`);
    }
    // For"all' tab - use all decisions
    console.log(
      `AFTER TAB FILTERING (${activeTab}): ${filteredDecisions.length} decisions`
    );

    // Then apply text filter if present
    if (filter) {
      const lowerFilter = filter.toLowerCase();
      const beforeCount = filteredDecisions.length;
      filteredDecisions = filteredDecisions.filter(
        (d) =>
        d.projectName &&
        d.projectName.toLowerCase().includes(lowerFilter) ||
        d.clientName && d.clientName.toLowerCase().includes(lowerFilter) ||
        d.reason && d.reason.toLowerCase().includes(lowerFilter)
      );
      console.log(
        `Applied text filter"${filter}": ${beforeCount} -> ${filteredDecisions.length} decisions`
      );
    }

    // Process each decision for display - use per-decision error handling to avoid losing all decisions
    console.log(
      `Processing ${filteredDecisions.length} decisions for display after filtering`
    );
    const processed = filteredDecisions.map((decision) => {
      try {
        const processed = processDecision(decision);
        return processed;
      } catch (error) {
        console.error("Error processing single decision:", error, decision);
        // Return a minimal valid decision that won't break rendering
        return {
          ...decision,
          projectName: decision.projectName ||"Unknown Project",
          clientName: decision.clientName ||"Unknown Client",
          formattedDescription: `${decision.projectName ||"Unknown"} - ${
          decision.clientName ||"Unknown"}`,

          ruleType: decision.rule ||"MEETS_CRITERIA",
          displayType:"Unknown",
          typeColor:"bg-surface-alt text-primary/30",
          invoiceType: decision.invoiceType ||"Uninvoiced Work", // Ensure invoiceType is always defined
          amount:
          typeof decision.amount ==="number" && !isNaN(decision.amount) ?
          decision.amount :
          0
        };
      }
    });

    // Count kept vs excluded AFTER processing
    const keptAfter = processed.filter((d) => d.action ==="kept").length;
    const excludedAfter = processed.filter(
      (d) => d.action ==="excluded"
    ).length;
    console.log(
      `AFTER PROCESSING: ${keptAfter} kept, ${excludedAfter} excluded`
    );

    return processed;
  }, [decisions, activeTab, filter]);

  // CRITICAL EMERGENCY FIX - add logging when component renders
  console.log(
    `DecisionTable RENDER - Received ${decisions.length} decisions, processed to ${processedDecisions.length}`
  );

  // Add even more debugging for processed decisions
  const keptProcessed = processedDecisions.filter(
    (d) => d.action ==="kept"
  ).length;
  const excludedProcessed = processedDecisions.filter(
    (d) => d.action ==="excluded"
  ).length;

  console.log(
    `FINAL PROCESSED DECISIONS: ${processedDecisions.length} total (${keptProcessed} kept, ${excludedProcessed} excluded)`
  );
  console.log(
    `DECISION TABS: activeTab=${activeTab}, showing ${processedDecisions.length} decisions`
  );

  // Debug - log the first few decisions if available
  if (decisions.length > 0) {
    console.log(
      `First decision sample: ${decisions[0].action} - ${decisions[0].id} - ${decisions[0].reason}`
    );
  } else {
    console.log("WARNING: No decisions passed to DecisionTable component!");
  }

  // Add ultra-detailed debug dump table just in case
  console.table(
    processedDecisions.map((d) => ({
      action: d.action,
      type: d.invoiceType,
      project: d.projectName,
      client: d.clientName,
      amount: d.amount
    }))
  );

  return (
    <div className="bg-surface-card shadow-md rounded-lg overflow-hidden decision-container">
      <div className="px-6 py-4 border-b border-default">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between">
          <div className="flex items-center mb-3">
            <svg
              className="w-5 h-5 text-blue-logo mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />

            </svg>
            <h3 className="text-base font-medium text-primary">
              Detailed Decision Log
            </h3>
          </div>
        </div>

        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex flex-grow items-center gap-2">
            {/* Tab controls */}
            <div className="inline-flex rounded-md shadow-sm">
              <Button variant="secondary"
              type="button"
              className={`relative inline-flex items-center px-4 py-2 rounded-l-md border border-strong text-sm font-medium ${
              activeTab ==="all" ?"bg-primary-light/30 text-primary-color z-10" :"bg-surface-card text-primary"}`
              }
              onClick={() => setActiveTab("all")}>

                All ({totalCount})
              </Button>
              <Button variant="secondary"
              type="button"
              className={`relative inline-flex items-center px-4 py-2 border-t border-b border-r border-l border-strong text-sm font-medium ${
              activeTab ==="kept" ?"bg-success-light/30 text-success z-10" :"bg-surface-card text-primary"}`
              }
              onClick={() => setActiveTab("kept")}>

                Included ({keptCount})
              </Button>
              <Button variant="secondary"
              type="button"
              className={`relative inline-flex items-center px-4 py-2 rounded-r-md border border-strong text-sm font-medium ${
              activeTab ==="excluded" ?"bg-error-light/30 text-error z-10" :"bg-surface-card text-primary"}`
              }
              onClick={() => setActiveTab("excluded")}>

                Excluded ({excludedCount})
              </Button>
            </div>
          </div>

          {/* Search input */}
          <div className="relative w-48 sm:w-56 md:w-64">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-4 w-4 text-subtle"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">

                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />

              </svg>
            </div>
            <Input
              type="text"
              className="block w-full pl-10 py-2 border border-strong rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
              placeholder="Search by project or reason"
              value={filter}
              onChange={(e) => setFilter(e.target.value)} />

          </div>
        </div>
      </div>

      {!isDesktop ?
      // Mobile view with cards
      <div>
          <DataList
          variant="default"
          density="default"
          data={processedDecisions}
          loading={false}
          empty={processedDecisions.length === 0}
          emptyMessage="No matching decisions found with current filters"
          emptyIcon={
          <svg
            className="w-5 h-5 mx-auto text-subtle mb-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24">

              <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />

            </svg>
          }
          emptyAction={
          <div className="text-xs mt-1 bg-warning-light/30 p-2 rounded">
              Debug info: Tab: {activeTab}, Total: {totalCount}, Kept:{""}
              {keptCount}, Excluded: {excludedCount}, Filtered:{""}
              {processedDecisions.length}
            </div>
          }
          keyExtractor={(decision, index) => `${decision.id}-${index}`}
          renderItem={(decision, index) =>
          <DecisionListItem
            decision={decision}
            isExpanded={!!expandedRows[String(index)]}
            onToggleExpand={() => toggleRowExpansion(index)}
            index={index}
            isMobile={true} />

          } />

        </div> :

      // Desktop view - simple table without DataList
      <div>
          <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-surface-page">
              <tr>
                <th className="px-4 py-2.5 text-xs font-medium text-muted uppercase tracking-wider text-center w-48">Decision</th>
                <th className="px-4 py-2.5 text-xs font-medium text-muted uppercase tracking-wider text-left min-w-[120px]">Description</th>
                <th className="px-4 py-2.5 text-xs font-medium text-muted uppercase tracking-wider text-left w-32">Date</th>
                <th className="px-4 py-2.5 text-xs font-medium text-muted uppercase tracking-wider text-left w-28">Amount</th>
                <th className="px-4 py-2.5 text-xs font-medium text-muted uppercase tracking-wider text-left w-24">Status</th>
                <th className="px-4 py-2.5 text-xs font-medium text-muted uppercase tracking-wider text-left w-40">Reason</th>
              </tr>
            </thead>
            <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
              {processedDecisions.length === 0 ?
              <tr>
                  <td colSpan={6} className="px-4 py-8 text-center">
                    <div className="text-muted">
                      <svg
                      className="w-5 h-5 mx-auto text-subtle mb-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">

                        <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />

                      </svg>
                      <p>No matching decisions found with current filters</p>
                      <div className="text-xs mt-1 bg-warning-light/30 p-2 rounded inline-block">
                        Debug info: Tab: {activeTab}, Total: {totalCount}, Kept:{""}
                        {keptCount}, Excluded: {excludedCount}, Filtered:{""}
                        {processedDecisions.length}
                      </div>
                    </div>
                  </td>
                </tr> :

              processedDecisions.map((decision, index) =>
              <DecisionListItem
                key={`${decision.id}-${index}`}
                decision={decision}
                isExpanded={!!expandedRows[String(index)]}
                onToggleExpand={() => toggleRowExpansion(index)}
                index={index}
                isMobile={false} />

              )
              }
            </tbody>
          </table>
          </div>
        </div>
      }
    </div>);

};

export default DecisionTable;