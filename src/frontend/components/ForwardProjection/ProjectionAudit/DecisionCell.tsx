import React from "react";
import {
  ProcessedDecision,
  FILTER_RULES,
} from "../types/projection-audit-types";
import DecisionBadge from "./DecisionBadge";
import { InvoiceDisplay } from "../../shared/InvoiceDisplay";

// Helper function to determine if an invoice is recent (within 5 days)
const isRecentInvoice = (date?: string): boolean => {
  if (!date) return false;
  try {
    const invoiceDate = new Date(date);
    if (isNaN(invoiceDate.getTime())) return false;

    const now = new Date();
    const diffTime = Math.abs(now.getTime() - invoiceDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 5;
  } catch (error) {
    return false;
  }
};

interface DecisionCellProps {
  decision: ProcessedDecision;
}

const DecisionCell: React.FC<DecisionCellProps> = ({ decision }) => {
  // Log decision props for debugging
  console.log("DecisionCell received:", {
    id: decision.id,
    ruleType: decision.ruleType,
    action: decision.action,
    relatedInvoiceId: decision.relatedInvoiceId,
    relatedInvoiceNumber: decision.relatedInvoiceNumber,
  });

  // SUPER CRITICAL DEBUG - If we have relatedInvoiceId but ruleType isn't REAL_INVOICE
  if (decision.relatedInvoiceId && decision.ruleType !== "REAL_INVOICE") {
    console.error(
      "!!! CRITICAL MISMATCH - Decision has relatedInvoiceId but wrong ruleType:",
      {
        id: decision.id,
        ruleType: decision.ruleType,
        shouldBe: "REAL_INVOICE",
        relatedInvoiceId: decision.relatedInvoiceId,
      },
    );
  }

  // Helper to safely handle potentially malformed data
  const safeString = (value: any): string => {
    if (value === null || value === undefined) {
      return "";
    }
    return typeof value === "object" ? JSON.stringify(value) : String(value);
  };

  // Special emergency case - if we have a relatedInvoiceId but wrong rule type
  if (decision.relatedInvoiceId) {
    return (
      <div className="flex items-center">
        {" "}
        {/* Removed space-x-1 */}
        {/* Always show Projected Income with strike-through - this is what we're excluding */}
        <span className="inline-flex items-center px-2.5 py-0.5 rounded border bg-success-light text-green-800 dark:bg-success-dark/30 dark:text-green-300 text-xs font-medium border-success dark:border-green-800/50 line-through whitespace-nowrap">
          <svg
            className="w-3 h-3 mr-1 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
              clipRule="evenodd"
            />
          </svg>
          Projected Income
        </span>
        {/* Arrow connector */}
        <svg
          className="h-3 w-3 text-muted"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M14 5l7 7m0 0l-7 7m7-7H3"
          />
        </svg>
        {/* REAL INVOICE GREEN BOX - Force it even if ruleType is wrong */}
        <InvoiceDisplay
          invoiceId={decision.relatedInvoiceId}
          invoiceNumber={
            decision.relatedInvoiceNumber || decision.relatedInvoiceId
          }
          issueDate={decision.relatedInvoiceDate}
        />
      </div>
    );
  }

  // Handle different rules - only used if no relatedInvoiceId
  switch (decision.ruleType) {
    case "REAL_INVOICE":
      return (
        <div className="flex items-center">
          {" "}
          {/* Removed space-x-1 */}
          {/* Excluded projected/uninvoiced with strikethrough */}
          <DecisionBadge decision={decision} isStrikethrough={true} />
          {/* Arrow connector */}
          <svg
            className="h-3 w-3 text-muted"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M14 5l7 7m0 0l-7 7m7-7H3"
            />
          </svg>
          {/* REAL INVOICE GREEN BOX - Should never be reached */}
          <span className="inline-flex items-center flex-shrink-0 px-2.5 py-0.5 rounded border border-error dark:border-red-800/30 text-xs font-medium bg-error-light text-red-800 dark:bg-error-dark/30 dark:text-red-300 whitespace-nowrap">
            Missing invoice ID (error)
          </span>
        </div>
      );

    case "UNINVOICED_WORK":
      return (
        <div className="flex items-center">
          {" "}
          {/* Removed space-x-1 */}
          {/* Excluded uninvoiced work with strikethrough */}
          <span className="inline-flex items-center px-2.5 py-0.5 rounded border bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300 text-xs font-medium border-cyan-200 dark:border-cyan-800/50 line-through">
            <svg
              className="w-3 h-3 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            Uninvoiced
          </span>
          {/* Arrow connector */}
          <svg
            className="h-3 w-3 text-muted"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M14 5l7 7m0 0l-7 7m7-7H3"
            />
          </svg>
          {/* Projected Income badge */}
          <span className="inline-flex items-center px-2.5 py-0.5 rounded border bg-success-light text-green-800 dark:bg-success-dark/30 dark:text-green-300 text-xs font-medium border-success dark:border-green-800/50">
            <svg
              className="w-3 h-3 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                clipRule="evenodd"
              />
            </svg>
            Projected
          </span>
        </div>
      );

    case "PAYMENT_TERMS":
      return (
        <div className="flex items-center">
          {" "}
          {/* Removed space-x-1 */}
          {/* Excluded due to payment terms */}
          <span className="inline-flex items-center px-2.5 py-0.5 rounded border bg-warning-light text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 text-xs font-medium border-warning dark:border-yellow-800/50 line-through">
            <svg
              className="w-3 h-3 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clipRule="evenodd"
              />
            </svg>
            Payment Terms
          </span>
          {/* Info tooltip icon */}
          <span
            className="inline-flex items-center text-subtle"
            title={`Outside projection window (Terms: ${decision.paymentTermsDays || "unknown"} days)`}
          >
            <svg
              className="w-3.5 h-3.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </span>
        </div>
      );

    default:
      // Default/kept - just show the badge
      return <DecisionBadge decision={decision} />;
  }
};

export default DecisionCell;
