import React from "react";
import { Button } from "@/frontend/components/ui/Button";

interface InfoAccordionProps {
  showInformation: boolean;
  setShowInformation: (show: boolean) => void;
}

const InfoAccordion: React.FC<InfoAccordionProps> = ({
  showInformation,
  setShowInformation
}) => {
  return (
    <div className="p-4 border-b border-default dark:border-default bg-primary-light/50 dark:bg-primary-dark/10">
      <Button variant="primary"

      onClick={() => setShowInformation(!showInformation)}>

        <div className="flex items-center">
          <svg className="w-5 h-5 text-primary-color dark:text-primary-light mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="font-medium text-sm text-primary-color dark:text-blue-300">Understanding Smart Forecast Decisions</span>
        </div>
        <svg
          className={`w-5 h-5 text-primary-color dark:text-primary-light transition-transform duration-200 ${showInformation ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24">

          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </Button>
      
      {showInformation &&
      <div className="mt-3 text-sm text-secondary dark:text-subtle border-l-2 border-blue-300 dark:border-blue-700 pl-4 py-0.5 ml-1.5">
          <p className="mb-2">
            Smart Forecast Decisions shows exactly how the system decides which income projections to include or exclude from your forecast, providing transparency into the decision-making process.
          </p>
          <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1 mt-3">Main Filtering Rules:</h4>
          <ul className="list-disc pl-5 space-y-1">
            <li><strong>Uninvoiced Work Rule:</strong> Prevents double-counting by excluding uninvoiced work when projected income exists within ±3 days</li>
            <li><strong>Real Invoice Rule:</strong> Gives precedence to actual invoices by excluding projected income when real invoices exist within ±5 days</li>
            <li><strong>Payment Terms Rule:</strong> Excludes projected income if it falls within payment terms from today (income expected too soon should already be invoiced)</li>
          </ul>
          <div className="mt-3 flex justify-between items-center">
            <span className="text-xs text-blue-800/80 dark:text-blue-300/80 italic">
              Click on rows with the <span className="px-1 py-0.5 bg-primary-light/70 dark:bg-primary-dark/30 rounded text-xs font-medium">Reconciled</span> badge to see detailed invoice relationships
            </span>
            <a href="#troubleshooting-section" className="text-xs text-primary-color dark:text-primary-light hover:underline">
              More details in Help docs →
            </a>
          </div>
        </div>
      }
    </div>);

};

export default InfoAccordion;