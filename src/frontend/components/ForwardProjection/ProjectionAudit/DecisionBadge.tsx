import React from "react";
import { ProcessedDecision } from "../types/projection-audit-types";
import { Badge } from "@/frontend/components/ui/Badge";

interface DecisionBadgeProps {
  decision: ProcessedDecision;
  isStrikethrough?: boolean;
}

const DecisionBadge: React.FC<DecisionBadgeProps> = ({ decision, isStrikethrough = false }) => {
  // Get display properties
  const ruleType = decision.ruleType || "MEETS_CRITERIA";
  // Use semantic colors from CSS variables instead of hardcoded Tailwind classes
  const typeColor = decision.typeColor || "bg-surface-alt text-primary";
  
  // Determine display type based on invoiceType and rule type for consistency
  let displayType = decision.displayType;
  
  // If ruleType is UNINVOICED_WORK and action is 'kept", always force "Uninvoiced'
  // If ruleType is UNINVOICED_WORK and action is 'excluded", force "Projected' 
  // since this is the related invoice
  if (ruleType === 'UNINVOICED_WORK') {
    if (decision.action === 'kept') {
      displayType = 'Uninvoiced';
    } else {
      displayType = 'Projected';  // For excluded UNINVOICED_WORK decisions, show as Projected
    }
  } else if (!displayType) {
    // For other cases, use invoiceType as a fallback
    if (decision.invoiceType === 'Projected Income') {
      displayType = 'Projected';
    } else if (decision.invoiceType === 'Uninvoiced Work') {
      displayType = 'Uninvoiced';
    } else if (decision.invoiceType === 'Invoice') {
      displayType = 'Invoice';
    } else {
      displayType = 'Unknown';
    }
  }
  
  // Determine icon based on type
  let icon = null;
  if (displayType === 'Projected') {
    icon = (
      <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
      </svg>
    );
  } else if (displayType === 'Uninvoiced') {
    icon = (
      <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    );
  } else if (ruleType === 'PAYMENT_TERMS') {
    icon = (
      <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
      </svg>
    );
  }
  
  // Apply strikethrough class if needed
  const contentClass = isStrikethrough ? 'line-through' : "";
  
  // Map display type to Badge variant
  const getVariant = (): "primary" | "secondary" | "success" | "warning" | "error" | "info" => {
    if (displayType === 'Projected') return 'info';
    if (displayType === 'Uninvoiced') return 'warning';
    if (displayType === 'Invoice') return 'success';
    return 'secondary';
  };

  return (
    <Badge variant={getVariant()} className={`flex-shrink-0 whitespace-nowrap ${contentClass}`}>
      {icon}
      <span className={contentClass}>{displayType}</span>
    </Badge>
  );
};

export default DecisionBadge;