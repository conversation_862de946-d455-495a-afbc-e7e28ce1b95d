import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react"; // Added useMemo back
import {
  getForwardProjection,
  getForwardProjectionWithDeals,
  getAvailableSnapshotDates,
  getProjectionSnapshot,
  createManualSnapshot,
} from "../../api/cashflow";
import {
  CashflowForecast,
  CustomExpense,
  ScenarioCashflowForecast,
} from "../../../types/financial"; // Corrected import path
import { useEvents, EVENTS } from "../../contexts"; // Assuming correct export from index
import { useLoading } from "../../contexts/LoadingContext";
import { calculateMonthlyEquivalent } from "./utils";

interface ProjectionContextValue {
  // State
  loading: boolean;
  error: string | null;
  projectionData: CashflowForecast | null;
  scenarioProjectionData: ScenarioCashflowForecast | null;
  timeframe: number;
  minBalanceThreshold: number;
  showMinBalanceLine: boolean;
  hasCustomExpenses: boolean;
  hoveredDate: string | null;
  highlightedDate: string | null; // Added for click interaction
  showScenarios: boolean; // Added for deal scenarios

  // Time Machine state
  isHistoricalView: boolean;
  historicalDate: string | null;
  availableSnapshotDates: string[];
  isCreatingSnapshot: boolean;

  // Calculated values
  totalInflows: number;
  totalOutflows: number;
  monthlyEquivalent: number;

  // Actions
  setTimeframe: (timeframe: number) => void;
  setMinBalanceThreshold: (threshold: number) => void;
  setShowMinBalanceLine: (show: boolean) => void;
  setHoveredDate: (date: string | null) => void;
  setHighlightedDate: (date: string | null) => void; // Added for click interaction
  setShowScenarios: (show: boolean) => void; // Added for deal scenarios
  loadProjectionData: (forceRefresh?: boolean) => Promise<void>; // Added forceRefresh param
  loadScenarioProjectionData: (forceRefresh?: boolean) => Promise<void>; // Added for deal scenarios
  handleTimeframeChange: (e: any) => void; // Revert back to any for now
  refreshProjection: () => void;

  // Time Machine actions
  loadHistoricalData: (date: string) => Promise<void>;
  exitHistoricalView: () => void;
  createSnapshot: () => Promise<void>;
}

interface ProjectionProviderProps {
  children: React.ReactNode; // Use React.ReactNode
  customExpenses?: CustomExpense[];
}

const ProjectionContext = createContext<ProjectionContextValue | undefined>(
  undefined,
);

export const ProjectionProvider: React.FC<ProjectionProviderProps> = ({
  // Use React.FC
  children,
  customExpenses = [],
}) => {
  // Get the events context
  const events = useEvents();
  const { setLoading: setGlobalLoading } = useLoading();

  const [loading, setLocalLoading] = useState<boolean>(false); // Ensure type
  const [error, setError] = useState<string | null>(null); // Ensure type
  const [projectionData, setProjectionData] = useState<CashflowForecast | null>(
    null,
  ); // Ensure type
  const [scenarioProjectionData, setScenarioProjectionData] =
    useState<ScenarioCashflowForecast | null>(null); // Added for deal scenarios
  const [timeframe, setTimeframe] = useState<number>(90); // Ensure type
  const [minBalanceThreshold, setMinBalanceThreshold] = useState<number>(20000); // Ensure type
  const [showMinBalanceLine, setShowMinBalanceLine] = useState<boolean>(true); // Ensure type
  const [hoveredDate, setHoveredDate] = useState<string | null>(null); // Ensure type
  const [highlightedDate, setHighlightedDate] = useState<string | null>(null); // Ensure type
  const [showScenarios, setShowScenariosState] = useState<boolean>(false); // Added for deal scenarios

  // Time Machine state
  const [isHistoricalView, setIsHistoricalView] = useState<boolean>(false);
  const [historicalDate, setHistoricalDate] = useState<string | null>(null);
  const [availableSnapshotDates, setAvailableSnapshotDates] = useState<
    string[]
  >([]);
  const [isCreatingSnapshot, setIsCreatingSnapshot] = useState<boolean>(false);

  // Track if a request is in progress to prevent redundant API calls
  const isFetchingRef = useRef<boolean>(false); // Ensure type
  const isScenarioFetchingRef = useRef<boolean>(false); // Added for deal scenarios
  // Track the last fetched timeframe and expenses to avoid duplicate requests
  const lastTimeframeRef = useRef<number | null>(null); // Ensure type
  const lastExpensesRef = useRef<string | null>(null); // Ensure type
  const lastScenarioTimeframeRef = useRef<number | null>(null); // Added for deal scenarios
  const lastScenarioExpensesRef = useRef<string | null>(null); // Added for deal scenarios

  // Check if we have custom expenses
  const hasCustomExpenses = customExpenses.length > 0;

  // Create a stable hash of custom expenses for comparison and dependency arrays
  const expensesHash = useMemo(
    () =>
      JSON.stringify(
        customExpenses.map((e) => ({
          id: e.id,
          amount: e.amount,
          date: e.date.toISOString(), // Ensure consistent date format
          frequency: e.frequency,
        })),
      ),
    [customExpenses],
  );

  /**
   * Load projection data
   * @param forceRefresh Optional flag to bypass cache
   */
  const loadProjectionData = useCallback(
    async (forceRefresh: boolean = false) => {
      // Skip if already fetching
      if (isFetchingRef.current && !forceRefresh) {
        // Allow forced refresh even if fetching
        console.log("Already fetching data, skipping redundant request");
        return;
      }

      // If we don't have projection data yet, always force a refresh
      if (!projectionData && !forceRefresh) {
        console.log("No projection data yet, forcing refresh");
        forceRefresh = true;
      }

      // expensesHash is now calculated above

      // Check if we should use cached data (unless forceRefresh is true)
      if (
        !forceRefresh &&
        lastTimeframeRef.current === timeframe &&
        lastExpensesRef.current === expensesHash &&
        projectionData
      ) {
        console.log("Same parameters already loaded, using existing data");
        return;
      }

      setLocalLoading(true);
      setGlobalLoading(true, "projection"); // Use projection loading indicator
      setError(null);
      isFetchingRef.current = true;

      try {
        // Get projection data with custom expenses
        // Get projection data with custom expenses
        // The 'data' object now includes filterDecisions directly from the API response
        const data = await getForwardProjection(timeframe, customExpenses);

        // Log if filterDecisions are present (optional)
        if (data.filterDecisions) {
          console.log(
            `ProjectionContext: Received ${data.filterDecisions.length} filter decisions from API`,
          );
        } else {
          console.warn(
            "ProjectionContext: No filterDecisions returned from API!",
          );
        }

        // Store the complete data object (including filterDecisions) in state
        setProjectionData(data);

        // Set loading false ONLY after successful data setting
        setLocalLoading(false);
        setGlobalLoading(false);

        // Update the last fetched parameters only if it wasn't a forced refresh
        // to allow subsequent non-forced calls to potentially use the cache.
        // If it was forced, we don't update cache keys, ensuring the *next* non-forced call refetches.
        // UPDATE: Always update cache keys so subsequent calls don't force refresh unnecessarily.
        lastTimeframeRef.current = timeframe;
        lastExpensesRef.current = expensesHash;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to load projection data";
        setError(errorMessage);
      } finally {
        // setLoading(false); // MOVED to the try block after setProjectionData
        // Ensure loading is false if an error occurred and it wasn't already set
        if (error) {
          setLocalLoading(false);
          setGlobalLoading(false);
        }
        isFetchingRef.current = false;
      }
    },
    [timeframe, expensesHash, projectionData],
  ); // Use expensesHash, restore projectionData for cache check

  // Refresh projection with current settings, forcing a cache bypass
  const refreshProjection = useCallback(() => {
    console.log("Forcing projection refresh...");
    loadProjectionData(true); // Pass true to force refresh
  }, [loadProjectionData]);

  /**
   * Load scenario projection data with deals
   * @param forceRefresh Optional flag to bypass cache
   */
  const loadScenarioProjectionData = useCallback(
    async (forceRefresh: boolean = false) => {
      // Skip if already fetching
      if (isScenarioFetchingRef.current && !forceRefresh) {
        console.log(
          "Already fetching scenario data, skipping redundant request",
        );
        return;
      }

      // If we don't have scenario projection data yet, always force a refresh
      if (!scenarioProjectionData && !forceRefresh) {
        console.log("No scenario projection data yet, forcing refresh");
        forceRefresh = true;
      }

      // Check if we should use cached data (unless forceRefresh is true)
      if (
        !forceRefresh &&
        lastScenarioTimeframeRef.current === timeframe &&
        lastScenarioExpensesRef.current === expensesHash &&
        scenarioProjectionData
      ) {
        console.log(
          "Same scenario parameters already loaded, using existing data",
        );
        return;
      }

      setLocalLoading(true);
      setGlobalLoading(true, "projection"); // Use projection loading indicator
      setError(null);
      isScenarioFetchingRef.current = true;

      try {
        // Get projection data with deals and custom expenses
        const data = await getForwardProjectionWithDeals(
          timeframe,
          customExpenses,
        );

        // Store the data in state
        setScenarioProjectionData(data);

        // Set loading false ONLY after successful data setting
        setLocalLoading(false);
        setGlobalLoading(false);

        // Update the last fetched parameters
        lastScenarioTimeframeRef.current = timeframe;
        lastScenarioExpensesRef.current = expensesHash;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to load scenario projection data";
        setError(errorMessage);
      } finally {
        // Ensure loading is false if an error occurred and it wasn't already set
        if (error) {
          setLocalLoading(false);
          setGlobalLoading(false);
        }
        isScenarioFetchingRef.current = false;
      }
    },
    [timeframe, expensesHash, scenarioProjectionData],
  );

  // Wrapper for setShowScenarios to ensure data is loaded when enabled
  const setShowScenarios = useCallback(
    (show: boolean) => {
      setShowScenariosState(show);
      if (show) {
        // Force refresh scenario data when scenarios are enabled
        loadScenarioProjectionData(true);
      }
    },
    [loadScenarioProjectionData],
  );

  /**
   * Handle timeframe change
   * @param e Change event
   */
  const handleTimeframeChange = useCallback(
    (e: any) => {
      // Revert back to any for now
      console.log("Timeframe dropdown clicked and changed!");
      const newTimeframe = parseInt(e.target.value, 10);
      setTimeframe(newTimeframe);

      // Force refresh both regular and scenario data when timeframe changes
      // This ensures the chart updates properly
      setTimeout(() => {
        loadProjectionData(true);
        if (showScenarios) {
          loadScenarioProjectionData(true);
        }
      }, 0);
    },
    [loadProjectionData, loadScenarioProjectionData, showScenarios],
  );

  // Log loading state
  useEffect(() => {
    console.log("Loading state:", loading);
    console.log("isFetching state:", isFetchingRef.current);
  }, [loading]);

  // Load projection data when timeframe changes (don't force refresh)
  useEffect(() => {
    loadProjectionData(); // Standard load, uses cache if applicable
  }, [timeframe]);

  // Load projection data when customExpenses changes (don't force refresh)
  useEffect(() => {
    // Standard load, uses cache if applicable. The effect below handles the initial load.
    // This effect handles subsequent changes to expensesHash.
    loadProjectionData();
  }, [expensesHash, loadProjectionData]); // Depend on the stable hash and load function

  // Subscribe to expense updates using the React Context API
  useEffect(() => {
    // Function to force refresh projection data when expenses are updated externally
    const forceRefreshData = () => {
      console.log("Expenses updated externally, forcing projection refresh.");
      loadProjectionData(true); // Force refresh on external update
    };

    // Subscribe to expense updates using the events context
    events.subscribe(EVENTS.EXPENSES_UPDATED, forceRefreshData);

    // Clean up subscription when component unmounts
    return () => {
      events.unsubscribe(EVENTS.EXPENSES_UPDATED, forceRefreshData);
    };
  }, [events, loadProjectionData]); // Assuming loadProjectionData is stable now

  // Load initial data when the provider mounts
  useEffect(() => {
    console.log(
      "ProjectionProvider mounted or expensesHash changed, forcing initial/updated data load.",
    );
    loadProjectionData(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expensesHash]); // Run initial load when expensesHash stabilizes (or changes)

  // Keep loading state in sync with isFetchingRef
  useEffect(() => {
    if (isFetchingRef.current && !loading) {
      setLocalLoading(true);
      setGlobalLoading(true, "projection"); // Use projection loading indicator
    }
  }, [loading]); // Intentionally only depend on loading

  // Load scenario data when showScenarios changes or timeframe changes
  useEffect(() => {
    if (showScenarios) {
      console.log(
        "Scenarios enabled or timeframe changed, loading scenario data",
      );
      loadScenarioProjectionData();
    }
  }, [showScenarios, timeframe, loadScenarioProjectionData]);

  /**
   * Load available snapshot dates
   */
  const loadAvailableSnapshotDates = useCallback(async () => {
    try {
      console.log("Loading available snapshot dates...");
      const dates = await getAvailableSnapshotDates(timeframe);
      setAvailableSnapshotDates(dates);
      console.log(`Loaded ${dates.length} available snapshot dates`);
    } catch (error) {
      console.error("Error loading snapshot dates:", error);
      setAvailableSnapshotDates([]);
    }
  }, [timeframe]);

  /**
   * Load historical data for a specific date
   */
  const loadHistoricalData = useCallback(
    async (date: string) => {
      setLocalLoading(true);
      setGlobalLoading(true, "projection");
      setError(null);

      try {
        console.log(`Loading historical data for ${date}...`);
        const snapshotData = await getProjectionSnapshot(date, timeframe);

        if (!snapshotData) {
          throw new Error(`No snapshot available for ${date}`);
        }

        setProjectionData(snapshotData);
        setHistoricalDate(date);
        setIsHistoricalView(true);
        console.log(`Successfully loaded historical data for ${date}`);
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to load historical data";
        setError(errorMessage);
        console.error("Error loading historical data:", error);
      } finally {
        setLocalLoading(false);
        setGlobalLoading(false);
      }
    },
    [timeframe, setGlobalLoading],
  );

  /**
   * Exit historical view and return to current projection
   */
  const exitHistoricalView = useCallback(() => {
    console.log("Exiting historical view...");
    setIsHistoricalView(false);
    setHistoricalDate(null);
    refreshProjection(); // Reload current data
  }, [refreshProjection]);

  /**
   * Create a manual snapshot of the current cashflow projection
   */
  const createSnapshot = useCallback(async () => {
    try {
      setIsCreatingSnapshot(true);
      console.log("Creating manual snapshot...");

      // Create snapshot with current timeframe and custom expenses
      const snapshotDate = await createManualSnapshot(
        timeframe,
        customExpenses,
      );

      if (snapshotDate) {
        console.log(`Successfully created snapshot for ${snapshotDate}`);
        // Refresh the list of available dates
        await loadAvailableSnapshotDates();
        return snapshotDate;
      } else {
        throw new Error("Failed to create snapshot");
      }
    } catch (error) {
      console.error("Error creating snapshot:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(`Failed to create snapshot: ${errorMessage}`);
    } finally {
      setIsCreatingSnapshot(false);
    }
  }, [timeframe, customExpenses, loadAvailableSnapshotDates]);

  // Load available snapshot dates when component mounts or timeframe changes
  useEffect(() => {
    if (!isHistoricalView) {
      loadAvailableSnapshotDates();
    }
  }, [timeframe, loadAvailableSnapshotDates, isHistoricalView]);

  // Only using custom expenses for projections

  // Calculate totals
  const totalInflows =
    projectionData?.dailyCashflow?.reduce(
      (sum: number, day) => sum + day.inflows,
      0,
    ) || 0;
  const totalOutflows =
    projectionData?.dailyCashflow?.reduce(
      (sum: number, day) => sum + day.outflows,
      0,
    ) || 0;

  const monthlyEquivalent = calculateMonthlyEquivalent(customExpenses);

  const value: ProjectionContextValue = {
    loading,
    error,
    projectionData,
    scenarioProjectionData,
    timeframe,
    minBalanceThreshold,
    showMinBalanceLine,
    hasCustomExpenses,
    hoveredDate,
    highlightedDate,
    showScenarios,
    isHistoricalView,
    historicalDate,
    availableSnapshotDates,
    isCreatingSnapshot,
    totalInflows,
    totalOutflows,
    monthlyEquivalent,
    setTimeframe,
    setMinBalanceThreshold,
    setShowMinBalanceLine,
    setHoveredDate,
    setHighlightedDate,
    setShowScenarios,
    loadProjectionData,
    loadScenarioProjectionData,
    handleTimeframeChange,
    refreshProjection,
    loadHistoricalData,
    exitHistoricalView,
    createSnapshot,
  };

  return (
    <ProjectionContext.Provider value={value}>
      {children}
    </ProjectionContext.Provider>
  );
};

export const useProjection = (): ProjectionContextValue => {
  const context = useContext(ProjectionContext);
  if (context === undefined) {
    throw new Error("useProjection must be used within a ProjectionProvider");
  }
  return context;
};
