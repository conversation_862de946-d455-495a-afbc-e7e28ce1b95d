import React from "react";
import { useProjection } from ".";

/**
 * Component for threshold settings
 */import { Input } from "@/frontend/components/ui/Input";
import { Checkbox } from "@/frontend/components/ui/Checkbox";
const ThresholdSettings = () => {
  const {
    showMinBalanceLine,
    minBalanceThreshold,
    setShowMinBalanceLine,
    setMinBalanceThreshold
  } = useProjection();

  return (
    <div className="flex flex-wrap items-center justify-between xs:justify-start gap-1 xs:gap-2 sm:gap-4">
      <div className="flex items-center">
        <Checkbox
          id="showMinBalance"
          checked={showMinBalanceLine}
          onChange={(e) => setShowMinBalanceLine(e.target.checked)}
          label="Show min balance"
          className="text-2xs xs:text-xs sm:text-sm"
        />
      </div>
      
      <div className="flex items-center">
        <label htmlFor="minBalanceThreshold" className="text-2xs xs:text-xs sm:text-sm mr-1 sm:mr-2 text-primary dark:text-subtle">
          Threshold:
        </label>
        <Input
          type="number"
          id="minBalanceThreshold"
          value={minBalanceThreshold}
          onChange={(e) => setMinBalanceThreshold(Number(e.target.value))}
          className="form-input py-0.5 sm:py-1 px-1 sm:px-2 w-16 sm:w-24 text-2xs xs:text-xs sm:text-sm"
          disabled={!showMinBalanceLine} />

      </div>
    </div>);

};

export default ThresholdSettings;