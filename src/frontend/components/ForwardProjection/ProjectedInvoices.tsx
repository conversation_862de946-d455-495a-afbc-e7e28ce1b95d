import React, { useState, useMemo } from "react";
import { formatCurrency, formatDate } from "./utils";
import { ProjectedInvoice } from "../../api/harvest";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Checkbox } from "@/frontend/components/ui/Checkbox";

export interface InvoiceFilters {
  types: string[];
  clients: string[];
  searchText: string;
  amountRange: [number | null, number | null];
}

interface ProjectedInvoicesProps {
  projectedInvoices: ProjectedInvoice[];
}

const ProjectedInvoices: React.FC<ProjectedInvoicesProps> = ({ projectedInvoices }) => {
  // Define filters state
  const [filters, setFilters] = useState<InvoiceFilters>({
    types: [],
    clients: [],
    searchText:"',
    amountRange: [null, null]
  });

  // State for filter accordion
  const [filtersExpanded, setFiltersExpanded] = useState<boolean>(false);

  // Toggle filters visibility
  const toggleFilters = () => {
    setFiltersExpanded(!filtersExpanded);
  };

  // Extract unique invoice types and clients for filter options
  const invoiceTypes = useMemo(() => {
    const typesSet = new Set<string>();
    projectedInvoices.forEach((invoice) => {
      typesSet.add(invoice.type);
    });
    return Array.from(typesSet);
  }, [projectedInvoices]);

  const clientNames = useMemo(() => {
    const clientsSet = new Set<string>();
    projectedInvoices.forEach((invoice) => {
      clientsSet.add(invoice.clientName);
    });
    return Array.from(clientsSet);
  }, [projectedInvoices]);

  // Filter invoices based on current filters
  const filteredInvoices = useMemo(() => {
    return projectedInvoices.filter((invoice) => {
      // Filter by type
      if (filters.types.length > 0 && !filters.types.includes(invoice.type)) {
        return false;
      }

      // Filter by client
      if (filters.clients.length > 0 && !filters.clients.includes(invoice.clientName)) {
        return false;
      }

      // Filter by search text (project name or client name)
      if (filters.searchText &&
      !invoice.projectName.toLowerCase().includes(filters.searchText.toLowerCase()) &&
      !invoice.clientName.toLowerCase().includes(filters.searchText.toLowerCase())) {
        return false;
      }

      // Filter by amount range
      if (filters.amountRange[0] !== null && invoice.amount < filters.amountRange[0]) {
        return false;
      }
      if (filters.amountRange[1] !== null && invoice.amount > filters.amountRange[1]) {
        return false;
      }

      return true;
    });
  }, [projectedInvoices, filters]);

  // Group invoices by type for better display
  // Note: We no longer include outstanding invoices as they're handled by the cashflow projection
  const uninvoicedWork = filteredInvoices.filter((inv) => inv.type ==="uninvoiced_work');
  const futureWork = filteredInvoices.filter((inv) => inv.type ==="future_work');

  // Calculate total projected income
  const totalProjected = filteredInvoices.reduce((sum, inv) => sum + inv.amount, 0);

  // Get type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case"uninvoiced_work':return'Uninvoiced Work';
      case"future_work':return'Future Work';
      default:return type;
    }
  };

  // Get type color class
  const getTypeColorClass = (type: string) => {
    switch (type) {
      case"uninvoiced_work':return'bg-accent-light text-accent';
      case"future_work':return'bg-success-light text-success';
      default:return "bg-surface-alt text-primary";
    }
  };

  // Handle filter changes
  const handleTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const type = e.target.value;
    const newTypes = e.target.checked ?
    [...filters.types, type] :
    filters.types.filter((t) => t !== type);

    setFilters({
      ...filters,
      types: newTypes
    });
  };

  const handleClientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const client = e.target.value;
    const newClients = e.target.checked ?
    [...filters.clients, client] :
    filters.clients.filter((c) => c !== client);

    setFilters({
      ...filters,
      clients: newClients
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({
      ...filters,
      searchText: e.target.value
    });
  };

  const handleAmountMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const min = e.target.value ==="' ? null : parseFloat(e.target.value);
    setFilters({
      ...filters,
      amountRange: [min, filters.amountRange[1]]
    });
  };

  const handleAmountMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const max = e.target.value ==="' ? null : parseFloat(e.target.value);
    setFilters({
      ...filters,
      amountRange: [filters.amountRange[0], max]
    });
  };

  const handleClearFilters = () => {
    setFilters({
      types: [],
      clients: [],
      searchText:"',
      amountRange: [null, null]
    });
  };

  return (
    <div className="py-2 md:py-4 px-2 md:px-4 onboarding-projected-invoices">
      <h2 className="text-lg font-medium text-primary mb-3">
        Projected Income
      </h2>
      
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted">Total Projected Income</div>
          <div className="text-lg font-semibold text-success">{formatCurrency(totalProjected)}</div>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2 mb-4">
        <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColorClass('uninvoiced_work')} border-accent/30`}>
          Uninvoiced: {uninvoicedWork.length}
        </span>
        <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColorClass('future_work')} border-success/30`}>
          Future: {futureWork.length}
        </span>
      </div>
      
      {/* Filter accordion */}
      <div className="mb-4">
        <div className="border border-subtle rounded-lg overflow-hidden shadow-sm">
          {/* Accordion header */}
          <div className="flex items-center justify-between w-full">
            <Button variant="secondary"
            type="button"
            onClick={toggleFilters}
            className="flex items-center grow justify-between text-left"
            aria-expanded={filtersExpanded}
            aria-controls="income-filters-panel">

              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                <span className="font-medium">Filter Projected Income</span>
                {(filters.types.length > 0 || filters.clients.length > 0 || filters.searchText ||
                filters.amountRange[0] !== null || filters.amountRange[1] !== null) &&
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded border border-primary/30 text-xs font-medium bg-primary-light text-primary/40">
                    Active
                  </span>
                }
              </div>
              <svg className={`w-5 h-5 transition-transform duration-200 ${filtersExpanded ? "transform rotate-180" :''}`} fill="currentColor" viewBox="0 0 20 20'>
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Button>
            {(filters.types.length > 0 || filters.clients.length > 0 || filters.searchText ||
            filters.amountRange[0] !== null || filters.amountRange[1] !== null) &&
            <Button variant="ghost"
            onClick={handleClearFilters}>


                Clear All
              </Button>
            }
          </div>
          
          {/* Filters UI - conditionally rendered based on accordion state */}
          {filtersExpanded &&
          <div id="income-filters-panel" className="bg-surface-card p-4">
          
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Search filter */}
            <div className="lg:col-span-2">
              <label htmlFor="invoice-search-filter" className="block text-xs font-medium text-primary mb-1">
                Search
              </label>
              <Input
                  type="text"
                  id="invoice-search-filter"
                  value={filters.searchText}
                  onChange={handleSearchChange}
                  placeholder="Search project or client..."
                  className="w-full" />

            </div>
            
            {/* Amount range */}
            <div className="lg:col-span-2">
              <label className="block text-xs font-medium text-primary mb-1">
                Amount Range
              </label>
              <div className="flex space-x-2">
                <Input
                    type="number"
                    value={filters.amountRange[0] === null ?"' : filters.amountRange[0]}
                    onChange={handleAmountMinChange}
                    placeholder="Min"
                    className="w-full" />

                <Input
                    type="number"
                    value={filters.amountRange[1] === null ?"' : filters.amountRange[1]}
                    onChange={handleAmountMaxChange}
                    placeholder="Max"
                    className="w-full" />

              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            {/* Invoice types filter */}
            {invoiceTypes.length > 0 &&
              <div>
                <label className="block text-xs font-medium text-primary mb-2">
                  Invoice Types
                </label>
                <div className="flex flex-wrap gap-2">
                  {invoiceTypes.map((type) => {
                    const labelElement = (
                      <span
                        className={`${getTypeColorClass(type)} text-xs px-2 py-1 rounded border ${type ==="uninvoiced_work' ?'border-accent/30' :'border-success/30'}`}
                      >
                        {getTypeLabel(type)}
                      </span>
                    );
                    return (
                      <Checkbox
                        key={type}
                        id={`invoice-type-${type}`}
                        value={type}
                        checked={filters.types.includes(type)}
                        onChange={handleTypeChange}
                        label={labelElement}
                      />
                    );
                  })}
                </div>
              </div>
              }
            
            {/* Clients filter */}
            {clientNames.length > 0 &&
              <div>
                <label className="block text-xs font-medium text-primary mb-2">
                  Clients
                </label>
                <div className="flex flex-wrap gap-2">
                  {clientNames.map((client) => (
                    <Checkbox
                      key={client}
                      id={`client-${client}`}
                      value={client}
                      checked={filters.clients.includes(client)}
                      onChange={handleClientChange}
                      label={client}
                    />
                  ))}
                </div>
              </div>
              }
          </div>
            </div>
          }
        </div>
      </div>
      
      <div className="w-full">
        {/* Desktop view - Grid layout for md screens and above */}
        <div className="hidden md:grid grid-cols-12 gap-2">
          {/* Table header */}
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-muted uppercase">Project</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-muted uppercase">Client</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-muted uppercase">Type</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-muted uppercase">Amount</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-muted uppercase">Invoice Date</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-muted uppercase">Payment Date</div>
          
          {/* Divider */}
          <div className="col-span-12 border-t border-default my-1"></div>
          
          {/* Invoice rows */}
          {filteredInvoices.length > 0 ?
          filteredInvoices.
          sort((a, b) => new Date(a.paymentDate).getTime() - new Date(b.paymentDate).getTime()).
          map((invoice) =>
          <React.Fragment key={invoice.id}>
                  {/* Project */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm font-medium text-primary truncate">{invoice.projectName}</div>
                  </div>
                  
                  {/* Client */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-muted truncate">{invoice.clientName}</div>
                  </div>
                  
                  {/* Type */}
                  <div className="col-span-2 py-3 px-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColorClass(invoice.type)} ${invoice.type ==="uninvoiced_work' ?'border-accent/30' :'border-success/30'}`}>
                      {getTypeLabel(invoice.type)}
                    </span>
                  </div>
                  
                  {/* Amount */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-success font-medium">{formatCurrency(invoice.amount)}</div>
                  </div>
                  
                  {/* Invoice Date */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-muted">{formatDate(invoice.invoiceDate)}</div>
                  </div>
                  
                  {/* Payment Date */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-muted">{formatDate(invoice.paymentDate)}</div>
                  </div>
                  
                  {/* Divider after each row */}
                  <div className="col-span-12 border-t border-default my-1"></div>
                </React.Fragment>
          ) :

          <div className="col-span-12 py-4 text-sm text-center text-muted">
              {projectedInvoices.length > 0 ? "No invoices match the current filters. Try adjusting your filter criteria." :'No projected invoices available.'}
            </div>
          }
        </div>
        
        {/* Mobile card view - visible below md screens */}
        <div className="md:hidden space-y-2">
          {filteredInvoices.length > 0 ?
          filteredInvoices.
          sort((a, b) => new Date(a.paymentDate).getTime() - new Date(b.paymentDate).getTime()).
          map((invoice) =>
          <div
            key={invoice.id}
            className={`py-2.5 px-3 rounded-md border border-subtle bg-surface-card shadow-sm mb-2 tap-highlight-transparent touch-manipulation mobile-active-feedback mobile-card`}>

                  {/* Top section: Dates and amount */}
                  <div className="mb-2">
                    {/* Invoice and payment dates */}
                    <div className="flex justify-between items-center mb-1">
                      <div className="text-xs text-muted">
                        <span className="inline-block mr-2">
                          <svg className="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Invoice: {formatDate(invoice.invoiceDate)}
                        </span>
                        <span className="inline-block">
                          <svg className="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                          </svg>
                          Payment: {formatDate(invoice.paymentDate)}
                        </span>
                      </div>
                      
                      <span className="text-sm font-medium text-success">
                        {formatCurrency(invoice.amount)}
                      </span>
                    </div>
                    
                    {/* Project name */}
                    <p className="font-medium text-sm truncate" title={invoice.projectName}>
                      {invoice.projectName}
                    </p>
                  </div>
                  
                  {/* Bottom row: client name and invoice type */}
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-muted truncate max-w-[60%]">
                      {invoice.clientName}
                    </div>
                    
                    <span className={`px-1.5 py-0.5 text-xs font-medium rounded border whitespace-nowrap ${getTypeColorClass(invoice.type)} ${invoice.type ==="uninvoiced_work' ?'border-accent/30' :'border-success/30'}`}>
                      {getTypeLabel(invoice.type)}
                    </span>
                  </div>
                </div>
          ) :

          <div className="py-4 text-sm text-center text-muted">
              {projectedInvoices.length > 0 ? "No invoices match the current filters. Try adjusting your filter criteria." :'No projected invoices available.'}
            </div>
          }
        </div>
      </div>
    </div>);

};

export default ProjectedInvoices;