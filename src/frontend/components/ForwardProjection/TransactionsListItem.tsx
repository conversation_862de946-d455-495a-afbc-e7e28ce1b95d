import React from "react";
import { ProcessedTransaction } from "./types/transactions-list-types";
import { formatDate, formatCurrency } from "./utils";
import { isXeroTransaction } from "./utils/xero-utils";
import {
  isGSTTransaction,
  extractInvoiceId,
} from "./utils/transactions-list-utils";
import { getCurrentQuarterInfo } from "../../utils/quarter-utils";
import XeroBadge from "../shared/XeroBadge";
import HarvestBadge from "../shared/HarvestBadge";
import PredictedBadge from "../shared/PredictedBadge";
import AccruedBadge from "../shared/AccruedBadge";
import PercentageChange from "./Transactions/PercentageChange";

interface TransactionsListItemProps {
  transaction: ProcessedTransaction;
  isHighlighted: boolean;
  index: number;
  isMobile: boolean;
}

/**
 * Modern unified TransactionsListItem component with container queries
 * Automatically adapts between card and table layouts based on container size
 * Consolidates badge logic from both mobile and desktop views
 */
export const TransactionsListItem = ({
  transaction,
  isHighlighted,
  index,
  isMobile,
}: TransactionsListItemProps) => {
  const {
    isExpense,
    isProjectedInvoice,
    typeColor,
    percentChange,
    runningBalance,
    id,
    source,
    type,
    metadata,
    description,
  } = transaction;

  const quarterInfo = getCurrentQuarterInfo();

  // Determine if it"s a Harvest invoice for description formatting
  const isHarvestInvoice =
    (source === "harvest" && type === "invoice") || isProjectedInvoice;

  // Consolidated badge logic (previously duplicated in card and table views)
  const getBadgeContent = () => {
    let badgeContent;
    let badgeLink: string | null = null;

    if (
      source === "harvest" &&
      type === "invoice" &&
      id.startsWith("future-work-")
    ) {
      badgeContent = (
        <>
          <svg
            className="inline-block w-3 h-3 mr-1"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
              clipRule="evenodd"
            />
          </svg>
          Projected Income
        </>
      );
    } else if (
      source === "harvest" &&
      type === "invoice" &&
      id.startsWith("uninvoiced-")
    ) {
      badgeContent = (
        <>
          <svg
            className="inline-block w-3 h-3 mr-1"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
              clipRule="evenodd"
            />
          </svg>
          Uninvoiced Work
        </>
      );
    } else if (source === "harvest" && type === "invoice") {
      const isRealHarvestInvoice =
        id.startsWith("harvest-invoice-") ||
        id.startsWith("outstanding-invoice-");
      if (isRealHarvestInvoice) {
        const invoiceId = extractInvoiceId(id);
        badgeLink = `https://onbord.harvestapp.com/invoices/${invoiceId}`;
        const issueDate = metadata?.issue_date
          ? new Date(metadata.issue_date)
          : null;
        const isRecent =
          issueDate &&
          !isNaN(issueDate.getTime()) &&
          (new Date().getTime() - issueDate.getTime()) /
            (1000 * 60 * 60 * 24) <=
            5;

        badgeContent = (
          <>
            {isRecent && (
              <span
                className="inline-block w-2 h-2 bg-primary-light0 rounded-full mr-1.5 animate-pulse"
                title="Recently created invoice"
              ></span>
            )}
            Invoice {metadata?.invoiceNumber || "N/A"}
            <svg
              className="inline-block w-3 h-3 ml-1 opacity-70 group-hover:opacity-100"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              ></path>
            </svg>
          </>
        );
      } else {
        badgeContent = "Invoice";
      }
    } else {
      badgeContent = type.startsWith("custom_expense_")
        ? type.replace("custom_expense_", "")
        : source || "Unknown";
    }

    return { badgeContent, badgeLink };
  };

  const { badgeContent, badgeLink } = getBadgeContent();
  const BadgeComponent = badgeLink ? "a" : "span";

  // Background color based on highlight state and index
  const getBackgroundColorClass = () => {
    if (isHighlighted) {
      return "bg-primary-light";
    }
    return index % 2 === 0
      ? "bg-surface-card"
      : "bg-surface-page/50";
  };

  const bgColorClass = getBackgroundColorClass();

  // Mobile card view
  if (isMobile) {
    return (
      <div
        className={`p-4 rounded-lg border border-default ${bgColorClass} ${
          isHighlighted ? "border-l-4 border-l-primary" : ""
        }`}
      >
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center min-w-0 flex-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              className={`w-5 h-5 mr-2 flex-shrink-0 ${
                isExpense ? "text-accent" : "text-success"
              }`}
            >
              {isExpense ? (
                <path
                  fillRule="evenodd"
                  d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                  clipRule="evenodd"
                />
              ) : (
                <path
                  fillRule="evenodd"
                  d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z"
                  clipRule="evenodd"
                />
              )}
            </svg>
            <div className="min-w-0 flex-1">
              <div className="text-sm text-secondary mb-1">
                {formatDate(transaction.date.toString())}
              </div>
              <div className="font-medium text-primary">
                {isHarvestInvoice ? (
                  <>
                    <div className="truncate">
                      {metadata?.projectName || "Unknown Project"}
                    </div>
                    <div className="text-xs text-muted truncate">
                      {metadata?.clientName || "Unknown Client"}
                    </div>
                  </>
                ) : (
                  <div className="truncate">{description || "Unknown"}</div>
                )}
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end flex-shrink-0 ml-3">
            <div
              className={`font-medium text-lg ${
                isExpense ? "text-accent" : "text-success"
              }`}
            >
              {formatCurrency(transaction.amount)}
            </div>
            <div className="mt-1">
              <PercentageChange percentChange={percentChange} />
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-1">
            {/* Xero badge */}
            {isXeroTransaction(transaction) && <XeroBadge size="sm" />}

            {/* Harvest badge - only for real invoices */}
            {source === "harvest" &&
              type === "invoice" &&
              (id.startsWith("harvest-invoice-") ||
                id.startsWith("outstanding-invoice-")) && (
                <HarvestBadge size="sm" />
              )}

            {/* GST/BAS badges */}
            {isGSTTransaction(transaction) &&
              transaction.metadata?.usedProjectedAmount === true && (
                <PredictedBadge size="sm" quarterInfo={quarterInfo} />
              )}
            {isGSTTransaction(transaction) &&
              transaction.metadata?.usedProjectedAmount === false && (
                <AccruedBadge size="sm" quarterInfo={quarterInfo} />
              )}

            <BadgeComponent
              className={`inline-flex items-center px-1.5 py-0.5 rounded border text-xs font-medium group ${typeColor} ${
                badgeLink ? "hover:opacity-80 transition-opacity" : ""
              }`}
              href={badgeLink ?? undefined}
              target={badgeLink ? "_blank" : undefined}
              rel={badgeLink ? "noopener noreferrer" : undefined}
              title={badgeLink ? "View invoice in Harvest" : undefined}
            >
              {badgeContent}
            </BadgeComponent>
          </div>

          <div
            className={`text-sm font-medium ${
              runningBalance >= 0
                ? "text-primary"
                : "text-accent"
            }`}
          >
            {formatCurrency(runningBalance)}
          </div>
        </div>
      </div>
    );
  }

  // Desktop table row view
  return (
    <tr
      className={`${bgColorClass} ${
        isHighlighted ? "border-l-4 border-l-primary" : ""
      }`}
    >
      {/* Flow Icon */}
      <td className="px-4 py-2.5 text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          className={`w-5 h-5 inline-block ${
            isExpense ? "text-accent" : "text-success"
          }`}
        >
          {isExpense ? (
            <path
              fillRule="evenodd"
              d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
              clipRule="evenodd"
            />
          ) : (
            <path
              fillRule="evenodd"
              d="M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z"
              clipRule="evenodd"
            />
          )}
        </svg>
      </td>

      {/* Date */}
      <td className="px-4 py-2.5 whitespace-nowrap text-primary">
        {formatDate(transaction.date.toString())}
      </td>

      {/* Description */}
      <td className="px-4 py-2.5 text-primary min-w-0">
        {isHarvestInvoice ? (
          <>
            <div className="font-medium truncate">
              {metadata?.projectName || "Unknown Project"}
            </div>
            <div className="text-xs text-muted truncate">
              {metadata?.clientName || "Unknown Client"}
            </div>
          </>
        ) : (
          <div className="truncate">{description || "Unknown"}</div>
        )}
      </td>

      {/* Type */}
      <td className="px-4 py-2.5 text-primary">
        <span className="capitalize">
          {type.replace(/_/g, " ").replace("custom expense ", "")}
        </span>
      </td>

      {/* Source/Badge */}
      <td className="px-4 py-2.5">
        <div className="flex items-center">
          {/* Xero badge */}
          {isXeroTransaction(transaction) && <XeroBadge className="mr-1.5" />}

          {/* Harvest badge - only for real invoices */}
          {source === "harvest" &&
            type === "invoice" &&
            (id.startsWith("harvest-invoice-") ||
              id.startsWith("outstanding-invoice-")) && (
              <HarvestBadge className="mr-1.5" />
            )}

          {/* GST/BAS badges */}
          {isGSTTransaction(transaction) &&
            transaction.metadata?.usedProjectedAmount === true && (
              <PredictedBadge className="mr-1.5" quarterInfo={quarterInfo} />
            )}
          {isGSTTransaction(transaction) &&
            transaction.metadata?.usedProjectedAmount === false && (
              <AccruedBadge className="mr-1.5" quarterInfo={quarterInfo} />
            )}

          <BadgeComponent
            className={`inline-flex items-center px-1.5 py-0.5 rounded border text-xs font-medium group ${typeColor} ${
              badgeLink ? "hover:opacity-80 transition-opacity" : ""
            }`}
            href={badgeLink ?? undefined}
            target={badgeLink ? "_blank" : undefined}
            rel={badgeLink ? "noopener noreferrer" : undefined}
            title={badgeLink ? "View invoice in Harvest" : undefined}
          >
            {badgeContent}
          </BadgeComponent>
        </div>
      </td>

      {/* Amount */}
      <td className="px-4 py-2.5 text-right">
        <div className="flex items-center justify-end gap-0.5">
          <span
            className={`font-medium whitespace-nowrap ${
              isExpense ? "text-accent" : "text-success"
            }`}
          >
            {formatCurrency(transaction.amount)}
          </span>
          <div className="scale-90 origin-right">
            <PercentageChange percentChange={percentChange} />
          </div>
        </div>
      </td>

      {/* Running Balance */}
      <td className="px-4 py-2.5 text-right">
        <div
          className={`font-medium whitespace-nowrap ${
            runningBalance >= 0
              ? "text-primary"
              : "text-accent"
          }`}
        >
          {formatCurrency(runningBalance)}
        </div>
      </td>
    </tr>
  );
};

export default TransactionsListItem;
