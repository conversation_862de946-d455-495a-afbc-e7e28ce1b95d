import { useState, useEffect } from "react";
import { getLateInvoices } from "../../api/harvest";
import { formatCurrency } from "../../utils/format";

interface LatePaymentsNetEffectProps {
  className?: string;
  inSummary?: boolean;
}

/**
 * Component for showing the net effect of late payments (invoices minus bills)
 * Uses green styling for positive net (more money coming in than going out)
 * Uses red styling for negative net (more money going out than coming in)
 */
const LatePaymentsNetEffect = ({
  className = "",
  inSummary = false,
}: LatePaymentsNetEffectProps) => {
  const [lateBillsTotal, setLateBillsTotal] = useState<number>(0);
  const [lateInvoicesTotal, setLateInvoicesTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Net effect calculation
  const netAmount = lateInvoicesTotal - lateBillsTotal;
  const isPositive = netAmount >= 0;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch late bills data
        const billsResponse = await fetch("/api/xero/late-bills", {
          credentials: "include",
        });

        if (!billsResponse.ok) {
          throw new Error("Failed to fetch late bills");
        }

        const billsData = await billsResponse.json();

        if (!billsData.success) {
          throw new Error(billsData.message || "Failed to load late bills");
        }

        // Set bills total
        setLateBillsTotal(billsData.data.totalAmount || 0);

        // Fetch late invoices
        const invoicesData = await getLateInvoices();

        // Set invoices total
        setLateInvoicesTotal(invoicesData.totalAmount || 0);
      } catch (err: any) {
        console.error("Error fetching late payment data:", err);
        setError(err.message || "Failed to load late payment data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // If there's an error, don't render anything
  if (error) {
    return null;
  }

  // If in summary mode, render a simplified version with just the amount
  if (inSummary) {
    // Show $0 with good indicator when no late payments
    const hasNoLatePayments = lateBillsTotal === 0 && lateInvoicesTotal === 0;
    
    return (
      <div className={`${className}`}>
        {loading ? (
          <span className="text-xs font-medium text-muted dark:text-subtle animate-pulse">
            Loading...
          </span>
        ) : (
          <div className="flex items-center gap-1">
            {hasNoLatePayments ? (
              <>
                <svg
                  className="w-3 h-3 flex-shrink-0 text-success dark:text-success-light"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-base font-bold text-success dark:text-success-light">
                  $0
                </span>
              </>
            ) : isPositive ? (
              <>
                <svg
                  className="w-3 h-3 flex-shrink-0 text-success dark:text-green-300"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-base font-bold text-success dark:text-green-300">
                  {formatCurrency(Math.abs(netAmount))}
                </span>
              </>
            ) : (
              <>
                <svg
                  className="w-3 h-3 flex-shrink-0 text-error dark:text-red-300"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-base font-bold text-error dark:text-red-300">
                  {formatCurrency(Math.abs(netAmount))}
                </span>
              </>
            )}
          </div>
        )}
      </div>
    );
  }

  // Standard full display version
  return (
    <div className={`${className}`}>
      {/* Net Effect Card - Styled like summary cards and matching height/width of other cards */}
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "8px 12px",
          borderRadius: "0.5rem",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
          backgroundColor: isPositive ? "#F0FDF4" : "#FEF2F2", // Light green if positive, light red if negative
          border: "1px solid",
          borderColor: isPositive ? "#BBF7D0" : "#FECACA", // Light green border if positive, light red if negative
          color: isPositive ? "#166534" : "#991B1B", // Dark green text if positive, dark red if negative
          transition: "all 0.2s ease",
        }}
        className={`
          ${
            isPositive
              ? "dark:bg-success-dark/20 dark:text-green-200 dark:border-green-800/50 dark:shadow-md dark:shadow-green-900/20"
              : "dark:bg-error-dark/20 dark:text-red-200 dark:border-red-800/50 dark:shadow-md dark:shadow-red-900/20"
          }
        `}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          {loading ? (
            <svg
              style={{
                width: "16px",
                height: "16px",
                animation: "spin 1s linear infinite",
              }}
              className="text-muted dark:text-subtle"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          ) : isPositive ? (
            <svg
              style={{
                width: "16px",
                height: "16px",
                flexShrink: 0,
              }}
              className="text-success"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            <svg
              style={{
                width: "16px",
                height: "16px",
                flexShrink: 0,
              }}
              className="text-accent"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          )}

          <div style={{ display: "flex", alignItems: "center" }}>
            <span
              style={{
                fontSize: "13px",
                fontWeight: 500,
                whiteSpace: "nowrap",
                marginRight: "8px",
              }}
            >
              Net Late Payments
            </span>
            <span
              style={{
                fontSize: "13px",
                fontWeight: 700,
                whiteSpace: "nowrap",
                color: isPositive ? "#15803D" : "#B91C1C", // Dark green if positive, dark red if negative
              }}
              className={
                isPositive ? "dark:text-green-300" : "dark:text-red-300"
              }
            >
              {formatCurrency(Math.abs(netAmount))}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LatePaymentsNetEffect;
