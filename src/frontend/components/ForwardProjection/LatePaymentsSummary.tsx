import React, { useState, useEffect } from "react";
import LateBillsSummary from "./LateBillsSummary";
import LateInvoicesSummary from "./LateInvoicesSummary";
import LatePaymentsNetEffect from "./LatePaymentsNetEffect";
import { Button } from "@/frontend/components/ui/Button";

interface LatePaymentsSummaryProps {
  className?: string;
}

/**
 * A container component that groups all late payment information into a single expandable card
 */
const LatePaymentsSummary: React.FC<LatePaymentsSummaryProps> = ({
  className = "",
}) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className={`${className}`}>
      {/* Summary card showing just the net effect when collapsed */}
      <div
        className={`
          border border-strong dark:border-gray-500
          rounded-lg bg-surface-card dark:bg-surface-card
          overflow-hidden shadow-sm dark:shadow-lg dark:shadow-gray-900/50
          transition-all duration-200 ease-in-out
          ${expanded ? "mb-2" : "mb-0"}
        `}
      >
        <Button variant="secondary" onClick={() => setExpanded(!expanded)}>
          <span className="text-base font-semibold text-primary dark:text-gray-100 transition-colors duration-200">
            Late Payments
          </span>

          <div className="flex items-center gap-2">
            {/* Only show net effect in the summary */}
            <LatePaymentsNetEffect inSummary={true} className="flex-grow-0" />

            {/* Expand/collapse icon */}
            <svg
              className={`h-5 w-5 transform transition-transform duration-200 text-muted dark:text-subtle ${
                expanded ? "rotate-180" : ""
              }`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </Button>
      </div>

      {/* Expanded details with both bills and invoices */}
      {expanded && (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "8px",
            transition: "all 0.3s ease",
          }}
          className="dark:bg-transparent rounded-md p-1"
        >
          <LateBillsSummary />
          <LateInvoicesSummary />
        </div>
      )}
    </div>
  );
};

export default LatePaymentsSummary;
