import { CustomExpense } from "../../../types";
import { format } from "date-fns";
import { isXeroSource } from "../../../constants/xero";

/**
 * Check if an expense is from Xero
 *
 * This function determines if an expense originated from Xero (not a payment TO Xero).
 * It uses specific source prefixes to make this determination rather than broad string matching.
 *
 * @param expense The expense to check
 * @returns True if the expense is from Xero
 */
export const isXeroExpense = (expense: CustomExpense): boolean => {
  // Check if the expense has a source field with a specific xero- prefix
  if (expense.source && isXeroSource(expense.source)) {
    return true;
  }

  // Check if the expense has metadata with a specific xero source
  if (expense.metadata?.source && typeof expense.metadata.source === 'string' &&
      isXeroSource(expense.metadata.source)) {
    return true;
  }

  // Check if the expense has an _source field with a specific xero type
  if (expense._source?.type && typeof expense._source.type === 'string' &&
      (expense._source.type === 'xero' || expense._source.type.startsWith('xero_'))) {
    return true;
  }

  // Check for metadata that explicitly marks this as from Xero
  if (expense.metadata?.isFromXero === true) {
    return true;
  }

  return false;
};

/**
 * Check if an expense is a GST Payment (BAS) expense from Xero
 *
 * This function first verifies that the expense is from Xero using the isXeroExpense function,
 * then checks if it has the specific 'xero-gst' source identifier.
 *
 * Note: We only rely on the source field and not name matching to avoid false positives
 * with manually created expenses that happen to have 'GST' or 'BAS' in their names.
 *
 * @param expense The expense to check
 * @returns True if the expense is a GST Payment (BAS) expense from Xero
 */
export const isGSTExpense = (expense: CustomExpense): boolean => {
  // First check if it's from Xero
  if (!isXeroExpense(expense)) {
    return false;
  }

  // Check if it's a Xero expense with source 'xero-gst'
  if (expense.source === 'xero-gst') {
    return true;
  }

  return false;
};

// Import centralized formatting and color utilities
import { formatCurrency, formatDate } from "../../utils/format";
import { getExpenseTypeClass, getExpenseFrequencyClass } from "../../utils/colors";

// Re-export for backwards compatibility
export { formatCurrency, formatDate };

/**
 * Calculate monthly equivalent amount
 */
export const calculateMonthlyEquivalent = (expense: CustomExpense): number => {
  switch (expense.frequency) {
    case 'weekly':
      return expense.amount * 4.333; // Average weeks per month
    case 'fortnightly':
      return expense.amount * 2.167; // Average fortnights per month (4.333 / 2)
    case 'monthly':
      return expense.amount;
    case 'quarterly':
      return expense.amount / 3;
    case 'one-off':
      // For one-off expenses, the monthly equivalent is 0
      // as they only occur once and aren't part of regular monthly expenses
      return 0;
    default:
      return expense.amount;
  }
};

/**
 * Get expense type label
 */
export const getTypeLabel = (type: CustomExpense['type']): string => {
  switch (type) {
    case 'Monthly Payroll':
      return 'Monthly Payroll';
    case 'Superannuation':
      return 'Superannuation';
    case 'Insurances':
      return 'Insurances';
    case 'Taxes':
      return 'Taxes';
    case 'Subcontractor Fees':
      return 'Subcontractor Fees';
    case 'Rent':
      return 'Rent';
    case 'Reimbursements':
      return 'Reimbursements';
    case 'Professional Fees':
      return 'Professional Fees';
    case 'General Expenses':
      return 'General Expenses';
    case 'Director Distributions':
      return 'Director Distributions';
    case 'Hardware':
      return 'Hardware';
    case 'Subscriptions':
      return 'Subscriptions';
    case 'Other Fees':
      return 'Other Fees';
    case 'Other':
      return 'Other';
    default:
      return type;
  }
};

/**
 * Get frequency label
 */
export const getFrequencyLabel = (frequency: CustomExpense['frequency']): string => {
  switch (frequency) {
    case 'one-off':
      return 'One-off';
    case 'weekly':
      return 'Weekly';
    case 'monthly':
      return 'Monthly';
    case 'quarterly':
      return 'Quarterly';
    default:
      return frequency;
  }
};

/**
 * Get CSS class for expense type
 * @deprecated Use getExpenseTypeClass from utils/colors.ts instead
 */
export const getTypeClass = getExpenseTypeClass;

/**
 * Get CSS class for frequency
 * @deprecated Use getExpenseFrequencyClass from utils/colors.ts instead
 */
export const getFrequencyClass = getExpenseFrequencyClass;