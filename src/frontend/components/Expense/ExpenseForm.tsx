import React from "react";
import { CustomExpense } from "../../../types";
import { calculateMonthlyEquivalent, formatCurrency } from "./utils";
import { Card } from "../ui";
import { getExpenseTypeOptions } from "../../../constants/expense-types";
import { Input, Select, FormGrid, FormSection } from "../shared/forms";
import { Button } from "../ui";

interface ExpenseFormProps {
  editingId: string | null;
  expense: {
    name: string;
    type: CustomExpense["type"];
    amount: string;
    date: string;
    frequency: CustomExpense["frequency"];
    repeatCount?: number;
  };
  onChange: {
    setName: (name: string) => void;
    setType: (type: CustomExpense["type"]) => void;
    setAmount: (amount: string) => void;
    setDate: (date: string) => void;
    setFrequency: (frequency: CustomExpense["frequency"]) => void;
    setRepeatCount?: (repeatCount: number) => void;
  };
  onSubmit: () => void;
  onCancel: () => void;
  loading: boolean;
  savingId: string | null;
  errors?: {
    name?: string;
    amount?: string;
    date?: string;
    general?: string;
  };
}

/**
 * Component for creating/editing expenses
 */
export const ExpenseForm: React.FC<ExpenseFormProps> = ({
  editingId,
  expense,
  onChange,
  onSubmit,
  onCancel,
  loading,
  savingId,
  errors = {}, // Default to empty object if not provided
}) => {
  const { name, type, amount, date, frequency, repeatCount } = expense;
  const { setName, setType, setAmount, setDate, setFrequency, setRepeatCount } =
    onChange;

  // For editingId, we use a border-bottom style
  // For new form, we use a card style
  const formClass = editingId ? "border-b pb-4 mb-4 border-primary" : "";

  return (
    <Card
      className={`${formClass} bg-primary/10 dark:bg-primary/20 border-primary p-4`}
    >
      {/* General error message */}
      {errors.general && (
        <div className="mb-3 text-sm text-error bg-error/10 border border-error rounded-md p-2 flex items-start">
          <svg
            className="w-4 h-4 mr-1.5 mt-0.5 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <span>{errors.general}</span>
        </div>
      )}

      {/* Modern form layout using FormGrid and modern components */}
      <FormGrid cols={3} gap="default">
        {/* Row 1 */}
        <Input
          label="Name"
          placeholder="Expense name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          error={errors.name}
          size="sm"
          required
          loading={loading}
        />

        <Select
          label="Type"
          value={type}
          onChange={(e) => setType(e.target.value as CustomExpense["type"])}
          options={getExpenseTypeOptions()}
          size="sm"
          required
          loading={loading}
        />

        <Input
          label="Amount"
          type="text"
          placeholder="0.00"
          value={amount}
          onChange={(e) => {
            // Only allow numbers and decimal point
            const value = e.target.value.replace(/[^0-9.]/g, "");
            setAmount(value);
          }}
          error={errors.amount}
          size="sm"
          required
          loading={loading}
          icon={<span className="text-muted text-sm">$</span>}
        />

        {/* Row 2 */}
        <Input
          label="Date"
          type="date"
          value={date}
          onChange={(e) => setDate(e.target.value)}
          error={errors.date}
          size="sm"
          required
          loading={loading}
        />

        <Select
          label="Frequency"
          value={frequency}
          onChange={(e) =>
            setFrequency(e.target.value as CustomExpense["frequency"])
          }
          options={[
            { value: "one-off", label: "One-off" },
            { value: "weekly", label: "Weekly" },
            { value: "fortnightly", label: "Fortnightly" },
            { value: "monthly", label: "Monthly" },
            { value: "quarterly", label: "Quarterly" },
          ]}
          size="sm"
          required
          loading={loading}
        />

        {/* Repeat Count - only shown for recurring expenses */}
        {frequency !== "one-off" ? (
          <Input
            label="Occurrences"
            type="text"
            placeholder="Unlimited"
            value={repeatCount || ""}
            onChange={(e) => {
              // Only allow numbers
              const value = e.target.value.replace(/[^0-9]/g, "");
              setRepeatCount && setRepeatCount(parseInt(value) || 0);
            }}
            helpText="Empty = unlimited"
            size="sm"
            loading={loading}
          />
        ) : (
          <div></div> // Empty div to maintain grid layout
        )}
      </FormGrid>

      {/* Monthly equivalent and buttons */}
      <div className="flex justify-between items-center mt-4">
        <div>
          <span className="text-sm text-muted dark:text-subtle">
            Monthly Equivalent:
          </span>
          <span className="ml-1 font-medium text-primary dark:text-gray-100">
            {amount && !isNaN(parseFloat(amount))
              ? formatCurrency(
                  calculateMonthlyEquivalent({
                    id: editingId || "",
                    name: name,
                    type: type,
                    amount: parseFloat(amount),
                    date: new Date(date),
                    frequency: frequency,
                  }),
                )
              : "$0.00"}
          </span>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="success"
            size="sm"
            onClick={onSubmit}
            loading={!!savingId}
            loadingText="Save"
            disabled={loading}
            className="h-[26px] w-[70px]"
            icon={
              <svg
                className="w-3.5 h-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            }
          >
            Save
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onCancel}
            disabled={loading}
            className="h-[26px] w-[70px]"
            icon={
              <svg
                className="w-3.5 h-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            }
          >
            Cancel
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ExpenseForm;
