/**
 * Activity Feed Page
 *
 * This component displays the main activity feed dashboard with filters,
 * timeline, and real-time updates.
 */

import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "react-query";
import { getActivityFeed, getActivityStats } from "../../api/activity";
import {
  ActivityFilters as ActivityFiltersType,
  ActivityFeedResponse,
  ActivityStats,
  Activity } from
"../../types/activity-types";
import ActivityTimeline from "./ActivityTimeline";
import ActivityFiltersComponent from "./ActivityFilters";
import ActivityStatsCard from "./ActivityStatsCard";
import { useEvents } from "../../contexts/EventContext";

/**
 * Activity Feed Page component
 */import { Button } from "@/frontend/components/ui/Button";
const ActivityFeedPage = () => {
  const [filters, setFilters] = useState<ActivityFiltersType>({
    limit: 20,
    offset: 0
  });
  const [allActivities, setAllActivities] = useState<Activity[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const queryClient = useQueryClient();
  const events = useEvents();

  // Query for activity feed
  const {
    data: activityFeedData,
    isLoading: isLoadingFeed,
    error: feedError,
    refetch: refetchFeed
  } = useQuery<ActivityFeedResponse>(
    ["activity-feed", filters],
    () => getActivityFeed(filters),
    {
      keepPreviousData: true,
      staleTime: 2 * 60 * 1000, // 2 minutes to reduce rate limiting
      refetchOnMount: false, // Reduce aggressive refetching
      refetchOnWindowFocus: false // Disable window focus refetch to prevent rate limiting
    }
  );

  // Query for activity stats
  const {
    data: statsData,
    isLoading: isLoadingStats,
    error: statsError
  } = useQuery<ActivityStats>(["activity-stats"], getActivityStats, {
    staleTime: 5 * 60 * 1000, // 5 minutes to reduce rate limiting
    refetchOnMount: false, // Reduce aggressive refetching
    refetchOnWindowFocus: false // Disable window focus refetch to prevent rate limiting
  });

  // Update activities and hasMore when data changes
  useEffect(() => {
    if (activityFeedData) {
      if (filters.offset === 0) {
        // Reset to new data (filters changed or refresh)
        setAllActivities(activityFeedData.activities || []);
      } else {
        // Append new data (load more)
        setAllActivities((prev: Activity[]) => {
          const existingIds = new Set(prev.map((a: Activity) => a.id));
          const newActivities = (activityFeedData.activities || []).filter((a: Activity) => !existingIds.has(a.id));
          return [...prev, ...newActivities];
        });
      }
      setHasMore(activityFeedData.hasMore || false);
    }
  }, [activityFeedData, filters.offset]);

  // Set up real-time updates (with throttling to prevent rate limiting)
  useEffect(() => {
    // Throttle invalidations to prevent rate limiting
    let lastInvalidation = 0;
    const THROTTLE_MS = 2000; // 2 seconds minimum between invalidations

    const throttledInvalidate = () => {
      const now = Date.now();
      if (now - lastInvalidation > THROTTLE_MS) {
        lastInvalidation = now;
        queryClient.invalidateQueries(["activity-feed"]);
        queryClient.invalidateQueries(["activity-stats"]);
      }
    };

    // Listen for activity events (throttled to prevent excessive API calls)
    const handleActivityCreated = () => {
      setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 }));
      throttledInvalidate();
    };

    const handleActivityUpdated = () => {
      setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 }));
      throttledInvalidate();
    };

    const handleActivityDeleted = () => {
      setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 }));
      throttledInvalidate();
    };

    // Subscribe to events
    events.subscribe("activity:created", handleActivityCreated);
    events.subscribe("activity:updated", handleActivityUpdated);
    events.subscribe("activity:deleted", handleActivityDeleted);

    // Cleanup
    return () => {
      events.unsubscribe("activity:created", handleActivityCreated);
      events.unsubscribe("activity:updated", handleActivityUpdated);
      events.unsubscribe("activity:deleted", handleActivityDeleted);
    };
  }, [events, queryClient]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: Partial<ActivityFiltersType>) => {
    setAllActivities([]); // Clear accumulated activities
    setFilters((prev: ActivityFiltersType) => ({
      ...prev,
      ...newFilters,
      offset: 0 // Reset offset when filters change
    }));
  };

  // Handle load more
  const handleLoadMore = () => {
    if (!hasMore || isLoadingFeed) return;

    setFilters((prev: ActivityFiltersType) => ({
      ...prev,
      offset: (prev.offset || 0) + (prev.limit || 20)
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    // Reset pagination without clearing activities immediately
    setFilters((prev: ActivityFiltersType) => ({ ...prev, offset: 0 })); // Reset pagination
    // Invalidate queries to force refresh
    queryClient.invalidateQueries(["activity-feed"]);
    queryClient.invalidateQueries(["activity-stats"]);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setAllActivities([]); // Clear accumulated activities
    setFilters({
      limit: 20,
      offset: 0
    });
  };

  const activities = allActivities;
  const totalCount = activityFeedData?.total || 0;

  return (
    <div className="min-h-screen page-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-primary dark:text-primary">
                Activity Feed
              </h1>
              <p className="mt-2 text-secondary dark:text-subtle">
                Track all system activities, user actions, and integration
                events
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="primary"
              onClick={handleRefresh}
              disabled={isLoadingFeed}>


                <svg
                  className={`-ml-1 mr-2 h-4 w-4 ${
                  isLoadingFeed ? "animate-spin" : ""}`
                  }
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">

                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />

                </svg>
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        {statsData && !isLoadingStats &&
        <div className="mb-8">
            <ActivityStatsCard stats={statsData} />
          </div>
        }

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium text-primary dark:text-primary">
                  Filters
                </h2>
                <Button variant="ghost"
                onClick={handleClearFilters}>


                  Clear all
                </Button>
              </div>
              <ActivityFiltersComponent
                filters={filters}
                onFiltersChange={handleFiltersChange} />

            </div>
          </div>

          {/* Activity Timeline */}
          <div className="lg:col-span-3">
            <div className="card">
              {/* Timeline Header */}
              <div className="px-6 py-4 border-b border-default dark:border-default">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-medium text-primary dark:text-primary">
                    Recent Activity
                  </h2>
                  <div className="text-sm text-muted dark:text-subtle">
                    {totalCount > 0 &&
                    <span>
                        Showing {activities.length} of {totalCount} activities
                      </span>
                    }
                  </div>
                </div>
              </div>

              {/* Timeline Content */}
              <div className="p-6">
                {feedError ?
                <div className="text-center py-8">
                    <div className="text-error dark:text-error-light mb-2">
                      Error loading activities
                    </div>
                    <Button variant="ghost"
                  onClick={handleRefresh}>


                      Try again
                    </Button>
                  </div> :

                <ActivityTimeline
                  activities={activities}
                  isLoading={isLoadingFeed}
                  hasMore={hasMore}
                  onLoadMore={handleLoadMore} />

                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>);

};

export default ActivityFeedPage;