import React, { useState, useEffect, useRef } from "react";
import {
  MessageCircle,
  X,
  Send,
  Loader2,
  Bo<PERSON>,
  User,
  ToggleLeft,
  ToggleRight
} from "lucide-react";
import { useFloatingPanels } from "../../contexts/FloatingPanelsContext";
import { Button, FAB, Input } from "@/frontend/components/ui";

interface Message {
  id: string;
  role:"user" |"assistant" |"system";
  content: string;
  timestamp: Date;
  toolCalls?: ToolCall[];
  provider:"xero" |"hubspot";
}

interface ToolCall {
  tool: string;
  arguments: any;
  result?: any;
}

interface Tool {
  name: string;
  description?: string;
  input_schema?: any;
}

export const AIChat: React.FC = () => {
  const {
    aiChatExpanded: isOpen,
    setAiChatExpanded: setIsOpen,
    aiChatProvider,
    setAiChatProvider
  } = useFloatingPanels();

  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [sessionIds, setSessionIds] = useState<{
    xero: string | null;
    hubspot: string | null;
  }>({
    xero: null,
    hubspot: null
  });
  const [tools, setTools] = useState<Tool[]>([]);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // Filter messages by current provider
  const currentMessages = messages.filter(
    (msg) => msg.provider === aiChatProvider
  );

  // Initialize MCP session when chat opens or provider changes
  useEffect(() => {
    if (isOpen && !sessionIds[aiChatProvider]) {
      initializeSession();
    }
  }, [isOpen, aiChatProvider]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior:"smooth" });
  }, [currentMessages]);

  const initializeSession = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const endpoint =
      aiChatProvider ==="xero" ?"/api/mcp/init" :"/api/hubspot-mcp/init";

      const response = await fetch(endpoint, {
        method:"POST",
        headers: {"Content-Type":"application/json"
        },
        credentials:"include"
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
          `Failed to initialize ${aiChatProvider.toUpperCase()} MCP session`
        );
      }

      const data = await response.json();
      setSessionIds((prev) => ({ ...prev, [aiChatProvider]: data.sessionId }));
      setTools(data.tools || []);

      // Add welcome message
      const welcomeContent =
      aiChatProvider === "xero" ? "Hello! I'm connected to your Xero account. I can help you with:\n\n• Viewing invoices, bills, and payments\n• Checking your accounts and bank transactions\n• Reviewing financial reports\n• Finding customer and supplier information\n\nWhat would you like to know?" : "Hello! I'm connected to your HubSpot account. I can help you with:\n\n• Managing contacts and companies\n• Tracking deals and opportunities\n• Reviewing engagement history\n• Creating associations\n• Searching CRM data\n\nWhat would you like to know?";

      setMessages((prev) => [
      ...prev,
      {
        id: Date.now().toString(),
        role:"assistant",
        content: welcomeContent,
        timestamp: new Date(),
        provider: aiChatProvider
      }]
      );
    } catch (err) {
      const errorMessage =
      err instanceof Error ? err.message :"Failed to connect";
      setError(errorMessage);
      console.error(`Error initializing ${aiChatProvider} MCP session:`, err);

      // Check if it's an API key issue
      if (errorMessage.includes("API not configured")) {
        setError(
          `The ${aiChatProvider.toUpperCase()} integration is not configured. Please check your API keys.`
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSend = async () => {
    if (!input.trim() || !sessionIds[aiChatProvider]) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role:"user",
      content: input,
      timestamp: new Date(),
      provider: aiChatProvider
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);
    setError(null);

    try {
      const endpoint =
      aiChatProvider ==="xero" ?"/api/mcp/chat" :"/api/hubspot-mcp/chat";

      // Send message to Claude
      const response = await fetch(endpoint, {
        method:"POST",
        headers: {"Content-Type":"application/json"
        },
        credentials:"include",
        body: JSON.stringify({
          sessionId: sessionIds[aiChatProvider],
          message: input
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message ||"Failed to send message");
      }

      const data = await response.json();

      // Add Claude's response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role:"assistant",
        content: data.message,
        timestamp: new Date(),
        toolCalls: data.toolCalls,
        provider: aiChatProvider
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (err) {
      const errorMessage =
      err instanceof Error ? err.message :"An error occurred";
      setError(errorMessage);
      console.error("Error processing message:", err);

      // Add error message to chat
      const errorMsg: Message = {
        id: (Date.now() + 1).toString(),
        role:"system",
        content: `Error: ${errorMessage}`,
        timestamp: new Date(),
        provider: aiChatProvider
      };
      setMessages((prev) => [...prev, errorMsg]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key ==="Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const closeChat = async () => {
    // Close both sessions if they exist
    for (const provider of ["xero","hubspot"] as const) {
      if (sessionIds[provider]) {
        try {
          const endpoint =
          provider ==="xero" ?"/api/mcp/close" :"/api/hubspot-mcp/close";
          await fetch(endpoint, {
            method:"POST",
            headers: {"Content-Type":"application/json"
            },
            credentials:"include",
            body: JSON.stringify({ sessionId: sessionIds[provider] })
          });
        } catch (err) {
          console.error(`Error closing ${provider} session:`, err);
        }
      }
    }

    setIsOpen(false);
    setSessionIds({ xero: null, hubspot: null });
    setMessages([]);
    setTools([]);
    setError(null);
  };

  const toggleProvider = () => {
    setAiChatProvider(aiChatProvider === "xero" ? "hubspot" : "xero");
    setError(null);
  };

  const suggestedQueries =
  aiChatProvider === "xero" ?
  ["Show me unpaid invoices", "What's my current bank balance?", "List recent payments", "Show this month's profit and loss"] :

  ["List recent contacts", "Show open deals", "Find companies in technology industry", "What's the status of Acme Inc deal?"];


  const providerName =
  aiChatProvider === "xero" ? "UpstreamAI - Xero" : "UpstreamAI - HubSpot";
  const isXero = aiChatProvider === "xero";

  return (
    <>
      {/* Floating button */}
      {!isOpen && (
        <FAB position="bottom-left" offset={{ y:"13rem" }}>
          <Button
            onClick={() => setIsOpen(true)}
            variant="primary"
            className="--fab"
            aria-label="Open AI Assistant"
          >
            <MessageCircle size={20} />
          </Button>
        </FAB>
      )}

      {/* Chat window */}
      {isOpen &&
      <div className="fixed bottom-6 left-4 w-96 h-[600px] bg-surface-card dark:bg-surface-card rounded-lg shadow-2xl flex flex-col z-50 border border-default dark:border-default">
          {/* Header */}
          <div
          className={`${
          isXero ?"bg-primary-color" :"bg-warning"} text-primary p-4 rounded-t-lg flex items-center justify-between`
          }>

            <div className="flex items-center gap-2">
              <Bot size={20} />
              <div className="flex flex-col">
                <h3 className="font-semibold">{providerName}</h3>
                <span
                className={`text-xs ${
                isXero ?"bg-primary/20 text-primary" :"bg-warning/20 text-warning"} px-2 py-0.5 rounded`
                }>

                  Powered by Claude
                </span>
              </div>
            </div>
            <Button variant="secondary"
          onClick={closeChat}
          className={`text-primary ${
          isXero ?"hover:bg-primary-dark" :"hover:bg-orange-700"} p-1 rounded transition-colors`
          }
          aria-label="Close chat">

              <X size={20} />
            </Button>
          </div>

          {/* Provider Toggle */}
          <div className="bg-surface-page dark:bg-surface-page border-b border-default dark:border-default px-4 py-2 flex items-center justify-between">
            <span className="text-sm text-secondary dark:text-subtle">
              Data Source:
            </span>
            <Button variant="secondary"
          onClick={toggleProvider}
          className="flex items-center gap-2 transition-colors rounded rounded-md">

              <span
              className={
              aiChatProvider ==="xero" ?"text-primary-color" :"text-subtle"
              }>

                Xero
              </span>
              {aiChatProvider ==="xero" ?
            <ToggleLeft size={20} className="text-primary-color" /> :

            <ToggleRight size={20} className="text-warning" />
            }
              <span
              className={
              aiChatProvider ==="hubspot" ?"text-warning" :"text-subtle"
              }>

                HubSpot
              </span>
            </Button>
          </div>

          {/* Messages area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {error &&
          <div className="bg-error/10 dark:bg-error/20 border border-error text-error p-3 rounded-md text-sm">
                {error}
              </div>
          }

            {currentMessages.map((message) =>
          <div
            key={message.id}
            className={`flex gap-2 ${
            message.role ==="user" ?"justify-end" :"justify-start"}`
            }>

                {message.role !=="user" &&
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
              message.role ==="system" ?"bg-surface-muted" :
              isXero ?"bg-primary/10 dark:bg-primary/20" :"bg-warning/10 dark:bg-warning/20"}`
              }>

                    <Bot
                size={16}
                className={
                message.role ==="system" ?"text-secondary" :
                isXero ?"text-primary-color" :"text-warning"
                } />

                  </div>
            }

                <div
              className={`max-w-[80%] rounded-lg p-3 ${
              message.role ==="user" ?
              `${isXero ?"bg-primary-color" :"bg-warning"} text-primary` :
              message.role ==="system" ?"bg-surface-alt dark:bg-surface-alt text-secondary dark:text-subtle" :"bg-surface-alt dark:bg-surface-alt text-primary dark:text-primary"}`
              }>

                  <p className="text-sm whitespace-pre-wrap">
                    {message.content}
                  </p>

                  {message.toolCalls && message.toolCalls.length > 0 &&
              <div className="mt-2 pt-2 border-t border-strong dark:border-strong">
                      <p className="text-xs opacity-70">
                        Used:{""}
                        {message.toolCalls.
                  map((tc) => tc.tool.replace(/_/g,"")).
                  join(",")}
                      </p>
                    </div>
              }
                </div>

                {message.role ==="user" &&
            <div className="w-8 h-8 bg-surface-muted rounded-full flex items-center justify-center flex-shrink-0">
                    <User size={16} className="text-secondary" />
                  </div>
            }
              </div>
          )}

            {isLoading &&
          <div className="flex gap-2 justify-start">
                <div
              className={`w-8 h-8 ${
              isXero ?"bg-primary/10 dark:bg-primary/20" :"bg-warning/10 dark:bg-warning/20"} rounded-full flex items-center justify-center`
              }>

                  <Bot
                size={16}
                className={isXero ?"text-primary-color" :"text-warning"} />

                </div>
                <div className="">
                  <Loader2 className="animate-spin" size={16} />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
          }

            <div ref={messagesEndRef} />
          </div>

          {/* Suggested queries for empty chat */}
          {currentMessages.length === 1 && !isLoading &&
        <div className="px-4 pb-2">
              <p className="text-xs text-muted dark:text-subtle mb-2">
                Try asking:
              </p>
              <div className="flex flex-wrap gap-1">
                {suggestedQueries.map((query, index) =>
            <Button variant="secondary"
            key={index}
            onClick={() => setInput(query)}>


                    {query}
                  </Button>
            )}
              </div>
            </div>
        }

          {/* Input area */}
          <div className="border-t border-default dark:border-default p-4">
            <div className="flex gap-2">
              <Input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Ask about your ${
              aiChatProvider ==="xero" ?"accounting" :"CRM"} data...`
              }
              className="flex-1 px-3 py-2 border border-strong dark:border-strong dark:bg-surface-alt dark:text-primary rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-400 text-sm"
              disabled={isLoading || !sessionIds[aiChatProvider]} />

              <Button variant="secondary"
            onClick={handleSend}
            disabled={
            isLoading || !input.trim() || !sessionIds[aiChatProvider]
            }
            className={`px-3 py-2 ${
            isXero ?"bg-primary hover:bg-primary/90" :"bg-warning hover:bg-warning/90"} text-primary rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed dark:disabled:bg-surface-muted`
            }
            aria-label="Send message">

                {isLoading ?
              <Loader2 className="animate-spin" size={20} /> :

              <Send size={20} />
              }
              </Button>
            </div>
          </div>
        </div>
      }
    </>);

};

export default AIChat;