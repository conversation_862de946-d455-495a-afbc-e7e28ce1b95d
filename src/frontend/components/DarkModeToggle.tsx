import React, { useState, useEffect } from "react";

/**
 * Component for toggling between light/dark mode.
 * Uses fixed sizing to match other header icons.
 */import { Button } from "@/frontend/components/ui/Button";
const DarkModeToggle: React.FC = () => {
  // Initialize state from localStorage or system preference
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check if dark mode preference is stored in localStorage
    const storedMode = localStorage.getItem("darkMode");

    if (storedMode !== null) {
      // Use stored preference
      return storedMode === "true";
    }

    // Check system preference
    return window.matchMedia("(prefers-color-scheme: dark)").matches;
  });

  // Apply dark mode class to html element whenever isDarkMode changes
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Store preference in localStorage
    localStorage.setItem("darkMode", String(isDarkMode));
  }, [isDarkMode]);

  // Handle toggle change
  const handleToggle = () => {
    setIsDarkMode(!isDarkMode);
  };

  // Render a button with fixed size to match other header icons
  return (
    <Button
      variant="secondary"
      onClick={handleToggle}
      className="flex items-center justify-center w-9 h-9 rounded-full"
      aria-label={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
      title={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
    >
      <svg
        className={`w-5 h-5 ${
          isDarkMode ? "text-warning" : "text-primary"
        }`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        {isDarkMode ? (
          // Moon icon for dark mode
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
        ) : (
          // Sun icon for light mode
          <path
            fillRule="evenodd"
            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
            clipRule="evenodd"
          />
        )}
      </svg>
    </Button>
  );

};

export default DarkModeToggle;