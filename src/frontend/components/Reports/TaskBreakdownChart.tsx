import { useState, useMemo, useCallback } from "react";
import type { TaskBreakdown } from "../../../services/harvest/time-report-service";
import SimpleTooltip, { TooltipData } from "../common/SimpleTooltip";
import { getTaskColorMap } from "../../utils/colorUtils";
import { useIsMobile } from "../../hooks";

interface TaskBreakdownChartProps {
  taskBreakdown: TaskBreakdown[];
  totalHours: number;
  billableHours: number;
  weeklyCapacity: number;
  utilization: number; // Utilization percentage to display
  periodDays?: number;
  excludeLeave?: boolean; // Whether to exclude leave from capacity calculations
  leaveHours?: number; // Hours spent on leave tasks
}

/**
 * A component that displays a segmented bar chart showing task breakdown
 * with billable time first, capacity line, and utilization badge
 */
const TaskBreakdownChart = ({
  taskBreakdown,
  totalHours,
  billableHours,
  weeklyCapacity,
  utilization, // Utilization percentage to display
  periodDays = 5, // Default to 5 days (one work week)
  excludeLeave = false,
  leaveHours = 0,
}: TaskBreakdownChartProps) => {
  const isMobile = useIsMobile();
  
  // State for tooltip
  const [tooltip, setTooltip] = useState<TooltipData>({
    visible: false,
    content: "",
    x: 0,
    y: 0,
  });

  // Generate a color map for tasks
  const taskColorMap = useMemo(() => {
    // Create a list of all unique tasks
    const allTasks = taskBreakdown.map((task) => ({
      taskId: task.taskId,
      taskName: task.taskName,
      isBillable: task.isBillable,
    }));

    return getTaskColorMap(allTasks);
  }, [taskBreakdown]);

  // Handle mouse enter for billable segment
  const handleBillableMouseEnter = (e: any) => {
    if (!isMobile) {
      setTooltip({
        visible: true,
        title: "Billable Time",
        content: `${billableHours.toFixed(
          1
        )} hours (${utilPercentage}% utilization${
          excludeLeave ? ", leave excluded" : ""
        })`,
        x: e.clientX,
        y: e.clientY,
      });
    }
  };

  // Handle mouse enter for tasks using data attributes
  const handleTaskMouseEnter = useCallback((e: any) => {
    if (!isMobile) {
      const taskId = e.currentTarget.dataset.taskId;
      const task = taskBreakdown.find(t => t.taskId === taskId);
      if (task) {
        setTooltip({
          visible: true,
          title: task.taskName,
          content: `${task.hours.toFixed(1)} hours (${Math.round(
            (task.hours / totalHours) * 100
          )}% of total time)`,
          x: e.clientX,
          y: e.clientY,
        });
      }
    }
  }, [taskBreakdown, totalHours, isMobile]);

  // Handle mouse leave
  const handleMouseLeave = () => {
    if (!isMobile) {
      setTooltip((prev: TooltipData) => ({ ...prev, visible: false }));
    }
  };

  // If no task breakdown data or no hours, return null
  if (!taskBreakdown || taskBreakdown.length === 0 || totalHours === 0) {
    return null;
  }

  // Define leave task names (case-insensitive)
  const LEAVE_TASK_NAMES = [
    "public holiday",
    "annual leave",
    "other unpaid leave",
    "carer's leave',
    "community service leave",
    "compassionate leave",
    "parental leave",
    "personal leave",
    "sick leave",
  ];

  // Helper function to check if a task is a leave task
  const isLeaveTask = (taskName: string) => {
    return LEAVE_TASK_NAMES.some((leaveName) =>
      taskName.toLowerCase().includes(leaveName.toLowerCase())
    );
  };

  // Filter out leave tasks if excludeLeave is true
  const filteredTasks = excludeLeave
    ? taskBreakdown.filter((task) => !isLeaveTask(task.taskName))
    : taskBreakdown;

  // Sort tasks: billable first, then non-billable by hours (descending)
  const sortedTasks = [...filteredTasks].sort((a, b) => {
    // Billable tasks first
    if (a.isBillable && !b.isBillable) return -1;
    if (!a.isBillable && b.isBillable) return 1;
    // Then by hours (descending)
    return b.hours - a.hours;
  });

  // Group billable and non-billable tasks
  const billableTasks = sortedTasks.filter((task) => task.isBillable);
  const nonBillableTasks = sortedTasks.filter((task) => !task.isBillable);

  // Calculate capacity for the period
  const periodCapacity = (weeklyCapacity / 5) * periodDays;

  // Adjust capacity if excluding leave
  const effectiveCapacity = excludeLeave
    ? Math.max(0.1, periodCapacity - leaveHours)
    : periodCapacity;

  // Use the passed utilization value
  const utilPercentage = Math.round(utilization);

  // Use a consistent max width for better visual comparison (55 hours)
  const MAX_HOURS = 55;

  // Calculate positions for the capacity line and billable segment
  // Base percentages on MAX_HOURS instead of totalHours for consistent scale
  const capacityPosition = Math.min(
    100,
    ((excludeLeave ? effectiveCapacity : periodCapacity) / MAX_HOURS) * 100
  );
  const billablePosition = (billableHours / MAX_HOURS) * 100;

  return (
    <div className="w-full">
      {/* Main chart container */}
      <div className="relative mb-3">
        {/* Segmented bar */}
        <div className="h-6 w-full bg-transparent rounded-sm overflow-hidden flex relative">
          {/* Billable tasks (grouped together) with badge at the end */}
          {billableTasks.length > 0 && (
            <div
              className="h-full bg-primary-light0 relative cursor-pointer"
              style={{ width: `${billablePosition}%` }}
              onMouseEnter={handleBillableMouseEnter}
              onMouseLeave={handleMouseLeave}
              onMouseMove={handleBillableMouseEnter}
            >
              {/* Badge placed at the end of the billable segment */}
              {billablePosition >= 5 && (
                <span className="text-xs font-medium text-primary whitespace-nowrap px-1 py-0.5 z-10 absolute right-0 top-0 bottom-0 flex items-center">
                  {utilPercentage}%
                </span>
              )}
            </div>
          )}

          {/* Non-billable tasks */}
          {nonBillableTasks.map((task) => (
            <div
              key={task.taskId}
              className="h-full cursor-pointer"
              style={{
                backgroundColor: taskColorMap[task.taskId] || "#878580", // Use our color map with Flexoki base-500 fallback
                width: `${(task.hours / MAX_HOURS) * 100}%`,
              }}
              data-task-id={task.taskId}
              onMouseEnter={handleTaskMouseEnter}
              onMouseLeave={handleMouseLeave}
              onMouseMove={handleTaskMouseEnter}
            ></div>
          ))}

          {/* Capacity line */}
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-black dark:bg-surface-card z-20"
            style={{ left: `${capacityPosition}%` }}
          >
            {/* Line extending below with capacity value */}
            <div className="absolute bottom-0 left-0 h-4 w-0.5 translate-y-full bg-black dark:bg-surface-card"></div>
            <div
              className="absolute bottom-0 translate-y-full pt-1 whitespace-nowrap text-xs text-primary dark:text-primary"
              style={{
                left:
                  capacityPosition < 10
                    ? "2px"
                    : capacityPosition > 90
                    ? "-20px"
                    : "-10px",
              }}
            >
              {(excludeLeave ? effectiveCapacity : periodCapacity).toFixed(1)}
              {excludeLeave && (
                <span className="text-muted dark:text-subtle ml-1">(leave excluded)</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Task legend */}
      <div className="flex flex-wrap gap-x-4 text-xs">
        {/* Billable legend item */}
        <div
          className="flex items-center cursor-pointer"
          onMouseEnter={handleBillableMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className="w-3 h-3 mr-1 bg-primary-light0 rounded-sm"></div>
          <span className="text-primary dark:text-primary">
            Billable: {billableHours.toFixed(1)} hrs
          </span>
        </div>

        {/* Non-billable legend items */}
        {nonBillableTasks.slice(0, 3).map((task) => (
          <div
            key={task.taskId}
            className="flex items-center cursor-pointer"
            onMouseEnter={(e) => handleTaskMouseEnter(e, task)}
            onMouseLeave={handleMouseLeave}
          >
            <div
              className="w-3 h-3 mr-1 rounded-sm"
              style={{
                backgroundColor: taskColorMap[task.taskId] || "#878580", // Flexoki base-500
              }}
            ></div>
            <span className="text-primary dark:text-primary">
              {task.taskName}: {task.hours.toFixed(1)} hrs
            </span>
          </div>
        ))}
      </div>

      {/* Tooltip - only on desktop */}
      {!isMobile && <SimpleTooltip tooltip={tooltip} />}
    </div>
  );
};

export default TaskBreakdownChart;
