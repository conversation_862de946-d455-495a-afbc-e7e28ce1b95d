/**
 * Refactored Utilization Report - Entry point
 * Uses container/presentation pattern for proper separation of concerns
 */

import React from "react";
import UtilizationReportContainer from "./UtilizationReportContainer";

/**
 * Main UtilizationReport component
 * Now uses the container/presentation pattern for clean separation of concerns
 */
const UtilizationReport: React.FC = () => {
  return <UtilizationReportContainer />;
};

export default UtilizationReport;