import { useState } from "react";
import SimpleTooltip, { TooltipData } from "../../common/SimpleTooltip";

interface TimeCapacitySectionProps {
  totalHours: number;
  totalBillableHours: number;
  totalNonBillableHours: number;
  availableCapacity: number;
  totalCapacity: number;
  excludeLeave: boolean;
}

/**
 * Component for displaying time and capacity metrics
 * Redesigned for more efficient space usage
 */
const TimeCapacitySection = ({
  totalHours,
  totalBillableHours,
  totalNonBillableHours,
  availableCapacity,
  totalCapacity,
  excludeLeave,
}: TimeCapacitySectionProps) => {
  // State for tooltip
  const [tooltip, setTooltip] = useState({
    visible: false,
    title: "",
    content: "",
    x: 0,
    y: 0,
  } as TooltipData);

  // Handle mouse enter for tooltips
  const handleMouseEnter = (
    e: React.MouseEvent,
    title: string,
    content: string,
  ) => {
    setTooltip({
      visible: true,
      title,
      content,
      x: e.clientX,
      y: e.clientY,
    });
  };

  // <PERSON>le mouse leave
  const handleMouseLeave = () => {
    setTooltip((prev: TooltipData) => ({ ...prev, visible: false }));
  };

  return (
    <div className="bg-surface-page rounded-lg p-3">
      <h4 className="text-sm font-medium text-secondary mb-2 pb-1 border-b border-default">
        Time & Capacity
      </h4>
      <div className="grid grid-cols-5 gap-3">
        <div
          className="bg-surface-card p-2 rounded-md shadow-sm cursor-help"
          onMouseEnter={(e) =>
            handleMouseEnter(
              e,
              "Total Hours",
              "All hours logged by staff in the selected period.",
            )
          }
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">Total hours</span>
            <span className="text-xl font-bold text-primary">
              {Math.round(totalHours)}
            </span>
          </div>
        </div>
        <div
          className="bg-surface-card p-2 rounded-md shadow-sm cursor-help"
          onMouseEnter={(e) =>
            handleMouseEnter(
              e,
              "Billable Hours",
              "Hours logged to billable tasks that generate revenue.",
            )
          }
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Billable hours
            </span>
            <span className="text-xl font-bold text-primary-color">
              {Math.round(totalBillableHours)}
            </span>
          </div>
        </div>
        <div
          className="bg-surface-card p-2 rounded-md shadow-sm cursor-help"
          onMouseEnter={(e) =>
            handleMouseEnter(
              e,
              "Non-Billable Hours",
              excludeLeave
                ? "Hours spent on non-billable tasks, excluding leave time."
                : "Hours spent on non-billable tasks, including leave time.",
            )
          }
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Non-bill. hours
            </span>
            <span className="text-xl font-bold text-warning">
              {Math.round(totalNonBillableHours)}
            </span>
          </div>
        </div>
        <div
          className="bg-surface-card p-2 rounded-md shadow-sm cursor-help"
          onMouseEnter={(e) =>
            handleMouseEnter(
              e,
              "Available Capacity",
              excludeLeave
                ? "Maximum available working hours after subtracting leave time."
                : "Maximum available working hours including time allocated to leave.",
            )
          }
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Avail. capacity
            </span>
            <span className="text-xl font-bold text-accent">
              {Math.round(availableCapacity)}
            </span>
          </div>
        </div>
        <div
          className="bg-surface-card p-2 rounded-md shadow-sm cursor-help"
          onMouseEnter={(e) =>
            handleMouseEnter(
              e,
              "Total Capacity",
              "Maximum theoretical working hours based on staff weekly capacity (typically 37.5 hours/week).",
            )
          }
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Total capacity
            </span>
            <span className="text-xl font-bold text-primary-color">
              {Math.round(totalCapacity)}
            </span>
          </div>
        </div>
      </div>

      {/* Tooltip */}
      <SimpleTooltip tooltip={tooltip} />
    </div>
  );
};

export default TimeCapacitySection;
