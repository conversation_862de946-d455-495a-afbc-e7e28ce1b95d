import React from "react";
import { FixedPriceProject } from "../../../api/harvest";

interface FinancialSummarySectionProps {
  billableAmountsByCurrency: Record<string, number>;
  fixedPriceIncomeByCurrency: Record<string, number>;
  fixedPriceProjects: FixedPriceProject[];
  isLoadingFixedPrice: boolean;
  financialMetricsByCurrency: Array<{
    currency: string;
    billableAmount: number;
    staffCost: number;
    nonBillableCost: number;
    profit: number;
    profitMargin: number;
  }>;
}

/**
 * Component for displaying financial summary metrics
 * Redesigned for more efficient space usage
 */
const FinancialSummarySection: React.FC<FinancialSummarySectionProps> = ({
  billableAmountsByCurrency,
  fixedPriceIncomeByCurrency,
  fixedPriceProjects,
  isLoadingFixedPrice,
  financialMetricsByCurrency,
}) => {
  // Only render if there's financial data to show
  if (
    Object.keys(billableAmountsByCurrency).length === 0 &&
    Object.keys(fixedPriceIncomeByCurrency).length === 0
  ) {
    return null;
  }

  return (
    <div className="bg-surface-page rounded-lg p-3 mt-3">
      <h4 className="text-sm font-medium text-secondary mb-2 pb-1 border-b border-default">
        Financial Summary
      </h4>
      <div className="grid grid-cols-3 gap-3">
        {/* Billable amount */}
        <div className="bg-surface-card p-2 rounded-md shadow-sm">
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Billable amount
            </span>
            {Object.entries(billableAmountsByCurrency).map(
              ([currency, amount]) => (
                <span key={currency} className="text-xl font-bold text-success">
                  {new Intl.NumberFormat("en-AU", {
                    style: "currency",
                    currency: currency,
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(amount)}
                </span>
              ),
            )}
            {Object.keys(billableAmountsByCurrency).length === 0 && (
              <span className="text-xl font-bold text-success">$0</span>
            )}
          </div>
        </div>

        {/* Fixed Price Income */}
        <div className="bg-surface-card p-2 rounded-md shadow-sm">
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Fixed price inc.
              {isLoadingFixedPrice && (
                <span className="ml-1 text-xs text-subtle">(loading...)</span>
              )}
            </span>
            <div>
              {Object.entries(fixedPriceIncomeByCurrency).map(
                ([currency, amount]) => (
                  <span
                    key={currency}
                    className="text-xl font-bold text-primary-color"
                  >
                    {new Intl.NumberFormat("en-AU", {
                      style: "currency",
                      currency: currency,
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(amount)}
                  </span>
                ),
              )}
              {Object.keys(fixedPriceIncomeByCurrency).length === 0 && (
                <span className="text-xl font-bold text-primary-color">$0</span>
              )}
              {fixedPriceProjects.length > 0 && (
                <span className="text-xs text-muted block">
                  {fixedPriceProjects.length} project
                  {fixedPriceProjects.length !== 1 ? "s" : ""}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Staff cost (estimated) */}
        <div className="bg-surface-card p-2 rounded-md shadow-sm">
          <div className="flex flex-col">
            <span className="text-xs font-medium text-muted">
              Staff cost (est.)
            </span>
            {financialMetricsByCurrency.map(
              ({ currency, staffCost, profit, profitMargin }) => (
                <div key={currency} className="flex flex-col">
                  <span className="text-xl font-bold text-error">
                    {new Intl.NumberFormat("en-AU", {
                      style: "currency",
                      currency: currency,
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(staffCost)}
                  </span>
                  <span
                    className={`text-xs font-medium ${
                      profit >= 0 ? "text-success" : "text-error"
                    }`}
                  >
                    Margin: {profitMargin}% (
                    {new Intl.NumberFormat("en-AU", {
                      style: "currency",
                      currency: currency,
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    }).format(profit)}
                    )
                  </span>
                </div>
              ),
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialSummarySection;
