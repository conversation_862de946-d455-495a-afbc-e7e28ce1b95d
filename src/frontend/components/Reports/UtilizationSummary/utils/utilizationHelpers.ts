/**
 * Helper functions for utilization calculations
 */

/**
 * Get utilization color based on percentage
 */
export const getUtilizationColor = (utilization: number): string => {
  if (utilization >= 70) return "bg-success-light0";
  if (utilization >= 50) return "bg-warning-light0";
  if (utilization > 0) return "bg-error-light0";
  return "bg-red-300"; // Light red for 0% utilization
};

/**
 * Calculate overall utilization percentage
 */
export const calculateOverallUtilization = (
  totalBillableHours: number,
  totalHours: number,
  totalLeaveHours: number,
  excludeLeave: boolean,
): number => {
  return excludeLeave
    ? Math.round(
        (totalBillableHours / Math.max(0.1, totalHours - totalLeaveHours)) *
          100,
      )
    : Math.round((totalBillableHours / totalHours) * 100);
};
