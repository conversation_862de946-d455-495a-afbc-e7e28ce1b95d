import { useState, useEffect, useCallback, useRef } from "react";
import { getStaffUtilization } from "../../api/harvest";
import {
  format,
  addWeeks,
  addMonths,
  addQuarters,
  addYears,
  subWeeks,
  subMonths,
  subQuarters,
  subYears,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfQuarter,
  endOfQuarter,
  startOfYear,
  endOfYear,
  isSameDay } from
"date-fns";
import { useLoading } from "../../contexts/LoadingContext";
import { StaffUtilization } from "../../../services/harvest/time-report-service";

// Import the component modules
import PeriodSelector from "./PeriodSelector";
import UtilizationSummary from "./UtilizationSummary";
import UtilizationTable from "./UtilizationTable";
import UtilizationSummaryGraph from "./UtilizationSummaryGraph";
import TaskBreakdownSummary from "./TaskBreakdownSummary";

// Define time period options
const TIME_PERIODS = {
  WEEK: "week",
  SEMIMONTH: "semimonth",
  MONTH: "month",
  QUARTER: "quarter",
  FISCAL_YEAR: "fiscal_year",
  YEAR: "year",
  ALL_TIME: "all_time",
  CUSTOM: "custom"
};

const UtilizationReport = () => {
  // State for selected time period
  const [timePeriod, setTimePeriod] = useState(TIME_PERIODS.WEEK);

  // State for current date range
  const [currentStartDate, setCurrentStartDate] = useState(
    startOfWeek(new Date(), { weekStartsOn: 1 })
  );
  const [currentEndDate, setCurrentEndDate] = useState(
    endOfWeek(new Date(), { weekStartsOn: 1 })
  );

  // State for custom date range
  const [customStartDate, setCustomStartDate] = useState("");
  const [customEndDate, setCustomEndDate] = useState("");

  // State for utilization data
  const [utilizationData, setUtilizationData] = useState<StaffUtilization[]>(
    []
  );

  // State to track if we're showing a skeleton loader for task breakdown
  const [showTaskBreakdownSkeleton, setShowTaskBreakdownSkeleton] =
  useState(false);

  // State for dropdown visibility
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // State for leave exclusion toggle
  const [excludeLeave, setExcludeLeave] = useState(false);

  // Use the loading context to track Harvest API calls
  const { isLoading, loadingType, setLoading } = useLoading();

  // Ref to track if data is already loading to prevent duplicate requests
  const isLoadingRef = useRef(false);

  // Initialize date range when time period changes
  useEffect(() => {
    if (timePeriod !== TIME_PERIODS.CUSTOM) {
      const today = new Date();

      switch (timePeriod) {
        case TIME_PERIODS.WEEK:
          setCurrentStartDate(startOfWeek(today, { weekStartsOn: 1 }));
          setCurrentEndDate(endOfWeek(today, { weekStartsOn: 1 }));
          break;

        case TIME_PERIODS.SEMIMONTH:
          // First half or second half of month
          const dayOfMonth = today.getDate();
          if (dayOfMonth <= 15) {
            // First half
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth(), 15);
            setCurrentStartDate(firstDay);
            setCurrentEndDate(lastDay);
          } else {
            // Second half
            const firstDay = new Date(
              today.getFullYear(),
              today.getMonth(),
              16
            );
            const lastDay = endOfMonth(today);
            setCurrentStartDate(firstDay);
            setCurrentEndDate(lastDay);
          }
          break;

        case TIME_PERIODS.MONTH:
          setCurrentStartDate(startOfMonth(today));
          setCurrentEndDate(endOfMonth(today));
          break;

        case TIME_PERIODS.QUARTER:
          setCurrentStartDate(startOfQuarter(today));
          setCurrentEndDate(endOfQuarter(today));
          break;

        case TIME_PERIODS.FISCAL_YEAR:
          // Assuming fiscal year starts in July
          const currentYear = today.getFullYear();
          const fiscalYearStart =
          today.getMonth() >= 6 ?
          new Date(currentYear, 6, 1) // July 1st of current year
          : new Date(currentYear - 1, 6, 1); // July 1st of previous year
          const fiscalYearEnd =
          today.getMonth() >= 6 ?
          new Date(currentYear + 1, 5, 30) // June 30th of next year
          : new Date(currentYear, 5, 30); // June 30th of current year
          setCurrentStartDate(fiscalYearStart);
          setCurrentEndDate(fiscalYearEnd);
          break;

        case TIME_PERIODS.YEAR:
          setCurrentStartDate(startOfYear(today));
          setCurrentEndDate(endOfYear(today));
          break;

        case TIME_PERIODS.ALL_TIME:
          // Harvest API has a 1-year limit on time reports
          // Set to the past year (maximum allowed by Harvest API)
          setCurrentStartDate(subYears(today, 1));
          setCurrentEndDate(today);
          break;
      }
    }
  }, [timePeriod]);

  // Fetch utilization data (wrapped in useCallback to prevent unnecessary re-renders)
  const fetchUtilizationData = useCallback(async () => {
    // Prevent multiple simultaneous requests
    if (isLoadingRef.current) {
      console.log("Already loading data, skipping duplicate fetch");
      return;
    }

    isLoadingRef.current = true;

    try {
      // Set global loading state manually
      setLoading(true, "harvest");

      // Show loading skeleton for task breakdown
      setShowTaskBreakdownSkeleton(true);

      // Get formatted date range for API call
      let from: string, to: string;

      if (timePeriod === TIME_PERIODS.CUSTOM) {
        // For custom dates, parse the string dates to Date objects
        const startDate = new Date(customStartDate);
        const endDate = new Date(customEndDate);

        from = format(startDate, "yyyy-MM-dd");
        to = format(endDate, "yyyy-MM-dd");

        // Validate dates for custom range
        if (!from || !to) {
          console.error("Custom date range is incomplete");
          setShowTaskBreakdownSkeleton(false);
          setLoading(false); // Clear loading state
          isLoadingRef.current = false;
          return;
        }
      } else {
        from = format(currentStartDate, "yyyy-MM-dd");
        to = format(currentEndDate, "yyyy-MM-dd");
      }

      console.log(`Fetching utilization data for period: ${from} to ${to}`);
      const data = await getStaffUtilization(from, to);
      setUtilizationData(data);
    } catch (error) {
      console.error('Error fetching utilization data: ', error);
    } finally {
      // Always clear loading states regardless of success or failure
      setShowTaskBreakdownSkeleton(false);
      setLoading(false); // Clear global loading state
      isLoadingRef.current = false;
    }
  }, [
  timePeriod,
  customStartDate,
  customEndDate,
  currentStartDate,
  currentEndDate,
  setLoading // Add setLoading to dependencies
  ]);

  // Navigate to previous period
  const goToPreviousPeriod = () => {
    switch (timePeriod) {
      case TIME_PERIODS.WEEK:
        setCurrentStartDate(subWeeks(currentStartDate, 1));
        setCurrentEndDate(subWeeks(currentEndDate, 1));
        break;
      case TIME_PERIODS.SEMIMONTH:
        // If first half of month, go to second half of previous month
        if (currentStartDate.getDate() === 1) {
          const prevMonth = subMonths(currentStartDate, 1);
          setCurrentStartDate(
            new Date(prevMonth.getFullYear(), prevMonth.getMonth(), 16)
          );
          setCurrentEndDate(endOfMonth(prevMonth));
        } else {
          // If second half, go to first half of same month
          setCurrentStartDate(
            new Date(
              currentStartDate.getFullYear(),
              currentStartDate.getMonth(),
              1
            )
          );
          setCurrentEndDate(
            new Date(
              currentStartDate.getFullYear(),
              currentStartDate.getMonth(),
              15
            )
          );
        }
        break;
      case TIME_PERIODS.MONTH:
        // Calculate previous month's start and end dates properly
        const prevMonthStart = subMonths(currentStartDate, 1);
        // Use endOfMonth to get the correct last day of the previous month
        const prevMonthEnd = endOfMonth(prevMonthStart);
        setCurrentStartDate(prevMonthStart);
        setCurrentEndDate(prevMonthEnd);
        break;
      case TIME_PERIODS.QUARTER:
        // Calculate previous quarter's start and end dates properly
        const prevQuarterStart = subQuarters(currentStartDate, 1);
        // Use endOfQuarter to get the correct last day of the previous quarter
        const prevQuarterEnd = endOfQuarter(prevQuarterStart);
        setCurrentStartDate(prevQuarterStart);
        setCurrentEndDate(prevQuarterEnd);
        break;
      case TIME_PERIODS.FISCAL_YEAR:
        // Calculate previous fiscal year's start and end dates
        const currentFiscalYearStart = currentStartDate;
        const prevFiscalYearStart = subYears(currentFiscalYearStart, 1);
        const prevFiscalYearEnd = new Date(
          prevFiscalYearStart.getFullYear() + 1,
          5,
          30
        ); // June 30th
        setCurrentStartDate(prevFiscalYearStart);
        setCurrentEndDate(prevFiscalYearEnd);
        break;
      case TIME_PERIODS.YEAR:
        // Calculate previous year's start and end dates
        const prevYearStart = subYears(startOfYear(currentStartDate), 1);
        const prevYearEnd = endOfYear(prevYearStart);
        setCurrentStartDate(prevYearStart);
        setCurrentEndDate(prevYearEnd);
        break;
    }
  };

  // Navigate to next period
  const goToNextPeriod = () => {
    const today = new Date();

    // Calculate next period dates
    let nextStart, nextEnd;

    switch (timePeriod) {
      case TIME_PERIODS.WEEK:
        nextStart = addWeeks(currentStartDate, 1);
        nextEnd = addWeeks(currentEndDate, 1);
        break;
      case TIME_PERIODS.SEMIMONTH:
        // If first half of month, go to second half
        if (currentStartDate.getDate() === 1) {
          nextStart = new Date(
            currentStartDate.getFullYear(),
            currentStartDate.getMonth(),
            16
          );
          nextEnd = endOfMonth(currentStartDate);
        } else {
          // If second half, go to first half of next month
          const nextMonth = addMonths(currentStartDate, 1);
          nextStart = new Date(
            nextMonth.getFullYear(),
            nextMonth.getMonth(),
            1
          );
          nextEnd = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 15);
        }
        break;
      case TIME_PERIODS.MONTH:
        // Calculate next month's start and end dates properly
        nextStart = addMonths(currentStartDate, 1);
        // Use endOfMonth to get the correct last day of the next month
        nextEnd = endOfMonth(nextStart);
        break;
      case TIME_PERIODS.QUARTER:
        // Calculate next quarter's start and end dates properly
        nextStart = addQuarters(currentStartDate, 1);
        // Use endOfQuarter to get the correct last day of the next quarter
        nextEnd = endOfQuarter(nextStart);
        break;
      case TIME_PERIODS.FISCAL_YEAR:
        // Calculate next fiscal year's start and end dates
        const currentFiscalYearStart = currentStartDate;
        nextStart = addYears(currentFiscalYearStart, 1);
        nextEnd = new Date(nextStart.getFullYear() + 1, 5, 30); // June 30th
        break;
      case TIME_PERIODS.YEAR:
        // Calculate next year's start and end dates
        nextStart = addYears(startOfYear(currentStartDate), 1);
        nextEnd = endOfYear(nextStart);
        break;
      default:
        return; // Don't navigate for custom or all time
    }

    // Check if we're trying to navigate to the current week/period
    const isCurrentPeriod = isCurrentTimePeriod(nextStart, nextEnd, timePeriod);

    // Allow navigation up to and including the current period
    if (nextStart <= today || isCurrentPeriod) {
      setCurrentStartDate(nextStart);
      setCurrentEndDate(nextEnd);
    }
  };

  // Get period title
  const getPeriodTitle = () => {
    // Format dates consistently for all time periods
    const formatDateRange = (prefix: string, start: Date, end: Date) => {
      return `${prefix}: ${format(start, "dd MMM")} – ${format(
        end,
        "dd MMM yyyy"
      )}`;
    };

    switch (timePeriod) {
      case TIME_PERIODS.WEEK:{
          const isCurrentWeek = isCurrentTimePeriod(
            currentStartDate,
            currentEndDate,
            TIME_PERIODS.WEEK
          );
          return formatDateRange(
            isCurrentWeek ? "This week" : "Week",
            currentStartDate,
            currentEndDate
          );
        }
      case TIME_PERIODS.SEMIMONTH:{
          const isCurrentPeriod = isCurrentTimePeriod(
            currentStartDate,
            currentEndDate,
            TIME_PERIODS.SEMIMONTH
          );
          const prefix = isCurrentPeriod ? "This " : "";
          return currentStartDate.getDate() === 1 ?
          formatDateRange(
            `${prefix}First half`,
            currentStartDate,
            currentEndDate
          ) :
          formatDateRange(
            `${prefix}Second half`,
            currentStartDate,
            currentEndDate
          );
        }
      case TIME_PERIODS.MONTH:{
          const isCurrentMonth = isCurrentTimePeriod(
            currentStartDate,
            currentEndDate,
            TIME_PERIODS.MONTH
          );
          const monthName = format(currentStartDate, "MMMM yyyy");
          return formatDateRange(
            isCurrentMonth ? `This month (${monthName})` : monthName,
            currentStartDate,
            currentEndDate
          );
        }
      case TIME_PERIODS.QUARTER:{
          const isCurrentQuarter = isCurrentTimePeriod(
            currentStartDate,
            currentEndDate,
            TIME_PERIODS.QUARTER
          );
          const quarter = Math.floor(currentStartDate.getMonth() / 3) + 1;
          const quarterLabel = `Q${quarter} ${currentStartDate.getFullYear()}`;
          return formatDateRange(
            isCurrentQuarter ? `This quarter (${quarterLabel})` : quarterLabel,
            currentStartDate,
            currentEndDate
          );
        }
      case TIME_PERIODS.FISCAL_YEAR:{
          const isCurrentFiscalYear = isCurrentTimePeriod(
            currentStartDate,
            currentEndDate,
            TIME_PERIODS.FISCAL_YEAR
          );
          const fiscalYearEnd = currentEndDate.getFullYear();
          const fiscalYearLabel = `FY${fiscalYearEnd}`;
          return formatDateRange(
            isCurrentFiscalYear ?
            `This fiscal year (${fiscalYearLabel})` :
            fiscalYearLabel,
            currentStartDate,
            currentEndDate
          );
        }
      case TIME_PERIODS.YEAR:{
          const isCurrentYear = isCurrentTimePeriod(
            currentStartDate,
            currentEndDate,
            TIME_PERIODS.YEAR
          );
          const yearLabel = `${currentStartDate.getFullYear()}`;
          return formatDateRange(
            isCurrentYear ? `This year (${yearLabel})` : yearLabel,
            currentStartDate,
            currentEndDate
          );
        }
      case TIME_PERIODS.ALL_TIME:
        return `Past year: ${format(
          currentStartDate,
          "dd MMM yyyy"
        )} – ${format(currentEndDate, "dd MMM yyyy")} (Harvest API limit)`;
      case TIME_PERIODS.CUSTOM:
        if (customStartDate && customEndDate) {
          return `Custom: ${format(
            new Date(customStartDate),
            "dd MMM yyyy"
          )} – ${format(new Date(customEndDate), "dd MMM yyyy")}`;
        }
        return "Custom range";
    }
  };

  // Fetch data when time period or date range changes - but NOT when fetchUtilizationData changes
  useEffect(() => {
    // This useEffect should not re-run when fetchUtilizationData changes
    // Only when actual data parameters change
    if (
    timePeriod !== TIME_PERIODS.CUSTOM ||
    customStartDate && customEndDate)
    {
      console.log("Date parameters changed, fetching new data");
      fetchUtilizationData();
    }
  }, [
  // Remove fetchUtilizationData from the dependency array to prevent infinite loops
  timePeriod,
  customStartDate,
  customEndDate,
  currentStartDate,
  currentEndDate
  // fetchUtilizationData is intentionally omitted from dependencies
  // eslint-disable-next-line react-hooks/exhaustive-deps
  ]);

  // Helper function to check if a date range corresponds to the current period
  const isCurrentTimePeriod = (
  start: Date,
  end: Date,
  periodType: string)
  : boolean => {
    const today = new Date();

    switch (periodType) {
      case TIME_PERIODS.WEEK:{
          const currentWeekStart = startOfWeek(today, { weekStartsOn: 1 });
          const currentWeekEnd = endOfWeek(today, { weekStartsOn: 1 });
          return (
            isSameDay(start, currentWeekStart) && isSameDay(end, currentWeekEnd));

        }
      case TIME_PERIODS.SEMIMONTH:{
          // First or second half of current month
          const currentMonth = today.getMonth();
          const currentYear = today.getFullYear();
          const isFirstHalf = today.getDate() <= 15;

          if (isFirstHalf) {
            const firstHalfStart = new Date(currentYear, currentMonth, 1);
            const firstHalfEnd = new Date(currentYear, currentMonth, 15);
            return (
              isSameDay(start, firstHalfStart) && isSameDay(end, firstHalfEnd));

          } else {
            const secondHalfStart = new Date(currentYear, currentMonth, 16);
            const secondHalfEnd = endOfMonth(today);
            return (
              isSameDay(start, secondHalfStart) && isSameDay(end, secondHalfEnd));

          }
        }
      case TIME_PERIODS.MONTH:{
          const currentMonthStart = startOfMonth(today);
          const currentMonthEnd = endOfMonth(today);
          return (
            isSameDay(start, currentMonthStart) && isSameDay(end, currentMonthEnd));

        }
      case TIME_PERIODS.QUARTER:{
          const currentQuarterStart = startOfQuarter(today);
          const currentQuarterEnd = endOfQuarter(today);
          return (
            isSameDay(start, currentQuarterStart) &&
            isSameDay(end, currentQuarterEnd));

        }
      case TIME_PERIODS.FISCAL_YEAR:{
          // Assuming fiscal year starts in July
          const currentYear = today.getFullYear();
          const fiscalYearStart =
          today.getMonth() >= 6 ?
          new Date(currentYear, 6, 1) // July 1st of current year
          : new Date(currentYear - 1, 6, 1); // July 1st of previous year
          const fiscalYearEnd =
          today.getMonth() >= 6 ?
          new Date(currentYear + 1, 5, 30) // June 30th of next year
          : new Date(currentYear, 5, 30); // June 30th of current year
          return (
            isSameDay(start, fiscalYearStart) && isSameDay(end, fiscalYearEnd));

        }
      case TIME_PERIODS.YEAR:{
          const currentYearStart = startOfYear(today);
          const currentYearEnd = endOfYear(today);
          return (
            isSameDay(start, currentYearStart) && isSameDay(end, currentYearEnd));

        }
      default:
        return false;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with period selector and leave toggle */}
      <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center">
        <div className="flex-grow">
          <PeriodSelector
            timePeriod={timePeriod}
            setTimePeriod={setTimePeriod}
            isDropdownOpen={isDropdownOpen}
            setIsDropdownOpen={setIsDropdownOpen}
            goToPreviousPeriod={goToPreviousPeriod}
            goToNextPeriod={goToNextPeriod}
            getPeriodTitle={getPeriodTitle}
            customStartDate={customStartDate}
            setCustomStartDate={setCustomStartDate}
            customEndDate={customEndDate}
            setCustomEndDate={setCustomEndDate}
            fetchUtilizationData={fetchUtilizationData}
            TIME_PERIODS={TIME_PERIODS} />

        </div>

        {/* Leave toggle - positioned to the right with added padding */}
        <div className="flex items-center space-x-2 ml-auto pl-6">
          <span className="text-sm text-secondary">
            Exclude leave from capacity
          </span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={excludeLeave}
              onChange={() => setExcludeLeave(!excludeLeave)}
              className="sr-only peer" />

            <div className="w-11 h-6 bg-surface-alt peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-surface-card after:border-strong after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-color"></div>
          </label>
        </div>
      </div>

      {/* Loading state for summary components */}
      {isLoading && loadingType === "harvest" ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-pulse">
          {/* Skeleton for summary stats */}
          <div className="lg:col-span-1 bg-surface-alt p-4 rounded-lg">
            <div className="h-6 bg-surface-alt rounded w-1/3 mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-surface-alt rounded w-2/3"></div>
              <div className="h-4 bg-surface-alt rounded w-3/4"></div>
              <div className="h-4 bg-surface-alt rounded w-1/2"></div>
            </div>
          </div>

          {/* Skeleton for utilization graph */}
          <div className="lg:col-span-1 bg-surface-alt p-4 rounded-lg">
            <div className="h-6 bg-surface-alt rounded w-1/3 mb-4"></div>
            <div className="h-40 bg-surface-alt rounded-lg"></div>
          </div>
        </div>
      ) : (
        /* Display components when data is loaded */
        utilizationData.length > 0 && (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Summary with stats - takes 50% of the width */}
              <div className="lg:col-span-1">
                <UtilizationSummary
              utilizationData={utilizationData}
              fromDate={
              timePeriod === TIME_PERIODS.CUSTOM ?
              customStartDate :
              format(currentStartDate, "yyyy-MM-dd")
              }
              toDate={
              timePeriod === TIME_PERIODS.CUSTOM ?
              customEndDate :
              format(currentEndDate, "yyyy-MM-dd")
              }
              excludeLeave={excludeLeave} />

              </div>

              {/* Compact staff utilization graph - takes 50% of the width */}
              <div className="lg:col-span-1">
                <UtilizationSummaryGraph
              utilizationData={utilizationData}
              excludeLeave={excludeLeave}
              overallUtilization={
              utilizationData.length > 0 ?
              excludeLeave ?
              Math.round(
                utilizationData.reduce(
                  (sum, staff) => sum + staff.billableHours,
                  0
                ) /
                Math.max(
                  0.1,
                  utilizationData.reduce(
                    (sum, staff) => sum + staff.totalHours,
                    0
                  ) -
                  utilizationData.reduce(
                    (sum, staff) => sum + staff.leaveHours,
                    0
                  )
                ) *
                100
              ) :
              Math.round(
                utilizationData.reduce(
                  (sum, staff) => sum + staff.billableHours,
                  0
                ) /
                utilizationData.reduce(
                  (sum, staff) => sum + staff.totalHours,
                  0
                ) *
                100
              ) :
              0
              } />

              </div>
            </div>

            {/* Task breakdown in full-width box */}
            {showTaskBreakdownSkeleton ?
        <div className="bg-surface-alt p-4 rounded-lg animate-pulse">
                <div className="h-6 bg-surface-alt rounded w-1/4 mb-4"></div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-40 bg-surface-alt rounded-lg"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-surface-alt rounded w-3/4"></div>
                    <div className="h-4 bg-surface-alt rounded w-2/3"></div>
                    <div className="h-4 bg-surface-alt rounded w-full"></div>
                    <div className="h-4 bg-surface-alt rounded w-1/2"></div>
                  </div>
                </div>
              </div> :

        <TaskBreakdownSummary
          utilizationData={utilizationData}
          excludeLeave={excludeLeave} />

        }
          </>
        )
      )}

      {/* Table component with task breakdown */}
      <UtilizationTable
        utilizationData={utilizationData}
        isLoading={isLoading}
        loadingType={loadingType}
        fromDate={
        timePeriod === TIME_PERIODS.CUSTOM ?
        customStartDate :
        format(currentStartDate, "yyyy-MM-dd")
        }
        toDate={
        timePeriod === TIME_PERIODS.CUSTOM ?
        customEndDate :
        format(currentEndDate, "yyyy-MM-dd")
        }
        excludeLeave={excludeLeave} />


      {/* Explanation of utilization calculation */}
      <div className="bg-surface-page p-4 rounded-lg">
        <h3 className="text-sm font-medium text-primary mb-2">
          About Utilization Calculation
        </h3>
        <p className="text-sm text-secondary">
          Utilization is calculated as{" "}
          <strong>billable hours ÷ capacity</strong>. When &quot;Exclude leave
          from capacity&quot; is {excludeLeave ? "enabled" : "disabled"},
          {excludeLeave ?
          " leave time is subtracted from capacity, showing utilisation based only on available working time." :
          " leave time counts against capacity, showing overall utilisation of total possible working time."}{" "}
          Capacity is retrieved from Harvest for each user (typically 37.5 hours
          per week), adjusted for the selected time period based on actual
          workdays (excluding weekends). For time periods that include future
          dates, capacity is prorated to only include days up to today. This
          differs from Harvest&apos;s calculation, which uses
          <strong> billable hours ÷ total hours</strong>.
        </p>
      </div>
    </div>
  );
};

export default UtilizationReport;