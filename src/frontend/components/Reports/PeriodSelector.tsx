import React, { useState } from "react";
import { format } from "date-fns";
import { useLoading } from "../../contexts/LoadingContext";
import { Button } from "../ui";
import { Input } from "@/frontend/components/ui/Input";

interface PeriodSelectorProps {
  timePeriod: string;
  setTimePeriod: (period: string) => void;
  isDropdownOpen: boolean;
  setIsDropdownOpen: (isOpen: boolean) => void;
  goToPreviousPeriod: () => void;
  goToNextPeriod: () => void;
  getPeriodTitle: () => string;
  customStartDate: string;
  setCustomStartDate: (date: string) => void;
  customEndDate: string;
  setCustomEndDate: (date: string) => void;
  fetchUtilizationData: () => void;
  TIME_PERIODS: Record<string, string>;
}

/**
 * A component for selecting and navigating time periods
 * Styled to match Harvest's UI
 */
const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  timePeriod,
  setTimePeriod,
  isDropdownOpen,
  setIsDropdownOpen,
  goToPreviousPeriod,
  goToNextPeriod,
  getPeriodTitle,
  customStartDate,
  setCustomStartDate,
  customEndDate,
  setCustomEndDate,
  fetchUtilizationData,
  TIME_PERIODS
}) => {
  // Access the loading context to know when we're making API calls
  const { isLoading, loadingType } = useLoading();

  // Check if we're specifically loading data from Harvest
  const isLoadingHarvestData = isLoading && loadingType === "harvest";

  // State for period dropdown
  const [isPeriodDropdownOpen, setIsPeriodDropdownOpen] = useState(false);

  // Get the display name for the current time period
  const getTimeFrameDisplayName = () => {
    switch (timePeriod) {
      case TIME_PERIODS.WEEK:
        return "Week";
      case TIME_PERIODS.SEMIMONTH:
        return "Semimonth";
      case TIME_PERIODS.MONTH:
        return "Month";
      case TIME_PERIODS.QUARTER:
        return "Quarter";
      case TIME_PERIODS.FISCAL_YEAR:
        return "Fiscal year";
      case TIME_PERIODS.YEAR:
        return "Year";
      case TIME_PERIODS.ALL_TIME:
        return "All time";
      case TIME_PERIODS.CUSTOM:
        return "Custom";
      default:
        return "Select period";
    }
  };

  return (
    <div className="space-y-4">
      {/* Top row with period title and navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            onClick={goToPreviousPeriod}
            disabled={
            timePeriod === TIME_PERIODS.CUSTOM ||
            timePeriod === TIME_PERIODS.ALL_TIME ||
            isLoadingHarvestData
            }
            variant="ghost"
            size="sm"
            className="p-1 rounded-full"
            aria-label="Previous period">

            <svg
              className="w-5 h-5 text-secondary dark:text-subtle"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7" />

            </svg>
          </Button>

          <Button
            onClick={goToNextPeriod}
            disabled={
            timePeriod === TIME_PERIODS.CUSTOM ||
            timePeriod === TIME_PERIODS.ALL_TIME ||
            isLoadingHarvestData
            }
            variant="ghost"
            size="sm"
            className="p-1 rounded-full"
            aria-label="Next period">

            <svg
              className="w-5 h-5 text-secondary dark:text-subtle"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7" />

            </svg>
          </Button>

          <h2 className="text-xl font-semibold text-primary dark:text-primary">
            {isLoadingHarvestData ?
            <span className="flex items-center">
                <span className="animate-spin h-4 w-4 mr-2 border-t-2 border-b-2 border-primary rounded-full"></span>
                Loading...
              </span> :

            getPeriodTitle()
            }
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="primary"
            onClick={fetchUtilizationData}
            disabled={isLoadingHarvestData}>

            {isLoadingHarvestData ?
            <>
                <span className="animate-spin h-3 w-3 mr-1 border-t-2 border-b-2 border-white rounded-full"></span>
                <span>Loading...</span>
              </> :

            "Update report"
            }
          </Button>

          {/* Time period dropdown */}
          <div className="relative">
            <Button
              onClick={() => setIsPeriodDropdownOpen(!isPeriodDropdownOpen)}
              disabled={isLoadingHarvestData}
              variant="secondary"
              className="flex items-center">

              <span>{getTimeFrameDisplayName()}</span>
              <svg
                className="w-3 h-3 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">

                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7" />

              </svg>
            </Button>

            {isPeriodDropdownOpen &&
            <div className="absolute right-0 z-10 mt-1 w-48 bg-surface-card dark:bg-surface-card rounded-md shadow-lg border border-default dark:border-default">
                <ul className="py-1">
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.WEEK);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Week
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.SEMIMONTH);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Semimonth
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.MONTH);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Month
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.QUARTER);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Quarter
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.FISCAL_YEAR);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Fiscal year
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.YEAR);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Year
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.ALL_TIME);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      All time
                    </Button>
                  </li>
                  <li>
                    <Button
                    variant="ghost"
                    className="w-full text-left"
                    onClick={() => {
                      setTimePeriod(TIME_PERIODS.CUSTOM);
                      setIsPeriodDropdownOpen(false);
                    }}>

                      Custom
                    </Button>
                  </li>
                </ul>
              </div>
            }
          </div>
        </div>
      </div>

      {/* Custom date range inputs - only shown when Custom is selected */}
      {timePeriod === TIME_PERIODS.CUSTOM &&
      <div className="flex items-center p-3 bg-surface-page dark:bg-surface-alt rounded-md">
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-primary dark:text-subtle">
              View report from
            </span>
            <Input
            type="date"
            className="rounded-md border-strong shadow-sm focus:border-secondary focus:ring focus:ring-secondary focus:ring-opacity-50 dark:bg-surface-alt dark:border-strong dark:text-primary disabled:opacity-50 disabled:cursor-not-allowed text-sm py-1 px-2"
            value={customStartDate}
            onChange={(e) => setCustomStartDate(e.target.value)}
            disabled={isLoadingHarvestData} />

            <span className="text-primary dark:text-subtle">to</span>
            <Input
            type="date"
            className="rounded-md border-strong shadow-sm focus:border-secondary focus:ring focus:ring-secondary focus:ring-opacity-50 dark:bg-surface-alt dark:border-strong dark:text-primary disabled:opacity-50 disabled:cursor-not-allowed text-sm py-1 px-2"
            value={customEndDate}
            onChange={(e) => setCustomEndDate(e.target.value)}
            disabled={isLoadingHarvestData} />

            <Button
            variant="primary"
            onClick={fetchUtilizationData}
            disabled={
            isLoadingHarvestData || !customStartDate || !customEndDate
            }>

              {isLoadingHarvestData ?
            <span className="flex items-center">
                  <span className="animate-spin h-3 w-3 mr-1 border-t-2 border-b-2 border-white rounded-full"></span>
                  Loading...
                </span> :

            "Update report"
            }
            </Button>
          </div>
        </div>
      }
    </div>);

};

export default PeriodSelector;