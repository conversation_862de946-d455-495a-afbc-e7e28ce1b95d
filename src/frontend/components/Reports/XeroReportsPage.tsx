import React, { useState, useEffect } from "react";
import { format, addDays } from "date-fns";
import { getXeroBalanceSheet, XeroBalanceSheetResponse } from "../../api/xero";
import { useAuthStatus } from "../../hooks/useAuthStatus";
import { useLoading } from "../../contexts/LoadingContext";
import { LoadingIndicator } from "../ForwardProjection";
import BalanceSheetReport from "./BalanceSheetReport";

// Define time period options to match Xero's options
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Radio } from "@/frontend/components/ui/Radio";

const TIME_PERIODS = {
  TODAY: "today",
  END_OF_MONTH: "end_of_month",
  END_OF_LAST_MONTH: "end_of_last_month",
  END_OF_LAST_QUARTER: "end_of_last_quarter",
  END_OF_LAST_FINANCIAL_YEAR: "end_of_last_financial_year",
  CUSTOM: "custom",
};

/**
 * Component for displaying Xero financial reports
 */
const XeroReportsPage: React.FC = () => {
  const { isAuthenticated } = useAuthStatus();
  const { setLoading } = useLoading();

  // State for active report type
  const [activeReportType, setActiveReportType] = useState<
    "balance-sheet" | "profit-loss" | "bank-summary"
  >("balance-sheet");

  // State for balance sheet data
  const [balanceSheetData, setBalanceSheetData] =
    useState<XeroBalanceSheetResponse | null>(null);

  // State for loading
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // State for date selection
  const [reportDate, setReportDate] = useState<Date>(new Date());

  // State for period selector dropdown
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [timePeriod, setTimePeriod] = useState(TIME_PERIODS.TODAY);
  const [customDate, setCustomDate] = useState(
    format(new Date(), "yyyy-MM-dd"),
  );

  // Fetch balance sheet data
  const fetchBalanceSheet = async (date: Date = new Date()) => {
    if (!isAuthenticated) {
      setError("Please authenticate with Xero first");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log(
        `Fetching balance sheet for date: ${date.toISOString().split("T")[0]}`,
      );
      const data = await getXeroBalanceSheet(date);
      setBalanceSheetData(data);
    } catch (err: any) {
      console.error("Error fetching balance sheet:", err);
      setError(err.message || "Failed to fetch balance sheet");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on initial load
  useEffect(() => {
    if (isAuthenticated) {
      fetchBalanceSheet(reportDate);
    }
  }, [isAuthenticated]);

  // Handle period change
  const handlePeriodChange = (period: string) => {
    setTimePeriod(period);

    // Update report date based on period
    let newDate = new Date();

    // Calculate the date based on the selected period
    switch (period) {
      case TIME_PERIODS.TODAY:
        // Today's date (already set)
        break;
      case TIME_PERIODS.END_OF_MONTH:
        // End of current month
        newDate = new Date(newDate.getFullYear(), newDate.getMonth() + 1, 0);
        break;
      case TIME_PERIODS.END_OF_LAST_MONTH:
        // End of last month
        newDate = new Date(newDate.getFullYear(), newDate.getMonth(), 0);
        break;
      case TIME_PERIODS.END_OF_LAST_QUARTER:
        // End of last quarter
        const currentMonth = newDate.getMonth();
        const currentQuarter = Math.floor(currentMonth / 3);
        const lastQuarterEndMonth = currentQuarter * 3 - 1;
        newDate = new Date(newDate.getFullYear(), lastQuarterEndMonth + 1, 0);
        break;
      case TIME_PERIODS.END_OF_LAST_FINANCIAL_YEAR:
        // End of last financial year (June 30)
        const currentYear = newDate.getFullYear();
        const month = newDate.getMonth();
        // If we're past June, use June of this year, otherwise use June of last year
        if (month >= 6) {
          // 6 is July (0-indexed)
          newDate = new Date(currentYear, 5, 30); // 5 is June (0-indexed)
        } else {
          newDate = new Date(currentYear - 1, 5, 30);
        }
        break;
      case TIME_PERIODS.CUSTOM:
        newDate = new Date(customDate);
        break;
    }

    setReportDate(newDate);
    fetchBalanceSheet(newDate);
  };

  // Handle custom date change
  const handleCustomDateChange = (date: string) => {
    setCustomDate(date);
    if (timePeriod === TIME_PERIODS.CUSTOM) {
      const newDate = new Date(date);
      setReportDate(newDate);
      fetchBalanceSheet(newDate);
    }
  };

  // Get period title
  const getPeriodTitle = (): string => {
    switch (timePeriod) {
      case TIME_PERIODS.TODAY:
        return `Today (${format(reportDate, "dd MMM yyyy")})`;
      case TIME_PERIODS.END_OF_MONTH:
        return `End of this month (${format(reportDate, "dd MMM yyyy")})`;
      case TIME_PERIODS.END_OF_LAST_MONTH:
        return `End of last month (${format(reportDate, "dd MMM yyyy")})`;
      case TIME_PERIODS.END_OF_LAST_QUARTER:
        return `End of last quarter (${format(reportDate, "dd MMM yyyy")})`;
      case TIME_PERIODS.END_OF_LAST_FINANCIAL_YEAR:
        return `End of last financial year (${format(
          reportDate,
          "dd MMM yyyy",
        )})`;
      case TIME_PERIODS.CUSTOM:
        return `Custom date (${format(new Date(customDate), "dd MMM yyyy")})`;
      default:
        return format(reportDate, "dd MMM yyyy");
    }
  };

  // Check if the selected date is in the future
  const isDateInFuture = (): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for comparison

    const compareDate = new Date(reportDate);
    compareDate.setHours(0, 0, 0, 0);

    return compareDate > today;
  };

  // Check if the data is from a previous year
  const isDataFromPreviousYear = (): boolean => {
    if (!balanceSheetData || !balanceSheetData.data) {
      return false;
    }

    // Get the current year
    const currentYear = new Date().getFullYear();

    // Try to extract the year from the report data
    const report =
      balanceSheetData.data?.Reports?.[0] ||
      balanceSheetData.data?.reports?.[0];
    if (!report) {
      return false;
    }

    const headerRow = report.Rows?.[0] || report.rows?.[0];
    if (!headerRow) {
      return false;
    }

    const headerCells = headerRow.Cells || headerRow.cells || [];
    if (headerCells.length === 0) {
      return false;
    }

    const headerValue =
      headerCells[headerCells.length - 1].Value ||
      headerCells[headerCells.length - 1].value;
    if (!headerValue || typeof headerValue !== "string") {
      return false;
    }

    // Extract the year from the header value
    const yearMatch = headerValue.match(/20\d\d/);
    if (!yearMatch) {
      return false;
    }

    const dataYear = parseInt(yearMatch[0]);

    // Check if the data year is less than the current year
    return dataYear < currentYear;
  };

  return (
    <div className="space-y-6">
      {/* Report type selector */}
      <div className="flex space-x-4">
        <Button
          variant="secondary"
          onClick={() => setActiveReportType("balance-sheet")}
          className={`px-4 py-2 rounded-md ${
            activeReportType === "balance-sheet"
              ? "bg-secondary text-primary"
              : "bg-surface-alt text-primary hover:bg-surface-alt dark:hover:bg-surface-alt"
          }`}
        >
          Balance Sheet
        </Button>
        <Button
          variant="secondary"
          onClick={() => setActiveReportType("profit-loss")}
          className={`px-4 py-2 rounded-md ${
            activeReportType === "profit-loss"
              ? "bg-secondary text-primary"
              : "bg-surface-alt text-primary hover:bg-surface-alt dark:hover:bg-surface-alt"
          }`}
          disabled
        >
          Profit & Loss
        </Button>
        <Button
          variant="secondary"
          onClick={() => setActiveReportType("bank-summary")}
          className={`px-4 py-2 rounded-md ${
            activeReportType === "bank-summary"
              ? "bg-secondary text-primary"
              : "bg-surface-alt text-primary hover:bg-surface-alt dark:hover:bg-surface-alt"
          }`}
          disabled
        >
          Bank Summary
        </Button>
      </div>

      {/* Date selector */}
      {/* Warning about outdated data */}
      {balanceSheetData && !isLoading && !error && isDataFromPreviousYear() && (
        <div className="bg-warning-light/20 border border-warning text-warning p-4 rounded-md mb-4">
          <div className="flex items-start">
            <svg
              className="w-5 h-5 mr-2 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <div className="flex-1">
              <p className="font-medium">Outdated Data Warning</p>
              <p className="text-sm mt-1">
                The Xero API is returning data from 2023 instead of 2025. Please
                check your Xero connection and data settings.
              </p>
              <p className="text-xs mt-2">
                This may indicate an issue with your Xero organization's data or
                the API connection. Try refreshing your Xero connection or
                contact support if this persists.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="relative">
          <Button
            variant="secondary"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          >
            <span>{getPeriodTitle()}</span>
            <svg
              className="w-5 h-5 text-subtle"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </Button>

          {isDropdownOpen && (
            <div className="absolute z-10 mt-1 w-56 bg-surface-card rounded-md shadow-lg border border-default">
              <div className="p-2">
                <div className="space-y-2">
                  <Button
                    variant="secondary"
                    onClick={() => {
                      handlePeriodChange(TIME_PERIODS.TODAY);
                      setIsDropdownOpen(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md ${
                      timePeriod === TIME_PERIODS.TODAY
                        ? "bg-secondary text-primary"
                        : "hover:bg-surface-alt dark:hover:bg-surface-elevated"
                    }`}
                  >
                    Today
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      handlePeriodChange(TIME_PERIODS.END_OF_MONTH);
                      setIsDropdownOpen(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md ${
                      timePeriod === TIME_PERIODS.END_OF_MONTH
                        ? "bg-secondary text-primary"
                        : "hover:bg-surface-alt dark:hover:bg-surface-elevated"
                    }`}
                  >
                    End of this month
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      handlePeriodChange(TIME_PERIODS.END_OF_LAST_MONTH);
                      setIsDropdownOpen(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md ${
                      timePeriod === TIME_PERIODS.END_OF_LAST_MONTH
                        ? "bg-secondary text-primary"
                        : "hover:bg-surface-alt dark:hover:bg-surface-elevated"
                    }`}
                  >
                    End of last month
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      handlePeriodChange(TIME_PERIODS.END_OF_LAST_QUARTER);
                      setIsDropdownOpen(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md ${
                      timePeriod === TIME_PERIODS.END_OF_LAST_QUARTER
                        ? "bg-secondary text-primary"
                        : "hover:bg-surface-alt dark:hover:bg-surface-elevated"
                    }`}
                  >
                    End of last quarter
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      handlePeriodChange(
                        TIME_PERIODS.END_OF_LAST_FINANCIAL_YEAR,
                      );
                      setIsDropdownOpen(false);
                    }}
                    className={`w-full text-left px-3 py-2 rounded-md ${
                      timePeriod === TIME_PERIODS.END_OF_LAST_FINANCIAL_YEAR
                        ? "bg-secondary text-primary"
                        : "hover:bg-surface-alt dark:hover:bg-surface-elevated"
                    }`}
                  >
                    End of last financial year
                  </Button>
                  <div className="border-t border-default pt-2">
                    <div
                      className={`px-3 py-2 rounded-md ${
                        timePeriod === TIME_PERIODS.CUSTOM
                          ? "bg-secondary text-primary"
                          : "hover:bg-surface-alt dark:hover:bg-surface-elevated"
                      }`}
                    >
                      <div className="flex items-center mb-2">
                        <Radio
                          id="custom-date"
                          name="time-period"
                          checked={timePeriod === TIME_PERIODS.CUSTOM}
                          onChange={() =>
                            handlePeriodChange(TIME_PERIODS.CUSTOM)
                          }
                          label="Custom Date"
                        />
                      </div>
                      <Input
                        type="date"
                        value={customDate}
                        onChange={(e) => handleCustomDateChange(e.target.value)}
                        className="w-full px-2 py-1 border border-strong rounded-md"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <Button variant="primary" onClick={() => fetchBalanceSheet(reportDate)}>
          Refresh
        </Button>
      </div>

      {/* Warning about data mismatch */}
      {balanceSheetData && !isLoading && !error && isDataFromPreviousYear() && (
        <div className="bg-warning-light/20 border border-warning text-warning p-4 rounded-md mb-4">
          <div className="flex items-start">
            <svg
              className="w-5 h-5 mr-2 mt-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <div className="flex-1">
              <p className="font-medium">Data Mismatch</p>
              <p className="text-sm mt-1">
                The Xero API is returning data from a different year than
                requested. This may be due to how Xero handles Australian
                financial years (July-June).
              </p>
              <p className="text-xs mt-2">
                Please verify that your Xero organization has up-to-date
                financial data for the requested period. If this issue persists,
                you may need to adjust your reporting period or contact Xero
                support.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Content area */}
      <div className="bg-surface-card shadow rounded-lg p-6">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <LoadingIndicator text="Loading Xero data..." />
          </div>
        ) : error ? (
          <div className="text-center text-error p-4 border border-error rounded-md bg-error-light/20">
            <p>{error}</p>
            <Button
              variant="primary"
              onClick={() => fetchBalanceSheet(reportDate)}
            >
              Try Again
            </Button>
          </div>
        ) : (
          <>
            {activeReportType === "balance-sheet" && balanceSheetData && (
              <BalanceSheetReport data={balanceSheetData} />
            )}
            {activeReportType === "profit-loss" && (
              <div className="text-center text-muted p-4">
                <p>Profit & Loss report will be available soon.</p>
              </div>
            )}
            {activeReportType === "bank-summary" && (
              <div className="text-center text-muted p-4">
                <p>Bank Summary report will be available soon.</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default XeroReportsPage;
