import React, { useState, useMemo } from "react";
import {
  StaffUtilization,
  TaskBreakdown,
} from "../../../services/harvest/time-report-service";
import SimpleTooltip, { TooltipData } from "../common/SimpleTooltip";
import { getTaskColorMap } from "../../utils/colorUtils";

interface TaskBreakdownSummaryProps {
  utilizationData: StaffUtilization[];
  excludeLeave?: boolean; // Whether to exclude leave from capacity calculations
}

/**
 * A component that displays a task breakdown summary in a full-width box
 */
const TaskBreakdownSummary = ({
  utilizationData,
  excludeLeave = false,
}: TaskBreakdownSummaryProps) => {
  // State for tooltip
  const [tooltip, setTooltip] = useState<TooltipData>({
    visible: false,
    content: "",
    x: 0,
    y: 0,
  });

  // If no utilization data, return null
  if (!utilizationData || utilizationData.length === 0) {
    return null;
  }

  // Define leave task names (case-insensitive)
  const LEAVE_TASK_NAMES = [
    "public holiday",
    "annual leave",
    "other unpaid leave",
    "carer"s leave",
    "community service leave",
    "compassionate leave",
    "parental leave",
    "personal leave",
    "sick leave",
  ];

  // Helper function to check if a task is a leave task
  const isLeaveTask = (taskName: string) => {
    return LEAVE_TASK_NAMES.some((leaveName) =>
      taskName.toLowerCase().includes(leaveName.toLowerCase())
    );
  };

  // Aggregate task breakdown data across all staff
  const aggregatedTasks = new Map<
    string,
    {
      taskId: number;
      taskName: string;
      hours: number;
      isBillable: boolean;
      color?: string;
      isLeave: boolean;
    }
  >();

  // Collect all tasks from all staff members
  utilizationData.forEach((staff) => {
    staff.taskBreakdown.forEach((task) => {
      // Skip leave tasks if excludeLeave is true
      const isLeave = isLeaveTask(task.taskName);
      if (excludeLeave && isLeave) {
        return;
      }

      const key = `${task.taskId}-${
        task.isBillable ? "billable" : "non-billable"
      }`;

      if (aggregatedTasks.has(key)) {
        const existingTask = aggregatedTasks.get(key)!;
        existingTask.hours += task.hours;
      } else {
        aggregatedTasks.set(key, {
          taskId: task.taskId,
          taskName: task.taskName,
          hours: task.hours,
          isBillable: task.isBillable,
          color: task.color,
          isLeave: isLeave,
        });
      }
    });
  });

  // Convert to array and sort
  const sortedTasks = Array.from(aggregatedTasks.values()).sort((a, b) => {
    // Billable tasks first
    if (a.isBillable && !b.isBillable) return -1;
    if (!a.isBillable && b.isBillable) return 1;
    // Then by hours (descending)
    return b.hours - a.hours;
  });

  // Group billable and non-billable tasks
  const billableTasks = sortedTasks.filter((task) => task.isBillable);
  const nonBillableTasks = sortedTasks.filter((task) => !task.isBillable);

  // Calculate total hours
  const totalHours = utilizationData.reduce(
    (sum: number, staff: StaffUtilization) => sum + staff.totalHours,
    0
  );

  // Calculate total billable hours
  const billableHoursTotal = billableTasks.reduce(
    (sum, task) => sum + task.hours,
    0
  );

  // Calculate total leave hours
  const totalLeaveHours = utilizationData.reduce(
    (sum: number, staff: StaffUtilization) => sum + staff.leaveHours,
    0
  );

  // Calculate overall utilization based on the toggle state
  // When excludeLeave is true, we need to recalculate the total hours without leave hours
  const effectiveHours = excludeLeave
    ? totalHours - totalLeaveHours
    : totalHours;
  const overallUtilization = Math.round(
    (billableHoursTotal / Math.max(0.1, effectiveHours)) * 100
  );

  // Use a consistent max width for better visual comparison
  const MAX_HOURS = 55;

  // Calculate positions for the billable and non-billable segments
  const billablePosition = (billableHoursTotal / MAX_HOURS) * 100;

  // Generate a color map for tasks
  const taskColorMap = useMemo(() => {
    // Create a list of all unique tasks
    const allTasks = sortedTasks.map((task) => ({
      taskId: task.taskId,
      taskName: task.taskName,
      isBillable: task.isBillable,
    }));

    return getTaskColorMap(allTasks);
  }, [sortedTasks]);

  // Handle mouse enter for billable segment
  const handleBillableMouseEnter = (e: any) => {
    setTooltip({
      visible: true,
      title: "Billable Time",
      content: `${billableHoursTotal.toFixed(
        1
      )} hours (${overallUtilization}% utilization${
        excludeLeave ? ", leave excluded" : ""
      })`,
      x: e.clientX,
      y: e.clientY,
    });
  };

  // Handle mouse enter for non-billable tasks
  const handleTaskMouseEnter = (
    e: any,
    task: { taskId: number; taskName: string; hours: number }
  ) => {
    // Use effectiveHours when leave is excluded
    const denominator = excludeLeave ? effectiveHours : totalHours;

    setTooltip({
      visible: true,
      title: task.taskName,
      content: `${task.hours.toFixed(1)} hours (${Math.round(
        (task.hours / denominator) * 100
      )}% of ${excludeLeave ? "available" : "total"} time)`,
      x: e.clientX,
      y: e.clientY,
    });
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setTooltip((prev: TooltipData) => ({ ...prev, visible: false }));
  };

  return (
    <div className="bg-surface-card dark:bg-surface-card rounded-lg shadow p-4 mt-6">
      <h3 className="text-lg font-medium text-primary dark:text-primary mb-4">
        Task Breakdown{" "}
        {excludeLeave && (
          <span className="text-xs text-muted">(leave excluded)</span>
        )}
      </h3>
      <div>
        {/* Segmented bar */}
        <div className="h-8 w-full bg-transparent rounded-sm overflow-hidden flex relative mb-3">
          {/* Billable tasks (grouped together) with badge at the end */}
          {billableHoursTotal > 0 && (
            <div
              className="h-full bg-primary-light0 dark:bg-primary-color relative cursor-pointer"
              style={{ width: `${billablePosition}%` }}
              onMouseEnter={handleBillableMouseEnter}
              onMouseLeave={handleMouseLeave}
              onMouseMove={handleBillableMouseEnter}
            >
              {/* Badge placed at the end of the billable segment */}
              {billablePosition >= 5 && (
                <span className="text-xs font-medium text-primary whitespace-nowrap px-1 py-0.5 z-10 absolute right-0 top-0 bottom-0 flex items-center">
                  {overallUtilization}%
                </span>
              )}
            </div>
          )}

          {/* Non-billable tasks */}
          {nonBillableTasks.map((task, index) => (
            <div
              key={index}
              className="h-full cursor-pointer"
              style={{
                backgroundColor: taskColorMap[task.taskId] || "#64748b", // Use our color map with slate fallback
                width: `${(task.hours / MAX_HOURS) * 100}%`,
              }}
              onMouseEnter={(e) => handleTaskMouseEnter(e, task)}
              onMouseLeave={handleMouseLeave}
              onMouseMove={(e) => handleTaskMouseEnter(e, task)}
            ></div>
          ))}
        </div>

        {/* Task legend */}
        <div className="flex flex-wrap gap-x-6 gap-y-2 text-sm">
          {/* Billable legend item */}
          <div
            className="flex items-center cursor-pointer"
            onMouseEnter={handleBillableMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div className="w-4 h-4 mr-2 bg-primary-light0 rounded-sm"></div>
            <span className="text-primary dark:text-primary">
              Billable: {billableHoursTotal.toFixed(1)} hrs
            </span>
          </div>

          {/* Non-billable legend items (show top 5) */}
          {nonBillableTasks.slice(0, 5).map((task, index) => (
            <div
              key={index}
              className="flex items-center cursor-pointer"
              onMouseEnter={(e) => handleTaskMouseEnter(e, task)}
              onMouseLeave={handleMouseLeave}
            >
              <div
                className="w-4 h-4 mr-2 rounded-sm"
                style={{
                  backgroundColor: taskColorMap[task.taskId] || "#64748b",
                }}
              ></div>
              <span className="text-primary dark:text-primary">
                {task.taskName}: {task.hours.toFixed(1)} hrs
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Tooltip */}
      <SimpleTooltip tooltip={tooltip} />
    </div>
  );
};

export default TaskBreakdownSummary;
