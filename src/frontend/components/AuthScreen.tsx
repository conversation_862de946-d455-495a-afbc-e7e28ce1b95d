import React from "react";
import XeroAuthSection from "./XeroAuthSection";

interface AuthScreenProps {
  onAuthenticated: () => void;
}

/**
 * Authentication screen component
 * Displays a clean, focused screen with just the Xero authentication button
 * Shown before the main dashboard when the user is not authenticated
 * Uses the XeroAuthSection component to handle the actual authentication logic
 */
const AuthScreen: React.FC<AuthScreenProps> = ({ onAuthenticated }) => {
  return (
    <div className="min-h-screen bg-background dark:bg-background-dark flex items-center justify-center">
      <div className="max-w-md w-full p-8 bg-surface-card dark:bg-surface-card rounded-lg shadow-lg dark:shadow-gray-900/30">
        <div className="text-center mb-8">
          <img
            src="/logo.png"
            alt="Upstream Logo"
            className="h-16 w-auto mx-auto mb-4"
          />
          <h1 className="text-2xl font-semibold text-primary dark:text-primary-light">
            Upstream
          </h1>
          <p className="text-secondary dark:text-subtle mt-2">
            Please authenticate with Xero to continue
          </p>
        </div>

        <XeroAuthSection
          onAuthenticated={() => {
            // Call the original callback to load data and update authentication state
            onAuthenticated();
          }}
        />
      </div>
    </div>
  );
};

export default AuthScreen;
