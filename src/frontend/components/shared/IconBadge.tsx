import React, { forwardRef } from "react";
import {
  iconBadgeVariants,
  type IconBadgeVariantProps,
} from "../../utils/component-variants";

interface IconBadgeProps extends IconBadgeVariantProps {
  /**
   * Image source URL
   */
  src: string;
  /**
   * Alt text for the image
   */
  alt: string;
  /**
   * Title/tooltip text
   */
  title?: string;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Loading behavior for the image
   */
  loading?: "eager" | "lazy";
  /**
   * Click handler for interactive badges
   */
  onClick?: () => void;
}

/**
 * An IconBadge component for logo/image badges with consistent sizing and styling.
 *
 * Features:
 * - Consistent sizing system
 * - Proper accessibility with alt text
 * - Loading optimization
 * - Flexible styling with variants
 * - Interactive support
 *
 * @example
 * ```tsx
 * <IconBadge
 *   src="/xero.svg"
 *   alt="Xero"
 *   title="From Xero"
 *   size="default"
 * />
 * ```
 */
export const IconBadge = forwardRef<HTMLImageElement, IconBadgeProps>(
  (
    {
      src,
      alt,
      title,
      className,
      size = "default",
      rounded = "default",
      loading = "lazy",
      onClick,
      ...props
    },
    ref,
  ) => {
    const classes = iconBadgeVariants({
      size,
      rounded,
      className,
    });

    const imageProps = {
      ref,
      src,
      alt,
      title,
      className: classes,
      loading,
      // Set explicit dimensions for performance
      width: size === "sm" ? 16 : size === "lg" ? 24 : 20,
      height: size === "sm" ? 16 : size === "lg" ? 24 : 20,
      onClick,
      // Add interactive attributes if clickable
      ...(onClick && {
        role: "button",
        tabIndex: 0,
        style: { cursor: "pointer" },
        onKeyDown: (e: React.KeyboardEvent) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onClick();
          }
        },
      }),
      ...props,
    };

    return <img {...imageProps} />;
  },
);

IconBadge.displayName = "IconBadge";

export { IconBadge as default };
