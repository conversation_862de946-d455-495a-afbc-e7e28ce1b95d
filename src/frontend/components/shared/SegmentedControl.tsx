import React from "react";
import { Button } from "@/frontend/components/ui/Button";

interface SegmentedControlOption<T> {
  value: T;
  label: string;
  icon?: React.ReactNode;
}

interface SegmentedControlProps<T> {
  /**
   * Array of options to display
   */
  options: SegmentedControlOption<T>[];

  /**
   * Currently selected value
   */
  value: T;

  /**
   * Callback when selection changes
   */
  onChange: (value: T) => void;

  /**
   * Size variant
   */
  size?: "sm' |'default" |'lg';

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Accessible label for the control group
   */"aria-label'?: string;
}

/**
 * A reusable segmented control component for selecting between multiple options.
 * 
 * Features:
 * - Type-safe option values
 * - Icon support
 * - Multiple sizes
 * - Full accessibility support
 * - Dark mode support
 * - Smooth transitions
 * 
 * @example
 * ```tsx
 * <SegmentedControl
 *   options={[
 *     { value: "daily', label:'Daily" },
 *     { value: "hourly', label:'Hourly" }
 *   ]}
 *   value={rateMode}
 *   onChange={setRateMode}
 *   aria-label="Rate display mode"
 * />
 * ```
 */
export function SegmentedControl<T extends string | number>({
  options,
  value,
  onChange,
  size ="default',
  className ="','aria-label': ariaLabel
}: SegmentedControlProps<T>) {
  const sizeClasses = {
    sm: "p-0.5",
    default: "p-1",
    lg: "p-1.5"
  };

  const buttonSizeClasses = {
    sm: "px-2 py-1 text-xs",
    default: "px-3 py-1.5 text-sm",
    lg: "px-4 py-2 text-base"
  };

  return (
    <div
      className={`inline-flex ${sizeClasses[size]} bg-surface-alt dark:bg-surface-card rounded-lg shadow-sm ${className}`}
      role="radiogroup"
      aria-label={ariaLabel}>

      {options.map((option) => {
        const isSelected = option.value === value;

        return (
          <Button variant="secondary"
          key={String(option.value)}
          onClick={() => onChange(option.value)}
          className={`${buttonSizeClasses[size]} font-medium rounded-md transition-all duration-200 flex items-center space-x-1.5 ${
          isSelected ? "bg-surface-card dark:bg-surface-alt text-primary dark:text-primary-light shadow-sm" :'text-muted dark:text-subtle hover:text-primary dark:hover:text-subtle'}`
          }
          role="radio"
          aria-checked={isSelected}
          aria-label={option.label}>

            {option.icon &&
            <span className="flex-shrink-0" aria-hidden="true">
                {option.icon}
              </span>
            }
            <span>{option.label}</span>
          </Button>);

      })}
    </div>);

}

export default SegmentedControl;