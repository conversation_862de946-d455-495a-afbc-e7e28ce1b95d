import React from "react";
import { Badge } from "../ui/Badge";

interface XeroBadgeProps {
  className?: string;
  /**
   * Click handler for interactive Xero badge
   */
  onClick?: () => void;
}

/**
 * Xero logo badge component with consistent styling and accessibility.
 *
 * Features:
 * - Uses official Xero logo SVG
 * - Consistent sizing with design system
 * - Proper accessibility attributes
 * - Optional interactive behavior
 * - Performance optimized loading
 *
 * @example
 * ```tsx
 * <XeroBadge />
 * ```
 */
const XeroBadge: React.FC<XeroBadgeProps> = ({ className, onClick }) => {
  return (
    <Badge
      variant="info"
      className={className}
      onClick={onClick}
      title="From Xero"
    >
      <img
        src="/xero.svg"
        alt="Xero"
        className="inline-block w-4 h-4 mr-1"
        loading="eager"
      />
      Xero
    </Badge>
  );
};

export default XeroBadge;
