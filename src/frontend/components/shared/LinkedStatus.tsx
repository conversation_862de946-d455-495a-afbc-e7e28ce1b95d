import React from "react";
import { CheckIcon } from "@heroicons/react/20/solid";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "../ui";
import HubSpotBadge from "./HubSpotBadge";
import HarvestBadge from "./HarvestBadge";

interface LinkedStatusProps {
  /**
   * The system type (hubspot or harvest)
   */
  system: "hubspot" | "harvest";
  /**
   * Whether the item is linked
   */
  isLinked: boolean;
  /**
   * The external ID (optional, for display)
   */
  externalId?: string | number;
  /**
   * Handler for linking action
   */
  onLink?: () => void;
  /**
   * Handler for unlinking action
   */
  onUnlink?: () => void;
  /**
   * Whether actions are disabled (loading state)
   */
  disabled?: boolean;
  /**
   * Size variant
   */
  size?: "sm" | "default";
}

/**
 * A consistent linked status component that shows linking state with proper branding.
 *
 * Features:
 * - System-specific branding (HubSpot/Harvest logos)
 * - Clear linked/unlinked states
 * - Consistent styling with app design system
 * - Accessible interaction patterns
 * - Loading states
 *
 * @example
 * ```tsx
 * <LinkedStatus
 *   system="hubspot"
 *   isLinked={true}
 *   externalId="12345"
 *   onUnlink={() => handleUnlink()}
 * />
 * ```
 */
const LinkedStatus = ({
  system,
  isLinked,
  externalId,
  onLink,
  onUnlink,
  disabled = false,
  size = "default",
}: LinkedStatusProps) => {
  const systemConfig = {
    hubspot: {
      name: "HubSpot",
      badge: <HubSpotBadge size={size === "sm" ? "sm" : "default"} />,
      linkText: "Link HubSpot",
      colors: {
        linked: "bg-warning-light text-warning-dark border-warning/20",
        unlinked:
          "border-warning text-warning hover:bg-warning-light dark:hover:bg-warning-dark/20",
      },
    },
    harvest: {
      name: "Harvest",
      badge: <HarvestBadge size={size === "sm" ? "sm" : "default"} />,
      linkText: "Link Harvest",
      colors: {
        linked: "bg-success-light text-success border-success/20",
        unlinked:
          "border-success text-success hover:bg-success-light dark:hover:bg-success-dark/20",
      },
    },
  };

  const config = systemConfig[system];

  if (isLinked) {
    return (
      <div className="space-y-1">
        {/* First line: System badge and Linked status */}
        <div className="flex items-center gap-2">
          {/* System badge */}
          {config.badge}

          {/* Linked badge with checkmark */}
          <Badge
            variant="success"
            size={size === "sm" ? "sm" : "default"}
            icon={<CheckIcon className="w-3 h-3" />}
            className={`${config.colors.linked} font-medium`}
          >
            Linked
          </Badge>
        </div>

        {/* Second line: External ID and Unlink button (if provided) */}
        {(externalId || onUnlink) && (
          <div className="flex items-center gap-2">
            {/* External ID (if provided) */}
            {externalId && (
              <span className="text-xs text-muted font-mono">{externalId}</span>
            )}

            {/* Unlink button */}
            {onUnlink && (
              <Button
                variant="ghost"
                size={size === "sm" ? "sm" : "default"}
                onClick={onUnlink}
                disabled={disabled}
                className="text-error hover:text-error hover:bg-error-light dark:hover:text-error dark:hover:bg-error-dark/20 px-3 whitespace-nowrap"
              >
                Unlink
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }

  // Unlinked state
  return (
    <div className="flex items-center gap-2">
      {/* System badge (grayed out) */}
      <div className="opacity-40">{config.badge}</div>

      {/* Link button */}
      {onLink && (
        <Button
          variant="outline"
          size={size === "sm" ? "sm" : "default"}
          onClick={onLink}
          disabled={disabled}
          className={`${config.colors.unlinked} border-dashed`}
        >
          {config.linkText}
        </Button>
      )}
    </div>
  );
};

export default LinkedStatus;
