import React from "react";

interface InvoiceDisplayProps {
  invoiceId?: string;
  invoiceNumber?: string;
  issueDate?: string; // Date the invoice was issued (for recent invoice detection)
  isRecent?: boolean; // Explicitly mark as recent (or will be calculated from issueDate)
}

/**
 * Shared component for displaying invoice information consistently across the application
 * This matches the display style used in TransactionsList
 */
export const InvoiceDisplay: React.FC<InvoiceDisplayProps> = ({
  invoiceId,
  invoiceNumber,
  issueDate,
  isRecent: explicitIsRecent,
}) => {
  if (!invoiceId) return null;

  // Determine if the invoice is recent (within last 5 days) if not explicitly provided
  const isRecent =
    explicitIsRecent ??
    (() => {
      if (!issueDate) return false;

      try {
        const date = new Date(issueDate);
        if (isNaN(date.getTime())) return false;

        const now = new Date();
        const diffTime = now.getTime() - date.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays >= 0 && diffDays <= 5;
      } catch {
        return false;
      }
    })();

  // Use invoice number for display if available, otherwise use ID
  const displayNumber = invoiceNumber || invoiceId;

  // URL to view the invoice in Harvest
  const harvestUrl = `https://onbord.harvestapp.com/invoices/${invoiceId}`;

  return (
    <span className="inline-flex items-center flex-shrink-0 px-2.5 py-0.5 rounded border border-warning/30 text-xs font-medium bg-warning-light text-warning/30 whitespace-nowrap">
      {isRecent && (
        <span
          className="w-2 h-2 bg-primary-light0 rounded mr-1 animate-pulse"
          title="Recently created invoice"
        ></span>
      )}
      <a
        href={harvestUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center text-primary-color hover:text-primary-color"
        title="View invoice in Harvest"
      >
        <span className="mr-0.5">Invoice</span>
        {displayNumber}
        <svg
          className="w-3.5 h-3.5 ml-1.5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
          />
        </svg>
      </a>
    </span>
  );
};

export default InvoiceDisplay;
