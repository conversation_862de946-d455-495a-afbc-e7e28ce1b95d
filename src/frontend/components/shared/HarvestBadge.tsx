import React from "react";
import { Badge } from "../ui/Badge";

interface HarvestBadgeProps {
  className?: string;
  /**
   * Click handler for interactive Harvest badge
   */
  onClick?: () => void;
}

/**
 * Harvest logo badge component with consistent styling and accessibility.
 * 
 * Features:
 * - Uses official Harvest logo SVG
 * - Consistent sizing with design system
 * - Proper accessibility attributes
 * - Optional interactive behavior
 * - Performance optimized loading
 * 
 * @example
 * ```tsx
 * <HarvestBadge />
 * ```
 */
const HarvestBadge: React.FC<HarvestBadgeProps> = ({ 
  className,
  onClick,
}) => {
  return (
    <Badge 
      variant="warning" 
      className={className}
      onClick={onClick}
      title="From Harvest"
    >
      <img 
        src="/HarvestLogo.svg" 
        alt="Harvest" 
        className="inline-block w-4 h-4 mr-1"
        loading="eager"
      />
      Harvest
    </Badge>
  );
};

export default HarvestBadge;