import React from "react";
import { Badge } from "../ui/Badge";

interface HubSpotBadgeProps {
  className?: string;
  /**
   * Click handler for interactive HubSpot badge
   */
  onClick?: () => void;
}

/**
 * HubSpot logo badge component with consistent styling and accessibility.
 *
 * Features:
 * - Uses official HubSpot logo SVG
 * - Consistent sizing with design system
 * - Proper accessibility attributes
 * - Optional interactive behavior
 * - Performance optimized loading
 *
 * @example
 * ```tsx
 * <HubSpotBadge />
 * ```
 */
const HubSpotBadge: React.FC<HubSpotBadgeProps> = ({
  className,
  onClick,
}) => {
  return (
    <Badge 
      variant="primary" 
      className={className}
      onClick={onClick}
      title="From HubSpot"
    >
      <img 
        src="/hubspot.svg" 
        alt="HubSpot" 
        className="inline-block w-4 h-4 mr-1"
        loading="eager"
      />
      HubSpot
    </Badge>
  );
};

export default HubSpotBadge;
