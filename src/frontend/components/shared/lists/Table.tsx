import React, { forwardRef } from "react";
import {
  cn,
  tableVariants,
  TableVariantProps,
} from "../../../utils/component-variants";

export interface TableProps
  extends Omit<React.TableHTMLAttributes<HTMLTableElement>, "children">,
    TableVariantProps {
  children: React.ReactNode;
  loading?: boolean;
  empty?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  emptyAction?: React.ReactNode;
}

export interface TableHeaderProps
  extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
}

export interface TableBodyProps
  extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
}

export interface TableRowProps
  extends React.HTMLAttributes<HTMLTableRowElement> {
  children: React.ReactNode;
  selected?: boolean;
  interactive?: boolean;
}

export interface TableCellProps
  extends React.TdHTMLAttributes<HTMLTableCellElement> {
  children: React.ReactNode;
  header?: boolean;
  align?: "left" | "center" | "right";
  width?: string;
}

/**
 * Modern Table component with variant system
 *
 * Features:
 * - Type-safe variants (default, striped, bordered, compact)
 * - Multiple sizes (sm, default, lg)
 * - Responsive design with container queries
 * - Loading and empty states
 * - Accessibility features
 * - Dark mode support
 */
export const Table = forwardRef<HTMLTableElement, TableProps>(
  (
    {
      className,
      variant = "default",
      size = "default",
      responsive = true,
      children,
      loading = false,
      empty = false,
      emptyMessage = "No data available",
      emptyIcon,
      emptyAction,
      ...props
    },
    ref
  ) => {
    const showEmpty = empty || (!loading && React.Children.count(children) === 0);

    if (loading || showEmpty) {
      return (
        <div className="table-container">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center space-x-2">
                <svg
                  className="animate-spin h-5 w-5 text-primary-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <span className="text-sm text-muted dark:text-subtle">
                  Loading...
                </span>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              {emptyIcon && (
                <div className="mb-4 text-subtle dark:text-muted">
                  {emptyIcon}
                </div>
              )}
              <p className="text-sm text-muted dark:text-subtle mb-4">
                {emptyMessage}
              </p>
              {emptyAction && <div>{emptyAction}</div>}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="table-container">
        <table
          ref={ref}
          className={cn(
            tableVariants({
              variant,
              size,
              responsive,
            }),
            className
          )}
          role="table"
          {...props}
        >
          {children}
        </table>
      </div>
    );
  }
);

Table.displayName = "Table";

/**
 * Table Header component
 */
export const TableHeader = forwardRef<HTMLTableSectionElement, TableHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <thead
      ref={ref}
      className={cn("table-header", className)}
      {...props}
    >
      {children}
    </thead>
  )
);

TableHeader.displayName = "TableHeader";

/**
 * Table Body component
 */
export const TableBody = forwardRef<HTMLTableSectionElement, TableBodyProps>(
  ({ className, children, ...props }, ref) => (
    <tbody
      ref={ref}
      className={cn("table-body bg-surface-card dark:bg-surface-card", className)}
      {...props}
    >
      {children}
    </tbody>
  )
);

TableBody.displayName = "TableBody";

/**
 * Table Row component
 */
export const TableRow = forwardRef<HTMLTableRowElement, TableRowProps>(
  ({ className, children, selected = false, interactive = false, ...props }, ref) => (
    <tr
      ref={ref}
      className={cn(
        "table-row",
        selected && "bg-primary-50 dark:bg-primary-900/20",
        interactive && "cursor-pointer",
        className
      )}
      aria-selected={selected}
      {...props}
    >
      {children}
    </tr>
  )
);

TableRow.displayName = "TableRow";

/**
 * Table Cell component
 */
export const TableCell = forwardRef<HTMLTableCellElement, TableCellProps>(
  ({ className, children, header = false, align = "left", width, ...props }, ref) => {
    const Component = header ? "th" : "td";
    const baseClass = header ? "table-header-cell" : "table-cell";
    
    return (
      <Component
        ref={ref}
        className={cn(
          baseClass,
          align === "center" && "text-center",
          align === "right" && "text-right",
          className
        )}
        style={{ width }}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

TableCell.displayName = "TableCell";

export default Table;
