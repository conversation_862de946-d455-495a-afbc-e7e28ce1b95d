import React, { forwardRef } from "react";
import {
  cn,
  listVariants,
  ListVariantProps,
} from "../../../utils/component-variants";

export interface ListProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "children">,
    ListVariantProps {
  children: React.ReactNode;
  loading?: boolean;
  empty?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  emptyAction?: React.ReactNode;
}

/**
 * Modern List component with variant system
 *
 * Features:
 * - Type-safe variants (default, compact, comfortable)
 * - Multiple layouts (stack, grid, table, cards)
 * - Container queries for responsive design
 * - Loading and empty states
 * - Accessibility features
 * - Dark mode support
 */
export const List = forwardRef<HTMLDivElement, ListProps>(
  (
    {
      className,
      variant = "default",
      layout = "stack",
      spacing = "default",
      children,
      loading = false,
      empty = false,
      emptyMessage = "No items found",
      emptyIcon,
      emptyAction,
      ...props
    },
    ref,
  ) => {
    // Show empty state if explicitly empty or no children
    const showEmpty =
      empty || (!loading && React.Children.count(children) === 0);

    return (
      <div
        ref={ref}
        className={cn(
          listVariants({
            variant,
            layout,
            spacing,
          }),
          loading && "opacity-50 pointer-events-none",
          className,
        )}
        role={layout === "table" ? "table" : "list"}
        aria-busy={loading}
        {...props}
      >
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <svg
                className="animate-spin h-5 w-5 text-primary-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              <span className="text-sm text-muted dark:text-subtle">
                Loading...
              </span>
            </div>
          </div>
        ) : showEmpty ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            {emptyIcon && (
              <div className="mb-4 text-subtle dark:text-muted">
                {emptyIcon}
              </div>
            )}
            <p className="text-sm text-muted dark:text-subtle mb-4">
              {emptyMessage}
            </p>
            {emptyAction && <div>{emptyAction}</div>}
          </div>
        ) : (
          children
        )}
      </div>
    );
  },
);

List.displayName = "List";

export default List;
