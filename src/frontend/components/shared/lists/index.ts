/**
 * List Components - Modern list and table components with variant system
 *
 * This module provides a comprehensive set of list components following
 * the established design system patterns from Phase 1 and Phase 2B.
 *
 * Components:
 * - List: Container component with layout variants
 * - ListItem: Flexible list item with interaction states
 * - Table: Enhanced table with responsive design
 * - DataList: Data-driven list with filtering and sorting
 *
 * Features:
 * - Type-safe variant system
 * - Container queries for responsive design
 * - Loading and empty states
 * - Accessibility support
 * - Dark mode compatibility
 * - Backwards compatibility
 */

// Core list components
export { default as List } from "./List";
export type { ListProps } from "./List";

export { default as ListItem } from "./ListItem";
export type { ListItemProps } from "./ListItem";

// Table components
export {
  default as Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
} from "./Table";
export type {
  TableProps,
  TableHeaderProps,
  TableBodyProps,
  TableRowProps,
  TableCellProps,
} from "./Table";

// Data-driven list component
export { default as DataList } from "./DataList";
export type { DataListProps } from "./DataList";

// Re-export variant types for convenience
export type {
  ListVariantProps,
  ListItemVariantProps,
  TableVariantProps,
  DataListVariantProps,
} from "../../../utils/component-variants";
