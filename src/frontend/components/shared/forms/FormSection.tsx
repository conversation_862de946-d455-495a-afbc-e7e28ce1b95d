import React from "react";
import { cn } from "../../../utils/component-variants";

export interface FormSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  description?: string;
  children: React.ReactNode;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

/**
 * FormSection component for grouping related form fields
 *
 * Features:
 * - Section titles and descriptions
 * - Consistent spacing between sections
 * - Optional collapsible functionality
 * - Semantic HTML structure
 * - Dark mode support
 */
export const FormSection: React.FC<FormSectionProps> = ({
  className,
  title,
  description,
  children,
  collapsible = false,
  defaultCollapsed = false,
  ...props
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  const toggleCollapsed = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  return (
    <div className={cn("form-section", className)} {...props}>
      {/* Section header */}
      {(title || description) && (
        <div className="mb-4">
          {title && (
            <div
              className={cn(
                "form-section__title",
                collapsible &&
                  "cursor-pointer select-none flex items-center justify-between",
              )}
              onClick={toggleCollapsed}
              role={collapsible ? "button" : undefined}
              tabIndex={collapsible ? 0 : undefined}
              onKeyDown={
                collapsible
                  ? (e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        toggleCollapsed();
                      }
                    }
                  : undefined
              }
              aria-expanded={collapsible ? !isCollapsed : undefined}
            >
              <span>{title}</span>
              {collapsible && (
                <svg
                  className={cn(
                    "w-5 h-5 transition-transform duration-200",
                    isCollapsed ? "rotate-0" : "rotate-180",
                  )}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              )}
            </div>
          )}

          {description && (
            <div className="form-section__description">{description}</div>
          )}
        </div>
      )}

      {/* Section content */}
      {(!collapsible || !isCollapsed) && (
        <div
          className={cn(
            collapsible && "transition-all duration-200 ease-in-out",
          )}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export default FormSection;
