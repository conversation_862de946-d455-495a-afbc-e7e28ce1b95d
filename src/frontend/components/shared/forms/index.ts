/**
 * Modern Form Components
 * 
 * A comprehensive set of form components built with:
 * - Type-safe variant system
 * - Consistent design patterns
 * - Enhanced accessibility
 * - Dark mode support
 * - Responsive design
 * - Loading states
 * - Error handling
 */

// Core form components
export { default as Input } from "./Input";
export { default as Select } from "./Select";
export { default as Textarea } from "./Textarea";

// Layout components
export { default as FormField } from "./FormField";
export { default as FormSection } from "./FormSection";
export { default as FormGrid } from "./FormGrid";

// Type exports
export type { InputProps } from "./Input";
export type { SelectProps, SelectOption } from "./Select";
export type { TextareaProps } from "./Textarea";
export type { FormFieldProps } from "./FormField";
export type { FormSectionProps } from "./FormSection";
export type { FormGridProps } from "./FormGrid";

// Re-export variant types for convenience
export type {
  InputVariantProps,
  SelectVariantProps,
  TextareaVariantProps,
  FormFieldVariantProps,
} from "../../../utils/component-variants";
