import React from "react";
import { formatCurrency, formatDate } from "../utils/format";
import { Card } from "./ui";
import XeroBadge from "./shared/XeroBadge";
import { Button } from "@/frontend/components/ui/Button";

interface XeroExpenseCardProps {
  title: string;
  amount: number;
  date: Date;
  isAdded: boolean;
  onSync: () => void;
  description: string;
  isLoading?: boolean;
  hasRecurringExpense?: boolean;
  type: "net-pay" | "tax" |"super';
}

const XeroExpenseCard: React.FC<XeroExpenseCardProps> = ({
  title,
  amount,
  date,
  isAdded,
  onSync,
  description,
  isLoading = false,
  hasRecurringExpense = false,
  type
}) => {
  // Determine card type classes
  const cardTypeClass = `xero-expense-card--${type}`;
  const iconTypeClass = `xero-expense-card__icon--${type}`;

  // Get the appropriate icon based on expense type
  const getIcon = () => {
    switch (type) {
      case "net-pay":
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>);

      case "tax":
        return (
          <img src="/ATO_Logo.svg" alt="ATO Logo" className="w-5 h-5" />);

      case "super":
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>);

    }
  };

  return (
    <Card className={`xero-expense-card ${cardTypeClass}`}>
      <div className="xero-expense-card__header">
        <div className={`xero-expense-card__icon ${iconTypeClass}`}>
          {getIcon()}
        </div>
        <h3 className="xero-expense-card__title">{title}</h3>
        <div className="ml-auto">
          <XeroBadge />
        </div>
      </div>

      <div className="xero-expense-card__amount text-accent">
        {formatCurrency(amount)}
      </div>

      <div className="xero-expense-card__details">
        <div className="xero-expense-card__detail">
          <span className="xero-expense-card__detail-label">Payment Date</span>
          <span className="xero-expense-card__detail-value">{formatDate(date)}</span>
        </div>
        <div className="xero-expense-card__detail">
          <span className="xero-expense-card__detail-label">Status</span>
          <span className="xero-expense-card__detail-value">
            {isAdded ? 'Synced' : hasRecurringExpense ?"Needs Update" : "Not Synced"}
          </span>
        </div>
      </div>

      <p className="xero-expense-card__description">
        {description}
      </p>

      <Button variant="secondary"
      onClick={onSync}
      disabled={isLoading || isAdded}
      className={`flex items-center justify-center w-full px-4 py-2 mt-2 text-sm font-medium transition-all duration-200 rounded-md
          ${isAdded ? "bg-success/10 text-success border border-success dark:bg-success/20" : "bg-primary/10 text-primary border border-primary hover:bg-primary hover:text-primary-foreground dark:bg-primary/20 dark:hover:bg-primary dark:hover:text-primary-foreground"}
          ${isLoading ? "opacity-80 cursor-wait" : ""}
          ${isLoading || isAdded ? "pointer-events-none" : "hover:shadow-sm"}`}>

        {isLoading ?
        <>
            <svg className="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Syncing...</span>
          </> :
        isAdded ?
        <>
            <svg className="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <span>Updated</span>
          </> :
        hasRecurringExpense ?
        <>
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Update</span>
          </> :

        <>
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
            <span>Sync with Upstream</span>
          </>
        }
      </Button>
    </Card>);

};

export default XeroExpenseCard;