/**
 * Question Mark Card Component
 * 
 * Displays a company with its associated action items
 */

import React from "react";
import { PlusIcon, ClockIcon, CheckCircleIcon, UserIcon } from "@heroicons/react/24/outline";
import type { Company } from "../../../../types/company-types";
import type { RadarAction } from "../../../../types/radar-action-types";
import { RADAR_ACTION_TYPE_META, RADAR_ACTION_PRIORITY_META } from "../../../../types/radar-action-types";
import { generateCompanyColors } from "../../../utils/colorUtils";
import { Button } from "@/frontend/components/ui/Button";

interface QuestionMarkCardProps {
  company: Company;
  actions: RadarAction[];
  onActionClick: (action: RadarAction) => void;
  onAddAction: () => void;
}

const QuestionMarkCard: React.FC<QuestionMarkCardProps> = ({
  company,
  actions,
  onActionClick,
  onAddAction
}) => {
  const { backgroundColor } = generateCompanyColors(company.name);
  const pendingActions = actions.filter((a) => a.status === 'pending');
  const inProgressActions = actions.filter((a) => a.status === 'in_progress');

  const formatDueDate = (date: string | undefined) => {
    if (!date) return null;
    const dueDate = new Date(date);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return { text: "Overdue", isOverdue: true };
    if (diffDays === 0) return { text: "Due today", isOverdue: false };
    if (diffDays === 1) return { text: "Due tomorrow", isOverdue: false };
    if (diffDays <= 7) return { text: `Due in ${diffDays} days`, isOverdue: false };
    return { text: dueDate.toLocaleDateString(), isOverdue: false };
  };

  return (
    <div className="question-mark-card">
      {/* Company Header */}
      <div className="question-mark-card-header">
        <div className="flex items-center gap-3">
          <div
            className="company-logo"
            style={{ backgroundColor }}>

            {company.name.charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="company-name">{company.name}</h3>
            {company.industry &&
            <p className="company-industry">{company.industry}</p>
            }
          </div>
        </div>
        
        <Button variant="primary"
        onClick={onAddAction}

        title="Add new action">

          <PlusIcon className="w-4 h-4" />
        </Button>
      </div>

      {/* Action Summary */}
      <div className="action-summary">
        {inProgressActions.length > 0 &&
        <span className="action-count in-progress">
            <ClockIcon className="w-4 h-4" />
            {inProgressActions.length} in progress
          </span>
        }
        {pendingActions.length > 0 &&
        <span className="action-count pending">
            {pendingActions.length} pending
          </span>
        }
      </div>

      {/* Actions List */}
      <div className="actions-list">
        {actions.slice(0, 3).map((action) => {
          const typeMeta = RADAR_ACTION_TYPE_META[action.actionType];
          const priorityMeta = RADAR_ACTION_PRIORITY_META[action.priority];
          const dueInfo = formatDueDate(action.dueDate);

          return (
            <div
              key={action.id}
              className="action-item"
              onClick={() => onActionClick(action)}>

              <div className="action-header">
                <span className="action-icon" title={typeMeta.label}>
                  {typeMeta.icon}
                </span>
                <span className="action-title">{action.title}</span>
              </div>
              
              <div className="action-meta">
                <span className={`priority-badge ${priorityMeta.bgColor} ${priorityMeta.textColor}`}>
                  {priorityMeta.label}
                </span>
                
                {dueInfo &&
                <span className={`due-date ${dueInfo.isOverdue ? 'overdue' : ""}`}>
                    {dueInfo.text}
                  </span>
                }
                
                {action.assignedTo &&
                <span className="assignee">
                    <UserIcon className="w-3 h-3" />
                    {action.assignedTo.split('@')[0]}
                  </span>
                }
              </div>
            </div>);

        })}
        
        {actions.length > 3 &&
        <div className="more-actions">
            +{actions.length - 3} more {actions.length - 3 === 1 ? 'action' : "actions"}
          </div>
        }
      </div>
    </div>);

};

export default QuestionMarkCard;