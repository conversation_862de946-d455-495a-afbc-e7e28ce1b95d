import React from "react";
import { useDrop } from "react-dnd";
import type { Quadrant, RadarCompany } from "../../../types/leads-types";
import RadarCompanyCard, { COMPANY_DRAG_TYPE } from "./RadarCompanyCard";

interface RadarQuadrantProps {
  quadrant: Quadrant;
  companies: RadarCompany[];
  onMoveCompany: (companyId: string, targetQuadrant: string) => void;
}

/**
 * Component for a single quadrant in the Radar view
 */
const RadarQuadrant: React.FC<RadarQuadrantProps> = ({
  quadrant,
  companies,
  onMoveCompany,
}) => {
  // Set up drop target
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: COMPANY_DRAG_TYPE,
    drop: (item: { id: string }) => {
      onMoveCompany(item.id, quadrant.id);
      return { moved: true };
    },
    canDrop: (item: { id: string }) => {
      // Check if the company is already in this quadrant
      const company = companies.find((c) => c.id === item.id);
      return !company; // Can only drop if the company is not already in this quadrant
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  });

  // Get quadrant-specific class name
  const getQuadrantClass = () => {
    const baseClass = "radar-quadrant";
    const quadrantClass = `quadrant-${quadrant.id.toLowerCase().replace(" ", "-")}`;
    const dropClass = isOver && canDrop ? "is-over" : canDrop ? "can-drop" : "";
    return `${baseClass} ${quadrantClass} ${dropClass}`.trim();
  };

  // Get icon for quadrant
  const getQuadrantIcon = () => {
    switch (quadrant.id) {
      case "Strategy":
        return (
          <svg
            className="quadrant-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
        );
      case "Transformation":
        return (
          <svg
            className="quadrant-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
            />
          </svg>
        );
      case "BAU":
        return (
          <svg
            className="quadrant-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
            />
          </svg>
        );
      case "Transition out":
        return (
          <svg
            className="quadrant-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div ref={drop} className={getQuadrantClass()}>
      <div className="quadrant-header">
        <h3 className="quadrant-title">
          {getQuadrantIcon()}
          {quadrant.title}
        </h3>
        <p className="text-sm text-muted dark:text-subtle">
          {quadrant.description}
        </p>
        <div className="quadrant-badges">
          <span
            className={`spend-badge ${quadrant.currentSpend.toLowerCase()}`}
          >
            Current: {quadrant.currentSpend}
          </span>
          <span
            className={`spend-badge ${quadrant.potentialSpend.toLowerCase()}`}
          >
            Potential: {quadrant.potentialSpend}
          </span>
        </div>
      </div>

      <div className="flex-1">
        {companies.length > 0 ? (
          <div className="company-grid">
            {companies.map((company) => (
              <RadarCompanyCard key={company.id} company={company} />
            ))}
          </div>
        ) : (
          <div className="quadrant-empty">
            <svg
              className="quadrant-empty-icon"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            <p className="quadrant-empty-text">Drag companies here</p>
          </div>
        )}
      </div>

      <div className="mt-auto pt-4 border-t border-default dark:border-default flex justify-between items-center text-xs">
        <span className="text-muted dark:text-subtle">
          {companies.length} {companies.length === 1 ? "company" : "companies"}
        </span>
        {companies.length > 0 && (
          <span className="font-semibold text-primary dark:text-subtle">
            Total: $
            {companies
              .reduce((sum, c) => sum + (c.totalSpend || 0), 0)
              .toLocaleString()}
          </span>
        )}
      </div>
    </div>
  );
};

export default RadarQuadrant;
