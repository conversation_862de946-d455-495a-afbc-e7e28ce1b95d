import React from "react";
import { useDrag } from "react-dnd";
import type { RadarCompany } from "../../../types/leads-types";
import { formatCurrency, formatRelativeDate } from "../../../utils/format";

interface RadarCompanyCardProps {
  company: RadarCompany;
}

// Define the drag type for companies
export const COMPANY_DRAG_TYPE = "company";

/**
 * Component for displaying a company card in the Radar view
 */
const RadarCompanyCard: React.FC<RadarCompanyCardProps> = ({ company }) => {
  // Set up drag source
  const [{ isDragging }, drag] = useDrag({
    type: COMPANY_DRAG_TYPE,
    item: { id: company.id, company },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  // Get company logo placeholder based on company name
  const getCompanyInitial = (name: string): string => {
    return name.charAt(0).toUpperCase();
  };

  // Get random color based on company ID for logo background
  const getLogoColor = (id: string): string => {
    const colors = [
      "bg-primary-light0",
      "bg-success-light0",
      "bg-accent-light0",
      "bg-warning-light0",
      "bg-magenta-500",
      "bg-primary-light0",
      "bg-error-light0",
      "bg-info-light0",
    ];

    // Simple hash function to get consistent color for the same ID
    const hash = id
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  return (
    <div
      ref={drag}
      className={`radar-company-card ${isDragging ? "is-dragging" : ""}`}
    >
      {/* Header section */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-start gap-3 min-w-0 flex-1">
          {/* Company logo/initial */}
          <div className={`company-logo ${getLogoColor(company.id)}`}>
            {getCompanyInitial(company.name)}
          </div>

          <div className="min-w-0 flex-1">
            <h4 className="company-name">{company.name}</h4>
            {company.industry && (
              <p className="company-industry">{company.industry}</p>
            )}
          </div>
        </div>

        {/* Website link */}
        {company.website && (
          <a
            href={company.website}
            target="_blank"
            rel="noopener noreferrer"
            className="p-1.5 text-subtle hover:text-primary-color dark:hover:text-primary-light transition-colors duration-150 rounded-md hover:bg-surface-alt dark:hover:bg-surface-elevated"
            title={`Visit ${company.website}`}
            onClick={(e) => e.stopPropagation()}
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
          </a>
        )}
      </div>

      {/* Last interaction */}
      {company.lastInteractionDate && (
        <div className="mb-3 text-xs text-muted dark:text-subtle">
          <svg
            className="w-3 h-3 inline mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          {formatRelativeDate(company.lastInteractionDate)}
        </div>
      )}

      {/* Metrics section */}
      <div className="space-y-2 pt-3 border-t border-default dark:border-default">
        {/* Active deals row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-muted dark:text-subtle">
            <svg
              className="w-3 h-3 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <span>Deals</span>
          </div>
          <div className="text-xs font-medium text-right">
            <span className="text-primary dark:text-subtle">
              {company.activeDealsCount || 0}
            </span>
            {company.totalDealValue ? (
              <span className="text-success dark:text-success-light ml-2">
                {formatCurrency(company.totalDealValue, { compact: true })}
              </span>
            ) : null}
          </div>
        </div>

        {/* Total spend row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-muted dark:text-subtle">
            <svg
              className="w-3 h-3 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>Spend</span>
          </div>
          <span className="text-xs font-medium text-primary dark:text-subtle">
            {company.totalSpend !== undefined && company.totalSpend !== null
              ? formatCurrency(company.totalSpend, { compact: true })
              : "—"}
          </span>
        </div>
      </div>
    </div>
  );
};

export default RadarCompanyCard;
