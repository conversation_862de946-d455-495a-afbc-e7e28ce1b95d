/**
 * Create Action Modal Component
 * 
 * Modal for creating new radar action items
 */

import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import type { Company } from "../../../../types/company-types";
import type { CreateRadarAction, RadarActionType, RadarActionPriority } from "../../../../types/radar-action-types";
import { RADAR_ACTION_TYPE_META, RADAR_ACTION_PRIORITY_META } from "../../../../types/radar-action-types";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface CreateActionModalProps {
  company: Company;
  onClose: () => void;
  onCreate: (action: Omit<CreateRadarAction,"createdBy" | "companyId">) => void;
}

const CreateActionModal: React.FC<CreateActionModalProps> = ({
  company,
  onClose,
  onCreate
}) => {
  const [formData, setFormData] = useState({
    actionType: "research" as RadarActionType,
    title: "",
    description: "",
    assignedTo: "",
    priority: "normal" as RadarActionPriority,
    dueDate: "",
    notes: ""
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.title.trim() || !formData.assignedTo.trim()) {
      return;
    }

    onCreate({
      actionType: formData.actionType,
      title: formData.title.trim(),
      description: formData.description.trim() || undefined,
      assignedTo: formData.assignedTo.trim(),
      priority: formData.priority,
      dueDate: formData.dueDate || undefined,
      notes: formData.notes.trim() || undefined
    });
  };

  const handleChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Get tomorrow's date as the minimum for due date
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-surface-card dark:bg-surface-card rounded-lg max-w-md w-full p-6 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-primary dark:text-primary">
              Create Action Item
            </h2>
            <p className="text-sm text-muted dark:text-subtle mt-1">
              For {company.name}
            </p>
          </div>
          <Button variant="ghost"
          onClick={onClose}>


            <XMarkIcon className="w-6 h-6" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Action Type */}
          <div>
            <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
              Action Type <span className="text-error">*</span>
            </label>
            <Select
              value={formData.actionType}
              onChange={(e) => handleChange('actionType", e.target.value)}
              className="w-full"
              required>

              {Object.entries(RADAR_ACTION_TYPE_META).map(([key, meta]) =>
              <option key={key} value={key}>
                  {meta.icon} {meta.label} - {meta.description}
                </option>
              )}
            </Select>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
              Title <span className="text-error">*</span>
            </label>
            <Input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title", e.target.value)}
              className="w-full"
              placeholder="e.g., Research company background"
              required />

          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
              Description
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleChange('description", e.target.value)}
              className="w-full"
              rows={3}
              placeholder="Provide more details about what needs to be done..." />

          </div>

          {/* Assigned To */}
          <div>
            <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
              Assigned To <span className="text-error">*</span>
            </label>
            <Input
              type="email"
              value={formData.assignedTo}
              onChange={(e) => handleChange('assignedTo", e.target.value)}
              className="w-full"
              placeholder="<EMAIL>"
              required />

          </div>

          {/* Priority and Due Date Row */}
          <div className="grid grid-cols-2 gap-4">
            {/* Priority */}
            <div>
              <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
                Priority
              </label>
              <Select
                value={formData.priority}
                onChange={(e) => handleChange('priority", e.target.value)}
                className="w-full">

                {Object.entries(RADAR_ACTION_PRIORITY_META).map(([key, meta]) =>
                <option key={key} value={key}>
                    {meta.label}
                  </option>
                )}
              </Select>
            </div>

            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
                Due Date
              </label>
              <Input
                type="date"
                value={formData.dueDate}
                onChange={(e) => handleChange('dueDate", e.target.value)}
                min={minDate}
                className="w-full" />

            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-primary dark:text-subtle mb-1">
              Initial Notes
            </label>
            <Textarea
              value={formData.notes}
              onChange={(e) => handleChange('notes", e.target.value)}
              className="w-full"
              rows={2}
              placeholder="Any initial thoughts or context..." />

          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button variant="primary"
            type="submit"
            className="flex-1">

              Create Action
            </Button>
            <Button variant="secondary"
            type="button"
            onClick={onClose}>


              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>);

};

export default CreateActionModal;