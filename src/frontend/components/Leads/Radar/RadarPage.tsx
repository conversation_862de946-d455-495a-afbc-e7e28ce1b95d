import React, { useState, useEffect } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useQuery, useMutation, useQueryClient } from "react-query";
import type { Quadrant, RadarCompany } from "../../../types/leads-types";
import { RADAR_QUADRANTS } from "../../../types/leads-types";
import {
  getRadarCompanies,
  updateRadarCompany,
  getHarvestCompanies,
  removeCompanyFromRadar } from
"../../../api/leads";
import { refreshCache } from "../../../api/harvest-cache";
import RadarQuadrant from "./RadarQuadrant";
import CompanySelectionModal from "./CompanySelectionModal";
import QuestionMarksSection from "./QuestionMarksSection";
import { useDrop } from "react-dnd";
import { COMPANY_DRAG_TYPE } from "./RadarCompanyCard";

/**
 * Component for the removal drop zone
 */import { Button } from "@/frontend/components/ui/Button";
interface RemoveDropZoneProps {
  onRemove: (id: string) => void;
}

const RemoveDropZone: React.FC<RemoveDropZoneProps> = ({ onRemove }) => {
  // Set up drop target for removal
  const [{ isRemoveOver }, removeDropRef] = useDrop({
    accept: COMPANY_DRAG_TYPE,
    drop: (item: {id: string;}) => {
      onRemove(item.id);
      return { removed: true };
    },
    collect: (monitor) => ({
      isRemoveOver: !!monitor.isOver()
    })
  });

  return (
    <div
      ref={removeDropRef}
      className={`remove-drop-zone ${
      isRemoveOver ? "is-over" : ""}`
      }>

      <div className="remove-drop-zone-content">
        <svg
          className="w-6 h-6 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24">

          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />

        </svg>
        <span>
          Drag here to remove company from radar
        </span>
      </div>
    </div>);

};

/**
 * Radar page component displaying the quadrant view
 */
const RadarPage: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isRefreshingCache, setIsRefreshingCache] = useState(false);
  const [cacheRefreshed, setCacheRefreshed] = useState(false);
  const queryClient = useQueryClient();

  // Fetch radar companies
  const {
    data: companies = [],
    isLoading,
    error
  } = useQuery("radarCompanies", getRadarCompanies, {
    staleTime: 60000 // 1 minute
  });

  // Auto-refresh Harvest cache on page load
  useEffect(() => {
    const refreshHarvestCache = async () => {
      try {
        setIsRefreshingCache(true);
        console.log("Auto-refreshing Harvest invoice cache...");
        await refreshCache();
        console.log("Harvest cache refreshed successfully");
        // Refetch companies to get updated spend data
        queryClient.invalidateQueries("radarCompanies");
        setCacheRefreshed(true);
      } catch (error) {
        console.error('Failed to refresh Harvest cache: ", error);
        // Don't show error to user as this is a background operation
        // Set cache refreshed to true even on failure to prevent infinite loop
        setCacheRefreshed(true);
      } finally {
        setIsRefreshingCache(false);
      }
    };

    // Only refresh if we have companies, cache refresh isn't running, and we haven"t refreshed yet
    if (companies.length > 0 && !isRefreshingCache && !cacheRefreshed) {
      refreshHarvestCache();
    }
  }, [companies.length, queryClient, isRefreshingCache, cacheRefreshed]); // Depend on companies.length to trigger after initial load

  // Mutation for updating company radar state
  const updateCompanyMutation = useMutation(
    ({ id, radarState }: {id: string;radarState: string;}) =>
    updateRadarCompany(id, { radarState: radarState as any }),
    {
      onSuccess: () => {
        // Invalidate and refetch
        queryClient.invalidateQueries("radarCompanies");
      }
    }
  );

  // Mutation for removing company from radar
  const removeCompanyMutation = useMutation(
    (id: string) => removeCompanyFromRadar(id),
    {
      onSuccess: () => {
        // Invalidate and refetch
        queryClient.invalidateQueries("radarCompanies");
      }
    }
  );

  // Handle removing a company from radar
  const handleRemoveCompany = (companyId: string) => {
    removeCompanyMutation.mutate(companyId);
  };

  // Handle moving a company between quadrants
  const handleMoveCompany = (companyId: string, targetQuadrant: string) => {
    updateCompanyMutation.mutate({
      id: companyId,
      radarState: targetQuadrant
    });
  };

  // Handle opening the add company modal
  const handleAddCompany = () => {
    setIsAddModalOpen(true);
  };

  // Handle closing the add company modal
  const handleCloseModal = () => {
    setIsAddModalOpen(false);
  };

  // Handle adding a company to the radar
  const handleCompanyAdded = () => {
    setIsAddModalOpen(false);
    queryClient.invalidateQueries("radarCompanies");
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="radar-page w-full">
        <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-primary dark:text-primary mb-2">
              Client Radar
            </h2>
            <div className="radar-stats">
              <div className="stat-item">
                <span className="stat-label">Companies</span>
                <span className="stat-value">{companies.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Total Spend</span>
                <span className="stat-value">
                  ${companies.reduce((sum, c) => sum + (c.totalSpend || 0), 0).toLocaleString()}
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Avg Spend</span>
                <span className="stat-value">
                  {companies.length > 0 ?
                  `$${Math.round(companies.reduce((sum, c) => sum + (c.totalSpend || 0), 0) / companies.length).toLocaleString()}` :
                  '$0'
                  }
                </span>
              </div>
              {isRefreshingCache &&
              <div className="flex items-center gap-2 text-accent dark:text-accent-light">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-accent border-t-transparent"></div>
                  <span className="text-sm">Syncing</span>
                </div>
              }
            </div>
          </div>

          <Button variant="primary"

          onClick={handleAddCompany}>

            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Company
          </Button>
        </div>

        {isLoading ?
        <div className="radar-loading">
            <div className="radar-loading-spinner"></div>
            <p className="radar-loading-text">Loading client radar...</p>
          </div> :
        error ?
        <div
          className="bg-error-light border border-red-400 text-error px-4 py-3 rounded relative"
          role="alert">

            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline">
              {" "}
              Failed to load companies. Please try again later.
            </span>
          </div> :

        <>
            <div className="radar-grid">
              {RADAR_QUADRANTS.map((quadrant: Quadrant) =>
            <RadarQuadrant
              key={quadrant.id}
              quadrant={quadrant}
              companies={companies.filter(
                (c: RadarCompany) => c.radarState === quadrant.id
              )}
              onMoveCompany={handleMoveCompany} />

            )}
            </div>

            {/* Remove drop zone */}
            <RemoveDropZone onRemove={handleRemoveCompany} />

            {/* Question Marks Section */}
            <QuestionMarksSection />
          </>
        }

        {/* Company selection modal */}
        {isAddModalOpen &&
        <CompanySelectionModal
          onClose={handleCloseModal}
          onCompanyAdded={handleCompanyAdded} />

        }
      </div>
    </DndProvider>);

};

export default RadarPage;