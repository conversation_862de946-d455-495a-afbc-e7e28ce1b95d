/**
 * Action Details Modal Component
 * 
 * Modal for viewing and updating radar action details
 */

import React, { useState } from "react";
import { useMutation, useQueryClient } from "react-query";
import { XMarkIcon, CheckCircleIcon, XCircleIcon, PlayIcon } from "@heroicons/react/24/outline";
import type { RadarAction, UpdateRadarAction } from "../../../../types/radar-action-types";
import { RADAR_ACTION_TYPE_META, RADAR_ACTION_PRIORITY_META } from "../../../../types/radar-action-types";
import { updateRadarAction, completeRadarAction, cancelRadarAction } from "../../../api/radar-actions";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface ActionDetailsModalProps {
  action: RadarAction;
  onClose: () => void;
  onUpdate: () => void;
}

const ActionDetailsModal: React.FC<ActionDetailsModalProps> = ({
  action,
  onClose,
  onUpdate
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [completionNotes, setCompletionNotes] = useState('');
  const [cancellationNotes, setCancellationNotes] = useState('');
  const [showCompleteForm, setShowCompleteForm] = useState(false);
  const [showCancelForm, setShowCancelForm] = useState(false);

  const queryClient = useQueryClient();
  const typeMeta = RADAR_ACTION_TYPE_META[action.actionType];
  const priorityMeta = RADAR_ACTION_PRIORITY_META[action.priority];

  // Edit form state
  const [editData, setEditData] = useState({
    title: action.title,
    description: action.description || "",
    assignedTo: action.assignedTo,
    priority: action.priority,
    dueDate: action.dueDate || "",
    notes: action.notes || ""
  });

  // Mutations
  const updateMutation = useMutation(
    (updates: Omit<UpdateRadarAction, 'updatedBy'>) => updateRadarAction(action.id, updates),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        setIsEditing(false);
        onUpdate();
      }
    }
  );

  const completeMutation = useMutation(
    (notes: string) => completeRadarAction(action.id, notes),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        onClose();
        onUpdate();
      }
    }
  );

  const cancelMutation = useMutation(
    (notes: string) => cancelRadarAction(action.id, notes),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        onClose();
        onUpdate();
      }
    }
  );

  const startMutation = useMutation(
    () => updateRadarAction(action.id, { status: "in_progress" }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        onUpdate();
      }
    }
  );

  const handleSaveEdit = () => {
    updateMutation.mutate({
      title: editData.title.trim(),
      description: editData.description.trim() || undefined,
      assignedTo: editData.assignedTo.trim(),
      priority: editData.priority,
      dueDate: editData.dueDate || undefined,
      notes: editData.notes.trim() || undefined
    });
  };

  const handleComplete = () => {
    completeMutation.mutate(completionNotes);
  };

  const handleCancel = () => {
    cancelMutation.mutate(cancellationNotes);
  };

  const handleStart = () => {
    startMutation.mutate();
  };

  const formatDate = (date: string | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString();
  };

  const formatDateTime = (date: string | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleString();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-surface-card rounded-lg max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl" title={typeMeta.label}>
                {typeMeta.icon}
              </span>
              <h2 className="text-xl font-semibold text-primary">
                {isEditing ?
                <Input
                  type="text"
                  value={editData.title}
                  onChange={(e) => setEditData((prev) => ({ ...prev, title: e.target.value }))}
                  className="w-full px-2 py-1 border border-strong rounded bg-surface-card" /> :


                action.title
                }
              </h2>
            </div>
            
            <div className="flex items-center gap-4 text-sm">
              <span className={`px-2 py-1 rounded-full ${priorityMeta.bgColor} ${priorityMeta.textColor}`}>
                {priorityMeta.label} Priority
              </span>
              <span className="text-muted">
                Status: {action.status.replace('_', ' ')}
              </span>
              {action.company &&
              <span className="text-muted">
                  {action.company.name}
                </span>
              }
            </div>
          </div>
          
          <Button variant="ghost"
          onClick={onClose}
          className="ml-4">

            <XMarkIcon className="w-6 h-6" />
          </Button>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <div>
            <h3 className="text-sm font-medium text-primary mb-1">
              Description
            </h3>
            {isEditing ?
            <Textarea
              value={editData.description}
              onChange={(e) => setEditData((prev) => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-strong rounded-md bg-surface-card"
              rows={3} /> :


            <p className="text-secondary">
                {action.description || "No description provided"}
              </p>
            }
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-primary mb-1">
                Assigned To
              </h3>
              {isEditing ?
              <Input
                type="email"
                value={editData.assignedTo}
                onChange={(e) => setEditData((prev) => ({ ...prev, assignedTo: e.target.value }))}
                className="w-full px-3 py-2 border border-strong rounded-md bg-surface-card" /> :


              <p className="text-secondary">{action.assignedTo}</p>
              }
            </div>

            <div>
              <h3 className="text-sm font-medium text-primary mb-1">
                Due Date
              </h3>
              {isEditing ?
              <Input
                type="date"
                value={editData.dueDate}
                onChange={(e) => setEditData((prev) => ({ ...prev, dueDate: e.target.value }))}
                className="w-full px-3 py-2 border border-strong rounded-md bg-surface-card" /> :


              <p className="text-secondary">{formatDate(action.dueDate)}</p>
              }
            </div>

            <div>
              <h3 className="text-sm font-medium text-primary mb-1">
                Created
              </h3>
              <p className="text-secondary">
                {formatDateTime(action.createdAt)} by {action.createdBy}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-primary mb-1">
                Started
              </h3>
              <p className="text-secondary">
                {action.startedAt ? formatDateTime(action.startedAt) : "Not started"}
              </p>
            </div>
          </div>

          {/* Notes */}
          <div>
            <h3 className="text-sm font-medium text-primary mb-1">
              Notes
            </h3>
            {isEditing ?
            <Textarea
              value={editData.notes}
              onChange={(e) => setEditData((prev) => ({ ...prev, notes: e.target.value }))}
              className="w-full px-3 py-2 border border-strong rounded-md bg-surface-card"
              rows={3} /> :


            <p className="text-secondary">
                {action.notes || "No notes added"}
              </p>
            }
          </div>

          {/* Completion Notes */}
          {action.completionNotes &&
          <div>
              <h3 className="text-sm font-medium text-primary mb-1">
                Completion Notes
              </h3>
              <p className="text-secondary">
                {action.completionNotes}
              </p>
              <p className="text-sm text-muted mt-1">
                Completed {formatDateTime(action.completedAt)}
              </p>
            </div>
          }

          {/* Complete Form */}
          {showCompleteForm &&
          <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-primary mb-2">
                Complete Action
              </h3>
              <Textarea
              value={completionNotes}
              onChange={(e) => setCompletionNotes(e.target.value)}
              className="w-full px-3 py-2 border border-strong rounded-md bg-surface-card mb-3"
              rows={3}
              placeholder="Add completion notes..." />

              <div className="flex gap-2">
                <Button variant="success"
              onClick={handleComplete}
              disabled={completeMutation.isLoading}>


                  {completeMutation.isLoading ? 'Completing...' : "Mark Complete"}
                </Button>
                <Button variant="secondary"
              onClick={() => {
                setShowCompleteForm(false);
                setCompletionNotes('');
              }}>


                  Cancel
                </Button>
              </div>
            </div>
          }

          {/* Cancel Form */}
          {showCancelForm &&
          <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-primary mb-2">
                Cancel Action
              </h3>
              <Textarea
              value={cancellationNotes}
              onChange={(e) => setCancellationNotes(e.target.value)}
              className="w-full px-3 py-2 border border-strong rounded-md bg-surface-card mb-3"
              rows={3}
              placeholder="Reason for cancellation..." />

              <div className="flex gap-2">
                <Button variant="danger"
              onClick={handleCancel}
              disabled={cancelMutation.isLoading}>


                  {cancelMutation.isLoading ? 'Cancelling...' : "Cancel Action"}
                </Button>
                <Button variant="secondary"
              onClick={() => {
                setShowCancelForm(false);
                setCancellationNotes('');
              }}>


                  Keep Action
                </Button>
              </div>
            </div>
          }
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t">
          <div className="flex gap-2">
            {action.status === 'pending' &&
            <Button variant="primary"
            onClick={handleStart}
            disabled={startMutation.isLoading}>


                <PlayIcon className="w-4 h-4" />
                {startMutation.isLoading ? 'Starting...' : "Start Work"}
              </Button>
            }
            
            {action.status !== 'completed' && action.status !== 'cancelled' && !showCompleteForm && !showCancelForm &&
            <>
                <Button variant="success"
              onClick={() => setShowCompleteForm(true)}>


                  <CheckCircleIcon className="w-4 h-4" />
                  Complete
                </Button>
                <Button variant="danger"
              onClick={() => setShowCancelForm(true)}>


                  <XCircleIcon className="w-4 h-4" />
                  Cancel
                </Button>
              </>
            }
          </div>

          <div className="flex gap-2">
            {isEditing ?
            <>
                <Button variant="primary"
              onClick={handleSaveEdit}
              disabled={updateMutation.isLoading}>


                  {updateMutation.isLoading ? 'Saving...' : "Save Changes"}
                </Button>
                <Button variant="secondary"
              onClick={() => {
                setIsEditing(false);
                setEditData({
                  title: action.title,
                  description: action.description || "",
                  assignedTo: action.assignedTo,
                  priority: action.priority,
                  dueDate: action.dueDate || "",
                  notes: action.notes || ""
                });
              }}>


                  Cancel Edit
                </Button>
              </> :

            action.status !== 'completed' && action.status !== 'cancelled' &&
            <Button variant="secondary"
            onClick={() => setIsEditing(true)}>


                  Edit
                </Button>

            }
          </div>
        </div>
      </div>
    </div>);

};

export default ActionDetailsModal;