import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { getRadarCompanies, addCompanyToRadar } from "../../../api/leads";
import { getCompanies } from "../../../api/crm";
import type {
  RadarState,
  CompanyPriority } from "../../../../types/shared-types";
import { RADAR_QUADRANTS } from "../../../types/leads-types";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface CompanySelectionModalProps {
  onClose: () => void;
  onCompanyAdded: () => void;
}

/**
 * Modal for selecting companies to add to the radar
 */
const CompanySelectionModal: React.FC<CompanySelectionModalProps> = ({
  onClose,
  onCompanyAdded
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(
    null
  );
  const [selectedQuadrant, setSelectedQuadrant] = useState<RadarState | null>(
    null
  );
  const [selectedPriority, setSelectedPriority] =
  useState<CompanyPriority | null>(null);

  const queryClient = useQueryClient();

  // Fetch all companies from local database
  const { data: allCompanies = [], isLoading } = useQuery("allCompanies",
    getCompanies,
    {
      staleTime: 300000 // 5 minutes
    }
  );

  // Fetch companies already on radar
  const { data: radarCompanies = [] } = useQuery("radarCompanies",
    getRadarCompanies,
    {
      staleTime: 300000 // 5 minutes
    }
  );

  // Mutation for adding a company to the radar
  const addCompanyMutation = useMutation(
    (data: {companyId: string;radarState: string;priority?: string;}) =>
    addCompanyToRadar(data.companyId, data.radarState, data.priority),
    {
      onSuccess: () => {
        console.log("Company added successfully, invalidating queries");
        queryClient.invalidateQueries("radarCompanies");
        onCompanyAdded();
      },
      onError: (error: any) => {
        console.error("Error in mutation:", error);
        // You could add error handling UI here
      }
    }
  );

  // Filter out companies already on radar and filter by search term
  const radarCompanyIds = new Set(radarCompanies.map((c) => c.id));
  const availableCompanies = allCompanies.filter((company) =>
  !radarCompanyIds.has(company.id) && !company.radarState
  );

  const filteredCompanies = availableCompanies.filter((company) =>
  company.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle company selection
  const handleCompanySelect = (companyId: string) => {
    console.log(`Selected company with ID: ${companyId} (${typeof companyId})`);
    setSelectedCompanyId(companyId);
  };

  // Handle quadrant selection
  const handleQuadrantSelect = (quadrant: RadarState) => {
    setSelectedQuadrant(quadrant);
  };

  // Handle priority selection
  const handlePrioritySelect = (priority: CompanyPriority) => {
    setSelectedPriority(priority);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    console.log("Form submitted with values:", {
      companyId: selectedCompanyId,
      radarState: selectedQuadrant,
      priority: selectedPriority
    });

    if (selectedCompanyId && selectedQuadrant) {
      console.log("Calling mutation with:", {
        companyId: selectedCompanyId,
        radarState: selectedQuadrant,
        priority: selectedPriority || undefined
      });

      addCompanyMutation.mutate({
        companyId: selectedCompanyId,
        radarState: selectedQuadrant,
        priority: selectedPriority || undefined
      });
    } else {
      console.warn("Form submission prevented: missing required fields");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-surface-card rounded-xl shadow-2xl max-w-3xl w-full max-h-[85vh] overflow-hidden animate-fadeIn">
        <div className="p-6 border-b border-default bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-primary">
                Add Company to Radar
              </h2>
              <p className="text-sm text-secondary mt-1">
                Select a company and assign it to a quadrant
              </p>
            </div>
            <Button variant="ghost"
            onClick={onClose}
            className="p-2">

              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-6 overflow-y-auto max-h-[calc(85vh-12rem)]">
            {/* Search input */}
            <div className="mb-6">
              <label
                htmlFor="search"
                className="block text-sm font-semibold text-primary mb-2">

                Search Companies
              </label>
              <div className="relative">
                <Input
                  type="text"
                  id="search"
                  className="w-full pl-10 pr-4 py-3 border border-strong rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent placeholder-gray-400 dark:placeholder-gray-500"
                  placeholder="Type to search by company name..."
                  value={searchTerm}
                  onChange={handleSearchChange} />

                <svg className="absolute left-3 top-3.5 w-5 h-5 text-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            {/* Company list */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-primary mb-2">
                Select Company
                <span className="font-normal text-muted ml-2">({filteredCompanies.length} available)</span>
              </label>
              <div className="border border-strong rounded-lg h-56 overflow-y-auto bg-surface-page/50">
                {isLoading ?
                <div className="flex justify-center items-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-2 border-accent border-t-transparent"></div>
                  </div> :
                filteredCompanies.length === 0 ?
                <div className="flex flex-col items-center justify-center h-full text-muted">
                    <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <p className="text-sm">No companies found</p>
                    {searchTerm && <p className="text-xs mt-1">Try a different search term</p>}
                  </div> :

                <div className="p-3 space-y-2">
                    {filteredCompanies.map((company) =>
                  <div
                    key={company.id}
                    className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedCompanyId === company.id ?"border-accent bg-surface-card shadow-md transform scale-[1.02]" :"border-transparent bg-surface-card hover:border-strong dark:hover:border-strong hover:shadow-sm"}`
                    }
                    onClick={() => handleCompanySelect(company.id)}>

                        <div className="flex items-center justify-between">
                          <div>
                            <div
                          className={`font-semibold ${
                          selectedCompanyId === company.id ?"text-accent-dark" :"text-primary"}`
                          }>

                              {company.name}
                            </div>
                            <div className="text-xs text-muted mt-0.5">
                              {company.industry && <span>{company.industry} • </span>}
                              <span className="uppercase">{company.source || "Unknown"}</span>
                            </div>
                          </div>
                          {selectedCompanyId === company.id &&
                      <svg className="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                      }
                        </div>
                      </div>
                  )}
                  </div>
                }
              </div>
            </div>

            {/* Quadrant selection */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-primary mb-2">
                Select Quadrant
              </label>
              <div className="grid grid-cols-2 gap-3">
                {RADAR_QUADRANTS.map((quadrant) =>
                <div
                  key={quadrant.id}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedQuadrant === quadrant.id ?"border-accent bg-accent-light/20 transform scale-[1.02]" :"border-default hover:border-strong dark:hover:border-strong hover:shadow-sm"}`
                  }
                  onClick={() => handleQuadrantSelect(quadrant.id)}>

                    <div className="flex items-start justify-between">
                      <div>
                        <div
                        className={`font-semibold mb-1 ${
                        selectedQuadrant === quadrant.id ?"text-accent-dark" :"text-primary"}`
                        }>

                          {quadrant.title}
                        </div>
                        <div className="text-xs text-muted">
                          {quadrant.description}
                        </div>
                      </div>
                      {selectedQuadrant === quadrant.id &&
                    <svg className="w-5 h-5 text-accent flex-shrink-0 ml-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                    }
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Priority selection */}
            <div>
              <label className="block text-sm font-semibold text-primary mb-2">
                Select Priority 
                <span className="font-normal text-muted ml-1">(Optional)</span>
              </label>
              <div className="grid grid-cols-2 gap-3">
                {(
                ["High","Medium","Low","Qualified out"] as
                CompanyPriority[]).
                map((priority) => {
                  const priorityColors = {"High": "bg-error-light0",'Medium": "bg-warning-light0",'Low": "bg-success-light0",'Qualified out": "bg-surface-page0"
                  };

                  return (
                    <div
                      key={priority}
                      className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                      selectedPriority === priority ?"border-accent bg-accent-light/20 transform scale-[1.02]" :"border-default hover:border-strong dark:hover:border-strong hover:shadow-sm"}`
                      }
                      onClick={() => handlePrioritySelect(priority)}>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className={`w-3 h-3 rounded-full ${priorityColors[priority]} mr-2`} />
                          <div
                            className={`font-medium ${
                            selectedPriority === priority ?"text-accent-dark" :"text-primary"}`
                            }>

                            {priority}
                          </div>
                        </div>
                        {selectedPriority === priority &&
                        <svg className="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        }
                      </div>
                    </div>);

                })}
              </div>
            </div>
          </div>

          <div className="p-6 border-t border-default bg-surface-page/50 flex justify-between items-center">
            <div className="text-sm text-muted">
              {selectedCompanyId && selectedQuadrant ?
              <div className="flex items-center">
                  <svg className="w-4 h-4 text-success mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Ready to add
                </div> :"Select a company and quadrant"
              }
            </div>
            
            <div className="flex space-x-3">
              <Button variant="secondary"
              type="button"

              onClick={onClose}>

                Cancel
              </Button>
              <Button variant="secondary"
              type="submit"
              className={`px-5 py-2.5 bg-gradient-to-r from-purple-600 to-blue-600 text-primary rounded-lg font-medium transition-all ${
              !selectedCompanyId || !selectedQuadrant ?"opacity-50 cursor-not-allowed" :"hover:from-purple-700 hover:to-blue-700 hover:shadow-lg transform hover:scale-105"}`
              }
              disabled={
              !selectedCompanyId ||
              !selectedQuadrant ||
              addCompanyMutation.isLoading
              }>

                {addCompanyMutation.isLoading ?
                <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Adding...
                  </span> :"Add to Radar"
                }
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>);

};

export default CompanySelectionModal;