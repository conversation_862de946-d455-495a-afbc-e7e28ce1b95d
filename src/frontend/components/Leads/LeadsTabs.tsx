import React from "react";
import { Button } from "@/frontend/components/ui/Button";

interface LeadsTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  showKnowledgeGraph?: boolean;
}

/**
 * Tabs navigation for the Leads section
 */
const LeadsTabs: React.FC<LeadsTabsProps> = ({
  activeTab,
  onTabChange,
  showKnowledgeGraph = true,
}) => {
  const tabs = [
    { id: "radar", label: "Radar" },
    ...(showKnowledgeGraph
      ? [{ id: "knowledge-graph", label: "Knowledge Graph" }]
      : []),
    { id: "relationships", label: "Relationships" },
    { id: "wagov", label: "WA Govt" },
  ];

  return (
    <div className="border-b border-default dark:border-default">
      <nav className="flex -mb-px">
        {tabs.map((tab) => (
          <Button
            variant="secondary"
            key={tab.id}
            className={`${activeTab === tab.id ? "--tab-active" : "--tab"}`}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </Button>
        ))}
      </nav>
    </div>
  );
};

export default LeadsTabs;
