import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getGravatarUrl } from "../utils/gravatar";
import { Button } from "./ui";

interface ProfileBadgeProps {
  userName: string;
  userEmail: string;
  onLogout: () => void;
}

const ProfileBadge: React.FC<ProfileBadgeProps> = ({
  userName,
  userEmail,
  onLogout,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [gravatarUrl, setGravatarUrl] = useState<string | null>(null);
  const [gravatarError, setGravatarError] = useState(false);
  const navigate = useNavigate();

  // Generate initials from user"s name
  const initials = userName
    .split(" ")
    .map((name) => name[0])
    .join("")
    .toUpperCase()
    .substring(0, 2);

  // Get Gravatar URL when email changes
  useEffect(() => {
    if (userEmail) {
      try {
        // Use the proper getGravatarUrl utility that creates an MD5 hash
        const url = getGravatarUrl(userEmail, 80, "identicon");
        setGravatarUrl(url);
      } catch (error) {
        console.error("Error setting Gravatar URL:", error);
        setGravatarError(true);
      }
    }
  }, [userEmail]);

  const toggleDropdown = () => setIsOpen(!isOpen);

  // Determine whether to show initials or Gravatar
  const showInitials = gravatarError || !gravatarUrl;

  return (
    <div className="relative">
      <Button
        onClick={toggleDropdown}
        variant="ghost"
        className="flex items-center space-x-2 px-3 py-2"
      >
        {showInitials ? (
          <div className="w-8 h-8 flex items-center justify-center rounded-full bg-surface-alt dark:bg-surface-alt text-primary dark:text-subtle">
            {initials}
          </div>
        ) : (
          <img
            src={gravatarUrl || ""}
            alt={userName}
            className="w-8 h-8 rounded-full border border-default dark:border-default"
            onError={() => setGravatarError(true)}
          />
        )}
        <span className="hidden md:block text-primary dark:text-subtle">
          {userName.split(" ")[0]}
        </span>
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-surface-card dark:bg-surface-card rounded-md shadow-lg py-1 z-10 border border-default dark:border-default">
          <div className="px-4 py-2 border-b border-default dark:border-default">
            <p className="text-sm font-medium text-primary dark:text-primary truncate">
              {userName}
            </p>
            <p className="text-xs text-muted dark:text-subtle truncate">
              {userEmail}
            </p>
          </div>
          <a
            href="#"
            className="block px-4 py-2 text-sm text-primary dark:text-subtle hover:bg-surface-alt dark:hover:bg-surface-elevated"
            onClick={(e) => {
              e.preventDefault();
              setIsOpen(false);
              navigate("/account");
            }}
          >
            Account Settings
          </a>
          <a
            href="#"
            className="block px-4 py-2 text-sm text-primary dark:text-subtle hover:bg-surface-alt dark:hover:bg-surface-elevated"
            onClick={(e) => {
              e.preventDefault();
              setIsOpen(false);
              onLogout();
            }}
          >
            Logout
          </a>
        </div>
      )}
    </div>
  );
};

export default ProfileBadge;
