import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import { useAuthStatus } from "../hooks/useAuthStatus";
import { MainLayout } from "./Layout";
import UpdateNotification from "./shared/UpdateNotification";
import { EventProvider, LoggingProvider, UserProvider } from "../contexts";
import { LoadingProvider } from "../contexts/LoadingContext";
import { SearchProvider, useSearch } from "../contexts/SearchContext";
import { FloatingPanelsProvider } from "../contexts/FloatingPanelsContext";
import GlobalLoadingIndicator from "./GlobalLoadingIndicator";
import Routes from "./Routes";
import AuthScreen from "./AuthScreen";
import SearchCommand from "./common/SearchCommand";
import { AIChat } from "./AIChat";
import { XeroChat } from "./XeroChat";
import MobileFAB from "./shared/MobileFAB";
import { useMediaQuery } from "../hooks/useMediaQuery";

/**
 * Main application component for the financial analysis dashboard
 * Handles authentication state and routing
 */
const App: React.FC = () => {
  const { isAuthenticated, login, logout } = useAuthStatus();

  return (
    <EventProvider>
      <LoggingProvider>
        <LoadingProvider>
          <UserProvider>
            <SearchProvider>
              <FloatingPanelsProvider>
                <BrowserRouter>
                  <div className="min-h-screen bg-background dark:bg-background-dark dark:text-text-dark">
                    <GlobalLoadingIndicator />
                    <AppContent
                      isAuthenticated={isAuthenticated}
                      handleAuthenticated={login}
                      handleLogout={logout}
                    />
                  </div>
                </BrowserRouter>
              </FloatingPanelsProvider>
            </SearchProvider>
          </UserProvider>
        </LoadingProvider>
      </LoggingProvider>
    </EventProvider>
  );
};

// Separate component for the app content
const AppContent: React.FC<{
  isAuthenticated: boolean;
  handleAuthenticated: () => void;
  handleLogout: () => void;
}> = ({ isAuthenticated, handleAuthenticated, handleLogout }) => {
  // Call hooks first - must be called in the same order on every render
  const { isSearchOpen, closeSearch } = useSearch();
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Only render the application if authenticated
  if (!isAuthenticated) {
    return <AuthScreen onAuthenticated={handleAuthenticated} />;
  }

  // If authenticated, render the main application
  return (
    <>
      <MainLayout handleLogout={handleLogout}>
        <Routes />
      </MainLayout>
      {/* Use mobile FAB on mobile, separate floating buttons on desktop */}
      {isMobile ? (
        <MobileFAB />
      ) : (
        <>
          <UpdateNotification />
          <AIChat />
        </>
      )}
      <SearchCommand isOpen={isSearchOpen} onClose={closeSearch} />
    </>
  );
};

export default App;
