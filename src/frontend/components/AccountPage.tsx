import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getGravatarUrl } from "../utils/gravatar";
import { Button } from "./ui";

interface AccountPageProps {
  userName: string;
  userEmail: string;
  organization: {
    name: string;
    id: string;
  };
  onLogout: () => void;
}

const AccountPage: React.FC<AccountPageProps> = ({
  userName,
  userEmail,
  organization,
  onLogout,
}) => {
  const navigate = useNavigate();
  const [gravatarUrl, setGravatarUrl] = useState<string | null>(null);
  const [gravatarError, setGravatarError] = useState(false);

  // Generate initials from user's name for the avatar
  const initials = userName
    .split("")
    .map((name) => name[0])
    .join("")
    .toUpperCase()
    .substring(0, 2);

  // Get Gravatar URL when email changes
  useEffect(() => {
    if (userEmail) {
      try {
        // Use the proper getGravatarUrl utility that creates an MD5 hash
        // Larger size for the account page avatar
        const url = getGravatarUrl(userEmail, 120,"identicon");
        setGravatarUrl(url);
      } catch (error) {
        console.error("Error setting Gravatar URL:", error);
        setGravatarError(true);
      }
    }
  }, [userEmail]);

  // Determine whether to show initials or Gravatar
  const showInitials = gravatarError || !gravatarUrl;

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-primary dark:text-primary">
          Account Settings
        </h1>
        <Button
          onClick={() => navigate(-1)}
          variant="secondary"
        >
          Back
        </Button>
      </div>

      <div className="bg-surface-card dark:bg-surface-card shadow rounded-lg mb-6">
        <div className="p-6">
          <div className="flex flex-col md:flex-row md:items-center mb-6">
            {/* User avatar */}
            <div className="mb-4 md:mb-0 md:mr-6">
              {showInitials ? (
                <div className="h-24">
                  {initials}
                </div>
              ) : (
                <img
                  src={gravatarUrl ||""}
                  alt={userName}
                  className="w-24 h-24 rounded-full border border-default dark:border-default"
                  onError={() => setGravatarError(true)}
                />
              )}
            </div>
            <div>
              <h2 className="text-lg font-medium text-primary dark:text-primary">
                {userName}
              </h2>
              <p className="text-sm text-muted dark:text-subtle">
                {userEmail}
              </p>
              <p className="text-sm text-muted dark:text-subtle mt-1">
                Organization: {organization.name}
              </p>
              <div className="mt-2 flex items-center">
                <div className="h-2 w-2 rounded-full bg-success-light0 mr-2"></div>
                <p className="text-sm text-primary dark:text-subtle">
                  Xero Connected
                </p>
              </div>
            </div>
          </div>

          <div className="text-sm text-muted dark:text-subtle mt-4">
            <p className="mb-2">
              Your profile photo is provided by Gravatar based on your email
              address.
            </p>
            <a
              href="https://gravatar.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary-color dark:text-primary-light hover:underline"
            >
              Update your Gravatar
            </a>
          </div>
        </div>
      </div>

      <div className="bg-surface-card dark:bg-surface-card shadow rounded-lg">
        <div className="p-6">
          <h2 className="text-lg font-medium text-primary dark:text-primary mb-4">
            Account Actions
          </h2>
          <div className="space-y-4">
            <Button
              onClick={onLogout}
              variant="danger"
            >
              Logout
            </Button>

            <div className="pt-4 border-t border-default dark:border-default">
              <p className="text-sm text-muted dark:text-subtle mb-2">
                To manage your Xero account details, visit the Xero portal.
              </p>
              <a
                href="https://www.xero.com/account"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-primary-color dark:text-primary-light hover:underline"
              >
                Go to Xero Account Settings
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountPage;
