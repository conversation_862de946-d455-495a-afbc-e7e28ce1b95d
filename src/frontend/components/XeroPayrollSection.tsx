import React, { useState, useEffect } from "react";
import {
  getXeroPayrollExpenses,
  syncPayrollWithUpstream,
  XeroPayrollExpenseDisplay,
  debouncedPublishExpenseUpdated,
} from "../api/xero";
import { useEvents } from "../contexts";
import { LoadingIndicator } from "./ForwardProjection";
import { useAuthStatus } from "../hooks/useAuthStatus";
import { Card } from "./ui";
import XeroBadge from "./shared/XeroBadge";

/**
 * Component for displaying the most recent payroll data and syncing with upstream
 */ import { Button } from "@/frontend/components/ui/Button";
export const XeroPayrollSection: React.FC = () => {
  const events = useEvents();
  const { isAuthenticated } = useAuthStatus();
  const [payrollTemplate, setPayrollTemplate] =
    useState<XeroPayrollExpenseDisplay | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Fixed lookback period of 90 days
  const lookbackDays = 90;
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [syncSuccess, setSyncSuccess] = useState<boolean>(false);

  // Load payroll template on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadPayrollTemplate();
    }
  }, [isAuthenticated]);

  /**
   * Load payroll template from API
   */
  const loadPayrollTemplate = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log("Loading payroll template from API...");
      const data = await getXeroPayrollExpenses(lookbackDays);
      console.log("API response:", data);

      if (Array.isArray(data) && data.length > 0) {
        // Debug log payroll template
        const template = data[0];
        console.log("Payroll template data:", {
          id: template.id,
          paymentDate: template.paymentDate,
          dateType: typeof template.paymentDate,
          isDate: template.paymentDate instanceof Date,
          amount: template.amount,
          wages: template.wages,
          superannuation: template.superannuation,
          tax: template.tax,
          reimbursement: template.reimbursement,
          description: template.description,
          employeeCount: template.employeeCount,
          source: template.source,
        });

        // Check for data integrity issues
        if (!template.id || template.amount === 0) {
          console.warn(
            "Payroll data has potential integrity issues:",
            template,
          );
        }

        setPayrollTemplate(template);
      } else {
        console.log("No payroll data returned from API");
        setPayrollTemplate(null);
      }
    } catch (error: any) {
      console.error("Error loading payroll template:", error);

      // Handle specific error types
      if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;

        // Handle various error status codes with meaningful messages
        if (statusCode === 403 && errorData.code === "MISSING_SCOPES") {
          setError(
            "You need to reconnect to Xero with full payroll access. Your current connection is missing the payroll.payruns scope.",
          );
        } else if (statusCode === 404 && errorData.code === "NO_DATA") {
          setError(
            "No payroll data was found in your Xero account. Please make sure you have payroll setup in Xero.",
          );
        } else if (statusCode === 501 && errorData.code === "API_UNAVAILABLE") {
          setError(
            "The Xero Payroll API is not available for your account type or region.",
          );
        } else if (statusCode === 401 && errorData.code === "AUTH_ERROR") {
          setError(
            "Your Xero authentication has expired. Please reconnect to Xero.",
          );
        } else if (errorData.details) {
          // Use the detailed error message if available
          setError(errorData.details);
        } else {
          // Fallback to the general message
          setError(
            errorData.message || "Failed to load payroll template from Xero",
          );
        }
      } else {
        // General error handling
        setError(error.message || "Failed to load payroll template from Xero");
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle syncing payroll template with upstream
   */
  const handleSyncWithUpstream = async () => {
    if (!payrollTemplate) return;

    setIsProcessing(true);
    setError(null);
    setSyncSuccess(false);

    try {
      await syncPayrollWithUpstream(payrollTemplate);

      // Show success state
      setSyncSuccess(true);

      // Notify other components that expenses have been updated
      debouncedPublishExpenseUpdated(events);

      // Refresh to update hasRecurringExpense status
      setTimeout(() => {
        loadPayrollTemplate();
        setSyncSuccess(false);
      }, 3000);
    } catch (error: any) {
      setError(error.message || "Failed to sync payroll with upstream");
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * Format currency
   */
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
    }).format(value);
  };

  /**
   * Format date safely
   */
  const formatDate = (date: Date | string | null | undefined): string => {
    // Handle various input types
    if (!date) return "Unknown date";

    // Convert string to Date object if needed
    const dateObj = typeof date === "string" ? new Date(date) : date;

    // Ensure we have a valid Date object
    if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
      console.warn("Invalid date encountered:", date);
      return "Invalid date";
    }

    try {
      return dateObj.toLocaleDateString("en-AU", {
        day: "numeric",
        month: "short",
        year: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      // Fallback formatting
      return `${dateObj.getDate()}/${
        dateObj.getMonth() + 1
      }/${dateObj.getFullYear()}`;
    }
  };

  /**
   * Calculate next payment date based on template
   */
  const getNextPaymentDate = (): string => {
    if (!payrollTemplate) return "Unknown";

    try {
      // Safely convert the payment date to a Date object
      let paymentDate;

      // Handle various date formats that might come from the API
      if (payrollTemplate.paymentDate instanceof Date) {
        paymentDate = payrollTemplate.paymentDate;
      } else if (typeof payrollTemplate.paymentDate === "string") {
        // Handle string date including special Xero format
        if (payrollTemplate.paymentDate.includes("/Date(")) {
          // Extract timestamp from Xero format:"/Date(timestamp)/"
          const timestamp = parseInt(
            payrollTemplate.paymentDate.replace(
              /\/Date\(([+-]?\d+)([+-]\d{4})?\)\//g,
              "$1",
            ),
          );
          paymentDate = new Date(timestamp);
        } else {
          // Regular date string
          paymentDate = new Date(payrollTemplate.paymentDate);
        }
      } else {
        // Fallback - use 15th of current month
        console.warn(
          "Invalid payment date format:",
          payrollTemplate.paymentDate,
        );
        paymentDate = new Date();
        paymentDate.setDate(15); // Default to middle of month
      }

      // Ensure we have a valid date
      if (isNaN(paymentDate.getTime())) {
        console.warn("Invalid date after conversion:", paymentDate);
        // Fallback to 15th of current month
        paymentDate = new Date();
        paymentDate.setDate(15);
      }

      console.log("Using payment date for calculation:", paymentDate);

      const today = new Date();
      const currentMonth = today.getMonth();
      const currentYear = today.getFullYear();

      // Check if we have a payroll run in the current month
      // This uses the period dates from the template to determine if this month's payroll has occurred
      // Log the actual date we're using
      console.log("Using actual date for calculation:", {
        today: today.toISOString(),
        currentMonth,
        currentYear,
      });

      // Dump the entire payrollTemplate object to see what data we're working with
      console.log(
        "Full payroll template data:",
        JSON.stringify(payrollTemplate, null, 2),
      );

      const periodStartDate = payrollTemplate.periodStartDate
        ? new Date(payrollTemplate.periodStartDate)
        : null;
      const periodEndDate = payrollTemplate.periodEndDate
        ? new Date(payrollTemplate.periodEndDate)
        : null;

      console.log("Period dates after conversion:", {
        periodStartDate: periodStartDate ? periodStartDate.toISOString() : null,
        periodEndDate: periodEndDate ? periodEndDate.toISOString() : null,
        periodStartMonth: periodStartDate ? periodStartDate.getMonth() : null,
        currentMonth,
      });

      const hasCurrentMonthPayroll =
        periodStartDate &&
        periodStartDate.getMonth() === currentMonth &&
        periodStartDate.getFullYear() === currentYear;

      console.log("Next payment date calculation info:", {
        today: today.toISOString(),
        currentMonth,
        currentYear,
        periodStartDate: periodStartDate ? periodStartDate.toISOString() : null,
        periodStartMonth: periodStartDate ? periodStartDate.getMonth() : null,
        periodStartYear: periodStartDate ? periodStartDate.getFullYear() : null,
        hasCurrentMonthPayroll,
      });

      // Get the day of month from the payment date
      let nextPaymentDay = paymentDate.getDate();

      // Check if the payment date is in the future
      const paymentDayIsInFuture = nextPaymentDay >= today.getDate();

      console.log("Payment day check:", {
        nextPaymentDay,
        todayDate: today.getDate(),
        paymentDayIsInFuture,
      });

      // If we already have a payroll run for the current month, the next one is in the next month
      // Otherwise, if the payment day is in the future, show it in the current month
      let nextPaymentMonth;

      // Always show the next payment in the current month if the payment day hasn't passed yet
      if (paymentDayIsInFuture) {
        nextPaymentMonth = currentMonth; // Current month
        console.log("Payment day is in the future, showing in current month");
      } else {
        // Payment day has passed, show in next month
        nextPaymentMonth = currentMonth + 1;
        console.log("Payment day has passed, showing in next month");
      }

      let nextPaymentYear = currentYear;

      // Adjust if we need to roll over to next year
      if (nextPaymentMonth > 11) {
        nextPaymentMonth = 0; // January
        nextPaymentYear += 1;
      }

      // Handle month length differences (e.g., January 31 -> February 28)
      const daysInNextMonth = new Date(
        nextPaymentYear,
        nextPaymentMonth + 1,
        0,
      ).getDate();
      if (nextPaymentDay > daysInNextMonth) {
        nextPaymentDay = daysInNextMonth;
      }

      const nextPaymentDate = new Date(
        nextPaymentYear,
        nextPaymentMonth,
        nextPaymentDay,
      );
      console.log("Final calculated next payment date for display:", {
        nextPaymentDate: nextPaymentDate.toISOString(),
        nextPaymentDay,
        nextPaymentMonth,
        nextPaymentYear,
        localeDateString: nextPaymentDate.toLocaleDateString(),
      });

      return formatDate(nextPaymentDate);
    } catch (error) {
      console.error("Error calculating next payment date:", error);
      return "Error calculating date";
    }
  };

  /**
   * Reset state and reload
   */
  const handleRefresh = () => {
    setSyncSuccess(false);
    loadPayrollTemplate();
  };

  // Show authentication banner if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <Card className="text-center py-8 bg-warning-light/50/20 border-warning">
          <div className="flex items-center justify-center mb-2">
            <svg
              className="w-6 h-6 text-warning mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 15v2m0 0v2m0-2h2m-2 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="text-lg font-semibold text-warning">
              Authentication Required
            </h3>
          </div>
          <p className="text-warning">
            You need to connect to Xero to access payroll data.
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-primary-light/80/20 border-primary border-l-4 border-l-blue-500">
        <div className="flex items-start">
          <svg
            className="w-5 h-5 text-primary-color mt-0.5 mr-2 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
          <div>
            <div className="flex items-center">
              <h3 className="font-medium text-primary">
                About Xero Payroll Integration
              </h3>
              <XeroBadge className="ml-2" />
            </div>
            <p className="text-sm text-primary-color mt-1">
              This section shows your most recent payroll data from Xero as a
              template for creating recurring monthly payroll expenses. Use
              the"Sync with Upstream" button to create a monthly recurring
              expense based on your latest payroll data.
            </p>
            <p className="text-sm text-primary-color mt-2">
              <strong>Note:</strong> This will create a forward-looking expense
              starting from next month, based on your most recent payroll
              amount. This includes both salary and superannuation.
            </p>
          </div>
        </div>
      </Card>

      {error && (
        <Card className="bg-error-light/80/20 border-error">
          <div className="flex items-start">
            <svg
              className="w-5 h-5 text-error mt-0.5 mr-2 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-error">
                Xero Payroll Error
              </h3>
              <div className="mt-2 text-sm text-error">
                <p>{error}</p>

                {error.includes("missing the payroll.payruns scope") && (
                  <div className="mt-2 border-t border-error pt-2">
                    <p className="font-medium">How to fix this:</p>
                    <ol className="list-decimal pl-4 mt-1 space-y-1 text-sm">
                      <li>
                        Click the"Reconnect to Xero" button at the top of the
                        page
                      </li>
                      <li>
                        When redirected to Xero, ensure you check the"Payroll
                        AU" permissions
                      </li>
                      <li>
                        Complete the authorization flow to return to this app
                      </li>
                    </ol>
                  </div>
                )}

                {error.includes("No payroll data was found") && (
                  <div className="mt-2 border-t border-error pt-2">
                    <p className="font-medium">How to fix this:</p>
                    <ol className="list-decimal pl-4 mt-1 space-y-1 text-sm">
                      <li>
                        Make sure you have processed at least one payroll run in
                        Xero
                      </li>
                      <li>
                        If you're using Xero Payroll, ensure it's properly
                        configured
                      </li>
                      <li>Click the"Refresh Data" button to try again</li>
                    </ol>
                  </div>
                )}

                {error.includes("authentication has expired") && (
                  <div className="mt-2 border-t border-error pt-2">
                    <p className="font-medium">How to fix this:</p>
                    <ol className="list-decimal pl-4 mt-1 space-y-1 text-sm">
                      <li>
                        Click the"Reconnect to Xero" button at the top of the
                        page
                      </li>
                      <li>
                        Complete the authorization flow to refresh your
                        connection
                      </li>
                    </ol>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}

      {syncSuccess && (
        <Card className="bg-success-light/80/20 border-success border-l-4 border-l-green-500">
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-success mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            <p className="text-sm text-success">
              Success! A recurring monthly payroll expense has been created for
              your cashflow projections.
            </p>
          </div>
        </Card>
      )}

      <div className="flex justify-end mb-6">
        <Button variant="primary" onClick={handleRefresh} disabled={loading}>
          {loading ? (
            <>
              <LoadingIndicator size="small" className="mr-2" />
              Loading...
            </>
          ) : (
            <>
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Refresh Data
            </>
          )}
        </Button>
      </div>

      {loading ? (
        <Card className="flex items-center justify-center py-12">
          <LoadingIndicator text="Loading payroll data from Xero..." />
        </Card>
      ) : !payrollTemplate ? (
        <Card className="text-center py-8 bg-surface-page/50/50 border-default/70/70">
          <svg
            className="mx-auto h-12 w-12 text-subtle"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-primary">
            No payroll data found
          </h3>
          <p className="mt-1 text-sm text-muted">
            Please make sure you have processed at least one payroll run in
            Xero.
          </p>
        </Card>
      ) : (
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <h2 className="text-xl font-semibold text-primary">
              Payroll Template
            </h2>
            <XeroBadge className="ml-2" />
          </div>

          <div className="mb-6 flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <div className="mb-3">
                <span className="block text-sm font-medium text-muted mb-1">
                  Last Payroll
                </span>
                <span className="text-lg font-medium text-primary">
                  {payrollTemplate.description}
                </span>
              </div>

              <div className="mb-3">
                <span className="block text-sm font-medium text-muted mb-1">
                  Last Payment Date
                </span>
                <span className="text-primary">
                  {formatDate(payrollTemplate.paymentDate)}
                </span>
              </div>

              {payrollTemplate.periodStartDate &&
              payrollTemplate.periodEndDate ? (
                <div className="mb-3">
                  <span className="block text-sm font-medium text-muted mb-1">
                    Pay Period
                  </span>
                  <span className="text-primary">
                    {formatDate(payrollTemplate.periodStartDate)} -{""}
                    {formatDate(payrollTemplate.periodEndDate)}
                  </span>
                </div>
              ) : null}

              <div className="mb-3">
                <span className="block text-sm font-medium text-muted mb-1">
                  Employees
                </span>
                <span className="text-primary">
                  {payrollTemplate.employeeCount}
                </span>
              </div>
            </div>

            <div className="flex-1">
              <div className="mb-3">
                <span className="block text-sm font-medium text-muted mb-1">
                  Total Amount
                </span>
                <span className="text-xl font-bold text-accent">
                  {formatCurrency(payrollTemplate.amount)}
                </span>
              </div>

              {/* Detailed breakdown of payroll costs */}
              <Card className="mb-3 bg-surface-page/80/80 border-default">
                <h4 className="text-sm font-medium text-primary mb-2">
                  Cost Breakdown
                </h4>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  {payrollTemplate.wages !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-secondary">Net Pay:</span>
                      <span className="font-medium">
                        {formatCurrency(payrollTemplate.wages)}
                      </span>
                    </div>
                  )}

                  {payrollTemplate.superannuation !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-secondary">Super:</span>
                      <span className="font-medium">
                        {formatCurrency(payrollTemplate.superannuation)}
                      </span>
                    </div>
                  )}

                  {payrollTemplate.tax !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-secondary">Tax:</span>
                      <span className="font-medium">
                        {formatCurrency(payrollTemplate.tax)}
                      </span>
                    </div>
                  )}

                  {payrollTemplate.reimbursement !== undefined &&
                    payrollTemplate.reimbursement > 0 && (
                      <div className="flex justify-between">
                        <span className="text-secondary">Reimbursements:</span>
                        <span className="font-medium">
                          {formatCurrency(payrollTemplate.reimbursement)}
                        </span>
                      </div>
                    )}
                </div>
              </Card>

              <div className="mb-3">
                <span className="block text-sm font-medium text-muted mb-1">
                  Payment Frequency
                </span>
                <span className="text-primary capitalize">
                  {payrollTemplate.frequency.toLowerCase()}
                </span>
              </div>

              <div className="mb-3">
                <span className="block text-sm font-medium text-muted mb-1">
                  Next Projected Payment
                </span>
                <span className="text-primary">{getNextPaymentDate()}</span>
              </div>

              {/* Source indicator */}
              {payrollTemplate.source && (
                <div className="text-xs text-muted mt-3">
                  Data source:{""}
                  {payrollTemplate.source === "xero"
                    ? "Xero Payroll API"
                    : payrollTemplate.source}
                </div>
              )}
            </div>
          </div>

          <div className="mt-6 border-t border-default pt-6">
            <div className="flex flex-col items-center justify-center">
              <h3 className="text-lg font-medium text-primary mb-2">
                Create Recurring Monthly Expense
              </h3>
              <p className="text-secondary text-center mb-4">
                This will create a recurring monthly expense starting{""}
                {getNextPaymentDate()}.
              </p>

              <Button
                variant="secondary"
                onClick={handleSyncWithUpstream}
                disabled={
                  isProcessing ||
                  syncSuccess ||
                  payrollTemplate.hasRecurringExpense
                }
                className={`${
                  isProcessing
                    ? "--processing"
                    : syncSuccess
                      ? "--success"
                      : payrollTemplate.hasRecurringExpense
                        ? "--secondary opacity-50 cursor-not-allowed"
                        : "--primary"
                }`}
              >
                {isProcessing
                  ? "Processing..."
                  : syncSuccess
                    ? "Synced Successfully!"
                    : payrollTemplate.hasRecurringExpense
                      ? "Already Synced"
                      : "Sync with Upstream"}
              </Button>

              {payrollTemplate.hasRecurringExpense && (
                <p className="text-sm text-muted mt-2">
                  A recurring payroll expense has already been created.
                </p>
              )}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default XeroPayrollSection;
