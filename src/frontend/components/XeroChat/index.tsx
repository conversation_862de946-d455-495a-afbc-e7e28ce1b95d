import React, { useState, useEffect, useRef } from "react";
import { MessageCircle, X, Send, Loader2, Bo<PERSON>, User } from "lucide-react";
import { useFloatingPanels } from "../../contexts/FloatingPanelsContext";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
  toolCalls?: ToolCall[];
}

interface ToolCall {
  tool: string;
  arguments: any;
  result?: any;
}

interface Tool {
  name: string;
  description?: string;
  input_schema?: any;
}

export const XeroChat: React.FC = () => {
  const { xeroExpanded: isOpen, setXeroExpanded: setIsOpen } = useFloatingPanels();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState(");
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [tools, setTools] = useState<Tool[]>([]);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // Initialize MCP session when chat opens
  useEffect(() => {
    if (isOpen && !sessionId) {
      initializeSession();
    }
  }, [isOpen, sessionId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const initializeSession = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/mcp/init', {
        method: "POST",
        headers: {"Content-Type": "application/json"
        },
        credentials: "include"
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to initialize MCP session");
      }

      const data = await response.json();
      setSessionId(data.sessionId);
      setTools(data.tools || []);

      // Add welcome message
      setMessages([{
        id: Date.now().toString(),
        role: "assistant",
        content: "Hello! I\"m connected to your Xero account. I can help you with:\n\n• Viewing invoices, bills, and payments\n• Checking your accounts and bank transactions\n• Reviewing financial reports\n• Finding customer and supplier information\n\nWhat would you like to know?",
        timestamp: new Date()
      }]);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to connect";
      setError(errorMessage);
      console.error('Error initializing MCP session:', err);

      // Check if it's an API key issue
      if (errorMessage.includes('Anthropic API not configured')) {
        setError('The Anthropic API is not configured. Please set the ANTHROPIC_API_KEY environment variable.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSend = async () => {
    if (!input.trim() || !sessionId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      timestamp: new Date()
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput(");
    setIsLoading(true);
    setError(null);

    try {
      // Send message to Claude
      const response = await fetch('/api/mcp/chat', {
        method: "POST",
        headers: {"Content-Type": "application/json"
        },
        credentials: "include",
        body: JSON.stringify({
          sessionId,
          message: input
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to send message");
      }

      const data = await response.json();

      // Add Claude's response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.message,
        timestamp: new Date(),
        toolCalls: data.toolCalls
      };

      setMessages((prev) => [...prev, assistantMessage]);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setError(errorMessage);
      console.error('Error processing message:', err);

      // Add error message to chat
      const errorMsg: Message = {
        id: (Date.now() + 1).toString(),
        role: "system",
        content: `Error: ${errorMessage}`,
        timestamp: new Date()
      };
      setMessages((prev) => [...prev, errorMsg]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key ==="Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const closeChat = async () => {
    if (sessionId) {
      try {
        await fetch('/api/mcp/close', {
          method: "POST",
          headers: {"Content-Type": "application/json"
          },
          credentials: "include",
          body: JSON.stringify({ sessionId })
        });
      } catch (err) {
        console.error('Error closing session:', err);
      }
    }
    setIsOpen(false);
    setSessionId(null);
    setMessages([]);
    setTools([]);
    setError(null);
  };

  const suggestedQueries = ["Show me unpaid invoices","What's my current bank balance?","List recent payments",'Show this month's profit and loss"];


  return (
    <>
      {/* Floating button */}
      {!isOpen &&
      <Button variant="primary"
      onClick={() => setIsOpen(true)}

      aria-label="Open UpstreamAI">

          <MessageCircle size={20} />
        </Button>
      }

      {/* Chat window */}
      {isOpen &&
      <div className="fixed bottom-6 left-4 w-96 h-[600px] bg-surface-card dark:bg-surface-card rounded-lg shadow-2xl flex flex-col z-50 border border-default dark:border-default">
          {/* Header */}
          <div className=">
            <div className="flex items-center gap-2">
              <Bot size={20} />
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">UpstreamAI</h3>
                <span className="text-xs bg-primary/20 px-2 py-0.5 rounded text-primary">
                  Powered by Claude
                </span>
              </div>
            </div>
            <Button variant="ghost"
          onClick={closeChat}
          className="p-1"
          aria-label="Close chat">

              <X size={20} />
            </Button>
          </div>

          {/* Messages area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {error &&
          <div className="bg-error/10 dark:bg-error/20 border border-error text-error p-3 rounded-md text-sm">
                {error}
              </div>
          }

            {messages.map((message) =>
          <div
            key={message.id}
            className={`flex gap-2 ${
            message.role ==="user' ?'justify-end' : "justify-start"}`
            }>

                {message.role !=="user' &&
            <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
            message.role ==="system' ?'bg-surface-muted' : "bg-primary/10 dark:bg-primary/20"}`
            }>
                    <Bot size={16} className={message.role ==="system' ?'text-secondary' : "text-primary-color"} />
                  </div>
            }
                
                <div
              className={`max-w-[80%] rounded-lg p-3 ${
              message.role ==="user' ?'bg-primary-color text-primary' :
              message.role ==="system' ?'bg-surface-alt dark:bg-surface-alt text-secondary dark:text-subtle" : "bg-surface-alt dark:bg-surface-alt text-primary dark:text-primary"}`
              }>

                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  
                  {message.toolCalls && message.toolCalls.length > 0 &&
              <div className="mt-2 pt-2 border-t border-strong dark:border-strong">
                      <p className="text-xs opacity-70">
                        Used: {message.toolCalls.map((tc) => tc.tool.replace(/_/g,")).join(',')}
                      </p>
                    </div>
              }
                </div>

                {message.role ==="user" &&
            <div className="w-8 h-8 bg-surface-muted rounded-full flex items-center justify-center flex-shrink-0">
                    <User size={16} className="text-secondary" />
                  </div>
            }
              </div>
          )}

            {isLoading &&
          <div className="flex gap-2 justify-start">
                <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center">
                  <Bot size={16} className="text-primary" />
                </div>
                <div className=">
                  <Loader2 className="animate-spin" size={16} />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
          }

            <div ref={messagesEndRef} />
          </div>

          {/* Suggested queries for empty chat */}
          {messages.length === 1 && !isLoading &&
        <div className="px-4 pb-2">
              <p className="text-xs text-muted dark:text-subtle mb-2">Try asking:</p>
              <div className="flex flex-wrap gap-1">
                {suggestedQueries.map((query, index) =>
            <Button variant="secondary"
            key={index}
            onClick={() => setInput(query)}>


                    {query}
                  </Button>
            )}
              </div>
            </div>
        }

          {/* Input area */}
          <div className="border-t border-default dark:border-default p-4">
            <div className="flex gap-2">
              <Input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me about your Xero data..."
              className="flex-1 px-3 py-2 border border-strong dark:border-strong dark:bg-surface-alt dark:text-primary rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-blue-400"
              disabled={isLoading || !sessionId} />

              <Button variant="primary"
            onClick={handleSend}
            disabled={isLoading || !input.trim() || !sessionId}

            aria-label="Send message">

                <Send size={20} />
              </Button>
            </div>
            
            {/* Status indicator */}
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${sessionId ? "bg-success" : "bg-surface-muted"}`} />
                <span className="text-xs text-muted dark:text-subtle">
                  {sessionId ? "Connected to Xero" : "Connecting..."}
                </span>
              </div>
              {tools.length > 0 &&
            <span className="text-xs text-muted dark:text-subtle">
                  {tools.length} tools available
                </span>
            }
            </div>
          </div>
        </div>
      }
    </>);

};