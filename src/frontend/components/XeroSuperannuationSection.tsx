import React, { useState, useEffect, useMemo } from "react";
import { getXeroSuperannuationExpenses, convertSuperannuationToExpense, XeroSuperannuationExpenseDisplay, debouncedPublishExpenseUpdated } from "../api/xero";
import { useEvents } from "../contexts";
import { LoadingIndicator } from "./ForwardProjection";
import { useAuthStatus } from "../hooks/useAuthStatus";
import { Card } from "./ui";
import XeroBadge from "./shared/XeroBadge";

/**
 * Component for displaying and converting Xero superannuation expenses
 */import { Button } from "@/frontend/components/ui/Button";
import { Select } from "@/frontend/components/ui/Select";
export const XeroSuperannuationSection: React.FC = () => {
  const events = useEvents();
  const { isAuthenticated } = useAuthStatus();
  const [expenses, setExpenses] = useState<XeroSuperannuationExpenseDisplay[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lookbackDays, setLookbackDays] = useState<number>(90);

  // Loading states for individual actions
  const [processingIds, setProcessingIds] = useState<string[]>([]);
  const [successIds, setSuccessIds] = useState<string[]>([]);
  const [importCount, setImportCount] = useState<number>(0);

  // Load superannuation expenses on component mount and when lookback days changes
  useEffect(() => {
    if (isAuthenticated) {
      loadExpenses();
    }
  }, [isAuthenticated, lookbackDays]);

  /**
   * Load superannuation expenses from API
   */
  const loadExpenses = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await getXeroSuperannuationExpenses(lookbackDays);
      setExpenses(data);
    } catch (error: any) {
      setError(error.message || "Failed to load Xero superannuation expenses");
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle adding a superannuation expense to expenses
   */
  const handleAddToUpstream = async (expense: XeroSuperannuationExpenseDisplay) => {
    setProcessingIds((prev) => [...prev, expense.id]);
    setError(null);

    try {
      const createdExpense = await convertSuperannuationToExpense(expense);

      // Show success state
      setSuccessIds((prev) => [...prev, expense.id]);
      setImportCount((prev) => prev + 1);

      // Update the local state to mark this expense as already added
      setExpenses((prevExpenses) =>
      prevExpenses.map((e) =>
      e.id === expense.id ? { ...e, isAlreadyAdded: true } : e
      )
      );

      // Notify other components that expenses have been updated
      debouncedPublishExpenseUpdated(events);

      // Remove from success state after 3 seconds
      setTimeout(() => {
        setSuccessIds((prev) => prev.filter((id) => id !== expense.id));
      }, 3000);
    } catch (error: any) {
      setError(error.message || `Failed to add"${expense.description}" to expenses`);
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== expense.id));
    }
  };

  /**
   * Format currency
   */
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-AU", {
      style: "currency",
      currency: "AUD"
    }).format(value);
  };

  /**
   * Format date
   */
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString("en-AU", {
      day: "numeric",
      month: "short",
      year: "numeric"
    });
  };

  /**
   * Get button state for an expense
   */
  const getButtonState = (expense: XeroSuperannuationExpenseDisplay) => {
    if (processingIds.includes(expense.id)) return { text: "Adding...", disabled: true };
    if (successIds.includes(expense.id)) return { text: "Added ✓", disabled: true };
    if (expense.isAlreadyAdded) return { text: "Already Added", disabled: true };
    return { text: "Add to Upstream", disabled: false };
  };

  /**
   * Reset state and reload
   */
  const handleRefresh = () => {
    setProcessingIds([]);
    setSuccessIds([]);
    loadExpenses();
  };

  // Show authentication banner if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <Card className="text-center py-8 bg-warning-light/50/20 border-warning">
          <div className="flex items-center justify-center mb-2">
            <svg className="w-6 h-6 text-warning mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m0 0v2m0-2h2m-2 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-semibold text-warning">Authentication Required</h3>
          </div>
          <p className="text-warning">
            You need to connect to Xero to access superannuation data.
          </p>
        </Card>
      </div>);

  }

  return (
    <div className="space-y-6">
      <Card className="bg-primary-light/80/20 border-primary border-l-4 border-l-blue-500">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-primary-color mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <div>
            <div className="flex items-center">
              <h3 className="font-medium text-primary">About Xero Superannuation Integration</h3>
              <XeroBadge className="ml-2" />
            </div>
            <p className="text-sm text-primary-color mt-1">
              This section shows superannuation expenses from your Xero account. Click"Add to Upstream" to include
              these expenses in your cashflow projection.
            </p>
          </div>
        </div>
      </Card>

      {error &&
      <Card className="bg-error-light/80/20 border-error">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-error mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-error">{error}</p>
          </div>
        </Card>
      }

      {importCount > 0 &&
      <Card className="bg-success-light/80/20 border-success border-l-4 border-l-green-500">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-success mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-success">
              <span className="font-semibold">{importCount}</span> superannuation expense{importCount !== 1 ? "s" : ""} successfully added.
            </p>
          </div>
        </Card>
      }

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex items-center">
          <label htmlFor="lookbackDays" className="mr-2 text-sm font-medium text-primary">Show from the last:</label>
          <Select
            id="lookbackDays"
            value={lookbackDays}
            onChange={(e) => setLookbackDays(Number(e.target.value))}
            className="form-select rounded-md border-strong py-1.5 shadow-sm focus:border-primary focus:ring focus:ring-primary/20">

            <option value={30}>30 days</option>
            <option value={90}>90 days</option>
            <option value={180}>180 days</option>
            <option value={365}>365 days</option>
          </Select>
        </div>

        <Button variant="primary"
        onClick={handleRefresh}
        disabled={loading}>


          {loading ?
          <>
              <LoadingIndicator size="small" className="mr-2" />
              Loading...
            </> :

          <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh Superannuation
            </>
          }
        </Button>
      </div>

      {loading ?
      <Card className="flex items-center justify-center py-12">
          <LoadingIndicator text="Loading superannuation expenses from Xero..." />
        </Card> :
      expenses.length === 0 ?
      <Card className="text-center py-8 bg-surface-page/50/50 border-default/70/70">
          <svg className="mx-auto h-12 w-12 text-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-primary">No superannuation expenses found</h3>
          <p className="mt-1 text-sm text-muted">
            No superannuation expenses found in the selected time period.
          </p>
        </Card> :

      <>
          {/* Expense Count */}
          <div className="mb-4 text-sm text-secondary">
            Showing {expenses.length} superannuation expense{expenses.length !== 1 ? "s" : ""}
          </div>

          {/* Mobile card view - only shown on small screens */}
          <div className="md:hidden space-y-3">
            {expenses.map((expense) => {
            const buttonState = getButtonState(expense);

            return (
              <Card key={expense.id} className="hover:shadow-md transition-all duration-200">
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                      <h3 className="font-medium text-primary truncate mr-2">{expense.description}</h3>
                      <XeroBadge className="ml-1" />
                    </div>
                    <span className="font-bold text-accent whitespace-nowrap">{formatCurrency(expense.amount)}</span>
                  </div>
                  <div className="mb-2">
                    <span className="text-sm text-secondary">Provider: {expense.provider}</span>
                  </div>
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center text-secondary">
                      <svg className="w-4 h-4 mr-1 text-muted" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" />
                      </svg>
                      <span className="text-sm">Payment: {formatDate(expense.paymentDate)}</span>
                    </div>
                    <div>
                      <span className="">
                        {expense.status}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button variant="secondary"
                  onClick={() => handleAddToUpstream(expense)}
                  disabled={buttonState.disabled}
                  className={`${
                  successIds.includes(expense.id) ? "--success" :
                  expense.isAlreadyAdded ? "--secondary opacity-50 cursor-not-allowed" : "--primary"}`
                  }>

                      {expense.isAlreadyAdded ? "Already Added" : buttonState.text}
                    </Button>
                  </div>
                </Card>);

          })}
          </div>

          {/* Desktop table view - only shown on medium screens and up */}
          <div className="hidden md:block overflow-x-auto">
            <Card className="p-0 overflow-hidden">
              <table className="min-w-full">
                <thead className="bg-surface-page">
                  <tr>
                    <th className="py-3 px-4 text-left text-xs font-medium text-muted uppercase tracking-wider border-b border-default">Description</th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-muted uppercase tracking-wider border-b border-default">Provider</th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-muted uppercase tracking-wider border-b border-default">Payment Date</th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-muted uppercase tracking-wider border-b border-default">Amount</th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-muted uppercase tracking-wider border-b border-default">Status</th>
                    <th className="py-3 px-4 text-left text-xs font-medium text-muted uppercase tracking-wider border-b border-default">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
                  {expenses.map((expense, index) => {
                  const buttonState = getButtonState(expense);

                  return (
                    <tr key={expense.id} className="hover:bg-surface-page dark:hover:bg-surface-page/50 transition-colors">
                        <td className="py-3 px-4 font-medium text-primary">
                          <div className="flex items-center">
                            <span className="mr-2">{expense.description}</span>
                            <XeroBadge className="ml-1" />
                          </div>
                        </td>
                        <td className="py-3 px-4 text-secondary">{expense.provider}</td>
                        <td className="py-3 px-4 text-secondary">{formatDate(expense.paymentDate)}</td>
                        <td className="py-3 px-4 font-medium text-accent">{formatCurrency(expense.amount)}</td>
                        <td className="py-3 px-4">
                          <span className="">
                            {expense.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <Button variant="secondary"
                        onClick={() => handleAddToUpstream(expense)}
                        disabled={buttonState.disabled}
                        className={`${
                        successIds.includes(expense.id) ? "--success" :
                        expense.isAlreadyAdded ? "--secondary opacity-50 cursor-not-allowed" : "--primary"}`
                        }>

                            {expense.isAlreadyAdded ? "Already Added" : buttonState.text}
                          </Button>
                        </td>
                      </tr>);

                })}
                </tbody>
              </table>
            </Card>
          </div>
        </>
      }
    </div>);

};

export default XeroSuperannuationSection;