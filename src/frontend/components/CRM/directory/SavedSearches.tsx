import React, { useState } from "react";
import {
  StarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  LinkIcon,
  UserIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon } from
'@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from "@heroicons/react/24/solid";
import type { SavedSearch } from "./types";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface SavedSearchesProps {
  savedSearches: SavedSearch[];
  onSelectSearch: (search: SavedSearch) => void;
  onSaveCurrentSearch: (name: string) => void;
  onDeleteSearch: (id: string) => void;
  onUpdateSearch: (id: string, updates: Partial<SavedSearch>) => void;
  currentSearchParams: {
    query?: string;
    filters: any;
  };
}

const DEFAULT_SEARCHES: Omit<SavedSearch, 'id'>[] = [
{
  name: "Recent Activity",
  icon: "clock",
  filters: { quickFilter: "recently-updated" },
  isDefault: true,
  isPinned: false
},
{
  name: "High Value Deals",
  icon: "currency",
  filters: { entityType: "deals", quickFilter: "high-value" },
  isDefault: true,
  isPinned: false
},
{
  name: "Setup Needed",
  icon: "link",
  filters: { quickFilter: "unlinked" },
  isDefault: true,
  isPinned: false
},
{
  name: "My Items",
  icon: "user",
  filters: { quickFilter: "my-items" },
  isDefault: true,
  isPinned: false
}];


const ICON_MAP = {
  clock: ClockIcon,
  currency: CurrencyDollarIcon,
  link: LinkIcon,
  user: UserIcon,
  star: StarIcon
};

export const SavedSearches: React.FC<SavedSearchesProps> = ({
  savedSearches,
  onSelectSearch,
  onSaveCurrentSearch,
  onDeleteSearch,
  onUpdateSearch,
  currentSearchParams
}) => {
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');

  const handleSave = () => {
    if (searchName.trim()) {
      onSaveCurrentSearch(searchName.trim());
      setSearchName('');
      setShowSaveDialog(false);
    }
  };

  const handleEdit = (search: SavedSearch) => {
    setEditingId(search.id);
    setEditingName(search.name);
  };

  const handleEditSave = (id: string) => {
    if (editingName.trim()) {
      onUpdateSearch(id, { name: editingName.trim() });
      setEditingId(null);
      setEditingName('');
    }
  };

  const handleTogglePin = (search: SavedSearch) => {
    onUpdateSearch(search.id, { isPinned: !search.isPinned });
  };

  // Combine default searches with user searches
  const allSearches = [
  ...DEFAULT_SEARCHES.map((s, i) => ({ ...s, id: `default-${i}` })),
  ...savedSearches];


  // Sort: pinned first, then by name
  const sortedSearches = allSearches.sort((a, b) => {
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="saved-searches">
      <div className="mb-3">
        <Button variant="primary"
        onClick={() => setShowSaveDialog(true)}>


          <PlusIcon className="w-3 h-3" />
          Save Current
        </Button>

        {showSaveDialog &&
        <div className="mt-2 p-2 bg-surface-page dark:bg-surface-page rounded-md">
            <Input
            type="text"
            value={searchName}
            onChange={(e) => setSearchName(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSave()}
            placeholder="Name..."
            className="w-full px-2 py-1 text-xs bg-surface-card dark:bg-surface-card border border-strong dark:border-strong rounded focus:outline-none focus:ring-1 focus:ring-accent"
            autoFocus />

            <div className="flex gap-1 mt-1">
              <Button variant="primary"
            onClick={handleSave}
            className="flex-1">

                Save
              </Button>
              <Button variant="secondary"
            onClick={() => {
              setShowSaveDialog(false);
              setSearchName('');
            }}>


                Cancel
              </Button>
            </div>
          </div>
        }
      </div>

      <div className="space-y-0.5">
        {sortedSearches.map((search) => {
          const IconComponent = ICON_MAP[search.icon as keyof typeof ICON_MAP] || StarIcon;
          const isEditing = editingId === search.id;

          return (
            <div
              key={search.id}
              className="group flex items-center gap-1.5 px-2 py-1.5 rounded-md hover:bg-surface-alt dark:hover:bg-surface-elevated cursor-pointer transition-colors">

              <IconComponent className="w-3.5 h-3.5 text-muted dark:text-subtle flex-shrink-0" />
              
              {isEditing ?
              <Input
                type="text"
                value={editingName}
                onChange={(e) => setEditingName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleEditSave(search.id);
                  if (e.key === 'Escape') {
                    setEditingId(null);
                    setEditingName('');
                  }
                }}
                onBlur={() => handleEditSave(search.id)}
                className="flex-1 px-1 py-0.5 text-xs bg-surface-card dark:bg-surface-card border border-strong dark:border-strong rounded focus:outline-none focus:ring-1 focus:ring-accent"
                autoFocus /> :


              <span
                onClick={() => onSelectSearch(search)}
                className="flex-1 text-xs text-primary dark:text-subtle truncate"
                title={search.name}>

                  {search.name}
                </span>
              }

              {!search.isDefault &&
              <div className="flex items-center gap-0.5 opacity-0 group-hover:opacity-100 transition-opacity">
                  {search.isPinned &&
                <StarSolidIcon className="w-2.5 h-2.5 text-yellow-500" />
                }
                  <Button variant="secondary"
                onClick={(e) => {
                  e.stopPropagation();
                  if (!search.isDefault) {
                    onDeleteSearch(search.id);
                  }
                }}

                title="Delete">

                    <TrashIcon className="w-2.5 h-2.5 text-subtle" />
                  </Button>
                </div>
              }
            </div>);

        })}
      </div>
    </div>);

};

export default SavedSearches;