import React from "react";
import { ClockIcon, XMarkIcon } from "@heroicons/react/24/outline";
import type { RecentSearch } from "./types";
import { Button } from "@/frontend/components/ui/Button";

interface RecentSearchesProps {
  recentSearches: RecentSearch[];
  onSelectSearch: (query: string) => void;
  onClearHistory: () => void;
  isVisible: boolean;
}

export const RecentSearches: React.FC<RecentSearchesProps> = ({
  recentSearches,
  onSelectSearch,
  onClearHistory,
  isVisible
}) => {
  if (!isVisible || recentSearches.length === 0) return null;

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return 'just now';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-surface-card dark:bg-surface-card rounded-lg shadow-lg border border-default dark:border-default z-50">
      <div className="p-2">
        <div className="flex items-center justify-between px-2 py-1 mb-1">
          <span className="text-xs font-medium text-muted dark:text-subtle">
            Recent Searches
          </span>
          <Button variant="ghost"
          onClick={(e) => {
            e.stopPropagation();
            onClearHistory();
          }}>


            Clear
          </Button>
        </div>
        
        <div className="space-y-0.5">
          {recentSearches.map((search, index) =>
          <Button variant="secondary"
          key={index}
          onClick={() => onSelectSearch(search.query)}>


              <div className="flex items-center gap-2 flex-1 min-w-0">
                <ClockIcon className="w-3.5 h-3.5 text-subtle flex-shrink-0" />
                <span className="text-sm text-primary dark:text-subtle truncate">
                  {search.query}
                </span>
              </div>
              <div className="flex items-center gap-2 text-xs text-muted dark:text-subtle ml-2">
                {search.resultCount !== undefined &&
              <span>{search.resultCount} results</span>
              }
                <span>{formatTimeAgo(search.timestamp)}</span>
              </div>
            </Button>
          )}
        </div>
      </div>
    </div>);

};

export default RecentSearches;