import { useState, useCallback, useRef, useEffect } from "react";
import type { Contact, Company, Deal } from "../../../../types/crm-types";

interface PreviewState {
  visible: boolean;
  entity: Contact | Company | Deal | null;
  type: 'contact' | 'company' | 'deal' | null;
  x: number;
  y: number;
}

export const useEntityPreview = (delay: number = 500) => {
  const [preview, setPreview] = useState<PreviewState>({
    visible: false,
    entity: null,
    type: null,
    x: 0,
    y: 0
  });
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);
  const isHoveringPreview = useRef<boolean>(false);

  const showPreview = useCallback((
    event: React.MouseEvent,
    entity: Contact | Company | Deal,
    type: 'contact' | 'company' | 'deal'
  ) => {
    // Clear any existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }

    // Store the element reference
    elementRef.current = event.currentTarget as HTMLElement;

    // Set timeout to show preview
    timeoutRef.current = setTimeout(() => {
      const rect = elementRef.current?.getBoundingClientRect();
      if (!rect) return;

      // Calculate position - try to show on the right, fall back to left if needed
      const previewWidth = 320; // w-80 = 20rem = 320px
      const previewHeight = 400; // Approximate height
      const padding = 8;

      let x = rect.right + padding;
      let y = rect.top;

      // If preview would go off the right edge, show on left
      if (x + previewWidth > window.innerWidth) {
        x = rect.left - previewWidth - padding;
      }

      // If preview would go off the bottom, adjust up
      if (y + previewHeight > window.innerHeight) {
        y = window.innerHeight - previewHeight - padding;
      }

      // Ensure we don't go off the top
      if (y < padding) {
        y = padding;
      }

      setPreview({
        visible: true,
        entity,
        type,
        x,
        y
      });
    }, delay);
  }, [delay]);

  const hidePreview = useCallback(() => {
    // Clear any pending show timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Clear any existing hide timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    // Set a delay before hiding to allow mouse to move to preview
    hideTimeoutRef.current = setTimeout(() => {
      // Only hide if not hovering over the preview
      if (!isHoveringPreview.current) {
        setPreview(prev => ({ ...prev, visible: false }));
      }
    }, 200); // 200ms delay to move mouse
  }, []);

  const setHoveringPreview = useCallback((hovering: boolean) => {
    isHoveringPreview.current = hovering;
    
    if (!hovering) {
      // If leaving preview, hide it after delay
      hidePreview();
    } else {
      // If entering preview, cancel any pending hide
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
        hideTimeoutRef.current = null;
      }
    }
  }, [hidePreview]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  return {
    preview,
    showPreview,
    hidePreview,
    setHoveringPreview
  };
};