import { useState, useEffect, useCallback } from "react";
import type { SavedSearch, RecentSearch } from "../types";

const SAVED_SEARCHES_KEY = "crm-saved-searches";
const RECENT_SEARCHES_KEY = "crm-recent-searches";
const MAX_RECENT_SEARCHES = 5;

export const useSearchPreferences = () => {
  // Saved searches
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);

  // Recent searches
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(SAVED_SEARCHES_KEY);
      if (saved) {
        setSavedSearches(JSON.parse(saved));
      }

      const recent = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (recent) {
        setRecentSearches(JSON.parse(recent));
      }
    } catch (error) {
      console.error("Error loading search preferences:", error);
    }
  }, []);

  // Save to localStorage whenever state changes
  useEffect(() => {
    try {
      localStorage.setItem(SAVED_SEARCHES_KEY, JSON.stringify(savedSearches));
    } catch (error) {
      console.error("Error saving saved searches:", error);
    }
  }, [savedSearches]);

  useEffect(() => {
    try {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(recentSearches));
    } catch (error) {
      console.error("Error saving recent searches:", error);
    }
  }, [recentSearches]);

  // Saved searches methods
  const addSavedSearch = useCallback(
    (name: string, filters: any, query?: string) => {
      const newSearch: SavedSearch = {
        id: `search-${Date.now()}`,
        name,
        filters,
        query,
        isPinned: false,
        createdAt: new Date().toISOString(),
      };
      setSavedSearches((prev) => [...prev, newSearch]);
    },
    [],
  );

  const updateSavedSearch = useCallback(
    (id: string, updates: Partial<SavedSearch>) => {
      setSavedSearches((prev) =>
        prev.map((search) =>
          search.id === id ? { ...search, ...updates } : search,
        ),
      );
    },
    [],
  );

  const deleteSavedSearch = useCallback((id: string) => {
    setSavedSearches((prev) => prev.filter((search) => search.id !== id));
  }, []);

  // Recent searches methods
  const addRecentSearch = useCallback((query: string, resultCount?: number) => {
    if (!query.trim()) return;

    const newRecent: RecentSearch = {
      query: query.trim(),
      timestamp: Date.now(),
      resultCount,
    };

    setRecentSearches((prev) => {
      // Remove duplicates and add new search at the beginning
      const filtered = prev.filter((s) => s.query !== query.trim());
      const updated = [newRecent, ...filtered].slice(0, MAX_RECENT_SEARCHES);
      return updated;
    });
  }, []);

  const clearRecentSearches = useCallback(() => {
    setRecentSearches([]);
  }, []);

  return {
    savedSearches,
    recentSearches,
    addSavedSearch,
    updateSavedSearch,
    deleteSavedSearch,
    addRecentSearch,
    clearRecentSearches,
  };
};
