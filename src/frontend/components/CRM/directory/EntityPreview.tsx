import React from "react";
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  BuildingOffice2Icon,
  CurrencyDollarIcon,
  CalendarIcon,
  TagIcon,
  LinkIcon,
  ChartBarIcon,
  ClockIcon } from "@heroicons/react/24/outline";
import type { Contact, Company, Deal } from "../../../types/crm-types";
import { Badge } from "../../ui";
import { Button } from "@/frontend/components/ui/Button";

interface EntityPreviewProps {
  entity: Contact | Company | Deal;
  type: "contact' |'company" |'deal';
  onAction?: (action: string) => void;
}

export const EntityPreview: React.FC<EntityPreviewProps> = ({ entity, type, onAction }) => {
  const formatDate = (date: string | undefined) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString('en-US', {
      month: "short",
      day: "numeric",
      year: "numeric"
    });
  };

  const formatCurrency = (value: number | undefined) => {
    if (!value) return "$0";
    return `$${value.toLocaleString()}`;
  };

  return (
    <div className="w-80 p-4 bg-surface-card dark:bg-surface-card rounded-lg shadow-xl border border-default dark:border-default">
      {/* Header */}
      <div className="flex items-start gap-3 mb-4">
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${
        type ==="contact' ?'bg-gradient-to-br from-purple-500 to-magenta-500' :
        type ==="company' ?'bg-gradient-to-br from-blue-500 to-cyan-500' :'bg-gradient-to-br from-green-500 to-green-500'}`
        }>
          {type ==="contact' && <UserIcon className="w-5 h-5 text-primary' />}
          {type ==="company' && <BuildingOffice2Icon className="w-5 h-5 text-primary' />}
          {type ==="deal' && <CurrencyDollarIcon className="w-5 h-5 text-primary' />}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-primary dark:text-primary truncate">
            {entity.name || "Unnamed"}
          </h3>
          <p className="text-sm text-muted dark:text-subtle">
            {type ==="contact' &&'Contact'}
            {type ==="company' &&'Company'}
            {type ==="deal' &&'Deal'}
          </p>
        </div>
      </div>

      {/* Type-specific content */}
      {type ==="contact' &&
      <div className="space-y-3">
          {(entity as Contact).email &&
        <div className="flex items-center gap-2 text-sm">
              <EnvelopeIcon className="w-4 h-4 text-subtle" />
              <Button href={`mailto:${(entity as Contact).email}`} variant="ghost" className="">
                {(entity as Contact).email}
              </a>
            </div>
        }
          {(entity as Contact).phone &&
        <div className="flex items-center gap-2 text-sm">
              <PhoneIcon className="w-4 h-4 text-subtle" />
              <a href={`tel:${(entity as Contact).phone}`} className="text-primary dark:text-subtle">
                {(entity as Contact).phone}
              </a>
            </div>
        }
          {(entity as Contact).company &&
        <div className="flex items-center gap-2 text-sm">
              <BuildingOffice2Icon className="w-4 h-4 text-subtle" />
              <span className="text-primary dark:text-subtle">
                {typeof (entity as Contact).company ==="string' ?
            (entity as Contact).company :
            (entity as Contact).company?.name}
              </span>
            </div>
        }
          {(entity as Contact).roles?.length > 0 &&
        <div className="flex items-center gap-2">
              <TagIcon className="w-4 h-4 text-subtle" />
              <div className="flex gap-1 flex-wrap">
                {(entity as Contact).roles.map((role) =>
            <Badge key={role} variant="neutral" size="sm">{role}</Badge>
            )}
              </div>
            </div>
        }
        </div>
      }

      {type ==="company' &&
      <div className="space-y-3">
          {(entity as Company).industry &&
        <div className="flex items-center gap-2 text-sm">
              <BuildingOffice2Icon className="w-4 h-4 text-subtle" />
              <span className="text-primary dark:text-subtle">{(entity as Company).industry}</span>
            </div>
        }
          {(entity as Company).website &&
        <div className="flex items-center gap-2 text-sm">
              <LinkIcon className="w-4 h-4 text-subtle" />
              <Button
            href={(entity as Company).website}
            target="_blank"
            rel="noopener noreferrer"
            variant="ghost" className="">

                {(entity as Company).website}
              </a>
            </div>
        }
          {(entity as Company).linkedStatus &&
        <div className="flex items-center gap-2">
              <LinkIcon className="w-4 h-4 text-subtle" />
              <div className="flex gap-1">
                {((entity as Company).linkedStatus ==="both' || (entity as Company).linkedStatus ==='hubspot_only') &&
            <Badge variant="secondary" size="sm">HubSpot</Badge>
            }
                {((entity as Company).linkedStatus ==="both' || (entity as Company).linkedStatus ==='harvest_only') &&
            <Badge variant="secondary" size="sm">Harvest</Badge>
            }
                {(entity as Company).linkedStatus ==="none' &&
            <Badge variant="neutral" size="sm">Unlinked</Badge>
            }
              </div>
            </div>
        }
          {((entity as Company).activeDealsCount !== undefined || (entity as Company).totalDealValue !== undefined) &&
        <div className="flex items-center gap-2 text-sm">
              <ChartBarIcon className="w-4 h-4 text-subtle" />
              <span className="text-primary dark:text-subtle">
                {(entity as Company).activeDealsCount || 0} deals • 
                {formatCurrency((entity as Company).totalDealValue)}
              </span>
            </div>
        }
        </div>
      }

      {type ==="deal' &&
      <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge
            variant={(entity as Deal).stage?.toLowerCase().includes('closed') ?'success' :'secondary'}
            size="sm">

              {(entity as Deal).stage || "No stage"}
            </Badge>
            <span className="text-lg font-semibold text-primary dark:text-primary">
              {formatCurrency((entity as Deal).value || (entity as Deal).amount)}
            </span>
          </div>
          
          {(entity as Deal).probability !== undefined &&
        <div className="flex items-center gap-2 text-sm">
              <ChartBarIcon className="w-4 h-4 text-subtle" />
              <span className="text-primary dark:text-subtle">
                {(entity as Deal).probability}% probability
              </span>
            </div>
        }
          
          {(entity as Deal).expectedCloseDate &&
        <div className="flex items-center gap-2 text-sm">
              <CalendarIcon className="w-4 h-4 text-subtle" />
              <span className="text-primary dark:text-subtle">
                Close: {formatDate((entity as Deal).expectedCloseDate)}
              </span>
            </div>
        }
          
          {(entity as Deal).company &&
        <div className="flex items-center gap-2 text-sm">
              <BuildingOffice2Icon className="w-4 h-4 text-subtle" />
              <span className="text-primary dark:text-subtle">
                {typeof (entity as Deal).company ==="string' ?
            (entity as Deal).company :
            (entity as Deal).company?.name}
              </span>
            </div>
        }
          
          {(entity as Deal).hasEstimates &&
        <Badge variant="predicted" size="sm">Has estimates</Badge>
        }
        </div>
      }

      {/* Metadata */}
      <div className="mt-4 pt-3 border-t border-default dark:border-default flex items-center gap-4 text-xs text-muted dark:text-subtle">
        <div className="flex items-center gap-1">
          <ClockIcon className="w-3 h-3" />
          Created: {formatDate(entity.created_at || entity.createdAt)}
        </div>
        {(entity.updated_at || entity.updatedAt) &&
        <div className="flex items-center gap-1">
            <ClockIcon className="w-3 h-3" />
            Updated: {formatDate(entity.updated_at || entity.updatedAt)}
          </div>
        }
      </div>

      {/* Quick Actions */}
      {onAction &&
      <div className="mt-3 pt-3 border-t border-default dark:border-default flex gap-2">
          <Button variant="primary"
        onClick={() => onAction('view')}>


            View Details
          </Button>
          <Button variant="secondary"
        onClick={() => onAction('edit')}>


            Edit
          </Button>
        </div>
      }
    </div>);

};

export default EntityPreview;