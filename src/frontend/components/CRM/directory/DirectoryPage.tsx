import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UnifiedSearch } from "./UnifiedSearch";
import ContactDetail from "../Contacts/ContactDetail";
import CompanyDetail from "../Companies/CompanyDetail";
import { useQuery } from "react-query";
import { getContactById, getCompanyById } from "../../../api/crm";

const DirectoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedEntity, setSelectedEntity] = useState<{
    type: 'contact' | 'company' | 'deal' | null;
    id: string | null;
  }>({ type: null, id: null });

  // Fetch selected contact if needed
  const { data: selectedContact } = useQuery(
    ['contact', selectedEntity.id],
    () => getContactById(selectedEntity.id!),
    { enabled: selectedEntity.type === 'contact' && !!selectedEntity.id }
  );

  // Fetch selected company if needed
  const { data: selectedCompany } = useQuery(
    ['company', selectedEntity.id],
    () => getCompanyById(selectedEntity.id!),
    { enabled: selectedEntity.type === 'company' && !!selectedEntity.id }
  );

  const handleSelectEntity = (type: string, id: string) => {
    switch (type) {
      case 'contact':
      case 'company':
        // Show modal instead of navigating
        setSelectedEntity({ type: type as 'contact' | 'company', id });
        break;
      case 'deal':
        // Still navigate for deals
        navigate(`/crm/deals/${id}`);
        break;
      default:
        console.log('Unknown entity type:', type);
    }
  };

  const handleCloseModal = () => {
    setSelectedEntity({ type: null, id: null });
  };

  return (
    <>
      <div className="h-full p-6">
        <div className="max-w-6xl mx-auto h-full">
          <UnifiedSearch onSelectEntity={handleSelectEntity} />
        </div>
      </div>

      {/* Contact Detail Modal */}
      {selectedEntity.type === 'contact' && selectedContact && (
        <ContactDetail
          contact={selectedContact}
          onClose={handleCloseModal}
        />
      )}

      {/* Company Detail Modal */}
      {selectedEntity.type === 'company' && selectedCompany && (
        <CompanyDetail
          company={selectedCompany}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
};

export default DirectoryPage;