export type SortField = "name" | "created" | "updated" | "value";
export type SortOrder = "asc" | "desc";

export interface SortConfig {
  field: SortField;
  order: SortOrder;
}

export const sortItems = <T extends any>(
  items: T[],
  sortConfig: SortConfig | null,
  type: "contacts" | "companies" | "deals",
): T[] => {
  if (!sortConfig) return items;

  return [...items].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortConfig.field) {
      case "name":
        aValue = a.name?.toLowerCase() || "";
        bValue = b.name?.toLowerCase() || "";
        break;

      case "created":
        aValue = new Date(a.created_at || a.createdAt).getTime();
        bValue = new Date(b.created_at || b.createdAt).getTime();
        break;

      case "updated":
        aValue = new Date(
          a.updated_at || a.updatedAt || a.created_at || a.createdAt,
        ).getTime();
        bValue = new Date(
          b.updated_at || b.updatedAt || b.created_at || b.createdAt,
        ).getTime();
        break;

      case "value":
        if (type === "deals") {
          aValue = a.value || a.amount || 0;
          bValue = b.value || b.amount || 0;
        } else {
          // For non-deals, use name as fallback
          aValue = a.name?.toLowerCase() || "";
          bValue = b.name?.toLowerCase() || "";
        }
        break;

      default:
        return 0;
    }

    if (aValue < bValue) return sortConfig.order === "asc" ? -1 : 1;
    if (aValue > bValue) return sortConfig.order === "asc" ? 1 : -1;
    return 0;
  });
};
