import React, { useState, useCallback } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import {
  ChartBarIcon,
  BookOpenIcon,
  LightBulbIcon,
  ArrowPathIcon,
  ArchiveBoxIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { CommandPalette } from "../shared/CommandPalette";
import { ContextualSidePanel } from "../shared/ContextualSidePanel";
import { useHotkeys } from "../../../hooks/useHotkeys";
import { Button } from "@/frontend/components/ui/Button";

export const CRMLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Command palette state
  const [isCommandOpen, setIsCommandOpen] = useState(false);

  // Side panel state
  const [sidePanel, setSidePanel] = useState<{
    isOpen: boolean;
    entityType: "deal" | "contact" | "company" | null;
    entityId: string | null;
  }>({
    isOpen: false,
    entityType: null,
    entityId: null,
  });

  // Primary navigation
  const primaryNav = [
    {
      id: "pipeline', label:'Pipeline",
      icon: ChartBarIcon,
      path: "/crm/pipeline",
    },
    {
      id: "tenders', label:'Tenders",
      icon: DocumentTextIcon,
      path: "/crm/tenders",
    },
    {
      id: "directory', label:'Directory",
      icon: BookOpenIcon,
      path: "/crm/directory",
    },
    {
      id: "intelligence', label:'Intelligence",
      icon: LightBulbIcon,
      path: "/crm/intelligence",
    },
  ];

  // Secondary navigation (integrations)
  const secondaryNav = [
    {
      id: "hubspot', label:'HubSpot Sync",
      icon: ArrowPathIcon,
      path: "/crm/hubspot",
    },
    {
      id: "data-management', label:'Data Management",
      icon: ArchiveBoxIcon,
      path: "/crm/data-management",
    },
  ];

  const allNav = [...primaryNav, ...secondaryNav];
  const currentSection =
    allNav.find((nav) => location.pathname.startsWith(nav.path))?.id ||
    "pipeline";

  // Keyboard shortcuts
  useHotkeys("cmd+k", () => setIsCommandOpen(true));
  useHotkeys("esc", () => {
    if (sidePanel.isOpen) setSidePanel({ ...sidePanel, isOpen: false });
  });

  // Open entity in side panel
  const openEntity = useCallback(
    (type: "deal" | "contact" | "company", id: string) => {
      console.log("Opening entity in side panel:", type, id);
      setSidePanel({
        isOpen: true,
        entityType: type,
        entityId: id,
      });
    },
    [],
  );

  // Provide context to child components
  const crmContext = { openEntity };

  return (
    <div className="min-h-full flex flex-col bg-surface-page dark:bg-surface-page">
      {/* CRM Header */}
      <div className="bg-surface-card dark:bg-surface-card border-b border-default dark:border-default flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-8">
              <h1 className="text-2xl font-bold text-primary dark:text-primary">
                CRM
              </h1>

              {/* Primary Navigation */}
              <nav className="flex items-center gap-4">
                <div className="flex gap-1">
                  {primaryNav.map((nav) => (
                    <Button
                      variant="secondary"
                      key={nav.id}
                      onClick={() => navigate(nav.path)}
                      className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all ${
                        currentSection === nav.id
                          ? "bg-accent-light dark:bg-accent-dark/30 text-accent-dark dark:text-purple-300"
                          : "text-secondary dark:text-subtle hover:text-primary dark:hover:text-primary hover:bg-surface-alt dark:hover:bg-surface-elevated"
                      }`}
                    >
                      <nav.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span>{nav.label}</span>
                    </Button>
                  ))}
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />

                {/* Secondary Navigation */}
                <div className="flex gap-1">
                  {secondaryNav.map((nav) => (
                    <Button
                      variant="secondary"
                      key={nav.id}
                      onClick={() => navigate(nav.path)}
                      className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all ${
                        currentSection === nav.id
                          ? "bg-accent-light dark:bg-accent-dark/30 text-accent-dark dark:text-purple-300"
                          : "text-secondary dark:text-subtle hover:text-primary dark:hover:text-primary hover:bg-surface-alt dark:hover:bg-surface-elevated"
                      }`}
                    >
                      <nav.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span>{nav.label}</span>
                    </Button>
                  ))}
                </div>
              </nav>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                onClick={() => setIsCommandOpen(true)}
              >
                <MagnifyingGlassIcon className="w-4 h-4" />
                <span>Search</span>
                <kbd className="ml-2 px-2 py-0.5 text-xs bg-surface-alt dark:bg-gray-600 rounded">
                  ⌘K
                </kbd>
              </Button>

              <Button variant="ghost" className="p-2">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                  />
                </svg>
              </Button>

              <Button variant="ghost" className="p-2">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        <div
          className={`h-full transition-all duration-300 ${
            sidePanel.isOpen ? "lg:mr-[480px]" : ""
          }`}
        >
          <Outlet context={crmContext} />
        </div>
      </div>

      {/* Command Palette */}
      <CommandPalette
        isOpen={isCommandOpen}
        onClose={() => setIsCommandOpen(false)}
      />

      {/* Contextual Side Panel */}
      <ContextualSidePanel
        isOpen={sidePanel.isOpen}
        entityType={sidePanel.entityType}
        entityId={sidePanel.entityId}
        onClose={() => setSidePanel({ ...sidePanel, isOpen: false })}
        onNavigate={(path) => {
          navigate(path);
          setSidePanel({ ...sidePanel, isOpen: false });
        }}
      />
    </div>
  );
};

export default CRMLayout;
