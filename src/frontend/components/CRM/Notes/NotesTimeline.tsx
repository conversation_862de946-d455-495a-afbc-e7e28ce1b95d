import React, { useState } from "react";
import { Note, NoteCreate } from "../../../types/crm-types";
import { useMutation, useQueryClient } from "react-query";
import { addNoteToDeal } from "../../../api/crm";
import { Button } from "@/frontend/components/ui/Button";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface NotesTimelineProps {
  dealId: string;
  notes: Note[];
}

/**
 * Component for displaying and adding notes for a deal
 */
const NotesTimeline: React.FC<NotesTimelineProps> = ({ dealId, notes }) => {
  const [newNote, setNewNote] = useState("");
  const queryClient = useQueryClient();

  // Mutation for adding a note
  const addNoteMutation = useMutation(
    (noteData: NoteCreate) => addNoteToDeal(noteData),
    {
      onSuccess: () => {
        // Only invalidate the specific deal, not all deals
        queryClient.invalidateQueries(["deal", dealId]);
        queryClient.invalidateQueries(["deal-notes", dealId]);
        setNewNote("");
      },
    },
  );

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newNote.trim()) return;

    addNoteMutation.mutate({
      dealId,
      content: newNote,
      // In a real app, you might get the current user's name here
      createdBy: "Current User",
    });
  };

  // Format date to local format with time
  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString("en-AU", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
    });
  };

  // Sort notes by date (newest first)
  const sortedNotes = [...(notes || [])].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );

  return (
    <div className="space-y-4">
      {/* Add note form */}
      <form onSubmit={handleSubmit} className="space-y-2">
        <Textarea
          value={newNote}
          onChange={(e) => setNewNote(e.target.value)}
          placeholder="Add a note..."
          className="w-full px-3 py-2 border border-strong dark:border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-surface-alt dark:text-primary"
          rows={3}
        />

        <div className="flex justify-end">
          <Button
            variant="primary"
            type="submit"
            disabled={!newNote.trim() || addNoteMutation.isLoading}
          >
            {addNoteMutation.isLoading ? "Adding..." : "Add Note"}
          </Button>
        </div>
      </form>

      {/* Notes timeline */}
      {sortedNotes.length > 0 ? (
        <div className="space-y-4">
          {sortedNotes.map((note) => (
            <div
              key={note.id}
              className="bg-surface-page dark:bg-surface-alt p-3 rounded-lg"
            >
              <div className="flex justify-between items-start">
                <p className="text-sm font-medium text-primary dark:text-primary">
                  {note.createdBy || "Unknown User"}
                </p>
                <p className="text-xs text-muted dark:text-subtle">
                  {formatDateTime(note.createdAt)}
                </p>
              </div>
              <p className="mt-1 text-sm text-primary dark:text-subtle whitespace-pre-line">
                {note.content}
              </p>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-4 text-muted dark:text-subtle">
          <p>No notes yet</p>
          <p className="text-sm">Add the first note above</p>
        </div>
      )}
    </div>
  );
};

export default NotesTimeline;
