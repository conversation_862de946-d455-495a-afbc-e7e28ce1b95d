import React from "react";
import { useQuery } from "react-query";
import { Card } from "../../ui";
import { fetchFromApi } from "../../../api/utils";
import { format, parseISO, formatDistanceToNow } from "date-fns";
import { 
  ExclamationTriangleIcon, 
  LightBulbIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  UserGroupIcon
} from "@heroicons/react/24/outline";

interface OpportunityIntelligenceProps {
  companyId: string;
}

interface Opportunity {
  id: string;
  companyId: string;
  type: 'renewal' | 'expansion' | 'engagement' | 'risk' | 'relationship';
  title: string;
  description: string;
  score: number;
  metadata: Record<string, any>;
  detectedAt: string;
}

const OpportunityIntelligence: React.FC<OpportunityIntelligenceProps> = ({ companyId }) => {
  // Fetch opportunities
  const { data: opportunities, isLoading, error } = useQuery(
    ['opportunities', companyId],
    async () => {
      const response = await fetchFromApi(`/api/crm/network/opportunities/${companyId}`);
      return response.data as Opportunity[];
    },
    {
      staleTime: 300000, // 5 minutes
      cacheTime: 600000, // 10 minutes
    }
  );

  if (isLoading) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Opportunity Intelligence
          </h3>
          <div className="mt-4 flex items-center justify-center">
            <div className="text-muted">Analyzing opportunities...</div>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Opportunity Intelligence
          </h3>
          <div className="mt-4 text-error">Failed to load opportunities</div>
        </div>
      </Card>
    );
  }

  // Get icon and color for opportunity type
  const getOpportunityIcon = (type: string) => {
    switch (type) {
      case 'renewal':
        return <ClockIcon className="h-5 w-5" />;
      case 'expansion':
        return <ArrowTrendingUpIcon className="h-5 w-5" />;
      case 'engagement':
        return <UserGroupIcon className="h-5 w-5" />;
      case 'risk':
        return <ExclamationTriangleIcon className="h-5 w-5" />;
      case 'relationship':
        return <UserGroupIcon className="h-5 w-5" />;
      default:
        return <LightBulbIcon className="h-5 w-5" />;
    }
  };

  const getOpportunityColor = (type: string) => {
    switch (type) {
      case 'renewal':
        return 'text-primary-color bg-primary-light';
      case 'expansion':
        return 'text-success bg-success-light';
      case 'engagement':
        return 'text-accent bg-accent-light';
      case 'risk':
        return 'text-error bg-error-light';
      case 'relationship':
        return 'text-primary-color bg-primary-light';
      default:
        return 'text-secondary bg-surface-alt';
    }
  };

  const getScoreBadge = (score: number) => {
    if (score >= 80) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-success-light text-success">
          High Priority
        </span>
      );
    } else if (score >= 50) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-warning-light text-warning">
          Medium Priority
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-surface-alt text-primary">
          Low Priority
        </span>
      );
    }
  };

  if (!opportunities || opportunities.length === 0) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6 border-b border-default">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Opportunity Intelligence
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-muted">
            AI-powered insights and recommendations
          </p>
        </div>
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center text-muted">
            <LightBulbIcon className="mx-auto h-12 w-12 text-subtle" />
            <p className="mt-2">No opportunities detected at this time.</p>
            <p className="text-sm mt-1">Check back later for new insights.</p>
          </div>
        </div>
      </Card>
    );
  }

  // Sort opportunities by score (highest first)
  const sortedOpportunities = [...opportunities].sort((a, b) => b.score - a.score);

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default">
        <h3 className="text-lg leading-6 font-medium text-primary">
          Opportunity Intelligence
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-muted">
          AI-powered insights and recommendations
        </p>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {sortedOpportunities.map((opportunity) => (
          <div key={opportunity.id} className="px-4 py-5 sm:p-6 hover:bg-surface-page dark:hover:bg-surface-page transition-colors">
            <div className="flex items-start gap-4">
              <div className={`flex-shrink-0 p-2 rounded-lg ${getOpportunityColor(opportunity.type)}`}>
                {getOpportunityIcon(opportunity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-base font-medium text-primary">
                    {opportunity.title}
                  </h4>
                  {getScoreBadge(opportunity.score)}
                </div>
                <p className="text-sm text-primary mb-3">
                  {opportunity.description}
                </p>
                
                {/* Metadata display */}
                {opportunity.metadata && Object.keys(opportunity.metadata).length > 0 && (
                  <div className="flex flex-wrap gap-4 text-sm text-muted">
                    {opportunity.metadata.lastActivityDays && (
                      <span>Last activity: {opportunity.metadata.lastActivityDays} days ago</span>
                    )}
                    {opportunity.metadata.projectEndDate && (
                      <span>Project ends: {format(parseISO(opportunity.metadata.projectEndDate), 'dd MMM yyyy')}</span>
                    )}
                    {opportunity.metadata.coverageGap && (
                      <span>Coverage gap: {opportunity.metadata.coverageGap}%</span>
                    )}
                    {opportunity.metadata.potentialValue && (
                      <span>Potential value: ${opportunity.metadata.potentialValue.toLocaleString()}</span>
                    )}
                  </div>
                )}
                
                <div className="mt-2 text-xs text-subtle">
                  Detected {formatDistanceToNow(parseISO(opportunity.detectedAt), { addSuffix: true })}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default OpportunityIntelligence;