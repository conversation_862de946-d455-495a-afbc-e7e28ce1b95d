import React, { useState, useMemo } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useNavigate } from "react-router-dom";
import {
  DocumentIcon,
  CalendarDaysIcon,
  BuildingOfficeIcon,
  MagnifyingGlassIcon,
  PlusIcon } from "@heroicons/react/24/outline";
import {
  useTenders,
  useTenderStats,
  useQualifyTender,
  useTendersClosingSoon } from "../../../api/tenders";
import { TenderColumn } from "./TenderColumn";
import { TenderCard } from "./TenderCard";
import { CreateTenderModal } from "./CreateTenderModal";
import { formatRelativeDate } from "../../../utils/format";
import type { BaseTender, TenderQualificationStatus } from "../../../../types/shared-types";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

const TENDER_STAGES: {
  id: TenderQualificationStatus;
  name: string;
  color: string;
  description: string;
}[] = [
{
  id: "new",
  name: "New",
  color: "bg-surface-page0",
  description: "Newly received tenders awaiting review"
},
{
  id: "reviewing",
  name: "Reviewing",
  color: "bg-primary-light0",
  description: "Tenders under active consideration"
},
{
  id: "not_interested",
  name: "Not Interested",
  color: "bg-error-light0",
  description: "Tenders we have decided not to pursue"
},
{
  id: "interested",
  name: "Interested",
  color: "bg-success-light0",
  description: "Tenders converted to opportunities"
}];


export const TenderPipeline: React.FC = () => {
  const navigate = useNavigate();

  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showClosingSoon, setShowClosingSoon] = useState(false);

  // Queries
  const { data: tenders = [], isLoading, error } = useTenders();
  const { data: stats } = useTenderStats();
  const { data: closingSoonTenders = [] } = useTendersClosingSoon(7);

  // Mutations
  const qualifyTenderMutation = useQualifyTender();

  // Filter tenders by search
  const filteredTenders = useMemo(() => {
    if (!searchTerm) return tenders;

    const search = searchTerm.toLowerCase();
    return tenders.filter((tender) =>
    tender.requestNo.toLowerCase().includes(search) ||
    tender.summary.toLowerCase().includes(search) ||
    tender.issuedBy.toLowerCase().includes(search)
    );
  }, [tenders, searchTerm]);

  // Group tenders by qualification status
  const tendersByStatus = useMemo(() => {
    const grouped: Record<TenderQualificationStatus, BaseTender[]> = {
      new: [],
      reviewing: [],
      not_interested: [],
      interested: []
    };

    filteredTenders.forEach((tender) => {
      if (grouped[tender.qualificationStatus]) {
        grouped[tender.qualificationStatus].push(tender);
      }
    });

    // Sort by closing date within each group
    Object.keys(grouped).forEach((status) => {
      grouped[status as TenderQualificationStatus].sort((a, b) =>
      new Date(a.closingDate).getTime() - new Date(b.closingDate).getTime()
      );
    });

    return grouped;
  }, [filteredTenders]);

  // Handle tender qualification
  const handleQualifyTender = async (
  tenderId: string,
  newStatus: TenderQualificationStatus,
  reason?: string) =>
  {
    try {
      await qualifyTenderMutation.mutateAsync({
        id: tenderId,
        request: {
          status: newStatus,
          reason
        }
      });

      // Show notification for interested status
      if (newStatus ==="interested') {
        alert('Tender marked as interested. Please create a deal in HubSpot to pursue this opportunity.');
      }
    } catch (error) {
      console.error('Failed to qualify tender:', error);
      alert('Failed to update tender status. Please try again.');
    }
  };

  // Handle tender click
  const handleTenderClick = (tender: BaseTender) => {
    // Could open a detail modal or navigate to detail page
    console.log('Tender clicked:', tender);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-muted">Loading tenders...</div>
      </div>);

  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-error">Error loading tenders</div>
      </div>);

  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="bg-surface-card border-b border-default">
          <div className="max-w-full md:max-w-[84rem] mx-auto px-4 md:px-6 py-4">
            <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-primary">
                Tender Qualification
              </h2>
              <p className="text-sm text-secondary mt-1">
                Process and qualify incoming tender opportunities
              </p>
            </div>
            
            <Button variant="primary"
              onClick={() => setShowCreateModal(true)}>


              <PlusIcon className="w-5 h-5" />
              Manual Entry
            </Button>
          </div>
          
          {/* Stats Bar */}
          {stats &&
            <div className="grid grid-cols-6 gap-4 mb-4">
              <div className="bg-surface-page rounded-lg p-3">
                <div className="text-2xl font-bold text-primary">
                  {stats.total}
                </div>
                <div className="text-xs text-muted">Total Tenders</div>
              </div>
              
              <div className="bg-primary-light/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-primary-color">
                  {stats.new}
                </div>
                <div className="text-xs text-muted">New</div>
              </div>
              
              <div className="bg-warning-light/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-warning">
                  {stats.reviewing}
                </div>
                <div className="text-xs text-muted">Reviewing</div>
              </div>
              
              <div className="bg-success-light/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-success">
                  {stats.interested}
                </div>
                <div className="text-xs text-muted">Interested</div>
              </div>
              
              <div className="bg-error-light/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-error">
                  {stats.notInterested}
                </div>
                <div className="text-xs text-muted">Not Interested</div>
              </div>
              
              <div className="bg-warning-light/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-warning">
                  {stats.closingSoon}
                </div>
                <div className="text-xs text-muted">Closing Soon</div>
              </div>
            </div>
            }
          
          {/* Controls */}
          <div className="flex items-center gap-4">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-subtle" />
                <Input
                    type="text"
                    placeholder="Search tenders..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full" />

              </div>
            </div>
            
            <Button variant="secondary"
              onClick={() => setShowClosingSoon(!showClosingSoon)}
              className={`px-4 py-2 rounded-lg transition-colors ${
              showClosingSoon ? "bg-warning-light/30 text-warning-dark" :'bg-surface-alt text-primary hover:bg-surface-alt dark:hover:bg-surface-alt'}`
              }>

              <CalendarDaysIcon className="w-5 h-5 inline mr-2" />
              Closing Soon
            </Button>
          </div>
          </div>
        </div>
        
        {/* Closing Soon Alert */}
        {showClosingSoon && closingSoonTenders.length > 0 &&
        <div className="bg-warning-light/20 border-b border-warning">
            <div className="max-w-full md:max-w-[84rem] mx-auto px-4 md:px-6 py-3">
            <div className="flex items-center gap-2 text-warning-dark">
              <CalendarDaysIcon className="w-5 h-5" />
              <span className="font-medium">
                {closingSoonTenders.length} tender{closingSoonTenders.length !== 1 ? "s" :''} closing within 7 days
              </span>
            </div>
            <div className="mt-2 space-y-1">
              {closingSoonTenders.slice(0, 3).map((tender) =>
              <div key={tender.id} className="text-sm text-warning">
                  {tender.requestNo} - {tender.summary} (closes {formatRelativeDate(tender.closingDate)})
                </div>
              )}
              {closingSoonTenders.length > 3 &&
              <div className="text-sm text-warning">
                  and {closingSoonTenders.length - 3} more...
                </div>
              }
            </div>
            </div>
          </div>
        }
        
        {/* Pipeline Board */}
        <div className="flex-1 overflow-x-auto -webkit-overflow-scrolling-touch">
          <div className="max-w-full md:max-w-[84rem] mx-auto px-4 md:px-6">
            <div className="flex gap-4 py-6 h-full min-w-max">
            {TENDER_STAGES.map((stage) =>
              <TenderColumn
                key={stage.id}
                stage={stage}
                tenders={tendersByStatus[stage.id]}
                onMoveTender={handleQualifyTender}
                onTenderClick={handleTenderClick} />

              )}
            </div>
          </div>
        </div>
        
        {/* Create Tender Modal */}
        {showCreateModal &&
        <CreateTenderModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)} />

        }
      </div>
    </DndProvider>);

};

export default TenderPipeline;