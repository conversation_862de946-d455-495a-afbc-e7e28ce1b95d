import React, { useState } from "react";
import { useQueryClient } from "react-query";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useCreateTender } from "../../../api/tenders";
import type { BaseTender } from "../../../../types/shared-types";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface CreateTenderModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreateTenderModal: React.FC<CreateTenderModalProps> = ({ isOpen, onClose }) => {
  const queryClient = useQueryClient();
  const createTenderMutation = useCreateTender();

  const [formData, setFormData] = useState({
    requestNo: "",
    summary: "",
    issuedBy: "",
    closingDate: "",
    unspsc: "",
    tenderUrl: "",
    notes: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.requestNo) {
      newErrors.requestNo = 'Request number is required';
    }
    if (!formData.summary) {
      newErrors.summary = 'Summary is required';
    }
    if (!formData.issuedBy) {
      newErrors.issuedBy = 'Issuer is required';
    }
    if (!formData.closingDate) {
      newErrors.closingDate = 'Closing date is required';
    } else if (new Date(formData.closingDate) < new Date()) {
      newErrors.closingDate = 'Closing date must be in the future';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      const tenderData: Partial<BaseTender> = {
        requestNo: formData.requestNo,
        summary: formData.summary,
        issuedBy: formData.issuedBy,
        closingDate: new Date(formData.closingDate).toISOString(),
        unspsc: formData.unspsc || undefined,
        tenderUrl: formData.tenderUrl || undefined,
        notes: formData.notes || undefined,
        status: "Current",
        type: "Tender",
        qualificationStatus: "new"
      };

      await createTenderMutation.mutateAsync(tenderData);

      // Success - close modal
      onClose();

      // Reset form
      setFormData({
        requestNo: "",
        summary: "",
        issuedBy: "",
        closingDate: "",
        unspsc: "",
        tenderUrl: "",
        notes: ""
      });
    } catch (error) {
      console.error('Failed to create tender:', error);
      setErrors({ submit: "Failed to create tender. Please try again." });
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-surface-card rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-default">
          <h2 className="text-xl font-semibold text-primary">
            Create Tender
          </h2>
          <Button variant="ghost"
          onClick={onClose}
          className="p-2">

            <XMarkIcon className="w-5 h-5 text-muted" />
          </Button>
        </div>
        
        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Request Number */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Request Number *
            </label>
            <Input
              type="text"
              value={formData.requestNo}
              onChange={(e) => setFormData({ ...formData, requestNo: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg bg-surface-card text-primary ${
              errors.requestNo ? "border-error" : "border-strong"} focus:outline-none focus:ring-2 focus:ring-accent`
              }
              placeholder="e.g., GEN250120" />

            {errors.requestNo &&
            <p className="mt-1 text-xs text-error">{errors.requestNo}</p>
            }
          </div>
          
          {/* Summary */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Summary *
            </label>
            <Textarea
              value={formData.summary}
              onChange={(e) => setFormData({ ...formData, summary: e.target.value })}
              rows={2}
              className={`w-full px-3 py-2 border rounded-lg bg-surface-card text-primary ${
              errors.summary ? "border-error" : "border-strong"} focus:outline-none focus:ring-2 focus:ring-accent`
              }
              placeholder="Brief description of the tender" />

            {errors.summary &&
            <p className="mt-1 text-xs text-error">{errors.summary}</p>
            }
          </div>
          
          {/* Issued By */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Issued By *
            </label>
            <Input
              type="text"
              value={formData.issuedBy}
              onChange={(e) => setFormData({ ...formData, issuedBy: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg bg-surface-card text-primary ${
              errors.issuedBy ? "border-error" : "border-strong"} focus:outline-none focus:ring-2 focus:ring-accent`
              }
              placeholder="e.g., Edith Cowan University" />

            {errors.issuedBy &&
            <p className="mt-1 text-xs text-error">{errors.issuedBy}</p>
            }
          </div>
          
          {/* Closing Date */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Closing Date *
            </label>
            <Input
              type="datetime-local"
              value={formData.closingDate}
              onChange={(e) => setFormData({ ...formData, closingDate: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg bg-surface-card text-primary ${
              errors.closingDate ? "border-error" : "border-strong"} focus:outline-none focus:ring-2 focus:ring-accent`
              } />

            {errors.closingDate &&
            <p className="mt-1 text-xs text-error">{errors.closingDate}</p>
            }
          </div>
          
          {/* UNSPSC */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              UNSPSC Code
            </label>
            <Input
              type="text"
              value={formData.unspsc}
              onChange={(e) => setFormData({ ...formData, unspsc: e.target.value })}
              className="w-full"
              placeholder="e.g., Management advisory services - (100%)" />

          </div>
          
          {/* Tender URL */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Tender URL
            </label>
            <Input
              type="url"
              value={formData.tenderUrl}
              onChange={(e) => setFormData({ ...formData, tenderUrl: e.target.value })}
              className="w-full"
              placeholder="https://..." />

          </div>
          
          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Notes
            </label>
            <Textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              className="w-full"
              placeholder="Any additional notes..." />

          </div>
          
          {/* Error Message */}
          {errors.submit &&
          <div className="p-3 bg-error-light/20 border border-error rounded-lg">
              <p className="text-sm text-error">{errors.submit}</p>
            </div>
          }
          
          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="secondary"
            type="button"
            onClick={onClose}>


              Cancel
            </Button>
            <Button variant="primary"
            type="submit"
            disabled={createTenderMutation.isLoading}>


              {createTenderMutation.isLoading ? "Creating..." : "Create Tender"}
            </Button>
          </div>
        </form>
      </div>
    </div>);

};

export default CreateTenderModal;