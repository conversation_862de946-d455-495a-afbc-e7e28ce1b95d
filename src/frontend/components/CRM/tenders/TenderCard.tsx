import React from "react";
import { useDrag } from "react-dnd";
import { 
  DocumentTextIcon, 
  CalendarIcon, 
  BuildingOfficeIcon,
  ExclamationTriangleIcon,
  LinkIcon
} from "@heroicons/react/24/outline";
import { formatRelativeDate } from "../../../utils/format";
import type { BaseTender } from "../../../../types/shared-types";

interface TenderCardProps {
  tender: BaseTender;
  onClick: () => void;
}

export const TenderCard: React.FC<TenderCardProps> = ({ tender, onClick }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'tender',
    item: { 
      id: tender.id,
      currentStatus: tender.qualificationStatus
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    })
  });

  // Calculate days until closing
  const daysUntilClosing = Math.ceil(
    (new Date(tender.closingDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
  );
  
  const isOverdue = daysUntilClosing < 0;
  const isUrgent = daysUntilClosing <= 3 && daysUntilClosing >= 0;
  
  // Determine urgency styling
  const urgencyClass = isOverdue 
    ? 'border-error bg-error-light/20'
    : isUrgent 
    ? 'border-warning bg-warning-light/20'
    : '';

  return (
    <div
      ref={drag}
      onClick={onClick}
      className={`bg-surface-card rounded-lg p-3 shadow-sm border cursor-pointer hover:shadow-lg transition-all hover:scale-[1.02] ${
        urgencyClass || 'border-default'
      } ${isDragging ? 'opacity-50' : ''}`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <DocumentTextIcon className="w-4 h-4 text-subtle flex-shrink-0" />
          <div className="min-w-0">
            <h4 className="text-sm font-semibold text-primary truncate">
              {tender.requestNo}
            </h4>
            <p className="text-xs text-muted">
              {tender.type}
            </p>
          </div>
        </div>
        
        {/* Urgency Indicator */}
        {(isOverdue || isUrgent) && (
          <ExclamationTriangleIcon 
            className={`w-4 h-4 flex-shrink-0 ${
              isOverdue ? 'text-error' : 'text-warning'
            }`}
            title={isOverdue ? 'Overdue' : `Closing in ${daysUntilClosing} days`}
          />
        )}
      </div>

      {/* Summary */}
      <p className="text-sm text-primary mb-2 line-clamp-2">
        {tender.summary}
      </p>

      {/* Metadata */}
      <div className="space-y-1.5">
        {/* Issuer */}
        <div className="flex items-center gap-2 text-xs">
          <BuildingOfficeIcon className="w-3.5 h-3.5 text-subtle" />
          <span className="text-secondary truncate">
            {tender.issuedBy}
          </span>
          {tender.company && (
            <span className="text-success" title="Linked to company">
              ✓
            </span>
          )}
        </div>

        {/* Closing Date */}
        <div className="flex items-center gap-2 text-xs">
          <CalendarIcon className="w-3.5 h-3.5 text-subtle" />
          <span className={`${
            isOverdue ? 'text-error font-medium' : 
            isUrgent ? 'text-warning font-medium' : 
            'text-secondary'
          }`}>
            {isOverdue ? 'Closed' : 'Closes'} {formatRelativeDate(tender.closingDate)}
          </span>
        </div>

        {/* Additional Info */}
        {tender.tenderUrl && (
          <div className="flex items-center gap-2 text-xs">
            <LinkIcon className="w-3.5 h-3.5 text-subtle" />
            <span className="text-primary-color">
              Has tender link
            </span>
          </div>
        )}
      </div>

      {/* Status Indicators */}
      {tender.qualificationReason && (
        <div className="mt-3">
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-surface-alt text-secondary" title={tender.qualificationReason}>
            Has reason
          </span>
        </div>
      )}
    </div>
  );
};

export default TenderCard;