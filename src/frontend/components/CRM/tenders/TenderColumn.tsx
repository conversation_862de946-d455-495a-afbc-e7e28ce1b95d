import React from "react";
import { useDrop } from "react-dnd";
import { TenderCard } from "./TenderCard";
import type { BaseTender, TenderQualificationStatus } from "../../../../types/shared-types";

interface TenderColumnProps {
  stage: {
    id: TenderQualificationStatus;
    name: string;
    color: string;
    description: string;
  };
  tenders: BaseTender[];
  onMoveTender: (tenderId: string, newStatus: TenderQualificationStatus, reason?: string) => void;
  onTenderClick: (tender: BaseTender) => void;
}

export const TenderColumn: React.FC<TenderColumnProps> = ({
  stage,
  tenders,
  onMoveTender,
  onTenderClick
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: "tender",
    drop: (item: { id: string; currentStatus: TenderQualificationStatus }) => {
      // Don't allow dropping on the same column
      if (item.currentStatus !== stage.id) {
        // Could show a modal here to get qualification reason
        onMoveTender(item.id, stage.id);
      }
    },
    canDrop: (item: { id: string; currentStatus: TenderQualificationStatus }) => {
      // Don't allow dropping on the same column
      return item.currentStatus !== stage.id;
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop()
    })
  });

  // Calculate days until closing for urgent tenders
  const urgentCount = tenders.filter(tender => {
    const daysUntilClosing = Math.ceil(
      (new Date(tender.closingDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
    );
    return daysUntilClosing <= 3 && daysUntilClosing >= 0;
  }).length;

  return (
    <div
      ref={drop}
      className={`flex-shrink-0 w-80 bg-surface-alt dark:bg-surface-card rounded-lg overflow-hidden transition-all duration-200 ${
        isOver && canDrop ? 'ring-2 ring-accent bg-accent-light dark:bg-accent-dark/20" : ""
      }`}
    >
      {/* Column Header */}
      <div className="p-3 border-b border-default dark:border-default">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${stage.color}`} />
            <h3 className="font-medium text-primary dark:text-primary">
              {stage.name}
            </h3>
            <span className="text-sm text-muted dark:text-subtle">
              {tenders.length}
            </span>
          </div>
          
          {urgentCount > 0 && (
            <span className="px-2 py-0.5 text-xs font-medium bg-warning-light dark:bg-warning-dark/30 text-warning-dark dark:text-orange-300 rounded-full">
              {urgentCount} urgent
            </span>
          )}
        </div>
        
        {/* Stage Description */}
        <p className="text-xs text-muted dark:text-subtle">
          {stage.description}
        </p>
      </div>

      {/* Tenders Container */}
      <div className="p-2 space-y-2 overflow-y-auto overflow-x-hidden" style={{ maxHeight: "calc(100vh - 400px)" }}>
        {tenders.length === 0 ? (
          <div className="text-center py-8 text-subtle dark:text-muted">
            <div className="text-sm italic">
              {isOver && canDrop ? 'Drop tender here' : "No tenders"}
            </div>
          </div>
        ) : (
          tenders.map(tender => (
            <TenderCard
              key={tender.id}
              tender={tender}
              onClick={() => onTenderClick(tender)}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default TenderColumn;