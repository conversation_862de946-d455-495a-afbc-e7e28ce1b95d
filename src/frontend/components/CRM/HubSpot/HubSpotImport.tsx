import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import {
  checkHubSpotStatus,
  importAllFromHubSpot,
  getHubSpotImportHistory } from "../../../api/hubspot";
import { HubSpotImport as HubSpotImportType } from "../../../types/crm-types";
import ImportProgressTracker from "./ImportProgressTracker";
import ImportSummary from "./ImportSummary";

/**
 * Component for importing data from HubSpot
 */import { Button } from "@/frontend/components/ui/Button";
const HubSpotImport: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"import" |"history">("import");
  const [showProgress, setShowProgress] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const queryClient = useQueryClient();

  // Fetch HubSpot status
  const { data: hubspotStatus, isLoading: isStatusLoading } = useQuery("hubspotStatus",
    checkHubSpotStatus
  );

  // Fetch import history
  const { data: importHistory = [], isLoading: isHistoryLoading } = useQuery("hubspotImportHistory",
    getHubSpotImportHistory,
    {
      enabled: activeTab ==="history"
    }
  );

  // Mutation for importing all data with retry disabled
  const importAllMutation = useMutation(() => importAllFromHubSpot(), {
    retry: false, // Disable automatic retries
    onMutate: () => {
      setShowProgress(true);
      setImportResults(null);
    },
    onSuccess: (data) => {
      setImportResults(data);
      setShowProgress(false);
      // Clear existing data and let it refetch naturally
      queryClient.removeQueries("deals");
      queryClient.removeQueries("contacts");
      queryClient.removeQueries("companies");
      queryClient.invalidateQueries("hubspotImportHistory");
    },
    onError: () => {
      setShowProgress(false);
    }
  });

  // Handle importing all data with double-click protection
  const handleImportAll = () => {
    // Prevent double-clicks
    if (importAllMutation.isLoading) {
      console.log('Import already in progress, ignoring click');
      return;
    }
    importAllMutation.mutate();
  };


  // Format date for display
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString("en-AU", {
      day:"numeric",
      month:"short",
      year:"numeric",
      hour:"numeric",
      minute:"2-digit"
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: HubSpotImportType["status"]): string => {
    switch (status) {
      case"completed":
        return"bg-success-light text-success";
      case"completed_with_warnings":
        return"bg-warning-light text-warning";
      case"failed":
        return"bg-error-light text-error";
      case"pending":
        return"bg-primary-light text-primary-color";
      default:
        return"bg-surface-alt text-primary";
    }
  };

  // Get status display text
  const getStatusDisplayText = (status: HubSpotImportType["status"]): string => {
    switch (status) {
      case"completed":
        return"Completed";
      case"completed_with_warnings":
        return"Completed with exceptions";
      case"failed":
        return"Failed";
      case"pending":
        return"Pending";
      default:
        return status;
    }
  };

  return (
    <div className="bg-surface-card shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold text-primary mb-4">
        HubSpot Data Import
      </h2>

      {/* Tabs */}
      <div className="border-b border-default mb-4">
        <nav className="-mb-px flex space-x-8">
          <Button variant="secondary"
          className={`${
          activeTab ==="import" ?"--tab-active" :"--tab"}`
          }
          onClick={() => setActiveTab("import")}>

            Import Data
          </Button>
          <Button variant="secondary"
          className={`${
          activeTab ==="history" ?"--tab-active" :"--tab"}`
          }
          onClick={() => setActiveTab("history")}>

            Import History
          </Button>
        </nav>
      </div>

      {isStatusLoading ?
      <div className="flex justify-center items-center h-24">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div> :
      !hubspotStatus?.isConfigured ?
      <div className="bg-warning-light border border-warning rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
              className="h-5 w-5 text-warning"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor">

                <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd" />

              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-warning">
                HubSpot integration not configured
              </h3>
              <p className="mt-2 text-sm text-warning">
                Please configure the HubSpot integration in the settings tab
                before importing data.
              </p>
            </div>
          </div>
        </div> :
      activeTab ==="import" ?
      <div className="space-y-6">
          <p className="text-sm text-muted">
            Import data from your HubSpot account. This will fetch the latest
            data and merge it with your existing CRM data.
          </p>

          <div className="max-w-2xl mx-auto">
            {/* Show ImportProgressTracker when importing */}
            {showProgress &&
          <div className="mb-6">
                <ImportProgressTracker
              isImporting={showProgress}
              onComplete={(results) => {
                setImportResults(results);
                setShowProgress(false);
              }} />

              </div>
          }

            {/* Show ImportSummary when results are available */}
            {importResults && !showProgress &&
          <div className="mb-6">
                <ImportSummary
              results={importResults.results}
              totalCount={importResults.totalCount}
              onClose={() => setImportResults(null)} />

              </div>
          }

            {/* Only show import button when not importing and no results */}
            {!showProgress && !importResults &&
          <>
                {/* Import All Data */}
                <div className="bg-surface-card border border-default rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-medium text-primary mb-3">
                    Import All HubSpot Data
                  </h3>
                  <p className="text-sm text-muted mb-6">
                    Import all data from HubSpot in the correct order: companies
                    first, then deals (with proper company associations), then
                    contacts. Existing records will be updated automatically.
                  </p>
                  <div className="space-y-3 mb-4">
                    <div className="bg-primary-light/20 border border-primary rounded-md p-3">
                      <p className="text-xs text-primary-color">
                        <strong>Note:</strong> Deals without company associations will be skipped. 
                        Make sure all deals are linked to companies in HubSpot before importing.
                      </p>
                    </div>
                    <div className="bg-warning-light/20 border border-warning rounded-md p-3">
                      <p className="text-xs text-warning">
                        <strong>Important:</strong> Large imports may take 2-5 minutes to complete. 
                        Please wait for the import to finish - do not refresh the page.
                      </p>
                    </div>
                  </div>
                  <Button variant="primary"
              type="button"
              onClick={handleImportAll}
              className="w-full"
              disabled={importAllMutation.isLoading}>

                    Import All Data
                  </Button>
                </div>
              </>
          }

            {/* Button to start new import when results are showing */}
            {importResults && !showProgress &&
          <div className="mt-4 text-center">
                <Button variant="ghost"
            type="button"
            onClick={() => setImportResults(null)}>


                  Start New Import
                </Button>
              </div>
          }
          </div>
        </div> :

      <div>
          {isHistoryLoading ?
        <div className="flex justify-center items-center h-24">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div> :
        importHistory.length === 0 ?
        <div className="text-center py-8 text-muted">
              <p>No import history found.</p>
              <p className="text-sm">
                Import data from HubSpot to see the history here.
              </p>
            </div> :

        <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-default dark:divide-strong">
                <thead className="bg-surface-page">
                  <tr>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Date
                    </th>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Status
                    </th>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Deals
                    </th>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Contacts
                    </th>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Companies
                    </th>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Notes
                    </th>
                    <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                      Associations
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-surface-card divide-y divide-default dark:divide-strong">
                  {importHistory.map((importItem) =>
              <tr key={importItem.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted">
                        {formatDate(importItem.importDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
                      importItem.status
                    )}`}>

                          {getStatusDisplayText(importItem.status)}
                        </span>
                        {importItem.errorMessage &&
                  <span className={`block mt-1 text-xs ${
                  importItem.status === 'completed_with_warnings' ?'text-warning' : "text-error"}`
                  }>
                            {importItem.errorMessage}
                          </span>
                  }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted">
                        {importItem.dealsCount ||"-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted">
                        {importItem.contactsCount ||"-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted">
                        {importItem.companiesCount ||"-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted">
                        {importItem.notesCount ||"-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted">
                        {importItem.associationsCount ||"-"}
                      </td>
                    </tr>
              )}
                </tbody>
              </table>
            </div>
        }
        </div>
      }
    </div>);

};

export default HubSpotImport;