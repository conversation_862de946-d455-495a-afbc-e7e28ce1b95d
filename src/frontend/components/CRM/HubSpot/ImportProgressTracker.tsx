import React, { useState, useEffect } from "react";
import { io, Socket } from "socket.io-client";

interface ImportProgress {
  step: 'companies' | 'deals' | 'contacts' | 'notes' | 'associations';
  current: number;
  total: number;
  currentItem?: string;
  errors: Array<{ item: string; error: string }>;
}

interface ImportResult {
  success: boolean;
  count: number;
  errors: Array<{ item: string; error: string }>;
  updates: Array<{ item: string; changes: string[] }>;
  created: Array<{ item: string }>;
  error?: string;
}

interface ImportProgressTrackerProps {
  isImporting: boolean;
  onComplete?: (results: {
    totalCount: number;
    results: {
      companies: ImportResult;
      deals: ImportResult;
      contacts: ImportResult;
      notes?: ImportResult;
      associations?: ImportResult;
    };
  }) => void;
}

const ImportProgressTracker: React.FC<ImportProgressTrackerProps> = ({
  isImporting,
  onComplete
}) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [progress, setProgress] = useState<ImportProgress | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [connectionError, setConnectionError] = useState(false);

  useEffect(() => {
    if (!isImporting) {
      // Clean up socket if import is not running
      if (socket) {
        socket.disconnect();
        setSocket(null);
      }
      setIsComplete(true);
      return;
    }

    // Reset states when starting new import
    setIsComplete(false);
    setConnectionError(false);
    setProgress(null);

    // Create socket connection with error handling
    // In development, we need to connect to the backend port
    const socketUrl = window.location.hostname === 'localhost' 
      ? 'http://localhost:3002' 
      : window.location.origin;
    
    console.log('Connecting to Socket.IO at:', socketUrl);
    
    const newSocket = io(socketUrl, {
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000,
      timeout: 5000,
      transports: ['polling', 'websocket'], // Start with polling for better compatibility
    });

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('Connected to import progress updates');
      setConnectionError(false);
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setConnectionError(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from import progress updates');
    });

    // Progress update handler
    newSocket.on('hubspot-import-progress', (progressData: ImportProgress) => {
      console.log('Received progress update:', progressData);
      setProgress(progressData);
    });

    setSocket(newSocket);

    // Cleanup
    return () => {
      newSocket.disconnect();
    };
  }, [isImporting]);

  if (!isImporting && !isComplete) {
    return null;
  }

  const getStepName = (step: string) => {
    switch (step) {
      case 'companies': return 'Companies';
      case 'deals': return 'Deals';
      case 'contacts': return 'Contacts';
      case 'notes': return 'Notes/Activities';
      case 'associations': return 'Associations';
      default: return step;
    }
  };

  const getProgressPercentage = () => {
    if (!progress || progress.total === 0) return 0;
    return Math.round((progress.current / progress.total) * 100);
  };

  const getOverallProgress = () => {
    if (!progress) return 0;
    
    const stepWeights = { 
      companies: 0.2, 
      deals: 0.3, 
      contacts: 0.2, 
      notes: 0.15, 
      associations: 0.15 
    };
    const stepProgress = progress.total > 0 ? (progress.current / progress.total) : 0;
    
    let overallProgress = 0;
    const steps = ['companies', 'deals', 'contacts', 'notes', 'associations'];
    const currentStepIndex = steps.indexOf(progress.step);
    
    // Add completed steps
    for (let i = 0; i < currentStepIndex; i++) {
      overallProgress += stepWeights[steps[i] as keyof typeof stepWeights];
    }
    
    // Add current step progress
    if (currentStepIndex >= 0) {
      overallProgress += stepProgress * stepWeights[progress.step as keyof typeof stepWeights];
    }
    
    return Math.round(overallProgress * 100);
  };

  return (
    <div className="bg-surface-card border border-default rounded-lg p-6 shadow-sm">
      <div className="space-y-4">
        {/* Connection Error Alert */}
        {connectionError && (
          <div className="mb-4 p-3 bg-warning-light/20 border border-warning rounded-md">
            <div className="flex items-center">
              <svg className="h-4 w-4 text-warning mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-xs text-warning">
                Unable to connect for real-time updates. Import is still running in the background.
              </p>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-primary">
            {isComplete ? 'Import Complete' : 'Importing HubSpot Data'}
          </h3>
          {!isComplete && (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span className="text-sm text-muted">
                {connectionError ? 'Processing...' : `${getOverallProgress()}% complete`}
              </span>
            </div>
          )}
        </div>

        {/* Overall Progress Bar */}
        <div className="w-full bg-surface-alt rounded-full h-2">
          <div
            className="bg-primary-color h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${getOverallProgress()}%` }}
          ></div>
        </div>

        {/* Current Step - show even if no real-time updates */}
        {!isComplete && (
          <div className="space-y-3">
            {progress ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-primary">
                    Processing {getStepName(progress.step)}
                  </span>
                  <span className="text-sm text-muted">
                    {progress.current} of {progress.total}
                  </span>
                </div>
                
                {/* Step Progress Bar */}
                <div className="w-full bg-surface-alt rounded-full h-1.5">
                  <div
                    className="bg-success h-1.5 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>

                {/* Current Item */}
                {progress.currentItem && (
                  <p className="text-xs text-muted truncate">
                    {progress.currentItem}
                  </p>
                )}
              </>
            ) : (
              <div className="text-center py-2">
                <p className="text-sm text-muted">
                  {connectionError 
                    ? 'Import is processing. This may take up to a minute...'
                    : 'Initializing import...'}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Step Indicators */}
        <div className="flex items-center justify-between flex-wrap gap-2">
          {['companies', 'deals', 'contacts', 'notes', 'associations'].map((step, index) => {
            const steps = ['companies', 'deals', 'contacts', 'notes', 'associations'];
            const isActive = progress?.step === step;
            const isCompleted = progress && 
              (steps.indexOf(progress.step) > index ||
               (progress.step === step && progress.current === progress.total));
            
            return (
              <div key={step} className="flex items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium
                  ${isCompleted 
                    ? 'bg-success-light text-success' 
                    : isActive 
                      ? 'bg-primary-light text-primary-color'
                      : 'bg-surface-alt text-muted'
                  }
                `}>
                  {isCompleted ? '✓' : index + 1}
                </div>
                <span className={`
                  ml-2 text-xs sm:text-sm
                  ${isActive 
                    ? 'text-primary-color font-medium' 
                    : 'text-muted'
                  }
                `}>
                  {getStepName(step)}
                </span>
                {index < 4 && (
                  <div className={`
                    mx-2 sm:mx-4 h-0.5 w-4 sm:w-8
                    ${isCompleted 
                      ? 'bg-success-light' 
                      : 'bg-surface-alt'
                    }
                  `}></div>
                )}
              </div>
            );
          })}
        </div>

        {/* Errors */}
        {progress && progress.errors.length > 0 && (
          <div className="mt-4 p-3 bg-warning-light/20 border border-warning rounded-md">
            <h4 className="text-sm font-medium text-warning mb-2">
              Issues encountered ({progress.errors.length})
            </h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {progress.errors.slice(0, 5).map((error, index) => (
                <p key={index} className="text-xs text-warning">
                  • {error.item}: {error.error}
                </p>
              ))}
              {progress.errors.length > 5 && (
                <p className="text-xs text-warning italic">
                  ... and {progress.errors.length - 5} more
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportProgressTracker;
