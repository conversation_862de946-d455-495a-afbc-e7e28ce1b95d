import React, { useState } from "react";
import { Company, CompanyUpdate, CompanyRelationship, CompanyRelationshipType } from "../../../types/crm-types";
import { updateCompany, deleteCompany } from "../../../api/crm";
import { enrichCompany } from "../../../api/enrichment";
import { useMutation, useQueryClient } from "react-query";
import { TeamCoverageMatrix } from "../TeamCoverage";
import { NetworkVisualization } from "../NetworkVisualization";
import { ProjectHistory } from "../ProjectHistory";
import { OpportunityIntelligence } from "../OpportunityIntelligence";
import { useNavigate } from "react-router-dom";
import ContactCompanyLinker from "../Relationships/ContactCompanyLinker";
import { Badge } from "../../ui";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface CompanyDetailProps {
  company: Company;
  onClose: () => void;
}

/**
 * Component for displaying detailed information about a company
 */
const CompanyDetail: React.FC<CompanyDetailProps> = ({ company, onClose }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showContactLinker, setShowContactLinker] = useState(false);
  const [formData, setFormData] = useState<CompanyUpdate>({
    name: company.name,
    industry: company.industry,
    size: company.size,
    website: company.website,
    address: company.address,
    description: company.description
  });

  // State for managing company relationships
  const [parentCompanies, setParentCompanies] = useState<CompanyRelationship[]>(
    company.parentCompanies || []
  );

  const [childCompanies, setChildCompanies] = useState<CompanyRelationship[]>(
    company.childCompanies || []
  );
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Mutation for updating a company
  const updateCompanyMutation = useMutation(
    (data: CompanyUpdate) => updateCompany(company.id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('companies');
        setIsEditing(false);
      }
    }
  );

  // Mutation for deleting a company
  const deleteCompanyMutation = useMutation(
    () => deleteCompany(company.id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('companies');
        onClose();
      }
    }
  );

  // Mutation for enriching company data
  const enrichCompanyMutation = useMutation(
    () => {
      console.log('Starting enrichment for company:', company.id, company.name);
      return enrichCompany(company.id);
    },
    {
      onSuccess: (data) => {
        console.log('Enrichment successful:', data);
        queryClient.invalidateQueries(['company', company.id]);
        queryClient.invalidateQueries('companies');
      },
      onError: (error) => {
        console.error('Enrichment failed:', error);
      }
    }
  );

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value === '' ? undefined : value
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateCompanyMutation.mutate(formData);
  };

  // Handle company deletion with confirmation
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
      deleteCompanyMutation.mutate();
    }
  };

  // Get company logo placeholder based on company name
  const getCompanyInitial = (name: string): string => {
    return name.charAt(0).toUpperCase();
  };

  // Get random color based on company ID for logo background
  const getLogoColor = (id: string | null | undefined): string => {
    const colors = [
    "bg-primary-light0",
    "bg-success-light0",
    "bg-accent-light0",
    "bg-warning-light0",
    "bg-magenta-500",
    "bg-primary-light0",
    "bg-error-light0",
    "bg-info-light0"];


    // If no ID, return a default color
    if (!id) {
      return colors[0];
    }

    // Simple hash function to get consistent color for the same ID
    const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  // Format date to local format
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Format relationship type for display
  const formatRelationshipType = (type: CompanyRelationshipType): string => {
    switch (type) {
      case "parent":
        return "Parent";
      case "subsidiary":
        return "Subsidiary";
      case "partner":
        return "Partner";
      case "acquisition":
        return "Acquisition";
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4"
      onClick={(e) => {
        // Only close if clicking the backdrop, not the modal content
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}>

      <div
        className="bg-surface-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}>

        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">
            {isEditing ? 'Edit Company' : 'Company Details'}
          </h2>
          <Button variant="ghost"
          onClick={onClose}>


            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </Button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isEditing ?
          // Edit form
          <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Company Name
                </label>
                <Input
                type="text"
                name="name"
                value={formData.name || ""}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                required />

              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Industry
                  </label>
                  <Input
                  type="text"
                  name="industry"
                  value={formData.industry || ""}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" />

                </div>
                
                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Size
                  </label>
                  <Select
                  name="size"
                  value={formData.size || ""}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">

                    <option value="">Select size</option>
                    <option value="1-10">1-10 employees</option>
                    <option value="11-50">11-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-500">201-500 employees</option>
                    <option value="501-1000">501-1000 employees</option>
                    <option value="1001+">1001+ employees</option>
                  </Select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Website
                </label>
                <Input
                type="url"
                name="website"
                value={formData.website || ""}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                placeholder="https://example.com" />

              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Address
                </label>
                <Input
                type="text"
                name="address"
                value={formData.address || ""}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" />

              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Description
                </label>
                <Textarea
                name="description"
                value={formData.description || ""}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" />

              </div>
              
              {/* Company Relationships Section - Will be implemented when backend supports it */}
              {parentCompanies.length > 0 &&
            <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Parent/Related Companies
                  </label>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {parentCompanies.map((parentRel, index) =>
                <div key={`parent-edit-${parentRel.company.id || index}`} className="p-3 border border-default rounded-md">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{parentRel.company.name}</span>
                        </div>
                        <div>
                          <label className="block text-sm text-primary mb-1">Relationship Type</label>
                          <Select
                      disabled
                      value={parentRel.relationshipType}
                      className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">

                            <option value="parent">Parent</option>
                            <option value="subsidiary">Subsidiary</option>
                            <option value="partner">Partner</option>
                            <option value="acquisition">Acquisition</option>
                          </Select>
                        </div>
                      </div>
                )}
                  </div>
                  <p className="mt-1 text-xs text-muted">
                    Note: Editing company relationships will be enabled in a future update.
                  </p>
                </div>
            }
              
              {childCompanies.length > 0 &&
            <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Subsidiaries & Related Companies
                  </label>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {childCompanies.map((childRel, index) =>
                <div key={`child-edit-${childRel.company.id || index}`} className="p-3 border border-default rounded-md">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{childRel.company.name}</span>
                        </div>
                        <div>
                          <label className="block text-sm text-primary mb-1">Relationship Type</label>
                          <Select
                      disabled
                      value={childRel.relationshipType}
                      className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">

                            <option value="parent">Parent</option>
                            <option value="subsidiary">Subsidiary</option>
                            <option value="partner">Partner</option>
                            <option value="acquisition">Acquisition</option>
                          </Select>
                        </div>
                      </div>
                )}
                  </div>
                  <p className="mt-1 text-xs text-muted">
                    Note: Editing company relationships will be enabled in a future update.
                  </p>
                </div>
            }
              
              <div className="flex justify-between pt-4">
                <Button variant="secondary"
              type="button"
              onClick={() => setIsEditing(false)}>


                  Cancel
                </Button>
                <div className="flex space-x-2">
                  <Button variant="danger"
                type="button"
                onClick={handleDelete}>


                    Delete
                  </Button>
                  <Button variant="primary"
                type="submit"

                disabled={updateCompanyMutation.isLoading}>

                    {updateCompanyMutation.isLoading ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </form> :

          // View mode
          <div className="space-y-6">
              {/* Company logo and name */}
              <div className="flex items-center space-x-4">
                <div className={`w-16 h-16 rounded-md ${getLogoColor(company.id)} flex items-center justify-center text-primary font-medium text-xl`}>
                  {getCompanyInitial(company.name)}
                </div>
                <div>
                  <h3 className="text-lg font-medium text-primary">
                    {company.name}
                  </h3>
                  {company.industry &&
                <p className="text-sm text-muted">
                      {company.industry}
                    </p>
                }
                </div>
              </div>
              
              {/* Company info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted">Size</p>
                  <p className="mt-1 text-sm text-primary">
                    {company.size || "Not specified"}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-muted">Website</p>
                  <p className="mt-1 text-sm text-primary">
                    {company.website ?
                  <a
                    href={company.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-color hover:text-primary dark:hover:text-primary"
                    onClick={(e) => e.stopPropagation()}>

                        {company.website}
                      </a> :

                  "Not provided"
                  }
                  </p>
                </div>
              </div>
              
              <div>
                <p className="text-sm font-medium text-muted">Address</p>
                <p className="mt-1 text-sm text-primary">
                  {company.address || "Not provided"}
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted">Created</p>
                  <p className="mt-1 text-sm text-primary">
                    {formatDate(company.createdAt)}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-muted">Last Updated</p>
                  <p className="mt-1 text-sm text-primary">
                    {formatDate(company.updatedAt)}
                  </p>
                </div>
              </div>
              
              {company.description &&
            <div>
                  <p className="text-sm font-medium text-muted">Description</p>
                  <p className="mt-1 text-sm text-primary whitespace-pre-line">
                    {company.description}
                  </p>
                </div>
            }
              
              {/* Enrichment Status */}
              <div className="border-t border-default pt-4">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-muted">Data Enrichment</p>
                  <Button variant="primary"
                onClick={() => enrichCompanyMutation.mutate()}
                disabled={enrichCompanyMutation.isLoading}>


                    {enrichCompanyMutation.isLoading ? 'Enriching...' : 'Enrich Data'}
                  </Button>
                </div>
                
                {/* Enrichment badges */}
                <div className="flex items-center gap-2">
                  {company.enrichmentStatus?.sources?.abn_lookup?.success &&
                <Badge variant="success" size="sm">
                      ABN Verified
                    </Badge>
                }
                  {company.lastEnrichedAt &&
                <span className="text-xs text-muted">
                      Last enriched: {new Date(company.lastEnrichedAt).toLocaleDateString()}
                    </span>
                }
                  {!company.enrichmentStatus && !company.lastEnrichedAt &&
                <span className="text-xs text-muted">
                      Not yet enriched
                    </span>
                }
                </div>
                
                {/* Show enrichment results if just enriched */}
                {enrichCompanyMutation.isSuccess && enrichCompanyMutation.data &&
              <div className="mt-2 p-2 bg-primary-light/20 rounded-md">
                    <p className="text-xs text-primary-color">
                      Enrichment complete! Found data from {enrichCompanyMutation.data.results.filter((r) => r.success).length} source(s).
                    </p>
                  </div>
              }
                
                {/* Show error if enrichment failed */}
                {enrichCompanyMutation.isError &&
              <div className="mt-2 p-2 bg-error-light/20 rounded-md">
                    <p className="text-xs text-error">
                      Enrichment failed. Please try again later.
                    </p>
                  </div>
              }
              </div>
              
              {/* Company Relationships - Parent Companies */}
              {parentCompanies.length > 0 &&
            <div>
                  <p className="text-sm font-medium text-muted">Parent Companies</p>
                  <div className="mt-1 space-y-2">
                    {parentCompanies.map((parentRel, index) =>
                <div key={`parent-${parentRel.company.id || index}`} className="bg-surface-page p-2 rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium text-primary">
                              {parentRel.company.name}
                            </p>
                            <p className="text-xs text-muted">
                              Relationship: {formatRelationshipType(parentRel.relationshipType)}
                            </p>
                          </div>
                        </div>
                      </div>
                )}
                  </div>
                </div>
            }
              
              {/* Company Relationships - Child Companies */}
              {childCompanies.length > 0 &&
            <div>
                  <p className="text-sm font-medium text-muted">Subsidiaries & Related Companies</p>
                  <div className="mt-1 space-y-2">
                    {childCompanies.map((childRel, index) =>
                <div key={`child-${childRel.company.id || index}`} className="bg-surface-page p-2 rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium text-primary">
                              {childRel.company.name}
                            </p>
                            <p className="text-xs text-muted">
                              Relationship: {formatRelationshipType(childRel.relationshipType)}
                            </p>
                          </div>
                        </div>
                      </div>
                )}
                  </div>
                </div>
            }
              
              {/* Action Button for Contact Management */}
              <div>
                <Button variant="primary"
              onClick={() => setShowContactLinker(true)}>


                  Manage Contact Relationships
                </Button>
              </div>
              
              {/* Associated contacts */}
              {company.contacts && company.contacts.length > 0 &&
            <div>
                  <p className="text-sm font-medium text-muted">Contacts</p>
                  <div className="mt-1 space-y-2">
                    {company.contacts.map((contact) =>
                <div key={contact.id} className="bg-surface-page p-2 rounded-md">
                        <p className="text-sm font-medium text-primary">
                          {contact.firstName} {contact.lastName}
                        </p>
                        <p className="text-xs text-muted">
                          {contact.jobTitle || "No title"} • {contact.email || "No email"}
                        </p>
                      </div>
                )}
                  </div>
                </div>
            }
              
              {/* Associated deals */}
              {company.deals && company.deals.length > 0 &&
            <div>
                  <p className="text-sm font-medium text-muted">Deals</p>
                  <div className="mt-1 space-y-2">
                    {company.deals.map((deal) =>
                <div key={deal.id} className="bg-surface-page p-2 rounded-md">
                        <p className="text-sm font-medium text-primary">{deal.name}</p>
                        <p className="text-xs text-muted">
                          {deal.stage} • {deal.value ? new Intl.NumberFormat('en-AU', {
                      style: 'currency',
                      currency: deal.currency || "AUD",
                      maximumFractionDigits: 0
                    }).format(deal.value) : 'No value'}
                        </p>
                      </div>
                )}
                  </div>
                </div>
            }
              
              {/* Team Coverage Matrix */}
              <div className="mt-6">
                <TeamCoverageMatrix
                companyId={company.id}
                companyName={company.name}
                onViewContact={(contactId) => {
                  navigate(`/crm/contacts?selected=${contactId}`);
                  onClose();
                }} />

              </div>
              
              {/* Relationship Network Visualization */}
              <div className="mt-6">
                <NetworkVisualization
                entityId={company.id}
                entityType="company"
                depth={2}
                height={400} />

              </div>
              
              {/* Project History from Harvest */}
              <div className="mt-6">
                <ProjectHistory
                companyId={company.id}
                harvestId={company.harvestId} />

              </div>
              
              {/* Opportunity Intelligence */}
              <div className="mt-6">
                <OpportunityIntelligence
                companyId={company.id} />

              </div>
            </div>
          }
        </div>
        
        {/* Footer */}
        {!isEditing &&
        <div className="p-4 border-t border-default flex justify-end">
            <Button variant="primary"
          onClick={() => setIsEditing(true)}>


              Edit Company
            </Button>
          </div>
        }
      </div>
      
      {/* Contact Linker Modal */}
      {showContactLinker &&
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
          <div className="bg-surface-card rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b border-default flex justify-between items-center">
              <h2 className="text-xl font-semibold text-primary">
                Manage Contact Relationships
              </h2>
              <Button variant="ghost"
            onClick={() => setShowContactLinker(false)}>


                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              <ContactCompanyLinker
              mode="company"
              entityId={company.id}
              onClose={() => setShowContactLinker(false)} />

            </div>
          </div>
        </div>
      }
    </div>);

};

export default CompanyDetail;