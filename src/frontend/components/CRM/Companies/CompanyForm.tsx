import React, { useState } from "react";
import {
  CompanyCreate,
  CompanyRelationship,
  CompanyRelationshipType,
} from "../../../types/crm-types";
import { useMutation, useQueryClient, useQuery } from "react-query";
import { createCompany, getCompanies } from "../../../api/crm";
import { RadarState, CompanyPriority } from "../../../../types/shared-types";
import { PRIORITY_COLORS } from "../../../../types/company-types";
import {
  Input,
  Select,
  Textarea,
  FormGrid,
  FormSection,
} from "../../shared/forms";
import { Button } from "../../ui";
import { Checkbox } from "../../ui/Checkbox";
import { getRetryableErrorMessage } from "../../../utils/error-helpers";

interface CompanyFormProps {
  onClose: () => void;
  initialData?: Partial<CompanyCreate>;
  includeRadarFields?: boolean;
}

/**
 * Form component for creating a new company
 * Now supports Radar-specific fields for the unified company model
 */
const CompanyForm: React.FC<CompanyFormProps> = ({
  onClose,
  initialData = {},
  includeRadarFields = false,
}) => {
  const [formData, setFormData] = useState<CompanyCreate>({
    name: "",
    industry: "",
    size: "",
    website: "",
    address: "",
    description: "",
    source: "Manual",
    ...initialData,
  });

  // State for company relationships
  const [showRelationshipFields, setShowRelationshipFields] =
    useState<boolean>(false);
  const [selectedParentCompany, setSelectedParentCompany] =
    useState<string>("");
  const [selectedRelationshipType, setSelectedRelationshipType] =
    useState<CompanyRelationshipType>("subsidiary");

  // Fetch companies for the relationship dropdown
  const { data: companiesData = [] } = useQuery("companies", getCompanies);

  const [showRadarFields, setShowRadarFields] = useState<boolean>(
    includeRadarFields || !!formData.radarState,
  );

  const queryClient = useQueryClient();

  // Mutation for creating a company
  const createCompanyMutation = useMutation(
    (data: CompanyCreate) => createCompany(data),
    {
      onSuccess: () => {
        // Invalidate both CRM and Radar company queries if needed
        queryClient.invalidateQueries("companies");
        if (showRadarFields) {
          queryClient.invalidateQueries("radarCompanies");
        }
        onClose();
      },
    },
  );

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target;

    // Special handling for numeric fields
    if (name === "currentSpend" || name === "potentialSpend") {
      const numericValue = value === "" ? undefined : parseFloat(value);
      setFormData({
        ...formData,
        [name]: numericValue,
      });
      return;
    }

    setFormData({
      ...formData,
      [name]: value === "" ? undefined : value,
    });
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    if (name === "enableRadar") {
      setShowRadarFields(checked);
      if (!checked) {
        // Clear radar fields if disabled
        setFormData({
          ...formData,
          radarState: undefined,
          priority: undefined,
          currentSpend: undefined,
          potentialSpend: undefined,
          lastInteractionDate: undefined,
        });
      }
    } else if (name === "enableRelationships") {
      setShowRelationshipFields(checked);
      if (!checked) {
        // Clear relationship selections if disabled
        setSelectedParentCompany("");
        setSelectedRelationshipType("subsidiary");
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createCompanyMutation.mutate(formData);
  };

  // Get radar state options
  const radarStateOptions: RadarState[] = [
    "Strategy",
    "Transformation",
    "BAU",
    "Transition out",
  ];

  // Get priority options
  const priorityOptions: CompanyPriority[] = [
    "High",
    "Medium",
    "Low",
    "Qualified out",
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-surface-card rounded-lg shadow-xl max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">
            Create New Company
          </h2>
          <Button variant="ghost" onClick={onClose}>
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </div>

        {/* Form */}
        <form
          onSubmit={handleSubmit}
          className="p-4 space-y-4 max-h-[80vh] overflow-y-auto"
        >
          <FormSection title="Company Information">
            <FormGrid cols={1} gap="default">
              <Input
                label="Company Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                loading={createCompanyMutation.isLoading}
              />
            </FormGrid>

            <FormGrid cols={2} gap="default">
              <Input
                label="Industry"
                name="industry"
                value={formData.industry || ""}
                onChange={handleChange}
                loading={createCompanyMutation.isLoading}
              />

              <Select
                label="Size"
                name="size"
                value={formData.size || ""}
                onChange={handleChange}
                options={[
                  { value: "", label: "Select size" },
                  { value: "1-10", label: "1-10 employees" },
                  { value: "11-50", label: "11-50 employees" },
                  { value: "51-200", label: "51-200 employees" },
                  { value: "201-500", label: "201-500 employees" },
                  { value: "501-1000", label: "501-1000 employees" },
                  { value: "1001+", label: "1001+ employees" },
                ]}
                loading={createCompanyMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          <FormSection title="Contact Details">
            <FormGrid cols={1} gap="default">
              <Input
                label="Website"
                type="url"
                name="website"
                value={formData.website || ""}
                onChange={handleChange}
                placeholder="https://example.com"
                loading={createCompanyMutation.isLoading}
              />

              <Input
                label="Address"
                name="address"
                value={formData.address || ""}
                onChange={handleChange}
                loading={createCompanyMutation.isLoading}
              />

              <Textarea
                label="Description"
                name="description"
                value={formData.description || ""}
                onChange={handleChange}
                rows={3}
                loading={createCompanyMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          {/* Radio box toggles */}
          <div className="flex flex-col space-y-2">
            <Checkbox
              id="enableRadar"
              name="enableRadar"
              checked={showRadarFields}
              onChange={handleCheckboxChange}
              label="Include Radar Information"
            />

            <Checkbox
              id="enableRelationships"
              name="enableRelationships"
              checked={showRelationshipFields}
              onChange={handleCheckboxChange}
              label="Set Company Relationships"
            />
          </div>

          {/* Radar Fields */}
          {showRadarFields && (
            <fieldset className="border border-strong rounded-md p-4 space-y-4">
              <legend className="text-sm font-medium text-primary px-2">
                Radar Information
              </legend>

              <FormGrid cols={1} gap="default">
                <Select
                  label="Radar State"
                  name="radarState"
                  value={formData.radarState || ""}
                  onChange={handleChange}
                  options={[
                    { value: "", label: "Select radar state" },
                    ...radarStateOptions.map((state) => ({
                      value: state,
                      label: state,
                    })),
                  ]}
                  loading={createCompanyMutation.isLoading}
                />
              </FormGrid>

              {/* Priority */}
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Priority
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {priorityOptions.map((priority) => (
                    <div
                      key={priority}
                      className={`p-2 border rounded-md cursor-pointer ${
                        formData.priority === priority
                          ? "border-accent bg-accent-light/20"
                          : "border-strong hover:bg-surface-page dark:hover:bg-surface-elevated"
                      }`}
                      onClick={() => setFormData({ ...formData, priority })}
                    >
                      <div
                        className={`font-medium ${
                          formData.priority === priority
                            ? "text-accent-dark"
                            : "text-primary"
                        }`}
                      >
                        {priority}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <FormGrid cols={2} gap="default">
                <Input
                  label="Current Spend"
                  type="number"
                  name="currentSpend"
                  value={formData.currentSpend || ""}
                  onChange={handleChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  loading={createCompanyMutation.isLoading}
                />

                <Input
                  label="Potential Spend"
                  type="number"
                  name="potentialSpend"
                  value={formData.potentialSpend || ""}
                  onChange={handleChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  loading={createCompanyMutation.isLoading}
                />
              </FormGrid>

              <FormGrid cols={1} gap="default">
                <Input
                  label="Last Interaction Date"
                  type="date"
                  name="lastInteractionDate"
                  value={formData.lastInteractionDate?.substring(0, 10) || ""}
                  onChange={handleChange}
                  loading={createCompanyMutation.isLoading}
                />

                <Textarea
                  label="Radar Notes"
                  name="notes"
                  value={formData.notes || ""}
                  onChange={handleChange}
                  rows={2}
                  placeholder="Notes specific to radar tracking"
                  loading={createCompanyMutation.isLoading}
                />
              </FormGrid>
            </fieldset>
          )}

          {/* Relationship Fields */}
          {showRelationshipFields && (
            <fieldset className="border border-strong rounded-md p-4 space-y-4">
              <legend className="text-sm font-medium text-primary px-2">
                Company Relationships
              </legend>

              <FormGrid cols={1} gap="default">
                <Select
                  label="Parent/Related Company"
                  value={selectedParentCompany}
                  onChange={(e) => setSelectedParentCompany(e.target.value)}
                  options={[
                    { value: "", label: "Select a company" },
                    ...companiesData.map((company) => ({
                      value: company.id,
                      label: company.name,
                    })),
                  ]}
                  loading={createCompanyMutation.isLoading}
                />

                {selectedParentCompany && (
                  <Select
                    label="Relationship Type"
                    value={selectedRelationshipType}
                    onChange={(e) =>
                      setSelectedRelationshipType(
                        e.target.value as CompanyRelationshipType,
                      )
                    }
                    options={[
                      { value: "subsidiary", label: "Subsidiary" },
                      { value: "partner", label: "Partner" },
                      { value: "acquisition", label: "Acquisition" },
                    ]}
                    loading={createCompanyMutation.isLoading}
                  />
                )}
              </FormGrid>

              <p className="text-xs text-muted">
                Note: Backend support for company relationships is coming soon.
                Your selections will be saved, but may not be immediately
                reflected in the interface.
              </p>
            </fieldset>
          )}

          {/* Error message */}
          {createCompanyMutation.isError && (
            <div className="text-error text-sm">
              {getRetryableErrorMessage(
                createCompanyMutation.error,
                "create company",
              )}
            </div>
          )}

          {/* Form actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={createCompanyMutation.isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={createCompanyMutation.isLoading}
              loadingText="Creating..."
              disabled={createCompanyMutation.isLoading}
            >
              Create Company
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CompanyForm;
