import React, { useState, useEffect } from "react";
import { Company } from "../../../types/crm-types";
import { useQuery } from "react-query";
import { getCompanies } from "../../../api/crm";
import CompanyCard from "./CompanyCard";
import CompanyDetail from "./CompanyDetail";
import { useLocation, useNavigate } from "react-router-dom";

/**
 * Component for displaying and managing companies
 */import { Input } from "@/frontend/components/ui/Input";
const CompaniesList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Fetch companies using React Query
  const {
    data: companies = [],
    isLoading,
    error
  } = useQuery("companies", getCompanies);

  // Handle URL-based selection
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const selectedId = params.get('selected');

    if (selectedId && companies.length > 0) {
      const company = companies.find((c) => c.id === selectedId);
      if (company) {
        setSelectedCompany(company);
      }
    } else if (!selectedId) {
      setSelectedCompany(null);
    }
  }, [location.search, companies]);

  // Filter companies based on search term
  const filteredCompanies = companies.filter((company) => {
    const name = company.name.toLowerCase();
    const industry = (company.industry || "").toLowerCase();
    const website = (company.website || "").toLowerCase();
    const search = searchTerm.toLowerCase();

    // Check in related company names (parent companies)
    let matchesParentCompany = false;
    if (company.parentCompanies && company.parentCompanies.length > 0) {
      matchesParentCompany = company.parentCompanies.some((rel) =>
      (rel.company.name || "").toLowerCase().includes(search)
      );
    }

    // Check in related company names (child companies)
    let matchesChildCompany = false;
    if (company.childCompanies && company.childCompanies.length > 0) {
      matchesChildCompany = company.childCompanies.some((rel) =>
      (rel.company.name || "").toLowerCase().includes(search)
      );
    }

    return (
      name.includes(search) ||
      industry.includes(search) ||
      website.includes(search) ||
      matchesParentCompany ||
      matchesChildCompany);

  });

  // Handle selecting a company for detailed view
  const handleSelectCompany = (company: Company) => {
    // Update URL to persist selection
    navigate(`/crm/companies?selected=${company.id}`);
  };

  // Close the detail view
  const handleCloseDetail = () => {
    // Remove selection from URL
    navigate('/crm/companies");
  };

  return (
    <div className="min-h-[calc(100vh-200px)] flex flex-col bg-surface-page dark:bg-surface-page">
      {/* Header */}
      <div className="bg-surface-card dark:bg-surface-card border-b border-default dark:border-default flex-shrink-0">
        <div className="max-w-[84rem] mx-auto px-4 md:px-6 py-4">
          <div>
            <h2 className="text-xl font-semibold text-primary dark:text-primary">
              Client Radar
            </h2>
            <p className="text-sm text-secondary dark:text-subtle mt-1">
              Track and manage your client relationships
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-4 overflow-x-hidden">
        <div className="max-w-[84rem] mx-auto w-full">
          <div className="space-y-6">

      {/* Search bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg
                  className="h-5 w-5 text-subtle"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true">

            <path
                    fillRule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clipRule="evenodd" />

          </svg>
        </div>
        <Input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-strong dark:border-strong rounded-md leading-5 bg-surface-card dark:bg-surface-card placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)} />

      </div>

      {/* Company detail modal */}
      {selectedCompany &&
            <CompanyDetail company={selectedCompany} onClose={handleCloseDetail} />
            }

      {/* Main content */}
      {isLoading ?
            <div className="bg-surface-card dark:bg-surface-card shadow rounded-lg p-6 flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div> :
            error ?
            <div
              className="bg-error-light border border-red-400 text-error px-4 py-3 rounded relative"
              role="alert">

          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline">
            {" "}
            Failed to load companies. Please try again later.
          </span>
        </div> :
            filteredCompanies.length > 0 ?
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCompanies.map((company) =>
              <CompanyCard
                key={company.id}
                company={company}
                onClick={() => handleSelectCompany(company)} />

              )}
        </div> :

            <div className="bg-surface-card dark:bg-surface-card shadow rounded-lg p-6">
          <div className="text-center py-12">
            <svg
                  className="mx-auto h-16 w-16 text-subtle dark:text-muted"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true">

              <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />

            </svg>
            <h3 className="mt-4 text-lg font-medium text-primary dark:text-primary">
              {searchTerm ? "No companies found" : "No companies yet"}
            </h3>
            <p className="mt-2 text-sm text-muted dark:text-subtle">
              {searchTerm ?
                  `No companies match "${searchTerm}". Try a different search term.` :
                  "No companies available."}
            </p>
          </div>
        </div>
            }
          </div>
        </div>
      </div>
    </div>);

};

export default CompaniesList;