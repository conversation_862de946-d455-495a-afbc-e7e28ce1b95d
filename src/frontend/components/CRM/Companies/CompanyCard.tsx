import React from "react";
import { Company, CompanyRelationshipType } from "../../../types/crm-types";

interface CompanyCardProps {
  company: Company;
  onClick: () => void;
}

/**
 * Card component for displaying a company
 */
const CompanyCard: React.FC<CompanyCardProps> = ({ company, onClick }) => {
  // Get company logo placeholder based on company name
  const getCompanyInitial = (name: string): string => {
    return name.charAt(0).toUpperCase();
  };

  // Get random color based on company ID for logo background
  const getLogoColor = (id: string | null | undefined): string => {
    const colors = [
      'bg-primary-light0',
      'bg-success-light0',
      'bg-accent-light0',
      'bg-warning-light0',
      'bg-accent-light',
      'bg-primary-light0',
      'bg-error-light0',
      'bg-info-light0'
    ];
    
    // If no ID, return a default color
    if (!id) {
      return colors[0];
    }
    
    // Simple hash function to get consistent color for the same ID
    const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };
  
  // Format relationship type for display
  const formatRelationshipType = (type: CompanyRelationshipType): string => {
    switch (type) {
      case 'parent':
        return 'Parent';
      case 'subsidiary':
        return 'Subsidiary';
      case 'partner':
        return 'Partner';
      case 'acquisition':
        return 'Acquisition';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <div
      className="company-card bg-surface-card rounded-lg shadow-sm border border-default p-4 cursor-pointer hover:shadow-md transition-shadow"
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        {/* Company logo placeholder */}
        <div className={`flex-shrink-0 w-12 h-12 rounded-md ${getLogoColor(company.id)} flex items-center justify-center text-primary font-medium text-lg`}>
          {getCompanyInitial(company.name)}
        </div>
        
        {/* Company info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-medium text-primary truncate">
            {company.name}
          </h3>
          
          {company.industry && (
            <p className="text-sm text-muted truncate">
              {company.industry}
            </p>
          )}
          
          {company.size && (
            <p className="text-sm text-muted truncate">
              Size: {company.size}
            </p>
          )}
          
          {company.website && (
            <p className="text-sm text-muted truncate mt-2">
              {company.website}
            </p>
          )}
          
          {company.address && (
            <p className="text-sm text-muted truncate">
              {company.address}
            </p>
          )}
          
          {/* Display parent company relationships */}
          {company.parentCompanies && company.parentCompanies.length > 0 && (
            <div className="mt-2">
              <span className="text-xs font-medium text-muted">
                Part of: {company.parentCompanies[0].company.name}
              </span>
              {company.parentCompanies.length > 1 && (
                <span className="text-xs text-muted"> +{company.parentCompanies.length - 1} more</span>
              )}
            </div>
          )}
          
          {/* Display child company relationships */}
          {company.childCompanies && company.childCompanies.length > 0 && (
            <div className="mt-1">
              <span className="text-xs font-medium text-muted">
                {company.childCompanies.length} related {company.childCompanies.length === 1 ? 'company' : "companies"}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CompanyCard;
