import React, { useState } from "react";
import DealPipeline from "./DealPipeline";

/**
 * Main component for CRM reports
 */import { Button } from "@/frontend/components/ui/Button";
const CRMReports: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'pipeline'>('pipeline');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-primary dark:text-primary">
          CRM Reports
        </h1>
      </div>

      {/* Tabs */}
      <div className="border-b border-default dark:border-default">
        <nav className="-mb-px flex space-x-8">
          <Button variant="secondary"
          className={`${
          activeTab ==="pipeline' ?'--tab-active' : "--tab"}`
          }
          onClick={() => setActiveTab('pipeline')}>

            Deal Pipeline
          </Button>
          {/* Add more tabs here as needed */}
        </nav>
      </div>

      {/* Tab content */}
      <div>
        {activeTab ==="pipeline' && <DealPipeline />}
        {/* Add more tab content here as needed */}
      </div>
    </div>);

};

export default CRMReports;