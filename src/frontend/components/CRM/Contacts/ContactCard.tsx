import React from "react";
import { Contact } from "../../../types/crm-types";

interface ContactCardProps {
  contact: Contact;
  onClick: () => void;
}

/**
 * Card component for displaying a contact
 */
const ContactCard: React.FC<ContactCardProps> = ({ contact, onClick }) => {
  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Get random color based on contact ID for avatar background
  const getAvatarColor = (id: string | null | undefined): string => {
    const colors = [
      "bg-primary-light0",
      "bg-success-light0",
      "bg-accent-light0",
      "bg-warning-light0",
      "bg-magenta-500",
      "bg-primary-light0",
      "bg-error-light0",
      "bg-info-light0",
    ];

    // If no ID, return a default color
    if (!id) {
      return colors[0];
    }

    // Simple hash function to get consistent color for the same ID
    const hash = id
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  return (
    <div
      className="contact-card bg-surface-card dark:bg-surface-card rounded-lg shadow-sm border border-default dark:border-default p-4 cursor-pointer hover:shadow-md transition-shadow"
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        {/* Avatar with initials */}
        <div
          className={`flex-shrink-0 w-12 h-12 rounded-full ${getAvatarColor(contact.id)} flex items-center justify-center text-primary font-medium text-lg`}
        >
          {getInitials(contact.firstName, contact.lastName)}
        </div>

        {/* Contact info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-medium text-primary dark:text-primary truncate">
            {contact.firstName} {contact.lastName}
          </h3>

          {contact.jobTitle && (
            <p className="text-sm text-muted dark:text-subtle truncate">
              {contact.jobTitle}
            </p>
          )}

          {/* Show primary company first */}
          {contact.company && (
            <p className="text-sm text-muted dark:text-subtle truncate">
              {contact.company.name}
            </p>
          )}

          {/* Show company from new relationship model if no legacy company */}
          {!contact.company &&
            contact.companies &&
            contact.companies.length > 0 && (
              <p className="text-sm text-muted dark:text-subtle truncate">
                {(() => {
                  const primaryCompany = contact.companies.find(
                    (c) => c.isPrimary,
                  );
                  if (primaryCompany) return primaryCompany.company.name;
                  return contact.companies[0].company.name;
                })()}
              </p>
            )}

          {contact.email && (
            <p className="text-sm text-muted dark:text-subtle truncate mt-2">
              {contact.email}
            </p>
          )}

          {contact.phone && (
            <p className="text-sm text-muted dark:text-subtle truncate">
              {contact.phone}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactCard;
