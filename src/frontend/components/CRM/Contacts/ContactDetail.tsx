import React, { useState } from "react";
import {
  Contact,
  ContactUpdate,
  ContactCompanyRelationship,
  ContactRole,
} from "../../../types/crm-types";
import { updateContact, deleteContact } from "../../../api/crm";
import { useMutation, useQueryClient } from "react-query";
import { NetworkVisualization } from "../NetworkVisualization";
import ContactCompanyLinker from "../Relationships/ContactCompanyLinker";
import ContactRelationshipManager from "../Relationships/ContactRelationshipManager";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Textarea } from "@/frontend/components/ui/Textarea";
import { Radio } from "@/frontend/components/ui/Radio";

interface ContactDetailProps {
  contact: Contact;
  onClose: () => void;
}

/**
 * Component for displaying detailed information about a contact
 */
const ContactDetail: React.FC<ContactDetailProps> = ({ contact, onClose }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showCompanyLinker, setShowCompanyLinker] = useState(false);
  const [showRelationshipManager, setShowRelationshipManager] = useState(false);
  const [formData, setFormData] = useState<ContactUpdate>({
    firstName: contact.firstName,
    lastName: contact.lastName,
    email: contact.email,
    phone: contact.phone,
    jobTitle: contact.jobTitle,
    companyId: contact.companyId, // For backward compatibility
    notes: contact.notes,
  });

  // State for managing company relationships
  const [companyRelationships, setCompanyRelationships] = useState<
    ContactCompanyRelationship[]
  >(contact.companies || []);

  // Helper function to get company name from ID
  const getCompanyName = (companyId?: string) => {
    if (!companyId) return "Unknown";
    if (contact.company && contact.company.id === companyId)
      return contact.company.name;
    if (contact.companies) {
      const company = contact.companies.find((c) => c.company.id === companyId);
      if (company) return company.company.name;
    }
    return "Unknown";
  };

  // Helper function to get role label
  const getRoleLabel = (role?: ContactRole) => {
    if (!role) return "Not specified";
    // Capitalize first letter
    return role.charAt(0).toUpperCase() + role.slice(1);
  };
  const queryClient = useQueryClient();

  // Mutation for updating a contact
  const updateContactMutation = useMutation(
    (data: ContactUpdate) => updateContact(contact.id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("contacts");
        setIsEditing(false);
      },
    },
  );

  // Mutation for deleting a contact
  const deleteContactMutation = useMutation(() => deleteContact(contact.id), {
    onSuccess: () => {
      queryClient.invalidateQueries("contacts");
      onClose();
    },
  });

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value === "" ? undefined : value,
    });
  };

  // Handle role change for a company relationship
  const handleRoleChange = (index: number, role: ContactRole | undefined) => {
    const updatedRelationships = [...companyRelationships];
    updatedRelationships[index] = {
      ...updatedRelationships[index],
      role: role,
    };
    setCompanyRelationships(updatedRelationships);
  };

  // Handle primary flag change for a company relationship
  const handlePrimaryChange = (index: number) => {
    const updatedRelationships = companyRelationships.map((rel, idx) => ({
      ...rel,
      isPrimary: idx === index,
    }));
    setCompanyRelationships(updatedRelationships);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a full implementation, we would submit both the form data and company relationships
    // but the backend doesn't support the new relationship model yet
    // For now, just submit the basic contact data
    updateContactMutation.mutate(formData);

    // TODO: Uncomment when backend supports the new relationship model
    // const updatedData = {
    //   ...formData,
    //   companies: companyRelationships.map(rel => ({
    //     companyId: rel.company.id || "",
    //     role: rel.role,
    //     isPrimary: rel.isPrimary
    //   }))
    // };
    // updateContactMutation.mutate(updatedData);
  };

  // Handle contact deletion with confirmation
  const handleDelete = () => {
    if (
      window.confirm(
        "Are you sure you want to delete this contact? This action cannot be undone.",
      )
    ) {
      deleteContactMutation.mutate();
    }
  };

  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Get random color based on contact ID for avatar background
  const getAvatarColor = (id: string | null | undefined): string => {
    const colors = [
      "bg-primary-light0",
      "bg-success-light0",
      "bg-accent-light0",
      "bg-warning-light0",
      "bg-magenta-500",
      "bg-primary-light0",
      "bg-error-light0",
      "bg-info-light0",
    ];

    // If no ID, return a default color
    if (!id) {
      return colors[0];
    }

    // Simple hash function to get consistent color for the same ID
    const hash = id
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  // Format date to local format
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-AU", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4"
      onClick={(e) => {
        // Only close if clicking the backdrop, not the modal content
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className="bg-surface-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">
            {isEditing ? "Edit Contact" : "Contact Details"}
          </h2>
          <Button variant="ghost" onClick={onClose}>
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isEditing ? (
            // Edit form
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    First Name
                  </label>
                  <Input
                    type="text"
                    name="firstName"
                    value={formData.firstName || ""}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Last Name
                  </label>
                  <Input
                    type="text"
                    name="lastName"
                    value={formData.lastName || ""}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Email
                </label>
                <Input
                  type="email"
                  name="email"
                  value={formData.email || ""}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Phone
                </label>
                <Input
                  type="tel"
                  name="phone"
                  value={formData.phone || ""}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Job Title
                </label>
                <Input
                  type="text"
                  name="jobTitle"
                  value={formData.jobTitle || ""}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Notes
                </label>
                <Textarea
                  name="notes"
                  value={formData.notes || ""}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                />
              </div>

              {/* Company relationships edit section */}
              {companyRelationships.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Company Relationships
                  </label>
                  <div className="space-y-3">
                    {companyRelationships.map((rel, index) => (
                      <div
                        key={`${rel.company.id || index}`}
                        className="p-3 border border-default rounded-md"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">
                            {rel.company.name}
                          </span>
                          <div className="flex items-center">
                            <label className="inline-flex items-center mr-4">
                              <Radio
                                name="primaryCompany"
                                checked={rel.isPrimary}
                                onChange={() => handlePrimaryChange(index)}
                                className="text-primary-color focus:ring-primary"
                              />

                              <span className="ml-2 text-sm text-primary">
                                Primary
                              </span>
                            </label>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm text-primary mb-1">
                            Role
                          </label>
                          <Select
                            value={rel.role || ""}
                            onChange={(e) =>
                              handleRoleChange(
                                index,
                                (e.target.value as ContactRole) || undefined,
                              )
                            }
                            className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                          >
                            <option value="">Not specified</option>
                            <option value="employee">Employee</option>
                            <option value="contractor">Contractor</option>
                            <option value="consultant">Consultant</option>
                            <option value="manager">Manager</option>
                            <option value="executive">Executive</option>
                            <option value="owner">Owner</option>
                            <option value="other">Other</option>
                          </Select>
                        </div>
                      </div>
                    ))}
                  </div>
                  <p className="mt-1 text-xs text-muted">
                    Note: Backend support for multiple company relationships is
                    coming soon.
                  </p>
                </div>
              )}

              <div className="flex justify-between pt-4">
                <Button
                  variant="secondary"
                  type="button"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
                <div className="flex space-x-2">
                  <Button variant="danger" type="button" onClick={handleDelete}>
                    Delete
                  </Button>
                  <Button
                    variant="primary"
                    type="submit"
                    disabled={updateContactMutation.isLoading}
                  >
                    {updateContactMutation.isLoading
                      ? "Saving..."
                      : "Save Changes"}
                  </Button>
                </div>
              </div>
            </form>
          ) : (
            // View mode
            <div className="space-y-6">
              {/* Contact avatar and name */}
              <div className="flex items-center space-x-4">
                <div
                  className={`w-16 h-16 rounded-full ${getAvatarColor(contact.id)} flex items-center justify-center text-primary font-medium text-xl`}
                >
                  {getInitials(contact.firstName, contact.lastName)}
                </div>
                <div>
                  <h3 className="text-lg font-medium text-primary">
                    {contact.firstName} {contact.lastName}
                  </h3>
                  {contact.jobTitle && (
                    <p className="text-sm text-muted">{contact.jobTitle}</p>
                  )}
                  {/* Show primary company first */}
                  {contact.company && (
                    <p className="text-sm text-muted">
                      {contact.company.name}{" "}
                      {/* (Primary company from legacy data) */}
                    </p>
                  )}

                  {/* Show new relationship model companies */}
                  {contact.companies &&
                    contact.companies.length > 0 &&
                    !contact.company && (
                      <p className="text-sm text-muted">
                        {contact.companies.find((c) => c.isPrimary)?.company
                          .name || contact.companies[0].company.name}
                      </p>
                    )}
                </div>
              </div>

              {/* Contact info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted">Email</p>
                  <p className="mt-1 text-sm text-primary">
                    {contact.email || "Not provided"}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted">Phone</p>
                  <p className="mt-1 text-sm text-primary">
                    {contact.phone || "Not provided"}
                  </p>
                </div>
              </div>

              {/* Companies relationships - new model */}
              {contact.companies && contact.companies.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-muted">
                    Company Relationships
                  </p>
                  <div className="mt-1 space-y-2">
                    {contact.companies.map((companyRel, idx) => (
                      <div
                        key={`${companyRel.company.id || idx}`}
                        className="bg-surface-page p-2 rounded-md flex justify-between items-center"
                      >
                        <div>
                          <p className="text-sm font-medium text-primary">
                            {companyRel.company.name}
                            {companyRel.isPrimary && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-light text-primary">
                                Primary
                              </span>
                            )}
                          </p>
                          {companyRel.role && (
                            <p className="text-xs text-muted">
                              Role: {getRoleLabel(companyRel.role)}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted">Created</p>
                  <p className="mt-1 text-sm text-primary">
                    {formatDate(contact.createdAt)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted">Last Updated</p>
                  <p className="mt-1 text-sm text-primary">
                    {formatDate(contact.updatedAt)}
                  </p>
                </div>
              </div>

              {contact.notes && (
                <div>
                  <p className="text-sm font-medium text-muted">Notes</p>
                  <p className="mt-1 text-sm text-primary whitespace-pre-line">
                    {contact.notes}
                  </p>
                </div>
              )}

              {/* Associated deals */}
              {contact.deals && contact.deals.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-muted">
                    Associated Deals
                  </p>
                  <div className="mt-1 space-y-2">
                    {contact.deals.map((deal) => (
                      <div
                        key={deal.id}
                        className="bg-surface-page p-2 rounded-md"
                      >
                        <p className="text-sm font-medium text-primary">
                          {deal.name}
                        </p>
                        <p className="text-xs text-muted">
                          {deal.stage} •{" "}
                          {deal.value
                            ? new Intl.NumberFormat("en-AU", {
                                style: "currency",
                                currency: deal.currency || "AUD",
                                maximumFractionDigits: 0,
                              }).format(deal.value)
                            : "No value"}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons for Relationship Management */}
              <div className="flex gap-2">
                <Button
                  variant="primary"
                  onClick={() => setShowCompanyLinker(true)}
                >
                  Manage Company Relationships
                </Button>
                <Button
                  variant="primary"
                  onClick={() => setShowRelationshipManager(true)}
                >
                  Manage Contact Relationships
                </Button>
              </div>

              {/* Relationship Network Visualization */}
              <div className="mt-6">
                <NetworkVisualization
                  entityId={contact.id}
                  entityType="contact"
                  depth={2}
                  height={400}
                />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {!isEditing && (
          <div className="p-4 border-t border-default flex justify-end">
            <Button variant="primary" onClick={() => setIsEditing(true)}>
              Edit Contact
            </Button>
          </div>
        )}
      </div>

      {/* Company Linker Modal */}
      {showCompanyLinker && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
          <div className="bg-surface-card rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b border-default flex justify-between items-center">
              <h2 className="text-xl font-semibold text-primary">
                Manage Company Relationships
              </h2>
              <Button
                variant="ghost"
                onClick={() => setShowCompanyLinker(false)}
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              <ContactCompanyLinker
                mode="contact"
                entityId={contact.id}
                onClose={() => setShowCompanyLinker(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Relationship Manager Modal */}
      {showRelationshipManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
          <div className="bg-surface-card rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b border-default flex justify-between items-center">
              <h2 className="text-xl font-semibold text-primary">
                Manage Contact Relationships
              </h2>
              <Button
                variant="ghost"
                onClick={() => setShowRelationshipManager(false)}
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              <ContactRelationshipManager
                contactId={contact.id}
                onClose={() => setShowRelationshipManager(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactDetail;
