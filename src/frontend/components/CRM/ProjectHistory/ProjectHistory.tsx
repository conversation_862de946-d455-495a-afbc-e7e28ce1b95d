import React from "react";
import { useQuery } from "react-query";
import { Card } from "../../ui";
import { format, parseISO } from "date-fns";
import { fetchFromApi } from "../../../api/utils";

interface ProjectHistoryProps {
  companyId: string;
  harvestId?: number;
}

interface HarvestProject {
  id: number;
  name: string;
  code: string;
  is_active: boolean;
  budget: number | null;
  budget_by: string | null;
  created_at: string;
  updated_at: string;
  starts_on: string | null;
  ends_on: string | null;
  over_budget_notification_percentage: number | null;
  show_budget_to_all: boolean;
  cost_budget: number | null;
  cost_budget_include_expenses: boolean;
  fee: number | null;
  notes: string | null;
}

interface ProjectsSummary {
  totalProjects: number;
  activeProjects: number;
  totalBudget: number;
  totalBillableHours: number;
  totalNonBillableHours: number;
  dateRange: {
    earliest: string | null;
    latest: string | null;
  };
}

const ProjectHistory: React.FC<ProjectHistoryProps> = ({ companyId, harvestId }) => {
  // Fetch project history
  const { data, isLoading, error } = useQuery(
    ['project-history', companyId],
    async () => {
      const response = await fetchFromApi(`/api/crm/network/projects/${companyId}`);
      return response.data as {
        projects: HarvestProject[];
        summary: ProjectsSummary;
      };
    },
    {
      enabled: !!harvestId, // Only fetch if company has a Harvest ID
      staleTime: 300000, // 5 minutes
      cacheTime: 600000, // 10 minutes
    }
  );

  if (!harvestId) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Project History
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-muted">
            This company is not linked to Harvest. Link it to see project history.
          </p>
        </div>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Project History
          </h3>
          <div className="mt-4 flex items-center justify-center">
            <div className="text-muted">Loading project history...</div>
          </div>
        </div>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Project History
          </h3>
          <div className="mt-4 text-error">Failed to load project history</div>
        </div>
      </Card>
    );
  }

  const { projects, summary } = data;

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    try {
      return format(parseISO(dateString), 'dd MMM yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default">
        <h3 className="text-lg leading-6 font-medium text-primary">
          Project History
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-muted">
          Historical project data from Harvest
        </p>
      </div>

      {/* Summary Statistics */}
      <div className="px-4 py-5 sm:p-6 bg-surface-page border-b border-default">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <p className="text-sm font-medium text-muted">Total Projects</p>
            <p className="mt-1 text-2xl font-semibold text-primary">
              {summary.totalProjects}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted">Active Projects</p>
            <p className="mt-1 text-2xl font-semibold text-success">
              {summary.activeProjects}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted">Total Budget</p>
            <p className="mt-1 text-2xl font-semibold text-primary">
              {formatCurrency(summary.totalBudget)}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted">Total Hours</p>
            <p className="mt-1 text-2xl font-semibold text-primary">
              {(summary.totalBillableHours + summary.totalNonBillableHours).toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Projects List */}
      <div className="px-4 py-5 sm:p-6">
        {projects.length === 0 ? (
          <p className="text-muted">No projects found for this company.</p>
        ) : (
          <div className="space-y-4">
            {projects.map((project) => (
              <div
                key={project.id}
                className="border border-default rounded-lg p-4 hover:bg-surface-page transition-colors"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <h4 className="text-base font-medium text-primary">
                      {project.name}
                    </h4>
                    {project.code && (
                      <span className="text-sm text-muted">
                        ({project.code})
                      </span>
                    )}
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        project.is_active
                          ? 'bg-success-light text-success'
                          : 'bg-surface-alt text-primary'
                      }`}
                    >
                      {project.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  {project.budget && (
                    <div className="text-right">
                      <p className="text-sm font-medium text-primary">
                        {formatCurrency(project.budget)}
                      </p>
                      <p className="text-xs text-muted">Budget</p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3 text-sm">
                  <div>
                    <p className="text-muted">Start Date</p>
                    <p className="text-primary">{formatDate(project.starts_on)}</p>
                  </div>
                  <div>
                    <p className="text-muted">End Date</p>
                    <p className="text-primary">{formatDate(project.ends_on)}</p>
                  </div>
                  <div>
                    <p className="text-muted">Created</p>
                    <p className="text-primary">{formatDate(project.created_at)}</p>
                  </div>
                  <div>
                    <p className="text-muted">Budget Type</p>
                    <p className="text-primary capitalize">
                      {project.budget_by || 'None'}
                    </p>
                  </div>
                </div>

                {project.notes && (
                  <div className="mt-3">
                    <p className="text-sm text-primary">{project.notes}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
};

export default ProjectHistory;