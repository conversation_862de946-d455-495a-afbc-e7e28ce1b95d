import React from "react";
import { useDrag } from "react-dnd";
import { formatCurrency, formatRelativeDate } from "../../../utils/format";
import type { Deal, DealEstimate } from "../../../types/crm-types";
import {
  DocumentTextIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ExclamationCircleIcon } from
'@heroicons/react/24/outline';
import {
  DocumentTextIcon as DocumentTextIconSolid,
  CurrencyDollarIcon as CurrencyDollarIconSolid,
  ClockIcon as ClockIconSolid,
  ExclamationCircleIcon as ExclamationCircleIconSolid } from
'@heroicons/react/24/solid";
import { Button } from "@/frontend/components/ui/Button";

interface DealCardProps {
  deal: Deal;
  onClick: () => void;
  compact: boolean;
  estimates: DealEstimate[];
  onLinkEstimate: () => void;
  onViewLinkedEstimates: () => void;
}

export const DealCard: React.FC<DealCardProps> = ({
  deal,
  onClick,
  compact,
  estimates,
  onLinkEstimate,
  onViewLinkedEstimates
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: "deal",
    item: { id: deal.id },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    })
  });

  // Remove automatic fetching - let parent component handle it
  // This was causing infinite loops due to fetchEstimates changing on every render

  // Calculate deal age
  const dealAge = deal.createdAt ?
  Math.floor((Date.now() - new Date(deal.createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 0;

  // Determine urgency indicators
  const isOld = dealAge > 30;
  const isHighValue = (deal.value || 0) > 199000;
  const isClosingSoon = deal.expectedCloseDate &&
  new Date(deal.expectedCloseDate).getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000;

  // Estimate-related calculations
  const hasEstimates = estimates.length > 0;
  const totalEstimateValue = estimates.reduce((sum, est) => sum + (est.amount || 0), 0);

  // Use a key to force re-render when estimates change
  const estimateKey = `${deal.id}-${estimates.length}-${hasEstimates}`;

  // Indicator component for cleaner display
  const DealIndicators = ({ className = "" }: {className?: string;}) =>
  <div key={estimateKey} className={`flex items-center gap-1 ${className}`}>
      {/* Estimate indicator */}
      <div className="relative group">
        {hasEstimates ?
      <DocumentTextIconSolid className="w-4 h-4 text-success" /> :

      <DocumentTextIcon className="w-4 h-4 text-subtle" />
      }
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-surface-page text-primary text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
          {hasEstimates ? `${estimates.length} estimate${estimates.length > 1 ? 's" : ""} linked` : "No estimates"}
        </div>
      </div>

      {/* High value indicator */}
      <div className="relative group">
        {isHighValue ?
      <CurrencyDollarIconSolid className="w-4 h-4 text-warning" /> :

      <CurrencyDollarIcon className="w-4 h-4 text-subtle" />
      }
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-surface-page text-primary text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
          {isHighValue ? 'High value (>$199k)' : "Standard value"}
        </div>
      </div>

      {/* Closing soon indicator */}
      <div className="relative group">
        {isClosingSoon ?
      <ClockIconSolid className="w-4 h-4 text-warning" /> :

      <ClockIcon className="w-4 h-4 text-subtle" />
      }
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-surface-page text-primary text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
          {isClosingSoon ? 'Closing within 7 days' : "Standard timeline"}
        </div>
      </div>

      {/* Old deal indicator */}
      <div className="relative group">
        {isOld ?
      <ExclamationCircleIconSolid className="w-4 h-4 text-error" /> :

      <ExclamationCircleIcon className="w-4 h-4 text-subtle" />
      }
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-surface-page text-primary text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
          {isOld ? `${dealAge} days old - needs attention` : `${dealAge} days old`}
        </div>
      </div>
    </div>;


  if (compact) {
    return (
      <div
        ref={drag}
        onClick={onClick}
        className={`bg-surface-card rounded-md p-2 shadow-sm border border-default cursor-pointer hover:shadow-md transition-all hover:scale-[1.02] ${
        isDragging ? 'opacity-50" : ""}`
        }>

        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <h4 className="text-sm font-medium text-primary truncate">
              {deal.name}
            </h4>
            <div className="flex items-center gap-2 mt-0.5">
              <p className="text-xs text-muted truncate">
                {deal.companyName || "No Company"}
              </p>
              <DealIndicators className="ml-auto" />
            </div>
          </div>
          <div className="text-right ml-3 flex-shrink-0">
            <div className="text-sm font-semibold text-primary">
              {formatCurrency(deal.value || 0, { compact: true })}
            </div>
            <div className="text-xs text-muted">
              {Math.round((deal.probability || 0) * 100)}%
            </div>
          </div>
        </div>
      </div>);

  }

  return (
    <div
      ref={drag}
      onClick={onClick}
      className={`bg-surface-card rounded-lg p-3 shadow-sm border border-default cursor-pointer hover:shadow-lg transition-all hover:scale-[1.02] ${
      isDragging ? 'opacity-50" : ""}`
      }>

      {/* Deal Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="min-w-0 flex-1">
          <h4 className="text-sm font-semibold text-primary truncate">
            {deal.name}
          </h4>
          <p className="text-sm text-secondary truncate">
            {deal.companyName || "No Company"}
          </p>
        </div>
        
        {/* Status Indicators */}
        <DealIndicators className="ml-2" />
      </div>

      {/* Deal Metrics */}
      <div className="grid grid-cols-2 gap-2 mb-2">
        <div>
          <div className="text-xs text-muted">Value</div>
          <div className="text-sm font-semibold text-primary">
            {formatCurrency(deal.value || 0, { compact: true })}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted">Probability</div>
          <div className="flex items-center gap-1">
            <div className="text-sm font-semibold text-primary">
              {Math.round((deal.probability || 0) * 100)}%
            </div>
            <div className="flex-1 bg-surface-alt rounded-full h-1.5">
              <div
                className="bg-accent-light0 h-1.5 rounded-full transition-all"
                style={{ width: `${(deal.probability || 0) * 100}%` }} />

            </div>
          </div>
        </div>
      </div>

      {/* Deal Info */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-3 text-muted">
          {deal.owner &&
          <div className="flex items-center gap-1">
              <div className="w-5 h-5 bg-surface-page rounded-full flex items-center justify-center text-[10px] font-medium text-secondary">
                {deal.owner.charAt(0).toUpperCase()}
              </div>
              <span className="truncate max-w-[80px]">{deal.owner}</span>
            </div>
          }
          <span>•</span>
          <span>{dealAge}d old</span>
        </div>
        
        {deal.expectedCloseDate &&
        <span className="text-accent font-medium">
            {formatRelativeDate(deal.expectedCloseDate)}
          </span>
        }
      </div>

      {/* Quick Actions (on hover) */}
      <div className="mt-2 pt-2 border-t border-subtle flex gap-1 opacity-0 hover:opacity-100 transition-opacity">
        <Button variant="secondary"
        onClick={(e) => {
          e.stopPropagation();
          // Handle activity creation
        }}>


          Activity
        </Button>
        {hasEstimates ?
        <Button variant="success"
        onClick={(e) => {
          e.stopPropagation();
          onViewLinkedEstimates();
        }}>


            Estimates ({estimates.length})
          </Button> :

        <Button variant="success"
        onClick={(e) => {
          e.stopPropagation();
          onLinkEstimate();
        }}>


            Link Estimate
          </Button>
        }
        <Button variant="secondary"
        onClick={(e) => {
          e.stopPropagation();
          // Handle note
        }}>


          Note
        </Button>
      </div>
    </div>);

};

export default DealCard;