import React from "react";
import { XMarkIcon, ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { useQuery } from "react-query";
import { getDraftEstimates } from "../../../api/estimates";
import type { DealEstimate } from "../../../types/crm-types";
import { Button } from "@/frontend/components/ui/Button";

interface SimpleLinkedEstimatesModalProps {
  isOpen: boolean;
  onClose: () => void;
  dealId: string;
  estimates: DealEstimate[];
}

const SimpleLinkedEstimatesModal: React.FC<SimpleLinkedEstimatesModalProps> = ({
  isOpen,
  onClose,
  dealId,
  estimates
}) => {
  // Fetch all draft estimates to get full details
  const { data: draftEstimates = [] } = useQuery(
    'draftEstimates',
    getDraftEstimates,
    {
      enabled: isOpen,
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: false
    }
  );
  // Unlinking is no longer supported - linking is permanent

  const handleViewEstimate = (estimate: DealEstimate) => {
    const url = `/estimates/${estimate.id}`;
    window.open(url, '_blank');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-surface-card dark:bg-surface-card rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-default dark:border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary dark:text-primary">
            Linked Estimates ({estimates.filter((e) => e.type === 'internal').length})
          </h2>
          <Button variant="ghost"
          onClick={onClose}>


            <XMarkIcon className="w-6 h-6" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {/* Filter to show only internal estimates */}
          {(() => {
            const internalEstimates = estimates.filter((e) => e.type === 'internal');
            return internalEstimates.length === 0 ?
            <div className="text-center py-8 text-muted dark:text-subtle">
              No estimates linked to this deal.
            </div> :

            <div className="space-y-3">
              {internalEstimates.map((estimate) => {
                // Find the full estimate details from the fetched draft estimates
                const fullEstimate = draftEstimates.find((de) => de.uuid === estimate.id);

                return (
                  <div
                    key={`${estimate.type}-${estimate.id}`}
                    className="p-4 border border-default dark:border-default rounded-lg bg-surface-page dark:bg-surface-alt/50">

                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-primary dark:text-primary truncate">
                          {fullEstimate?.projectName || estimate.name || 'Untitled Estimate'}
                        </h3>
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-primary-light text-primary-color dark:bg-primary-dark/30 dark:text-blue-300">
                          Internal
                        </span>
                      </div>
                      <p className="text-sm text-secondary dark:text-subtle truncate">
                        {fullEstimate?.clientName || estimate.clientName || 'No client'}
                      </p>
                    </div>
                    <div className="text-right ml-3">
                      {(fullEstimate?.totalFees || estimate.amount) &&
                        <div className="font-medium text-primary dark:text-primary">
                          ${(fullEstimate?.totalFees || estimate.amount || 0).toLocaleString()}
                        </div>
                        }
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm text-secondary dark:text-subtle mb-3">
                    <div>
                      <span className="font-medium">Status:</span>{' '}
                      <span className="capitalize">{fullEstimate?.status || estimate.status || 'draft'}</span>
                    </div>
                    <div>
                      <span className="font-medium">Linked:</span>{' '}
                      {estimate.linkedAt ?
                        format(new Date(estimate.linkedAt), 'MMM d, yyyy') :
                        'Unknown date'}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="primary"
                      onClick={() => handleViewEstimate(estimate)}>


                      <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                      View
                    </Button>
                  </div>
                </div>);

              })}
            </div>;

          })()}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-default dark:border-default flex justify-end">
          <Button variant="secondary"
          onClick={onClose}>


            Close
          </Button>
        </div>
      </div>
    </div>);

};

export default SimpleLinkedEstimatesModal;