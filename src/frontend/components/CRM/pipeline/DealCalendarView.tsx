import React, { useMemo, useState } from "react";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  parseISO,
  startOfWeek,
  endOfWeek,
} from "date-fns";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { Deal } from "../../../types/crm-types";
import { Button } from "@/frontend/components/ui/Button";

interface DealCalendarViewProps {
  deals: Deal[];
  onDealClick: (deal: Deal) => void;
}

// Stage colors matching the Kanban board
const STAGE_COLORS: Record<
  string,
  { bg: string; text: string; border: string }
> = {
  Prospecting: {
    bg: "bg-surface-alt",
    text: "text-primary",
    border: "border-slate-300",
  },
  Qualification: {
    bg: "bg-primary-light",
    text: "text-primary-color",
    border: "border-primary",
  },
  Proposal: {
    bg: "bg-warning-light",
    text: "text-warning",
    border: "border-warning",
  },
  Negotiation: {
    bg: "bg-warning-light",
    text: "text-warning-dark",
    border: "border-warning",
  },
  "Closed won": {
    bg: "bg-success-light",
    text: "text-success",
    border: "border-success",
  },
  "Closed lost": {
    bg: "bg-error-light",
    text: "text-error",
    border: "border-error",
  },
  Abandoned: {
    bg: "bg-surface-alt",
    text: "text-primary",
    border: "border-strong",
  },
};

interface CalendarDeal {
  deal: Deal;
  type: "created" | "closing";
  date: Date;
}

export const DealCalendarView: React.FC<DealCalendarViewProps> = ({
  deals,
  onDealClick,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<"month" | "week">("month");

  // Process deals to get calendar events
  const calendarDeals = useMemo(() => {
    const events: CalendarDeal[] = [];

    deals.forEach((deal) => {
      // Add creation date event
      if (deal.createdAt) {
        events.push({
          deal,
          type: "created",
          date: parseISO(deal.createdAt),
        });
      }

      // Add expected close date event
      if (deal.expectedCloseDate) {
        events.push({
          deal,
          type: "closing",
          date: parseISO(deal.expectedCloseDate),
        });
      }
    });

    return events;
  }, [deals]);

  // Get calendar days based on view mode
  const calendarDays = useMemo(() => {
    if (viewMode === "month") {
      const start = startOfWeek(startOfMonth(currentDate));
      const end = endOfWeek(endOfMonth(currentDate));
      return eachDayOfInterval({ start, end });
    } else {
      const start = startOfWeek(currentDate);
      const end = endOfWeek(currentDate);
      return eachDayOfInterval({ start, end });
    }
  }, [currentDate, viewMode]);

  // Group deals by date
  const dealsByDate = useMemo(() => {
    const grouped = new Map<string, CalendarDeal[]>();

    calendarDeals.forEach((calendarDeal) => {
      const dateKey = format(calendarDeal.date, "yyyy-MM-dd");
      if (!grouped.has(dateKey)) {
        grouped.set(dateKey, []);
      }
      grouped.get(dateKey)!.push(calendarDeal);
    });

    return grouped;
  }, [calendarDeals]);

  // Navigation handlers
  const navigatePrevious = () => {
    if (viewMode === "month") {
      setCurrentDate(
        (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1)
      );
    } else {
      setCurrentDate(
        (prev) => new Date(prev.getTime() - 7 * 24 * 60 * 60 * 1000)
      );
    }
  };

  const navigateNext = () => {
    if (viewMode === "month") {
      setCurrentDate(
        (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1)
      );
    } else {
      setCurrentDate(
        (prev) => new Date(prev.getTime() + 7 * 24 * 60 * 60 * 1000)
      );
    }
  };

  const navigateToday = () => {
    setCurrentDate(new Date());
  };

  // Get stage color configuration
  const getStageColor = (stage: string) => {
    return STAGE_COLORS[stage] || STAGE_COLORS["Abandoned"];
  };

  return (
    <div className="h-full flex flex-col bg-surface-card rounded-lg shadow-sm">
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b border-default">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-primary">
            {format(
              currentDate,
              viewMode === "month" ? "MMMM yyyy" : "'Week of" MMM d, yyyy"
            )}
          </h2>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              onClick={navigatePrevious}
              className="p-1.5"
            >
              <ChevronLeftIcon className="w-5 h-5" />
            </Button>
            <Button variant="secondary" onClick={navigateToday}>
              Today
            </Button>
            <Button variant="ghost" onClick={navigateNext} className="p-1.5">
              <ChevronRightIcon className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2 bg-surface-alt rounded-lg p-1">
          <Button
            variant="secondary"
            onClick={() => setViewMode("week")}
            className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
              viewMode === "week"
                ? "bg-surface-card text-primary shadow-sm"
                : "text-secondary hover:text-primary dark:hover:text-primary"
            }`}
          >
            Week
          </Button>
          <Button
            variant="secondary"
            onClick={() => setViewMode("month")}
            className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
              viewMode === "month"
                ? "bg-surface-card text-primary shadow-sm"
                : "text-secondary hover:text-primary dark:hover:text-primary"
            }`}
          >
            Month
          </Button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="flex-1 overflow-auto">
        <div className="min-h-full">
          {/* Day Headers */}
          <div className="grid grid-cols-7 border-b border-default">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
              <div
                key={day}
                className="p-2 text-center text-sm font-medium text-primary border-r border-default last:border-r-0"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div
            className={`grid grid-cols-7 ${
              viewMode === "month" ? "grid-rows-6" : ""
            }`}
          >
            {calendarDays.map((day, index) => {
              const dateKey = format(day, "yyyy-MM-dd");
              const dayDeals = dealsByDate.get(dateKey) || [];
              const isCurrentMonth = isSameMonth(day, currentDate);
              const isToday = isSameDay(day, new Date());

              return (
                <div
                  key={index}
                  className={`border-r border-b border-default last:border-r-0
                    ${viewMode === "month" ? "min-h-[120px]" : "min-h-[200px]"}
                    ${
                      !isCurrentMonth && viewMode === "month"
                        ? "bg-surface-page"
                        : ""
                    }
                    ${isToday ? "bg-primary-light/20" : ""}`}
                >
                  {/* Day Number */}
                  <div className="p-2">
                    <div
                      className={`text-sm font-medium ${
                        isToday
                          ? "text-primary-color"
                          : isCurrentMonth
                          ? "text-primary"
                          : "text-subtle"
                      }`}
                    >
                      {format(day, "d")}
                    </div>
                  </div>

                  {/* Deal Events */}
                  <div className="px-2 pb-2 space-y-1">
                    {dayDeals
                      .slice(0, viewMode === "month" ? 3 : 5)
                      .map((calendarDeal, i) => {
                        const colors = getStageColor(calendarDeal.deal.stage);
                        return (
                          <Button
                            variant="secondary"
                            key={`${calendarDeal.deal.id}-${calendarDeal.type}-${i}`}
                            onClick={() => onDealClick(calendarDeal.deal)}
                            className={`w-full text-left px-2 py-1 rounded text-xs font-medium
                            ${colors.bg} ${colors.text} hover:opacity-80 transition-opacity
                            border ${colors.border}`}
                            title={`${calendarDeal.deal.name} - ${
                              calendarDeal.type === "created"
                                ? "Created"
                                : "Expected Close"
                            }`}
                          >
                            <div className="flex items-center gap-1">
                              <span
                                className={`w-1.5 h-1.5 rounded-full flex-shrink-0
                              ${
                                calendarDeal.type === "created"
                                  ? "bg-surface-page0"
                                  : colors.text.replace("text", "bg")
                              }`}
                              />
                              <span className="truncate">
                                {calendarDeal.deal.name}
                              </span>
                            </div>
                          </Button>
                        );
                      })}
                    {dayDeals.length > (viewMode === "month" ? 3 : 5) && (
                      <div className="text-xs text-muted text-center">
                        +{dayDeals.length - (viewMode === "month" ? 3 : 5)} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="border-t border-default p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-xs">
            <div className="flex items-center gap-1">
              <span className="w-2 h-2 rounded-full bg-surface-page0" />
              <span className="text-secondary">Created</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="w-2 h-2 rounded-full bg-primary-light0" />
              <span className="text-secondary">Expected Close</span>
            </div>
          </div>
          <div className="text-xs text-muted">
            {deals.length} deals • {calendarDeals.length} events
          </div>
        </div>
      </div>
    </div>
  );
};

export default DealCalendarView;
