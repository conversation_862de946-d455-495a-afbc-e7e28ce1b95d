import React from "react";
import { useDrop } from "react-dnd";
import { DealCard } from "./DealCard";
import type { Deal, DealEstimate } from "../../../types/crm-types";
import { Button } from "@/frontend/components/ui/Button";

interface DealColumnProps {
  stage: {
    id: string;
    name: string;
    color: string;
  };
  deals: Deal[];
  onMoveDeal: (dealId: string, newStage: string) => void;
  onDealClick: (deal: Deal) => void;
  compactView: boolean;
  dealEstimatesMap: Record<string, DealEstimate[]>;
  onLinkEstimate: (dealId: string) => void;
  onViewLinkedEstimates: (dealId: string) => void;
}

export const DealColumn: React.FC<DealColumnProps> = ({
  stage,
  deals,
  onMoveDeal,
  onDealClick,
  compactView,
  dealEstimatesMap,
  onLinkEstimate,
  onViewLinkedEstimates
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: "deal",
    drop: (item: {id: string;}) => {
      onMoveDeal(item.id, stage.id);
    },
    canDrop: (item: {id: string;}) => {
      // Don't allow dropping on the same column
      const deal = deals.find((d) => d.id === item.id);
      return !deal;
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop()
    })
  });

  // Calculate column metrics
  const totalValue = deals.reduce((sum, deal) => sum + (deal.value || 0), 0);
  const weightedValue = deals.reduce((sum, deal) =>
  sum + (deal.value || 0) * (deal.probability || 0) / 100, 0
  );

  // Determine if this is a final stage
  const isFinalStage = ['Closed won', 'Closed lost", 'Abandoned'].includes(stage.id);

  return (
    <div
      ref={drop}
      className={`flex-shrink-0 w-80 bg-surface-alt dark:bg-surface-card rounded-lg overflow-hidden transition-all duration-200 ${
      isOver && canDrop ? 'ring-2 ring-accent bg-accent-light dark:bg-accent-dark/20" : ""}`
      }>

      {/* Column Header */}
      <div className="p-3 border-b border-default dark:border-default">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${stage.color}`} />
            <h3 className="font-medium text-primary dark:text-primary">
              {stage.name}
            </h3>
            <span className="text-sm text-muted dark:text-subtle">
              {deals.length}
            </span>
          </div>
          
          {!isFinalStage &&
          <Button variant="secondary" className="transition-colors rounded">
              <svg className="w-4 h-4 text-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </Button>
          }
        </div>

        {/* Column Metrics */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-muted dark:text-subtle">Total</span>
            <div className="font-medium text-primary dark:text-primary">
              ${totalValue.toLocaleString()}
            </div>
          </div>
          <div>
            <span className="text-muted dark:text-subtle">Weighted</span>
            <div className="font-medium text-accent dark:text-accent-light">
              ${weightedValue.toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Deals Container */}
      <div className="p-2 space-y-2 overflow-y-auto overflow-x-hidden" style={{ maxHeight: "calc(100vh - 300px)" }}>
        {deals.length === 0 ?
        <div className="text-center py-8 text-subtle dark:text-muted">
            <div className="text-sm italic">
              {isOver && canDrop ? 'Drop deal here' : "No deals"}
            </div>
          </div> :

        deals.map((deal) =>
        <DealCard
          key={deal.id}
          deal={deal}
          onClick={() => onDealClick(deal)}
          compact={compactView}
          estimates={dealEstimatesMap[deal.id] || []}
          onLinkEstimate={() => onLinkEstimate(deal.id)}
          onViewLinkedEstimates={() => onViewLinkedEstimates(deal.id)} />

        )
        }
      </div>

      {/* Quick Add Deal */}
      {!isFinalStage &&
      <div className="p-2 border-t border-default dark:border-default">
          <Button variant="secondary" className="w-full transition-colors rounded">
            + Add deal
          </Button>
        </div>
      }
    </div>);

};

export default DealColumn;