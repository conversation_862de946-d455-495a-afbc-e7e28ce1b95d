import React, { useRef, useCallback, useMemo, useState } from "react";
import { useQuery } from "react-query";
import ForceGraph2D from "react-force-graph-2d";
import { Card } from "../../ui";
import { useNavigate } from "react-router-dom";
import { getRelationshipNetwork } from "../../../api/crm";

interface NetworkVisualizationProps {
  entityId: string;
  entityType: "contact" | "company";
  depth?: number;
  height?: number;
}

interface Node {
  id: string;
  label: string;
  type: "contact" | "company";
  level: number;
  color?: string;
  x?: number;
  y?: number;
  fx?: number;
  fy?: number;
}

interface Link {
  source: string;
  target: string;
  type: string;
  strength: number;
  label?: string;
}

interface GraphData {
  nodes: Node[];
  links: Link[];
}

const NetworkVisualization: React.FC<NetworkVisualizationProps> = ({
  entityId,
  entityType,
  depth = 2,
  height = 400
}) => {
  const navigate = useNavigate();
  const fgRef = useRef<any>();
  const [hoveredNode, setHoveredNode] = useState<Node | null>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(entityId);

  // Fetch network data
  const { data, isLoading, error } = useQuery(
    ['network', entityId, entityType, depth],
    () => getRelationshipNetwork(entityId, entityType, depth),
    {
      staleTime: 60000, // 1 minute
      cacheTime: 300000 // 5 minutes
    }
  );

  // Process graph data
  const graphData = useMemo<GraphData>(() => {
    if (!data) return { nodes: [], links: [] };

    const nodes = data.nodes.map((node: any) => ({
      ...node,
      color: getNodeColor(node.type, node.id === entityId),
      size: node.id === entityId ? 12 : 8
    }));

    const links = data.links.map((link: any) => ({
      ...link,
      width: Math.max(1, link.strength * 2),
      curvature: 0.1
    }));

    return { nodes, links };
  }, [data, entityId]);

  // Get node color based on type and selection
  function getNodeColor(type: string, isRoot: boolean): string {
    if (isRoot) return '#205EA6'; // Flexoki blue-600 for root node
    return type === 'company' ? '#66800B' : "#A34DAB"; // Flexoki green-600 for companies, purple-600 for contacts
  }

  // Handle node click
  const handleNodeClick = useCallback((node: Node) => {
    if (node.type === 'company') {
      navigate(`/crm/companies?selected=${node.id}`);
    } else {
      navigate(`/crm/contacts?selected=${node.id}`);
    }
  }, [navigate]);

  // Handle node hover
  const handleNodeHover = useCallback((node: Node | null) => {
    setHoveredNode(node);
  }, []);

  // Draw custom node
  const drawNode = useCallback((node: Node, ctx: CanvasRenderingContext2D, globalScale: number) => {
    const label = node.label;
    const fontSize = 12 / globalScale;
    const isHovered = hoveredNode?.id === node.id;
    const isSelected = selectedNode === node.id;
    
    // Draw node circle
    ctx.beginPath();
    const radius = (node.size || 8) * (isHovered ? 1.2 : 1);
    ctx.arc(node.x || 0, node.y || 0, radius, 0, 2 * Math.PI, false);
    ctx.fillStyle = node.color || "#878580"; // Flexoki base-500
    ctx.fill();

    // Draw border for selected/hovered nodes
    if (isSelected || isHovered) {
      ctx.strokeStyle = isSelected ? '#205EA6' : "#6F6E69"; // Flexoki blue-600 : base-600
      ctx.lineWidth = isSelected ? 3 : 2;
      ctx.stroke();
    }

    // Draw label
    ctx.font = `${fontSize}px Sans-Serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = '#100F0F'; // Flexoki black
    ctx.fillText(label, node.x || 0, (node.y || 0) + radius + 10);

    // Draw type indicator
    if (node.type === 'company') {
      // Small building icon
      ctx.font = `${fontSize * 0.8}px Sans-Serif`;
      ctx.fillText('🏢', (node.x || 0) - radius - 5, (node.y || 0) - radius - 5);
    }
  }, [hoveredNode, selectedNode]);

  // Draw custom link
  const drawLink = useCallback((link: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
    // Draw link label if exists
    if (link.label && globalScale > 0.5) {
      const midX = (link.source.x + link.target.x) / 2;
      const midY = (link.source.y + link.target.y) / 2;
      
      ctx.font = `${10 / globalScale}px Sans-Serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillStyle = '#6F6E69'; // Flexoki base-600
      ctx.fillText(link.label, midX, midY);
    }
  }, []);

  if (isLoading) {
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="text-muted">Loading relationship network...</div>
        </div>
      </Card>
    );
  }

  if (error) {
    console.error('Network visualization error:', error);
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="text-error">
            Failed to load relationship network
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs mt-2">{(error as any)?.message || "Unknown error"}</div>
            )}
          </div>
        </div>
      </Card>
    );
  }

  if (!graphData.nodes.length) {
    return (
      <Card>
        <div className="px-4 py-5 sm:px-6 border-b border-default">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Relationship Network
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-muted">
            Visual representation of connections
          </p>
        </div>
        <div className="flex flex-col items-center justify-center h-64 text-muted">
          <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-base font-medium mb-2">No relationships yet</p>
          <p className="text-sm text-center max-w-sm">
            Relationships will appear here once contacts are connected to each other or to companies.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default">
        <h3 className="text-lg leading-6 font-medium text-primary">
          Relationship Network
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-muted">
          Visual representation of connections up to {depth} degrees
        </p>
      </div>
      
      <div className="relative" style={{ height }}>
        <ForceGraph2D
          ref={fgRef}
          graphData={graphData}
          nodeCanvasObject={drawNode}
          nodeCanvasObjectMode={() => 'replace'}
          linkCanvasObjectMode={() => 'after'}
          linkCanvasObject={drawLink}
          onNodeClick={handleNodeClick}
          onNodeHover={handleNodeHover}
          cooldownTicks={50}
          nodeRelSize={1}
          linkDirectionalArrowLength={3}
          linkDirectionalArrowRelPos={1}
          enableNodeDrag={true}
          enableZoomInteraction={true}
          enablePanInteraction={true}
          width={undefined}
          height={height}
        />
        
        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-surface-card rounded-lg shadow-lg p-3">
          <div className="text-xs font-medium text-primary mb-2">Legend</div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-primary-light0 rounded-full"></div>
              <span className="text-xs text-secondary">Current Entity</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-success-light0 rounded-full"></div>
              <span className="text-xs text-secondary">Company</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-accent-light0 rounded-full"></div>
              <span className="text-xs text-secondary">Contact</span>
            </div>
          </div>
        </div>

        {/* Hover tooltip */}
        {hoveredNode && (
          <div className="absolute top-4 right-4 bg-surface-card rounded-lg shadow-lg p-3 max-w-xs">
            <div className="font-medium text-primary">{hoveredNode.label}</div>
            <div className="text-sm text-muted capitalize">{hoveredNode.type}</div>
            <div className="text-xs text-subtle mt-1">Click to view details</div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default NetworkVisualization;