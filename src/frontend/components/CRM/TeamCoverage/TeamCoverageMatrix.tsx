/**
 * Team Coverage Matrix Component
 * Shows who on the team knows which contacts at a company
 */

import React, { useState, useMemo } from "react";
import { useQuery } from "react-query";
import {
  UsersIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  ArrowRightIcon,
  UserPlusIcon,
  ClockIcon } from
'@heroicons/react/24/outline';
import { getUsers } from "../../../api/harvest";
import { Badge } from "../../ui";
import type { CompanyCoverageSummary } from "../../../../api/repositories/team-coverage-repository";
import { Button } from "@/frontend/components/ui/Button";

interface TeamCoverageMatrixProps {
  companyId: string;
  companyName: string;
  onAssignContact?: (contactId: string, teamMemberId: string) => void;
  onViewContact?: (contactId: string) => void;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

const TeamCoverageMatrix: React.FC<TeamCoverageMatrixProps> = ({
  companyId,
  companyName,
  onAssignContact,
  onViewContact
}) => {
  const [selectedContact, setSelectedContact] = useState<string | null>(null);
  const [isAssigning, setIsAssigning] = useState(false);

  // Fetch team members from Harvest
  const { data: teamMembers = [], isLoading: loadingTeam } = useQuery<TeamMember[]>(
    'harvest-users',
    async () => {
      const users = await getUsers();
      return users.map((user) => ({
        id: user.id.toString(),
        name: `${user.first_name} ${user.last_name}`,
        email: user.email,
        avatar: user.avatar_url
      }));
    },
    {
      staleTime: 30 * 60 * 1000, // 30 minutes
      cacheTime: 60 * 60 * 1000 // 1 hour
    }
  );

  // Fetch company coverage data
  const { data: coverage, isLoading: loadingCoverage, refetch } = useQuery<CompanyCoverageSummary>(
    ['team-coverage', companyId],
    async () => {
      const response = await fetch(`/api/crm/network/team/coverage/${companyId}`);
      if (!response.ok) throw new Error('Failed to fetch coverage data');
      const result = await response.json();
      return result.data;
    },
    {
      enabled: !!companyId,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchInterval: 30 * 1000 // Refresh every 30 seconds
    }
  );

  // Get team member details by ID
  const getTeamMember = (memberId: string): TeamMember | undefined => {
    return teamMembers.find((tm) => tm.id === memberId);
  };

  // Handle assigning a contact to a team member
  const handleAssignContact = async (contactId: string, teamMemberId: string) => {
    setIsAssigning(true);
    try {
      const response = await fetch('/api/crm/network/team/coverage', {
        method: "POST",
        headers: { 'Content-Type': "application/json" },
        body: JSON.stringify({
          contactId,
          teamMemberId,
          relationshipStrength: "secondary",
          lastInteractionDate: new Date().toISOString()
        })
      });

      if (!response.ok) throw new Error('Failed to assign contact');

      // Refresh coverage data
      await refetch();

      if (onAssignContact) {
        onAssignContact(contactId, teamMemberId);
      }
    } catch (error) {
      console.error('Error assigning contact:', error);
    } finally {
      setIsAssigning(false);
      setSelectedContact(null);
    }
  };

  // Calculate coverage statistics
  const stats = useMemo(() => {
    if (!coverage) return null;

    const coveragePercentage = coverage.totalContacts > 0 ?
    Math.round(coverage.coveredContacts / coverage.totalContacts * 100) :
    0;

    return {
      coveragePercentage,
      uncoveredCount: coverage.coverage.uncovered.length,
      singleThreadedCount: coverage.coverage.singleThreaded.length,
      wellCoveredCount: coverage.coverage.wellCovered.length
    };
  }, [coverage]);

  if (loadingTeam || loadingCoverage) {
    return (
      <div className="bg-surface-card rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-surface-alt rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-surface-alt rounded"></div>
            <div className="h-4 bg-surface-alt rounded"></div>
            <div className="h-4 bg-surface-alt rounded"></div>
          </div>
        </div>
      </div>);

  }

  if (!coverage) {
    return (
      <div className="bg-surface-card rounded-lg p-6">
        <p className="text-muted">Unable to load coverage data</p>
      </div>);

  }

  return (
    <div className="bg-surface-card rounded-lg shadow-sm">
      {/* Header with stats */}
      <div className="p-6 border-b border-default">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-primary">
              Team Coverage Matrix
            </h3>
            <p className="text-sm text-muted mt-1">
              {companyName} · {coverage.totalContacts} contacts
            </p>
          </div>
          <div className="flex items-center gap-4">
            {/* Coverage percentage */}
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {stats?.coveragePercentage}%
              </div>
              <div className="text-xs text-muted">Coverage</div>
            </div>
            
            {/* Risk indicator */}
            <div className="flex items-center gap-2">
              {coverage.riskLevel === 'high' &&
              <Badge variant="danger" size="sm">
                  <ShieldExclamationIcon className="w-3.5 h-3.5 mr-1" />
                  High Risk
                </Badge>
              }
              {coverage.riskLevel === 'medium' &&
              <Badge variant="warning" size="sm">
                  <ExclamationTriangleIcon className="w-3.5 h-3.5 mr-1" />
                  Medium Risk
                </Badge>
              }
              {coverage.riskLevel === 'low' &&
              <Badge variant="success" size="sm">
                  <ShieldCheckIcon className="w-3.5 h-3.5 mr-1" />
                  Low Risk
                </Badge>
              }
            </div>
          </div>
        </div>

        {/* Coverage breakdown */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="bg-error-light/20 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-error">Uncovered</span>
              <span className="text-lg font-semibold text-error">
                {stats?.uncoveredCount}
              </span>
            </div>
          </div>
          <div className="bg-warning-light/20 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-warning">Single-threaded</span>
              <span className="text-lg font-semibold text-warning">
                {stats?.singleThreadedCount}
              </span>
            </div>
          </div>
          <div className="bg-success-light/20 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-success">Well covered</span>
              <span className="text-lg font-semibold text-success">
                {stats?.wellCoveredCount}
              </span>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        {coverage.recommendations.length > 0 &&
        <div className="mt-4 space-y-2">
            {coverage.recommendations.map((rec, index) =>
          <div
            key={index}
            className="flex items-start gap-2 text-sm text-secondary">

                <ExclamationTriangleIcon className="w-4 h-4 text-warning mt-0.5 flex-shrink-0" />
                <span>{rec}</span>
              </div>
          )}
          </div>
        }
      </div>

      {/* Uncovered contacts section */}
      {coverage.coverage.uncovered.length > 0 &&
      <div className="p-6 border-b border-default">
          <h4 className="text-sm font-medium text-primary mb-3 flex items-center gap-2">
            <ExclamationTriangleIcon className="w-4 h-4 text-error" />
            Uncovered Contacts ({coverage.coverage.uncovered.length})
          </h4>
          <div className="space-y-2">
            {coverage.coverage.uncovered.map((contact) =>
          <div
            key={contact.id}
            className="flex items-center justify-between p-3 bg-surface-page/50 rounded-lg hover:bg-surface-alt dark:hover:bg-surface-elevated transition-colors">

                <div className="flex items-center gap-3">
                  <div
                className="cursor-pointer"
                onClick={() => onViewContact?.(contact.id)}>

                    <div className="font-medium text-primary hover:text-accent dark:hover:text-accent-light">
                      {contact.name}
                    </div>
                    {contact.jobTitle &&
                <div className="text-sm text-muted">
                        {contact.jobTitle}
                      </div>
                }
                  </div>
                </div>
                <Button variant="ghost"
            onClick={() => setSelectedContact(contact.id)}>


                  <UserPlusIcon className="w-4 h-4" />
                  Assign
                </Button>
              </div>
          )}
          </div>
        </div>
      }

      {/* Single-threaded contacts section */}
      {coverage.coverage.singleThreaded.length > 0 &&
      <div className="p-6 border-b border-default">
          <h4 className="text-sm font-medium text-primary mb-3 flex items-center gap-2">
            <ExclamationTriangleIcon className="w-4 h-4 text-warning" />
            Single-threaded Relationships ({coverage.coverage.singleThreaded.length})
          </h4>
          <div className="space-y-2">
            {coverage.coverage.singleThreaded.map((contact) => {
            const teamMember = getTeamMember(contact.coveredBy);
            return (
              <div
                key={contact.id}
                className="flex items-center justify-between p-3 bg-surface-page/50 rounded-lg">

                  <div className="flex items-center gap-3">
                    <div
                    className="cursor-pointer"
                    onClick={() => onViewContact?.(contact.id)}>

                      <div className="font-medium text-primary hover:text-accent dark:hover:text-accent-light">
                        {contact.name}
                      </div>
                      {contact.jobTitle &&
                    <div className="text-sm text-muted">
                          {contact.jobTitle}
                        </div>
                    }
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <ArrowRightIcon className="w-4 h-4 text-subtle" />
                      {teamMember &&
                    <div className="flex items-center gap-2">
                          {teamMember.avatar ?
                      <img
                        src={teamMember.avatar}
                        alt={teamMember.name}
                        className="w-6 h-6 rounded-full" /> :


                      <div className="w-6 h-6 bg-surface-alt rounded-full flex items-center justify-center text-xs font-medium text-secondary">
                              {teamMember.name.charAt(0)}
                            </div>
                      }
                          <span className="text-sm text-secondary">
                            {teamMember.name}
                          </span>
                        </div>
                    }
                    </div>
                    <Button variant="ghost"
                  onClick={() => setSelectedContact(contact.id)}>


                      Add backup
                    </Button>
                  </div>
                </div>);

          })}
          </div>
        </div>
      }

      {/* Well-covered contacts section */}
      {coverage.coverage.wellCovered.length > 0 &&
      <div className="p-6">
          <h4 className="text-sm font-medium text-primary mb-3 flex items-center gap-2">
            <CheckCircleIcon className="w-4 h-4 text-success" />
            Well-covered Contacts ({coverage.coverage.wellCovered.length})
          </h4>
          <div className="space-y-2">
            {coverage.coverage.wellCovered.map((contact) => {
            const members = contact.teamMembers.
            map((id) => getTeamMember(id)).
            filter(Boolean) as TeamMember[];

            return (
              <div
                key={contact.id}
                className="flex items-center justify-between p-3 bg-surface-page/50 rounded-lg">

                  <div className="flex items-center gap-3">
                    <div
                    className="cursor-pointer"
                    onClick={() => onViewContact?.(contact.id)}>

                      <div className="font-medium text-primary hover:text-accent dark:hover:text-accent-light">
                        {contact.name}
                      </div>
                      {contact.jobTitle &&
                    <div className="text-sm text-muted">
                          {contact.jobTitle}
                        </div>
                    }
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <ArrowRightIcon className="w-4 h-4 text-subtle" />
                    <div className="flex -space-x-2">
                      {members.slice(0, 3).map((member) =>
                    <div
                      key={member.id}
                      className="relative"
                      title={member.name}>

                          {member.avatar ?
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className="w-6 h-6 rounded-full border-2 border-surface-card" /> :


                      <div className="w-6 h-6 bg-surface-alt rounded-full border-2 border-surface-card flex items-center justify-center text-xs font-medium text-secondary">
                              {member.name.charAt(0)}
                            </div>
                      }
                        </div>
                    )}
                      {members.length > 3 &&
                    <div className="w-6 h-6 bg-surface-alt rounded-full border-2 border-surface-card flex items-center justify-center text-xs font-medium text-secondary">
                          +{members.length - 3}
                        </div>
                    }
                    </div>
                  </div>
                </div>);

          })}
          </div>
        </div>
      }

      {/* Assignment modal */}
      {selectedContact &&
      <div className="fixed inset-0 bg-overlay/50 flex items-center justify-center z-50">
          <div className="bg-surface-card rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-primary mb-4">
              Assign Team Member
            </h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {teamMembers.map((member) =>
            <Button variant="secondary"
            key={member.id}
            onClick={() => handleAssignContact(selectedContact, member.id)}
            disabled={isAssigning}>


                  {member.avatar ?
              <img
                src={member.avatar}
                alt={member.name}
                className="w-8 h-8 rounded-full" /> :


              <div className="w-8 h-8 bg-surface-page rounded-full flex items-center justify-center text-sm font-medium text-secondary">
                      {member.name.charAt(0)}
                    </div>
              }
                  <div>
                    <div className="font-medium text-primary">
                      {member.name}
                    </div>
                    <div className="text-sm text-muted">
                      {member.email}
                    </div>
                  </div>
                </Button>
            )}
            </div>
            <div className="mt-4 flex justify-end">
              <Button variant="ghost"
            onClick={() => setSelectedContact(null)}>


                Cancel
              </Button>
            </div>
          </div>
        </div>
      }
    </div>);

};

export default TeamCoverageMatrix;