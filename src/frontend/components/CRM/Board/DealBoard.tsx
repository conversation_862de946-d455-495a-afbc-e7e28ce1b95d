import React, { useState, useEffect, useRef } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Deal, DealStage } from "../../../types/crm-types";
import { getDeals, updateDeal, getDealById } from "../../../api/crm";
import DealColumn from "./DealColumn";
import DealDetail from "./DealDetail";
import { useQuery, useMutation, useQueryClient } from "react-query";

// Define all possible deal stages
const DEAL_STAGES: DealStage[] = [
  "Identified",
  "Qualified",
  "Solution proposal",
  "Solution presentation",
  "Objection handling",
  "Finalising terms",
  "Closed won",
  "Closed lost",
  "Abandoned",
];

interface DealBoardProps {
  isCompactView?: boolean;
  hideInactive?: boolean;
}

/**
 * Main kanban board component for displaying deals by stage
 */
const DealBoard: React.FC<DealBoardProps> = ({
  isCompactView = false,
  hideInactive = false,
}) => {
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [collapsedColumns, setCollapsedColumns] = useState<DealStage[]>([]);
  const boardRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  // Define inactive stages
  const inactiveStages: DealStage[] = [
    "Closed won",
    "Closed lost",
    "Abandoned",
  ];

  // Track which inactive columns have been manually expanded
  const [manuallyExpandedColumns, setManuallyExpandedColumns] = useState<
    DealStage[]
  >([]);

  // Auto-collapse inactive columns when hideInactive is toggled
  useEffect(() => {
    if (hideInactive) {
      // Add all inactive stages to collapsed columns if they're not already there
      // and they haven"t been manually expanded
      setCollapsedColumns((prev) => {
        const newCollapsed = [...prev];
        inactiveStages.forEach((stage) => {
          if (
            !newCollapsed.includes(stage) &&
            !manuallyExpandedColumns.includes(stage)
          ) {
            newCollapsed.push(stage);
          }
        });
        return newCollapsed;
      });
    } else {
      // Remove inactive stages from collapsed columns
      setCollapsedColumns((prev) =>
        prev.filter((stage) => !inactiveStages.includes(stage))
      );
      // Clear the manually expanded columns when hideInactive is turned off
      setManuallyExpandedColumns([]);
    }
  }, [hideInactive, manuallyExpandedColumns]); // Fixed dependency array

  // Fetch deals using React Query
  const { data: deals = [], isLoading, error } = useQuery("deals", getDeals, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  });

  // Group deals by stage
  const dealsByStage = DEAL_STAGES.reduce<Record<DealStage, Deal[]>>(
    (acc, stage) => {
      acc[stage] = deals.filter((deal) => deal.stage === stage);
      return acc;
    },
    {} as Record<DealStage, Deal[]>
  );

  // Mutation for updating deal stage
  const updateDealMutation = useMutation(
    (params: { id: string; data: { stage: DealStage } }) =>
      updateDeal(params.id, params.data),
    {
      onSuccess: (updatedDeal, { id, data }) => {
        // Optimistic update instead of full invalidation
        queryClient.setQueryData("deals", (oldDeals: Deal[] | undefined) => {
          if (!oldDeals) return oldDeals;
          return oldDeals.map(deal => 
            deal.id === id ? { ...deal, ...data } : deal
          );
        });
        // Only invalidate the specific deal query if it exists
        queryClient.invalidateQueries(["deal", id]);
      },
    }
  );

  // Handle moving a deal to a different stage
  const handleMoveDeal = (dealId: string, newStage: DealStage) => {
    updateDealMutation.mutate({
      id: dealId,
      data: { stage: newStage },
    });
  };

  // Handle selecting a deal for detailed view
  const handleSelectDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    // Prefetch the deal to ensure we have the latest data
    queryClient.prefetchQuery(["deal", deal.id], () => getDealById(deal.id));
  };

  // Close the detail view
  const handleCloseDetail = () => {
    setSelectedDeal(null);
  };

  // Toggle column collapse/expand
  const toggleColumnCollapse = (stage: DealStage) => {
    // If this is an inactive column and we're expanding it while hideInactive is true,
    // we need to temporarily override the hideInactive behavior for this column
    const isInactive = inactiveStages.includes(stage);
    const isCurrentlyCollapsed = collapsedColumns.includes(stage);

    // First update the manually expanded columns if needed
    if (isInactive && hideInactive) {
      if (isCurrentlyCollapsed) {
        // Expanding the column - add to manually expanded columns
        setManuallyExpandedColumns((prev) => [...prev, stage]);
      } else {
        // Collapsing the column - remove from manually expanded columns
        setManuallyExpandedColumns((prev) => prev.filter((s) => s !== stage));
      }
    }

    // Then update the collapsed columns
    setCollapsedColumns((prev) => {
      if (isCurrentlyCollapsed) {
        // Expanding the column
        return prev.filter((s) => s !== stage);
      } else {
        // Collapsing the column
        return [...prev, stage];
      }
    });
  };

  // Check if a column is collapsed
  const isColumnCollapsed = (stage: DealStage) => {
    return collapsedColumns.includes(stage);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-error-light border border-error text-error px-4 py-3 rounded relative"
        role="alert"
      >
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline">
          {" "}
          Failed to load deals. Please try again later.
        </span>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="deal-board overflow-auto">
        {/* HubSpot source of truth notice */}
        {!isCompactView && (
          <div className="mb-4 mx-4 bg-primary-light border border-primary rounded-md p-3">
            <p className="text-xs text-primary-color">
              <strong>Note:</strong> Deal names can only be updated in HubSpot, as it remains our source of truth for CRM data. 
              To update a deal name, make the change in HubSpot then run a quick sync on the <a href="/data-management" className="underline hover:text-primary-dark">data management page</a>.
            </p>
          </div>
        )}
        
        {/* This div ensures we don't have excess space at the bottom */}
        <div className="deal-board-container w-full h-full relative">
          {/* Deal detail view (shown when a deal is selected) */}
          {selectedDeal && (
            <DealDetail
              dealId={selectedDeal.id}
              initialDealData={selectedDeal}
              onClose={handleCloseDetail}
            />
          )}

          {/* Main board with columns */}
          <div
            ref={boardRef}
            className={`deal-board__columns flex overflow-x-auto pb-3 ${
              isCompactView ? "space-x-0 w-full" : "space-x-1"
            }`}
          >
            {/* Render stages based on filters */}
            {DEAL_STAGES.map((stage) => {
              const deals = dealsByStage[stage] || [];
              const isInactive = inactiveStages.includes(stage);
              const isCollapsed = isColumnCollapsed(stage);

              // Hide columns based on filters
              if (
                isCompactView &&
                deals.length === 0 &&
                stage !== "Identified"
              ) {
                return null;
              }

              return (
                <DealColumn
                  key={stage}
                  stage={stage}
                  deals={deals}
                  onMoveDeal={handleMoveDeal}
                  onSelectDeal={handleSelectDeal}
                  isCompactView={isCompactView}
                  isCollapsed={isCollapsed}
                  onToggleCollapse={() => toggleColumnCollapse(stage)}
                  isInactive={isInactive}
                  hideInactive={hideInactive}
                />
              );
            })}
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default DealBoard;
