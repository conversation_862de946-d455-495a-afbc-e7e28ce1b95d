import React, { useState } from "react";
import { useDrag } from "react-dnd";
import { Deal, DealPriority } from "../../../types/crm-types";
import { DEAL_DRAG_TYPE } from "./DealColumn";
import {
  DocumentTextIcon,
  PencilIcon,
  LinkIcon,
  ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { useMutation, useQueryClient } from "react-query";
import { linkDealEstimate } from "../../../api/crm";
import EstimateLinkModal from "../DealEdit/EstimateLinkModal";
import { useQuery } from "react-query";
import { getDraftEstimates } from "../../../api/estimates";
import LinkedEstimateModal from "../DealEdit/LinkedEstimateModal";
import { Button } from "@/frontend/components/ui/Button";

interface DealCardProps {
  deal: Deal;
  onSelect: () => void;
  isCompactView?: boolean;
}

/**
 * Card component for displaying a deal
 */
const DealCard: React.FC<DealCardProps> = ({
  deal,
  onSelect,
  isCompactView = false
}) => {
  // Initialize state and hooks at the top
  const [showEstimateLinkModal, setShowEstimateLinkModal] = useState(false);
  const [showLinkedEstimateModal, setShowLinkedEstimateModal] = useState(false);
  const [selectedEstimate, setSelectedEstimate] = useState<any>(null);
  const [estimateType, setEstimateType] = useState<"internal" |"harvest">("internal"
  );
  const [linkError, setLinkError] = useState<string | undefined>(undefined);

  const queryClient = useQueryClient();

  // Fetch draft estimates for the modal
  const { data: draftEstimates = [] } = useQuery("draftEstimates",
    getDraftEstimates
  );

  // Set up drag source
  const [{ isDragging }, drag] = useDrag({
    type: DEAL_DRAG_TYPE,
    item: { id: deal.id, stage: deal.stage },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging()
    })
  });


  // Mutation for linking estimates
  const linkEstimateMutation = useMutation(
    (params: {estimateId: string;estimateType:"internal" |"harvest";}) =>
    linkDealEstimate(deal.id, params.estimateId, params.estimateType),
    {
      onSuccess: (result) => {
        if (result.success) {
          // Close the modal
          setShowEstimateLinkModal(false);
          setLinkError(undefined);

          // Update the deal in the cached deals list if the response includes the updated deal
          if (result.deal) {
            queryClient.setQueryData("deals", (oldDeals: Deal[] | undefined) => {
              if (!oldDeals) return oldDeals;
              return oldDeals.map((d) => d.id === deal.id ? result.deal! : d);
            });
          }

          // Refresh specific deal and estimate data
          queryClient.invalidateQueries(["deal", deal.id]);
          queryClient.invalidateQueries(["deal-estimates", deal.id]);
          queryClient.invalidateQueries(["dealFieldOwnership", deal.id]);

          // Also invalidate the deals list to ensure the board refreshes
          queryClient.invalidateQueries("deals");
          queryClient.invalidateQueries("draftEstimates");
          // IMPORTANT: Also invalidate the batch estimates query used by EnhancedDealBoard
          queryClient.invalidateQueries(['all-deal-estimates-batch']);
        } else {
          // Set error message if the operation wasn"t successful
          setLinkError(result.error ||"Failed to link estimate");
        }
      },
      onError: (error: Error) => {
        // Set error message on mutation error
        setLinkError(error.message ||"Failed to link estimate");
      }
    }
  );

  // Get available draft estimates (excluding already linked ones)
  const getAvailableDraftEstimates = () => {
    const linkedDraftIds =
    deal.estimates?.filter((e) => e.type ==="internal").map((e) => e.id) || [];

    return draftEstimates.filter((e: any) => !linkedDraftIds.includes(e.uuid));
  };


  // Format currency value
  const formatCurrency = (value?: number, currency?: string): string => {
    if (value === undefined) return"N/A";

    return new Intl.NumberFormat("en-AU", {
      style:"currency",
      currency: currency ||"AUD",
      maximumFractionDigits: 0
    }).format(value);
  };

  // Format currency in a more compact way for compact view
  const formatCompactCurrency = (value?: number, currency?: string): string => {
    if (value === undefined) return"N/A";

    // For values less than 1000, just show the number with $ sign
    if (value < 1000) {
      return"$" + value;
    }

    // For values 1000-999999, show as $Xk (e.g., $10k, $100k)
    if (value < 1000000) {
      return"$" + Math.round(value / 1000) +"k";
    }

    // For values 1000000+, show as $XM (e.g., $1M, $10M)
    return"$" + (value / 1000000).toFixed(1) +"M";
  };

  // Get priority badge color
  const getPriorityColor = (priority?: DealPriority): string => {
    switch (priority) {
      case"High":
        return"bg-error-light text-error";
      case"Medium":
        return"bg-warning-light text-warning";
      case"Low":
        return"bg-success-light text-success";
      default:
        return"bg-surface-alt text-primary";
    }
  };

  // Format date to local format
  const formatDate = (dateString?: string): string => {
    if (!dateString) return"No date set";

    return new Date(dateString).toLocaleDateString("en-AU", {
      day:"numeric",
      month:"short",
      year:"numeric"
    });
  };

  // Format date to short format (dd/mm)
  const formatShortDate = (dateString?: string): string => {
    if (!dateString) return"";
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}`;
  };

  // Check if date is within 30 days
  const isCloseDateWithin30Days = (dateString?: string): boolean => {
    if (!dateString) return false;

    const closeDate = new Date(dateString);
    const now = new Date();
    const diffTime = closeDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays >= 0 && diffDays <= 30;
  };

  // Get days remaining to close date
  const getDaysRemaining = (dateString?: string): string => {
    if (!dateString) return"";

    const closeDate = new Date(dateString);
    const now = new Date();
    const diffTime = closeDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return"Overdue";
    if (diffDays === 0) return"Today";
    if (diffDays === 1) return"1 day";
    return `${diffDays} days`;
  };

  // Handle right-click (context menu)
  const handleContextMenu = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(`/crm/deals/${deal.id}`,"_blank");
  };

  // Handle view estimate click (show estimate details modal)
  const handleViewEstimate = (e: any) => {
    e.stopPropagation(); // Prevent card selection

    if (deal.estimates && deal.estimates.length > 0) {
      const firstEstimate = deal.estimates[0];
      setSelectedEstimate(firstEstimate);
      setEstimateType(firstEstimate.type);
      setShowLinkedEstimateModal(true);
    }
  };

  // Handle edit button click
  const handleEditClick = (e: any) => {
    e.stopPropagation(); // Prevent card selection
    onSelect(); // Use the existing onSelect to open the edit modal
  };

  // Handle link estimate click - opens the estimate link modal
  const handleLinkEstimateClick = (e: any) => {
    e.stopPropagation(); // Prevent card selection
    setEstimateType("internal"); // Default to internal estimates
    setShowEstimateLinkModal(true);
  };

  // Handle linking an estimate
  const handleLinkEstimate = (
  estimateId: string,
  type:"internal") =>
  {
    linkEstimateMutation.mutate({ estimateId, estimateType:"internal" });
  };

  // Render compact view
  if (isCompactView) {
    return (
      <>
        <div
          ref={drag}
          className={`deal-card deal-card--compact bg-surface-card rounded-md shadow-sm border border-default p-1 px-2 cursor-grab ${
          isDragging ?"opacity-50" :""}`
          }
          onClick={onSelect}
          onContextMenu={handleContextMenu}
          title="Right-click to open in advanced edit mode">

          {/* Deal name - compact version */}
          <div className="flex flex-col">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-primary truncate max-w-[75%]">
                {deal.name}
              </h4>

              {/* Estimate badge for compact view */}
              <div className="flex items-center space-x-1">
                {deal.estimates && deal.estimates.length > 0 ?
                <Button variant="ghost"
                onClick={handleViewEstimate}
                className="flex-shrink-0"
                title="View linked estimate">

                    <DocumentTextIcon className="w-3 h-3" />
                  </Button> :

                <Button variant="ghost"
                onClick={handleLinkEstimateClick}
                className="flex-shrink-0"
                title="Link to estimate">

                    <LinkIcon className="w-3 h-3" />
                  </Button>
                }
                <Button variant="ghost"
                onClick={handleEditClick}
                className="flex-shrink-0"
                title="Edit deal">

                  <PencilIcon className="w-3 h-3" />
                </Button>
                <a
                  href={`/crm/deals/${deal.id}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    return true;
                  }}
                  className="flex-shrink-0 text-muted hover:text-primary dark:hover:text-subtle focus:outline-none"
                  title="Advanced edit">

                  <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                </a>
              </div>
            </div>

            {/* Value and probability on second line */}
            <div className="flex justify-between items-center mt-0.5">
              <div className="text-2xs font-medium text-secondary">
                {formatCompactCurrency(deal.value, deal.currency)}
              </div>

              <div className="flex items-center space-x-1">
                {/* Priority indicator (colored dot) */}
                {deal.priority &&
                <span
                  className={`inline-block w-2 h-2 rounded-full ${
                  deal.priority ==="High" ?"bg-error-light0" :
                  deal.priority ==="Medium" ?"bg-warning-light0" :"bg-success-light0"}`
                  }
                  title={`Priority: ${deal.priority}`}>
                </span>
                }

                {/* Probability badge */}
                {deal.probability !== undefined &&
                <div className="text-2xs bg-primary-light text-primary px-0.5 rounded">
                    {Math.round(deal.probability * 100)}%
                  </div>
                }

                {/* Expected close date (only days if within 30 days) */}
                {deal.expectedCloseDate &&
                <div className="text-2xs text-muted">
                    {isCloseDateWithin30Days(deal.expectedCloseDate) ?
                  getDaysRemaining(deal.expectedCloseDate) :
                  formatShortDate(deal.expectedCloseDate)}
                  </div>
                }
              </div>
            </div>
          </div>
        </div>

        {/* Estimate Link Modal - shared across views */}
        <EstimateLinkModal
          isOpen={showEstimateLinkModal}
          onClose={() => {
            setShowEstimateLinkModal(false);
            setLinkError(undefined); // Clear error when closing modal
          }}
          estimateType="internal"
          draftEstimates={getAvailableDraftEstimates()}
          onLinkEstimate={handleLinkEstimate}
          isLoading={linkEstimateMutation.isLoading}
          error={linkError} />


        {/* Linked Estimate Modal - shared across views */}
        {selectedEstimate &&
        <LinkedEstimateModal
          isOpen={showLinkedEstimateModal}
          onClose={() => {
            setShowLinkedEstimateModal(false);
            // Refresh data after potential unlink
            queryClient.invalidateQueries(["deal", deal.id]);
            queryClient.invalidateQueries(["deal-estimates", deal.id]);
          }}
          deal={deal}
          estimate={selectedEstimate}
          estimateType={estimateType} />

        }
      </>);

  }

  // Render normal view
  return (
    <>
      <div
        ref={drag}
        className={`deal-card bg-surface-card rounded-lg shadow-sm border border-default p-1.5 cursor-grab ${
        isDragging ?"opacity-50" :""}`
        }
        onClick={onSelect}
        onContextMenu={handleContextMenu}
        title="Right-click to open in advanced edit mode">

        {/* Deal name and estimate badge */}
        <div className="mb-0.5">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-primary truncate max-w-[90%]">
              {deal.name}
            </h4>

            {/* Estimate badge */}
            {deal.estimates && deal.estimates.length > 0 &&
            <div
              className="flex-shrink-0 ml-1"
              title={`Linked to ${deal.estimates.length} estimate${
              deal.estimates.length > 1 ?"s" :""}`
              }>

                <DocumentTextIcon className="w-4 h-4 text-success" />
              </div>
            }
          </div>

          {deal.company &&
          <p className="text-xs text-muted truncate max-w-full">
              {deal.company.name}
            </p>
          }
        </div>

        {/* Deal value and probability */}
        <div className="flex justify-between items-center mb-0.5">
          <div className="text-xs font-medium">
            {formatCurrency(deal.value, deal.currency)}
          </div>
          {deal.probability !== undefined &&
          <div className="text-xs bg-primary-light text-primary px-1 py-0.5 rounded-full">
              {Math.round(deal.probability * 100)}%
            </div>
          }
        </div>

        {/* Deal metadata */}
        <div className="flex justify-between items-center text-xs text-muted mb-0.5">
          {/* Expected close date */}
          <div>
            {deal.expectedCloseDate &&
            <div className="flex items-center">
                <svg
                className="w-2 h-2 mr-0.5"
                fill="currentColor"
                viewBox="0 0 20 20">

                  <path
                  fillRule="evenodd"
                  d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                  clipRule="evenodd" />

                </svg>
                {formatDate(deal.expectedCloseDate)}
              </div>
            }
          </div>

          {/* Priority badge */}
          {deal.priority &&
          <span
            className={`px-1 py-0.5 rounded-full text-xs ${getPriorityColor(
              deal.priority
            )}`}>

              {deal.priority}
            </span>
          }
        </div>

        {/* Action buttons */}
        <div className="flex justify-between space-x-2 border-t border-subtle pt-1.5">
          {/* Quick edit button */}
          <Button variant="secondary"
          onClick={handleEditClick}

          title="Quick edit deal properties">

            <PencilIcon className="w-3.5 h-3.5" />
            <span className="sr-only">Quick Edit</span>
          </Button>

          {/* Estimate button */}
          {deal.estimates && deal.estimates.length > 0 ?
          <Button variant="success"
          onClick={handleViewEstimate}
          className="flex-1"
          title="View linked estimate">

              <DocumentTextIcon className="w-3.5 h-3.5 mr-1" />
              View Estimate
            </Button> :

          <Button variant="outline"
          onClick={handleLinkEstimateClick}
          className="flex-1"
          title="Link to estimate">

              <LinkIcon className="w-3.5 h-3.5 mr-1" />
              Link Estimate
            </Button>
          }

          {/* View/edit button */}
          <a
            href={`/crm/deals/${deal.id}`}
            onClick={(e) => {
              e.stopPropagation();
              return true;
            }}
            className=""
            title="View and edit deal in detail">

            <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5" />
            <span className="sr-only">View/Edit</span>
          </a>
        </div>
      </div>

      {/* Estimate Link Modal */}
      <EstimateLinkModal
        isOpen={showEstimateLinkModal}
        onClose={() => {
          setShowEstimateLinkModal(false);
          setLinkError(undefined); // Clear error when closing modal
        }}
        estimateType="internal"
        draftEstimates={getAvailableDraftEstimates()}
        onLinkEstimate={handleLinkEstimate}
        isLoading={linkEstimateMutation.isLoading}
        error={linkError} />


      {/* Linked Estimate Modal */}
      {selectedEstimate &&
      <LinkedEstimateModal
        isOpen={showLinkedEstimateModal}
        onClose={() => {
          setShowLinkedEstimateModal(false);
          // Refresh data after potential unlink
          queryClient.invalidateQueries(["deal", deal.id]);
          queryClient.invalidateQueries(["deal-estimates", deal.id]);
        }}
        deal={deal}
        estimate={selectedEstimate}
        estimateType={estimateType} />

      }
    </>);

};

export default DealCard;