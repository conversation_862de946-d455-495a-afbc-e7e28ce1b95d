import React, { useMemo } from "react";
import { useDrop } from "react-dnd";
import { Deal, DealStage } from "../../../types/crm-types";
import DealCard from "./DealCard";

// Define the drag item type
import { Button } from "@/frontend/components/ui/Button";export const DEAL_DRAG_TYPE = "deal";

interface DealColumnProps {
  stage: DealStage;
  deals: Deal[];
  onMoveDeal: (dealId: string, newStage: DealStage) => void;
  onSelectDeal: (deal: Deal) => void;
  isCompactView?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse: () => void;
  isInactive?: boolean;
  hideInactive?: boolean;
}

/**
 * Column component for a specific deal stage
 */
const DealColumn: React.FC<DealColumnProps> = ({
  stage,
  deals,
  onMoveDeal,
  onSelectDeal,
  isCompactView = false,
  isCollapsed = false,
  onToggleCollapse,
  isInactive = false,
  hideInactive = false
}) => {
  // Set up drop target for this column
  const [{ isOver }, drop] = useDrop({
    accept: DEAL_DRAG_TYPE,
    drop: (item: {id: string;stage: DealStage;}) => {
      // Only move if the stage is different
      if (item.stage !== stage) {
        onMoveDeal(item.id, stage);
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver()
    })
  });

  // Get stage color based on stage name
  const getStageColor = (stage: DealStage): string => {
    switch (stage) {
      case "Identified":
        return "bg-primary-light";
      case "Qualified":
        return "bg-primary-light";
      case "Solution proposal":
        return "bg-accent-light";
      case "Solution presentation":
        return "bg-accent-light";
      case "Objection handling":
        return "bg-warning-light";
      case "Finalising terms":
        return "bg-warning-light";
      case "Closed won":
        return "bg-success-light";
      case "Closed lost":
        return "bg-error-light";
      case "Abandoned":
        return "bg-surface-alt";
      default:
        return "bg-surface-alt";
    }
  };

  // Get stage text color based on stage name
  const getStageTextColor = (stage: DealStage): string => {
    switch (stage) {
      case "Identified":
        return "text-primary";
      case "Qualified":
        return "text-primary";
      case "Solution proposal":
        return "text-secondary";
      case "Solution presentation":
        return "text-secondary";
      case "Objection handling":
        return "text-warning";
      case "Finalising terms":
        return "text-accent";
      case "Closed won":
        return "text-success";
      case "Closed lost":
        return "text-error";
      case "Abandoned":
        return "text-primary";
      default:
        return "text-primary";
    }
  };

  // Format currency value
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
      maximumFractionDigits: 0
    }).format(value);
  };

  // Format currency in a more compact way for compact view
  const formatCompactCurrency = (value: number): string => {
    // For values less than 1000, just show the number with $ sign
    if (value < 1000) {
      return "$" + value;
    }

    // For values 1000-999999, show as $Xk (e.g., $10k, $100k)
    if (value < 1000000) {
      return "$" + Math.round(value / 1000) + "k";
    }

    // For values 1000000+, show as $XM (e.g., $1M, $10M)
    return "$" + (value / 1000000).toFixed(1) + "M";
  };

  // Calculate total value of deals in this column
  const totalValue = useMemo(() => {
    return deals.reduce((sum, deal) => sum + (deal.value || 0), 0);
  }, [deals]);

  // Determine column width based on state
  const getColumnWidthClass = () => {
    if (isCollapsed) {
      return "min-w-[40px]";
    }
    if (isInactive && hideInactive && !isCollapsed) {
      // This is a special case - an inactive column that was expanded while hideInactive is true
      return "inactive-expanded";
    }
    if (isCompactView) {
      return "deal-column--compact";
    }
    return "";
  };

  return (
    <div
      ref={drop}
      className={`deal-column ${getColumnWidthClass()} ${
      isOver ? "bg-surface-page" : ""} ${

      isCompactView ?
      "border-r border-default flex-1" :
      ""} transition-all duration-300 ease-in-out`
      }>

      {/* Column header */}
      <div
        className={`deal-column__header ${
        isCompactView ? "deal-column__header--compact" : ""} ${
        getStageColor(stage)} flex justify-between items-center`}>

        {isCollapsed ?
        <div className="w-full flex justify-center">
            <Button variant="ghost"
          onClick={onToggleCollapse}

          title="Expand column">

              <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor">

                <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7" />

              </svg>
            </Button>
          </div> :

        <div className="flex flex-col flex-grow">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <h3
                className={`font-medium ${
                isCompactView ? "text-2xs" : "text-sm"} ${
                getStageTextColor(stage)} truncate max-w-[90%]`}>

                  {stage}
                </h3>
                <span
                className={`${
                isCompactView ? "ml-0.5 text-2xs" : "ml-1 text-xs"} font-normal text-secondary`
                }>

                  ({deals.length})
                </span>
              </div>
              <Button variant="ghost"
            onClick={onToggleCollapse}
            className="ml-1"
            title="Collapse column">

                <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor">

                  <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7" />

                </svg>
              </Button>
            </div>
            <div
            className={`${
            isCompactView ? "text-2xs" : "text-xs"} font-medium text-secondary ${

            isCompactView ? "mt-0" : "mt-0.5"}`
            }>

              {isCompactView ?
            formatCompactCurrency(totalValue) :
            formatCurrency(totalValue)}
            </div>
          </div>
        }
      </div>

      {/* Column content - list of deal cards */}
      {!isCollapsed &&
      <div
        className={`deal-column__content ${
        isCompactView ? "px-1 py-0.5" : "p-1"} flex-1 overflow-y-auto`
        }>

          {deals.length > 0 ?
        <div className={`${isCompactView ? "space-y-1" : "space-y-1.5"}`}>
              {deals.map((deal) =>
          <DealCard
            key={deal.id}
            deal={deal}
            onSelect={() => onSelectDeal(deal)}
            isCompactView={isCompactView} />

          )}
            </div> :

        <div className="text-center py-2 text-subtle">
              <p className={`${isCompactView ? "text-2xs" : "text-xs"}`}>
                {isCompactView ? "Empty" : "No deals"}
              </p>
              {!isCompactView && <p className="text-xs">Drag deals here</p>}
            </div>
        }
        </div>
      }
    </div>);

};

export default DealColumn;