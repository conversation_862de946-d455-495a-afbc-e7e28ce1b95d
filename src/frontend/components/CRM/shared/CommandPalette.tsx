import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "react-query";
import {
  PlusIcon,
  UserIcon,
  BriefcaseIcon,
  BuildingOffice2Icon,
} from "@heroicons/react/24/outline";
import { getCompanies, getContacts, getDeals } from "../../../api/crm";
import { Command } from "cmdk";
import type { Company, Contact, Deal } from "../../../types/crm-types";

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
}

type SearchResult = {
  id: string;
  type: "deal" | "contact" | "company" | "action";
  title: string;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
};

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
}) => {
  const navigate = useNavigate();
  const [search, setSearch] = useState("");

  // Fetch data with proper caching and conditions
  const { data: companies = [] } = useQuery("companies", getCompanies, {
    enabled: isOpen,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
  const { data: contacts = [] } = useQuery("contacts", getContacts, {
    enabled: isOpen,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
  const { data: deals = [] } = useQuery("deals", getDeals, {
    enabled: isOpen,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });

  // Convert to search results
  const searchResults = useMemo((): SearchResult[] => {
    const results: SearchResult[] = [];

    // Quick actions
    if (!search || "create contact".includes(search.toLowerCase())) {
      results.push({
        id: "create-contact",
        type: "action",
        title: "Create New Contact",
        icon: UserIcon,
        action: () => {
          navigate("/crm/contacts?action=create");
          onClose();
        },
      });
    }

    // Search deals
    deals
      .filter(
        (deal) =>
          deal?.name?.toLowerCase().includes(search.toLowerCase()) ||
          deal?.company?.toLowerCase().includes(search.toLowerCase()),
      )
      .slice(0, 3)
      .forEach((deal) => {
        results.push({
          id: `deal-${deal.id}`,
          type: "deal",
          title: deal?.name || "Untitled Deal",
          subtitle: `${deal.stage} • $${(deal.value || 0).toLocaleString()}`,
          icon: BriefcaseIcon,
          action: () => {
            navigate(`/crm/deals/${deal.id}`);
            onClose();
          },
        });
      });

    // Search contacts
    contacts
      .filter(
        (contact) =>
          contact?.name?.toLowerCase().includes(search.toLowerCase()) ||
          contact?.email?.toLowerCase().includes(search.toLowerCase()),
      )
      .slice(0, 3)
      .forEach((contact) => {
        results.push({
          id: `contact-${contact.id}`,
          type: "contact",
          title: contact?.name || "Unnamed Contact",
          subtitle: contact?.email,
          icon: UserIcon,
          action: () => {
            navigate(`/crm/contacts?selected=${contact.id}`);
            onClose();
          },
        });
      });

    // Search companies
    companies
      .filter((company) =>
        company?.name?.toLowerCase().includes(search.toLowerCase()),
      )
      .slice(0, 3)
      .forEach((company) => {
        results.push({
          id: `company-${company.id}`,
          type: "company",
          title: company?.name || "Unnamed Company",
          subtitle: company?.industry,
          icon: BuildingOffice2Icon,
          action: () => {
            navigate(`/crm/companies?selected=${company.id}`);
            onClose();
          },
        });
      });

    return results;
  }, [search, deals, contacts, companies, navigate, onClose]);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        // This would be handled by the parent component
      }
      // Handle ESC key
      if (e.key === "Escape" && isOpen) {
        e.preventDefault();
        onClose();
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20"
      onClick={onClose}
    >
      <Command
        className="w-full max-w-2xl bg-surface-card dark:bg-surface-card rounded-xl shadow-2xl overflow-hidden animate-in"
        onClick={(e) => e.stopPropagation()}
      >
        <Command.Input
          value={search}
          onValueChange={setSearch}
          placeholder="Search deals, contacts, companies or type a command..."
          className="w-full px-6 py-4 text-lg bg-transparent border-b border-default dark:border-default focus:outline-none"
        />

        <Command.List className="max-h-96 overflow-y-auto p-2">
          {searchResults.length === 0 && search && (
            <Command.Empty className="text-center py-8 text-muted">
              No results found for "{search}"
            </Command.Empty>
          )}

          {searchResults.map((result) => (
            <Command.Item
              key={result.id}
              onSelect={result.action}
              className="flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer hover:bg-surface-alt dark:hover:bg-surface-elevated transition-colors"
            >
              <result.icon className="w-5 h-5 text-secondary dark:text-subtle" />
              <div className="flex-1">
                <div className="font-medium text-primary dark:text-primary">
                  {result.title}
                </div>
                {result.subtitle && (
                  <div className="text-sm text-muted dark:text-subtle">
                    {result.subtitle}
                  </div>
                )}
              </div>
              <kbd className="text-xs bg-surface-alt dark:bg-surface-alt px-2 py-1 rounded">
                ↵
              </kbd>
            </Command.Item>
          ))}
        </Command.List>

        <div className="border-t border-default dark:border-default px-4 py-2 text-xs text-muted dark:text-subtle">
          <span>
            Type to search • ↑↓ to navigate • ↵ to select • esc to close
          </span>
        </div>
      </Command>
    </div>
  );
};

export default CommandPalette;
