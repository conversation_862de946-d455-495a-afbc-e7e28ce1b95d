import React, { useState } from "react";
import { Contact, Company } from "../../../types/crm-types";

export interface DragData {
  entity: Contact | Company;
  entityType: "contact" | "company";
}

interface DraggableEntityProps {
  entity: Contact | Company;
  entityType: "contact" | "company";
  children: React.ReactNode;
  onDragStart?: (data: DragData) => void;
  onDragEnd?: () => void;
  onDrop?: (
    draggedData: DragData,
    targetEntity: Contact | Company,
    targetType: "contact" | "company",
  ) => void;
  className?: string;
}

const DraggableEntity: React.FC<DraggableEntityProps> = ({
  entity,
  entityType,
  children,
  onDragStart,
  onDragEnd,
  onDrop,
  className = "",
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragStart = (e: React.DragEvent) => {
    const dragData: DragData = { entity, entityType };
    console.log("DraggableEntity: Drag started", {
      entityType,
      entityId: entity.id,
    });

    // Safari requires both text/plain and custom data types
    e.dataTransfer.setData("text/plain", JSON.stringify(dragData));
    e.dataTransfer.setData("application/json", JSON.stringify(dragData));

    // Safari bug: "link" effectAllowed can prevent drop events!
    // Use 'all' or 'copyMove' instead
    e.dataTransfer.effectAllowed = "all";

    setIsDragging(true);
    onDragStart?.(dragData);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    onDragEnd?.();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("DraggableEntity: Drag enter", {
      entityType,
      entityId: entity.id,
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // This is CRITICAL - prevents default to allow drop
    e.stopPropagation(); // Safari needs this to properly handle nested drop zones

    // Safari needs explicit dropEffect setting
    if (e.dataTransfer) {
      // Use 'copy' instead of 'link' for better Safari compatibility
      e.dataTransfer.dropEffect = "copy";
    }

    if (!isDragOver) {
      console.log("DraggableEntity: Drag over", {
        entityType,
        entityId: entity.id,
      });
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only set dragOver to false if we're actually leaving the element
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Important for Safari
    setIsDragOver(false);

    console.log("DraggableEntity: Drop event triggered", {
      targetEntityType: entityType,
      targetEntityId: entity.id,
    });

    try {
      // Try to get data from both types (Safari compatibility)
      let dragDataString = e.dataTransfer.getData("application/json");
      if (!dragDataString) {
        dragDataString = e.dataTransfer.getData("text/plain");
      }

      console.log("DraggableEntity: Drag data retrieved", { dragDataString });

      if (!dragDataString) {
        console.log("DraggableEntity: No drag data found");
        return;
      }

      const draggedData: DragData = JSON.parse(dragDataString);
      console.log("DraggableEntity: Parsed drag data", draggedData);

      // Don't allow dropping on self
      if (draggedData.entity.id === entity.id) {
        console.log("DraggableEntity: Cannot drop on self");
        return;
      }

      // Don't allow dropping same type entities that don't make sense
      // (e.g., company on company is not implemented yet)
      if (draggedData.entityType === "company" && entityType === "company") {
        console.log("DraggableEntity: Company to company not supported");
        return;
      }

      console.log("DraggableEntity: Calling onDrop callback");
      onDrop?.(draggedData, entity, entityType);
    } catch (error) {
      console.error("DraggableEntity: Error handling drop:", error);
    }
  };

  const baseClasses = `
    transition-all duration-200 ease-in-out
    ${isDragging ? "opacity-50 scale-95 shadow-lg" : ""}
    ${isDragOver ? "ring-2 ring-primary ring-opacity-50 bg-primary-light dark:bg-primary-dark/20" : ""}
    ${className}
  `;

  return (
    <div
      draggable="true"
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className={baseClasses}
      data-entity-type={entityType}
      data-entity-id={entity.id}
      style={{
        WebkitUserDrag: "element",
        WebkitTouchCallout: "none",
        userSelect: "none",
      }}
    >
      <div style={{ pointerEvents: isDragging ? "none" : "auto" }}>
        {children}
      </div>

      {/* Visual indicator when dragging over */}
      {isDragOver && (
        <div className="absolute inset-0 pointer-events-none border-2 border-primary border-dashed rounded-lg bg-primary-light/50 dark:bg-primary-dark/30 flex items-center justify-center">
          <div className="bg-primary-light0 text-primary text-xs px-2 py-1 rounded-full font-medium">
            Drop to associate
          </div>
        </div>
      )}

      {/* Dragging indicator */}
      {isDragging && (
        <div className="absolute top-2 right-2 bg-primary-light0 text-primary text-xs px-2 py-1 rounded-full font-medium">
          Dragging...
        </div>
      )}
    </div>
  );
};

export default DraggableEntity;
