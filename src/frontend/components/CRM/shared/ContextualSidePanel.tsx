import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { getDealById as getDeal, updateDeal, getDealEstimates, linkDealEstimate } from "../../../api/crm";
import { formatCurrency, formatRelativeDate } from "../../../utils/format";
import { ArrowTopRightOnSquareIcon, LinkIcon, PlusIcon } from "@heroicons/react/24/outline";
import type { Deal, DealEstimate } from "../../../types/crm-types";
import EstimateLinkModal from "../DealEdit/EstimateLinkModal";
import { getDraftEstimates } from "../../../api/estimates";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface ContextualSidePanelProps {
  isOpen: boolean;
  entityType: "deal" | "contact" | "company" | null;
  entityId: string | null;
  onClose: () => void;
  onNavigate?: (path: string) => void;
}

export const ContextualSidePanel: React.FC<ContextualSidePanelProps> = ({
  isOpen,
  entityType,
  entityId,
  onClose,
  onNavigate
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const queryClient = useQueryClient();

  // Debug logging
  console.log('ContextualSidePanel render:', { isOpen, entityType, entityId });

  // Example: Deal view
  const { data: deal, isLoading, error } = useQuery(
    ['deal', entityId],
    () => getDeal(entityId!),
    {
      enabled: entityType === 'deal' && !!entityId,
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      onError: (err) => {
        console.error('Failed to fetch deal:', err);
      },
      onSuccess: (data) => {
        console.log('Successfully fetched deal:', data);
      }
    }
  );

  // Fetch draft estimates for linking
  const { data: draftEstimates = [] } = useQuery(
    'draftEstimates',
    getDraftEstimates,
    {
      enabled: entityType === 'deal' && !!entityId,
      staleTime: 5 * 60 * 1000, // 5 minutes cache to prevent rate limiting
      refetchOnWindowFocus: false
    }
  );

  // Fetch deal estimates
  const { data: estimateLinks = [], isLoading: isEstimatesLoading } = useQuery(
    ['deal-estimates", entityId],
    () => getDealEstimates(entityId!),
    {
      enabled: entityType === 'deal' && !!entityId,
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false
    }
  );

  // Enrich estimate links with details from draft estimates
  const estimates = React.useMemo(() => {
    return estimateLinks.map((link) => {
      if (link.type === 'internal") {
        const draftEstimate = draftEstimates.find((e) => e.uuid === link.estimateId);
        if (draftEstimate) {
          return {
            ...link,
            name: draftEstimate.projectName || "Unnamed Project",
            clientName: draftEstimate.clientName,
            status: draftEstimate.status,
            amount: draftEstimate.totalFees
          };
        }
      }
      // For harvest estimates or if draft estimate not found, return with defaults
      return {
        ...link,
        name: link.name || "Unknown Estimate",
        clientName: link.clientName || "No client specified",
        status: link.status || "Unknown",
        amount: link.amount
      };
    });
  }, [estimateLinks, draftEstimates]);

  // Note: Harvest estimates are not fetched since they can't be linked to deals anymore

  const updateDealMutation = useMutation(
    (updates: Partial<Deal>) => updateDeal(entityId!, updates),
    {
      onSuccess: (updatedDeal) => {
        // Optimistic update to deal cache
        queryClient.setQueryData(['deal", entityId], (oldDeal: Deal | undefined) => {
          return oldDeal ? { ...oldDeal, ...updatedDeal } : updatedDeal;
        });
        // Update the deal in the deals list without full invalidation
        queryClient.setQueryData('deals", (oldDeals: Deal[] | undefined) => {
          if (!oldDeals) return oldDeals;
          return oldDeals.map((deal) =>
          deal.id === entityId ? { ...deal, ...updatedDeal } : deal
          );
        });
      }
    }
  );

  // Unlinking is no longer supported - linking is permanent

  // Link estimate mutation
  const linkEstimateMutation = useMutation(
    ({ estimateId, estimateType }: {estimateId: string;estimateType: "internal";}) =>
    linkDealEstimate(entityId!, estimateId, estimateType),
    {
      onSuccess: (result) => {
        if (result.success) {
          // Update the deal in the cached deals list if the response includes the updated deal
          if (result.deal) {
            queryClient.setQueryData("deals", (oldDeals: Deal[] | undefined) => {
              if (!oldDeals) return oldDeals;
              return oldDeals.map((d) => d.id === entityId ? result.deal! : d);
            });
          }

          // Invalidate specific queries
          queryClient.invalidateQueries(['deal-estimates', entityId]);
          queryClient.invalidateQueries(['deal', entityId]);
          queryClient.invalidateQueries(['dealFieldOwnership', entityId]);
          queryClient.invalidateQueries('draftEstimates');

          // Also invalidate the deals list to ensure the board refreshes
          queryClient.invalidateQueries('deals');
          // IMPORTANT: Also invalidate the batch estimates query used by EnhancedDealBoard
          queryClient.invalidateQueries(['all-deal-estimates-batch']);

          setIsEstimateLinkModalOpen(false);
        }
      },
      onError: (error: any) => {
        console.error('Failed to link estimate:", error);
      }
    }
  );

  // Inline editing state
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');

  // Estimate linking state
  const [isEstimateLinkModalOpen, setIsEstimateLinkModalOpen] = useState(false);
  const estimateType = 'internal' as const; // Only internal estimates can be linked now

  const handleEdit = (field: string, value: any) => {
    setEditingField(field);
    setEditValue(value?.toString() || "");
  };

  const handleSave = (field: string) => {
    let processedValue: any = editValue;

    // Convert numeric fields to numbers
    if (field === 'value' || field === 'probability') {
      const numValue = parseFloat(editValue);
      processedValue = isNaN(numValue) ? undefined : numValue;
    }

    updateDealMutation.mutate({ [field]: processedValue });
    setEditingField(null);
  };

  // Estimate action handlers
  const handleViewEstimate = (estimate: DealEstimate) => {
    if (estimate.type === 'internal') {
      window.open(`/estimates/${estimate.estimateId}`, '_blank');
    } else {
      window.open(`https://onbord.harvestapp.com/estimates/${estimate.estimateId}`, '_blank');
    }
  };

  // Unlinking is no longer supported

  // Handle linking an estimate
  const handleLinkEstimate = (estimateId: string, type: "internal") => {
    linkEstimateMutation.mutate({ estimateId, estimateType: "internal" });
  };

  // Open modal for linking estimates
  const openEstimateLinkModal = (type: "internal") => {
    setIsEstimateLinkModalOpen(true);
  };

  // Get available draft estimates (excluding already linked ones)
  const getAvailableDraftEstimates = () => {
    const linkedDraftIds = estimates.
    filter((e) => e.type === 'internal').
    map((e) => e.estimateId);

    return draftEstimates.filter(
      (e: any) => !linkedDraftIds.includes(e.uuid)
    );
  };

  // Note: getAvailableHarvestEstimates removed since Harvest estimates can't be linked anymore

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    document.addEventListener('keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/20 z-40 lg:hidden"
        onClick={onClose} />

      
      {/* Panel */}
      <div className={`fixed right-0 top-0 h-full w-full lg:w-[480px] bg-surface-card shadow-2xl z-50 transform transition-transform duration-300 ${
      isOpen ? 'translate-x-0' : "translate-x-full"}`
      }>
        {/* Header */}
        <div className="sticky top-0 bg-surface-card border-b border-default z-10">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <Button variant="ghost"
              onClick={onClose}
              className="p-2">

                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
              
              {entityType === 'deal" &&
              <div>
                  <h2 className="text-lg font-semibold text-primary">
                    {deal?.name || "Loading Deal..."}
                  </h2>
                  <p className="text-sm text-muted">
                    {deal ?
                  `${deal?.company?.name || deal?.companyName || "No Company"} • ${deal?.stage || "No Stage"}` :
                  `Entity ID: ${entityId}`
                  }
                  </p>
                </div>
              }
            </div>

            <Button variant="ghost"
            onClick={() => onNavigate?.(`/crm/deals/${entityId}`)}>


              Open Full View →
            </Button>
          </div>

          {/* Tabs */}
          <div className="flex gap-1 px-4 pb-0">
            {['overview', 'estimates', 'activity', 'contacts", 'notes"].map((tab) =>
            <Button variant="primary"
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
            activeTab === tab ?
            'bg-surface-alt text-primary' :
            'text-secondary hover:text-primary dark:hover:text-primary'}`
            }>

                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto h-[calc(100%-120px)] p-4">
          {isLoading ?
          <div className="animate-pulse space-y-4">
              <div className="h-4 bg-surface-alt rounded w-3/4" />
              <div className="h-4 bg-surface-alt rounded w-1/2" />
            </div> :
          error ?
          <div className="text-center py-8">
              <div className="text-error mb-2">Error loading deal</div>
              <div className="text-sm text-muted">{error?.message || "Unknown error occurred"}</div>
            </div> :
          !deal ?
          <div className="text-center py-8">
              <div className="text-muted mb-2">No deal data found</div>
              <div className="text-sm text-subtle">Entity ID: {entityId}</div>
            </div> :

          <>
              {activeTab === 'overview' && deal &&
            <div className="space-y-6">
                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-surface-page rounded-lg p-4">
                      <div className="text-sm text-muted mb-1">Value</div>
                      <div className="flex items-baseline gap-2">
                        {editingField === 'value' ?
                    <Input
                      type="number"
                      value={editValue}
                      onChange={(e) => setEditValue(e.target.value)}
                      onBlur={() => handleSave('value')}
                      onKeyDown={(e) => e.key === 'Enter' && handleSave('value')}
                      className="text-2xl font-bold bg-transparent border-b-2 border-accent focus:outline-none"
                      autoFocus /> :


                    <div
                      className="text-2xl font-bold text-primary cursor-pointer hover:text-accent"
                      onClick={() => handleEdit("value", deal?.value)}>

                            {formatCurrency(deal?.value || 0)}
                          </div>
                    }
                      </div>
                    </div>
                    
                    <div className="bg-surface-page rounded-lg p-4">
                      <div className="text-sm text-muted mb-1">Probability</div>
                      <div className="flex items-center gap-2">
                        <div className="text-2xl font-bold text-primary">
                          {Math.round((deal?.probability || 0) * 100)}%
                        </div>
                        <div className="flex-1 bg-surface-alt rounded-full h-2">
                          <div
                        className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full"
                        style={{ width: `${(deal?.probability || 0) * 100}%` }} />

                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Linked Estimates Summary */}
                  {estimates.length > 0 &&
              <div className="bg-success-light/20 border border-success rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <LinkIcon className="w-4 h-4 text-success" />
                          <h3 className="text-sm font-medium text-success">
                            Linked Estimates ({estimates.length})
                          </h3>
                        </div>
                        <Button variant="ghost"
                  onClick={() => setActiveTab('estimates")}>


                          View All →
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {estimates.slice(0, 2).map((estimate) =>
                  <div
                    key={`${estimate.type}-${estimate.estimateId}`}
                    className="flex items-center justify-between p-2 bg-surface-card rounded border border-success">

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-primary truncate">
                                  {estimate.name || "Untitled Estimate"}
                                </span>
                                <span className={`px-1.5 py-0.5 text-xs font-medium rounded ${
                        estimate.type === 'internal' ?
                        'bg-primary-light text-primary-color/30' :
                        'bg-warning-light text-warning-dark/30'}`
                        }>
                                  {estimate.type === 'internal' ? 'Internal' : "Harvest"}
                                </span>
                              </div>
                              <div className="text-xs text-muted">
                                {estimate.clientName || "No client"}
                              </div>
                            </div>
                            {estimate.amount &&
                    <div className="text-sm font-medium text-success">
                                {formatCurrency(estimate.amount)}
                              </div>
                    }
                          </div>
                  )}
                        {estimates.length > 2 &&
                  <div className="text-xs text-center text-success pt-1">
                            +{estimates.length - 2} more estimate{estimates.length - 2 !== 1 ? 's' : ""}
                          </div>
                  }
                      </div>
                    </div>
              }

                  {/* Stage Progress */}
                  <div>
                    <h3 className="text-sm font-medium text-primary mb-3">Stage Progress</h3>
                    <div className="flex items-center gap-2">
                      {['Identified', 'Qualified', 'Proposal', 'Negotiation", 'Closed"].map((stage, idx) =>
                  <div key={stage} className="flex items-center flex-1">
                          <div className={`h-1 flex-1 rounded ${
                    idx <= 2 ? 'bg-accent' : "bg-surface-alt"}`
                    } />
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                    idx <= 2 ?
                    'bg-accent text-primary' :
                    'bg-surface-alt text-secondary'}`
                    }>
                            {idx + 1}
                          </div>
                        </div>
                  )}
                    </div>
                  </div>

                  {/* Key Dates */}
                  <div>
                    <h3 className="text-sm font-medium text-primary mb-3">Key Dates</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-secondary">Created</span>
                        <span className="text-sm font-medium">{deal?.createdAt ? formatRelativeDate(deal.createdAt) : "Unknown"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-secondary">Last Activity</span>
                        <span className="text-sm font-medium">{deal?.updatedAt ? formatRelativeDate(deal.updatedAt) : "Unknown"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-secondary">Expected Close</span>
                        <span className="text-sm font-medium text-accent">
                          {deal?.closeDate ? formatRelativeDate(deal.closeDate) : "Not set"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="flex gap-2">
                    <Button variant="secondary">
                      Send Email
                    </Button>
                  </div>
                </div>
            }

              {activeTab === 'estimates' &&
            <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-primary">
                      Linked Estimates ({estimates.length})
                    </h3>
                    <Button variant="primary"
                onClick={() => openEstimateLinkModal('internal')}>


                      <PlusIcon className="w-3.5 h-3.5 mr-1" />
                      Link Estimate
                    </Button>
                  </div>

                  {isEstimatesLoading ?
              <div className="space-y-3">
                      {[1, 2, 3].map((i) =>
                <div key={i} className="animate-pulse">
                          <div className="h-20 bg-surface-alt rounded-lg" />
                        </div>
                )}
                    </div> :
              estimates.length === 0 ?
              <div className="text-center py-12">
                      <LinkIcon className="w-12 h-12 text-subtle mx-auto mb-4" />
                      <h4 className="text-sm font-medium text-primary mb-2">
                        No Linked Estimates
                      </h4>
                      <p className="text-sm text-muted mb-4">
                        This deal doesn&apos;t have any linked estimates yet.
                      </p>
                      <div className="flex justify-center">
                        <Button variant="primary"
                  onClick={() => openEstimateLinkModal('internal")}>


                          <LinkIcon className="w-4 h-4 mr-2" />
                          Link Estimate
                        </Button>
                      </div>
                    </div> :

              <div className="space-y-3">
                      {estimates.map((estimate) =>
                <div
                  key={`${estimate.type}-${estimate.estimateId}`}
                  className="bg-surface-card border border-default rounded-lg p-4 hover:shadow-md transition-shadow">

                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-primary truncate">
                                  {estimate.name || "Untitled Estimate"}
                                </h4>
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        estimate.type === 'internal' ?
                        'bg-primary-light text-primary-color/30' :
                        'bg-warning-light text-warning-dark/30'}`
                        }>
                                  {estimate.type === 'internal' ? 'Internal' : "Harvest"}
                                </span>
                              </div>
                              <p className="text-sm text-secondary">
                                {estimate.clientName || "No client specified"}
                              </p>
                            </div>
                            {estimate.amount &&
                    <div className="text-right ml-3">
                                <div className="text-lg font-semibold text-success">
                                  {formatCurrency(estimate.amount)}
                                </div>
                              </div>
                    }
                          </div>

                          <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                            <div>
                              <span className="text-muted">Status:</span>
                              <span className="ml-2 capitalize text-primary">
                                {estimate.status || "Unknown"}
                              </span>
                            </div>
                            <div>
                              <span className="text-muted">Linked:</span>
                              <span className="ml-2 text-primary">
                                {estimate.linkedAt ?
                        formatRelativeDate(estimate.linkedAt) :
                        'Unknown date'}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-end space-x-2 pt-3 border-t border-subtle">
                            <Button variant="primary"
                    onClick={() => handleViewEstimate(estimate)}>


                              <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                              View
                            </Button>
                          </div>
                        </div>
                )}
                    </div>
              }
                </div>
            }

              {activeTab === 'activity' &&
            <div className="space-y-4">
                  <div className="text-center py-8 text-muted">
                    Activity timeline coming soon...
                  </div>
                </div>
            }

              {activeTab === 'contacts' &&
            <div className="space-y-4">
                  <div className="text-center py-8 text-muted">
                    Related contacts coming soon...
                  </div>
                </div>
            }

              {activeTab === 'notes' &&
            <div className="space-y-4">
                  <div className="text-center py-8 text-muted">
                    Notes section coming soon...
                  </div>
                </div>
            }
            </>
          }
        </div>
      </div>

      {/* Estimate Link Modal */}
      {entityType === 'deal' &&
      <EstimateLinkModal
        isOpen={isEstimateLinkModalOpen}
        onClose={() => setIsEstimateLinkModalOpen(false)}
        estimateType={estimateType}
        draftEstimates={getAvailableDraftEstimates()}
        onLinkEstimate={handleLinkEstimate}
        isLoading={linkEstimateMutation.isLoading}
        dealName={deal?.name}
        dealCompanyName={deal?.company?.name} />

      }
    </>);

};

export default ContextualSidePanel;