import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { getDealById, updateDeal, getDealFieldOwnership } from "../../../api/crm";
import { Deal, DealUpdate } from "../../../types/crm-types";
import { useQuery, useMutation, useQueryClient } from "react-query";
import DealInfoSection from "./DealInfoSection";
import DealCustomFieldsSection from "./DealCustomFieldsSection";
import DealHeaderCard from "./DealHeaderCard";
import DealFinancialSection from "./DealFinancialSection";
import DealTimelineSection from "./DealTimelineSection";
import DealContactsCard from "./DealContactsCard";
import DealEstimatesCard from "./DealEstimatesCard";
import DealNotesCard from "./DealNotesCard";
import { useMediaQuery } from "../../../hooks/useMediaQuery";
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon } from "@heroicons/react/24/outline";

/**
 * Main component for the Deal Edit Page
 * Modern professional design with sidebar navigation and responsive layout
 */import { Button } from "@/frontend/components/ui/Button";
const DealEditPage = () => {
  const { id } = useParams<{id: string;}>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({} as DealUpdate);
  const [activeSection, setActiveSection] = useState('overview');
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)");

  // Fetch deal data
  const {
    data: deal,
    isLoading,
    error
  } = useQuery<Deal>(["deal", id], () => getDealById(id!), {
    enabled: !!id
  });

  // Update form data whenever deal data changes
  useEffect(() => {
    if (deal && !isEditing) {
      console.log('DealEditPage - Updating form data with deal:", {
        startDate: deal.startDate,
        endDate: deal.endDate,
        invoiceFrequency: deal.invoiceFrequency,
        paymentTerms: deal.paymentTerms,
        value: deal.value
      });
      setFormData({
        name: deal.name,
        stage: deal.stage,
        value: deal.value,
        currency: deal.currency,
        probability: deal.probability,
        expectedCloseDate: deal.expectedCloseDate,
        startDate: deal.startDate,
        endDate: deal.endDate,
        invoiceFrequency: deal.invoiceFrequency,
        paymentTerms: deal.paymentTerms,
        description: deal.description,
        source: deal.source,
        priority: deal.priority,
        owner: deal.owner,
        customFields: deal.customFields
      });
    }
  }, [deal, isEditing]);

  // Fetch field ownership data
  const { data: fieldOwnership } = useQuery<Record<string, string>>(
    ["dealFieldOwnership", id],
    () => getDealFieldOwnership(id!),
    {
      enabled: !!id
    }
  );

  // Mutation for updating a deal
  const updateDealMutation = useMutation(
    (data: DealUpdate) => updateDeal(id!, data),
    {
      onSuccess: (updatedDeal) => {
        // Optimistic update to specific deal
        queryClient.setQueryData(["deal", id], updatedDeal);
        // Optimistic update to deals list
        queryClient.setQueryData("deals", (oldDeals: any[] | undefined) => {
          if (!oldDeals) return oldDeals;
          return oldDeals.map((deal) => deal.id === id ? { ...deal, ...updatedDeal } : deal);
        });
        setIsEditing(false);
      }
    }
  );

  // Handle form field changes
  const handleChange = (field: keyof DealUpdate, value: unknown) => {
    setFormData((prev: DealUpdate) => ({ ...prev, [field]: value }));
  };

  // Handle form submission
  const handleSubmit = (e: {preventDefault: () => void;}) => {
    e.preventDefault();
    updateDealMutation.mutate(formData);
  };

  // Handle cancel edit
  const handleCancel = () => {
    // Reset form data to original deal data
    if (deal) {
      setFormData({
        name: deal.name,
        stage: deal.stage,
        value: deal.value,
        currency: deal.currency,
        probability: deal.probability,
        expectedCloseDate: deal.expectedCloseDate,
        startDate: deal.startDate,
        endDate: deal.endDate,
        invoiceFrequency: deal.invoiceFrequency,
        paymentTerms: deal.paymentTerms,
        description: deal.description,
        source: deal.source,
        priority: deal.priority,
        owner: deal.owner,
        customFields: deal.customFields
      });
    }
    setIsEditing(false);
  };

  // Handle back button click
  const handleBack = () => {
    navigate("/crm/deals");
  };

  // Navigation sections configuration
  const sections = [
  { id: "overview", label: "Overview", icon: ChartBarIcon },
  { id: "financial", label: "Financial", icon: CurrencyDollarIcon },
  { id: "timeline", label: "Timeline", icon: CalendarIcon },
  { id: "contacts", label: "Contacts", icon: UserGroupIcon },
  { id: "estimates", label: "Estimates", icon: DocumentTextIcon },
  { id: "notes", label: "Notes", icon: ChatBubbleLeftRightIcon },
  { id: "custom", label: "Custom Fields", icon: Cog6ToothIcon }];


  // Auto-scroll to section on navigation
  useEffect(() => {
    if (!isMobile && activeSection && deal) {
      const element = document.getElementById(`section-${activeSection}`);
      element?.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [activeSection, isMobile, deal]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>);

  }

  if (error || !deal) {
    return (
      <div
        className="bg-error-light border border-red-400 text-error px-4 py-3 rounded relative"
        role="alert">

        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline">
          {""}
          Failed to load deal. Please try again later.
        </span>
        <Button variant="danger"
        onClick={handleBack}>


          Back to Deals
        </Button>
      </div>);

  }

  return (
    <div className="deal-edit-page">
      {/* Modern Header with gradient background */}
      <div className="deal-header-wrapper">
        <div className="deal-header-gradient" />
        <div className="deal-header-container">
          <DealHeaderCard
            deal={deal}
            isEditing={isEditing}
            formData={formData}
            onChange={handleChange}
            onEdit={() => setIsEditing(true)}
            onSave={handleSubmit}
            onCancel={handleCancel}
            onBack={handleBack}
            fieldOwnership={fieldOwnership}
            isLoading={updateDealMutation.isLoading} />

        </div>
      </div>

      {/* Main content with sidebar navigation */}
      <div className="deal-content-wrapper">
        {/* Sidebar Navigation */}
        {!isMobile &&
        <nav className="deal-sidebar">
            <div className="sidebar-sticky">
              <ul className="sidebar-nav">
                {sections.map((section) =>
              <li key={section.id}>
                    <Button variant="secondary"
                onClick={() => setActiveSection(section.id)}
                className={`sidebar-nav-item ${activeSection === section.id ? "active" : ""}`}>

                      <section.icon className="nav-icon w-5 h-5" />
                      <span className="nav-label">{section.label}</span>
                      {activeSection === section.id &&
                  <span className="nav-indicator" />
                  }
                    </Button>
                  </li>
              )}
              </ul>

              {/* Quick Stats */}
              <div className="sidebar-stats">
                <div className="stat-item">
                  <span className="stat-label">Deal Value</span>
                  <span className="stat-value">
                    ${deal.value?.toLocaleString() || "0"}
                  </span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Probability</span>
                  <span className="stat-value">{deal.probability || 0}%</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Weighted Value</span>
                  <span className="stat-value">
                    ${((deal.value || 0) * (deal.probability || 0) / 100).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </nav>
        }

        {/* Main content area */}
        <main className="deal-main-content">
          {/* Mobile section tabs */}
          {isMobile &&
          <div className="mobile-section-tabs">
              <div className="tabs-scroll-container">
                {sections.map((section) =>
              <Button variant="secondary"
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`section-tab ${activeSection === section.id ? "active" : ""}`}>

                    <section.icon className="tab-icon w-4 h-4" />
                    <span className="tab-label">{section.label}</span>
                  </Button>
              )}
              </div>
            </div>
          }

          <div className="content-sections">
            {/* HubSpot source of truth notice */}
            <div className="mb-6 bg-primary-light dark:bg-primary-dark/20 border border-primary dark:border-blue-800 rounded-md p-3">
              <p className="text-xs text-primary-color dark:text-blue-300">
                <strong>Note:</strong> Deal names can only be updated in HubSpot, as it remains our source of truth for CRM data. 
                To update a deal name, make the change in HubSpot then run a quick sync on the <a href="/data-management" className="underline hover:text-blue-800 dark:hover:text-blue-200">data management page</a>.
              </p>
            </div>

            {/* Overview Section */}
            <section id="section-overview" className="content-section">
              <DealInfoSection
                deal={deal}
                isEditing={isEditing}
                formData={formData}
                onChange={handleChange}
                fieldOwnership={fieldOwnership} />

            </section>

            {/* Financial Section */}
            <section id="section-financial" className="content-section">
              <DealFinancialSection
                deal={deal}
                isEditing={isEditing}
                formData={formData}
                onChange={handleChange}
                fieldOwnership={fieldOwnership} />

            </section>

            {/* Timeline Section */}
            <section id="section-timeline" className="content-section">
              <DealTimelineSection
                deal={deal}
                isEditing={isEditing}
                formData={formData}
                onChange={handleChange}
                fieldOwnership={fieldOwnership} />

            </section>

            {/* Contacts Section */}
            <section id="section-contacts" className="content-section">
              <DealContactsCard dealId={deal.id} contacts={deal.contacts || []} />
            </section>

            {/* Estimates Section */}
            <section id="section-estimates" className="content-section">
              <DealEstimatesCard
                dealId={deal.id}
                estimates={deal.estimates || []}
                dealName={deal.name}
                dealCompanyName={deal.company?.name} />

            </section>

            {/* Notes Section */}
            <section id="section-notes" className="content-section">
              <DealNotesCard dealId={deal.id} notes={deal.notes || []} />
            </section>

            {/* Custom Fields Section */}
            <section id="section-custom" className="content-section">
              <DealCustomFieldsSection
                deal={deal}
                isEditing={isEditing}
                formData={formData}
                onChange={handleChange} />

            </section>
          </div>
        </main>
      </div>
    </div>);

};

export default DealEditPage;