import React, { useState, useEffect, useMemo } from "react";
import { useQuery } from "react-query";
import { getDeals } from "../../../api/crm";
import { Deal, DealStage } from "../../../types/crm-types";
import { format, parseISO } from "date-fns";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";

// Define deal stages in pipeline order
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";const DEAL_STAGES: DealStage[] = [
"Identified",
"Qualified",
"Solution proposal",
"Solution presentation",
"Objection handling",
"Finalising terms",
"Closed won",
"Closed lost",
"Abandoned"];


// Closed deal stages to filter out
const CLOSED_STAGES: DealStage[] = ["Closed won", "Closed lost", "Abandoned"];

/**
 * Calculate similarity score between two strings
 * Returns a score between 0 and 1, where 1 is an exact match
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0;

  const s1 = str1.toLowerCase().trim();
  const s2 = str2.toLowerCase().trim();

  // Exact match
  if (s1 === s2) return 1;

  // Check if one contains the other
  if (s1.includes(s2) || s2.includes(s1)) return 0.8;

  // Check for common words
  const words1 = s1.split(/\s+/);
  const words2 = s2.split(/\s+/);
  const commonWords = words1.filter((word) => words2.includes(word));
  const commonScore = commonWords.length / Math.max(words1.length, words2.length);

  // Simple character-based similarity
  const maxLen = Math.max(s1.length, s2.length);
  let matches = 0;
  for (let i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) matches++;
  }
  const charScore = matches / maxLen;

  // Return weighted average
  return commonScore * 0.7 + charScore * 0.3;
};

interface DealSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDeal: (deal: Deal) => void;
  estimateClientName?: string;
  estimateProjectName?: string;
}

/**
 * Modal for selecting a deal to populate estimate form fields
 */
const DealSelectionModal: React.FC<DealSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectDeal,
  estimateClientName,
  estimateProjectName
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedDealId, setSelectedDealId] = useState<string>("");

  // Fetch all deals and filter out closed ones
  const { data: allDeals = [], isLoading: isDealsLoading } = useQuery(
    "deals",
    getDeals,
    {
      enabled: isOpen, // Only fetch when modal is open
      staleTime: 30000 // 30 seconds
    }
  );

  // Filter out closed deals
  const activeDeals = useMemo(() => {
    return allDeals.filter(
      (deal) => !CLOSED_STAGES.includes(deal.stage as DealStage)
    );
  }, [allDeals]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm("");
      setSelectedDealId("");
    }
  }, [isOpen]);

  // Calculate suggested matches based on similarity
  const suggestedMatches = useMemo(() => {
    if (!estimateClientName && !estimateProjectName) return [];
    if (activeDeals.length === 0) return [];

    const dealsWithScores = activeDeals.map((deal) => {
      let maxScore = 0;

      // Calculate similarity with client name
      if (estimateClientName) {
        const companyScore = deal.company?.name ?
        calculateSimilarity(estimateClientName, deal.company.name) :
        0;
        maxScore = Math.max(maxScore, companyScore);

        // Also check if deal name contains client name
        const dealNameScore = calculateSimilarity(estimateClientName, deal.name);
        maxScore = Math.max(maxScore, dealNameScore);
      }

      // Calculate similarity with project name
      if (estimateProjectName) {
        const projectScore = calculateSimilarity(estimateProjectName, deal.name);
        maxScore = Math.max(maxScore, projectScore);
      }

      return { deal, score: maxScore };
    });

    // Sort by score and take top 3 matches with score > 0.3
    return dealsWithScores.
    filter((item) => item.score > 0.3).
    sort((a, b) => b.score - a.score).
    slice(0, 3).
    map((item) => item.deal);
  }, [estimateClientName, estimateProjectName, activeDeals]);

  // Filter and sort deals based on search term and pipeline order
  const filteredDeals = useMemo(() => {
    // Start with active deals
    let deals = activeDeals;

    // If searching, filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      deals = deals.filter((deal) => {
        return (
          deal.name.toLowerCase().includes(searchLower) ||
          deal.company?.name &&
          deal.company.name.toLowerCase().includes(searchLower) ||
          deal.stage.toLowerCase().includes(searchLower));

      });
    } else {
      // If not searching, exclude suggested matches from the main list
      const suggestedIds = new Set(suggestedMatches.map((d) => d.id));
      deals = deals.filter((deal) => !suggestedIds.has(deal.id));
    }

    // Sort by pipeline stage order first
    return deals.sort((a, b) => {
      const aStageIndex = DEAL_STAGES.indexOf(a.stage);
      const bStageIndex = DEAL_STAGES.indexOf(b.stage);

      if (aStageIndex !== bStageIndex) {
        return aStageIndex - bStageIndex;
      }

      // If same stage, sort by deal name alphabetically
      return a.name.localeCompare(b.name);
    });
  }, [activeDeals, searchTerm, suggestedMatches]);

  // Handle deal selection
  const handleSelectDeal = () => {
    const selectedDeal = activeDeals.find((deal) => deal.id === selectedDealId);
    if (selectedDeal) {
      onSelectDeal(selectedDeal);
      onClose();
    }
  };

  // Format currency
  const formatCurrency = (amount?: number) => {
    if (!amount) return "No value";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "No date";
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch {
      return "Invalid date";
    }
  };

  // Get stage color
  const getStageColor = (stage: string) => {
    switch (stage) {
      case "Closed won":
        return "bg-success-light text-success/20";
      case "Closed lost":
      case "Abandoned":
        return "bg-error-light text-error/20";
      case "Finalising terms":
        return "bg-primary-light text-primary/20";
      default:
        return "bg-surface-alt text-primary";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-surface-card rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">
            Select Deal
          </h2>
          <Button variant="ghost"
          onClick={onClose}>


            <XMarkIcon className="w-6 h-6" />
          </Button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-default">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-subtle" />
            <Input
              type="text"
              placeholder="Search deals by name, company, or stage..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-strong rounded-lg focus:ring-2 focus:ring-primary focus:border-primary" />

          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isDealsLoading ?
          <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-success"></div>
            </div> :
          activeDeals.length === 0 ?
          <div className="text-center py-8 text-muted">
              No active deals available.
            </div> :

          <div className="space-y-4">
              {/* Suggested Matches Section */}
              {!searchTerm && suggestedMatches.length > 0 &&
            <div>
                  <h3 className="text-sm font-medium text-primary mb-3 flex items-center">
                    <svg className="w-4 h-4 mr-2 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Suggested Matches
                  </h3>
                  <div className="space-y-3">
                    {suggestedMatches.map((deal: Deal) =>
                <div
                  key={deal.id}
                  onClick={() => setSelectedDealId(deal.id)}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedDealId === deal.id ?
                  "border-success bg-success-light/20" :
                  "border-success hover:border-success dark:hover:border-success bg-success-light/50/10"}`
                  }>

                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-medium text-primary">
                            {deal.name}
                          </h3>
                          <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getStageColor(
                        deal.stage
                      )}`}>

                            {deal.stage}
                          </span>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm text-secondary">
                          <div>
                            <span className="font-medium">Company:</span>{" "}
                            {deal.company?.name || "No company"}
                          </div>
                          <div>
                            <span className="font-medium">Value:</span>{" "}
                            {formatCurrency(deal.value)}
                          </div>
                          <div>
                            <span className="font-medium">Expected Close:</span>{" "}
                            {formatDate(deal.expectedCloseDate)}
                          </div>
                          <div>
                            <span className="font-medium">Probability:</span>{" "}
                            {deal.probability || 0}%
                          </div>
                        </div>

                        {deal.description &&
                  <p className="mt-2 text-sm text-muted line-clamp-2">
                            {deal.description}
                          </p>
                  }
                      </div>
                )}
                  </div>
                </div>
            }

              {/* Divider between suggested and all deals */}
              {!searchTerm && suggestedMatches.length > 0 && filteredDeals.length > 0 &&
            <div className="relative">
                  <div className="absolute inset-0 flex items-center" aria-hidden="true">
                    <div className="w-full border-t border-strong" />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="px-3 bg-surface-card text-sm text-muted">
                      All Deals
                    </span>
                  </div>
                </div>
            }

              {/* All Deals or Search Results */}
              {filteredDeals.length === 0 && searchTerm ?
            <div className="text-center py-8 text-muted">
                  No deals found matching your search.
                </div> :

            <div className="space-y-3">
                  {filteredDeals.map((deal: Deal) =>
              <div
                key={deal.id}
                onClick={() => setSelectedDealId(deal.id)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedDealId === deal.id ?
                "border-success bg-success-light/20" :
                "border-default hover:border-strong dark:hover:border-default"}`
                }>

                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium text-primary">
                          {deal.name}
                        </h3>
                        <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getStageColor(
                      deal.stage
                    )}`}>

                          {deal.stage}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm text-secondary">
                        <div>
                          <span className="font-medium">Company:</span>{" "}
                          {deal.company?.name || "No company"}
                        </div>
                        <div>
                          <span className="font-medium">Value:</span>{" "}
                          {formatCurrency(deal.value)}
                        </div>
                        <div>
                          <span className="font-medium">Expected Close:</span>{" "}
                          {formatDate(deal.expectedCloseDate)}
                        </div>
                        <div>
                          <span className="font-medium">Probability:</span>{" "}
                          {deal.probability || 0}%
                        </div>
                      </div>

                      {deal.description &&
                <p className="mt-2 text-sm text-muted line-clamp-2">
                          {deal.description}
                        </p>
                }
                    </div>
              )}
                </div>
            }
            </div>
          }
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-default flex justify-end space-x-3">
          <Button variant="secondary"
          onClick={onClose}>


            Cancel
          </Button>
          <Button variant="success"
          onClick={handleSelectDeal}
          disabled={!selectedDealId}>


            Select Deal
          </Button>
        </div>
      </div>
    </div>);

};

export default DealSelectionModal;