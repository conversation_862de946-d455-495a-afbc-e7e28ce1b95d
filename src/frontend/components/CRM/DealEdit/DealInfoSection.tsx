import React from "react";
import { Deal, DealUpdate, DealPriority } from "../../../types/crm-types";
import { format, parseISO } from "date-fns";
import { 
  LinkIcon,
  CalendarDaysIcon,
  FlagIcon,
  UserIcon,
  SparklesIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ClockIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { isFieldControlledByEstimate } from "./utils";
import { Input, Select, Textarea, FormField, FormGrid } from "../../shared/forms";

// Define invoice frequency options
const INVOICE_FREQUENCY_OPTIONS = [
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Every 2 Weeks" },
  { value: "monthly", label: "Monthly" },
  { value: "custom", label: "Custom" },
];

// Define payment terms options
const PAYMENT_TERMS_OPTIONS = [
  { value: 7, label: "Net 7" },
  { value: 14, label: "Net 14" },
  { value: 20, label: "Net 20" },
  { value: 30, label: "Net 30" },
];

interface DealInfoSectionProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
  fieldOwnership?: Record<string, string>;
}

/**
 * Modern component for displaying and editing basic deal information
 */
const DealInfoSection: React.FC<DealInfoSectionProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
  fieldOwnership,
}) => {
  // Format date to Australian format (DD/MM/YYYY)
  const formatDate = (dateString?: string): string => {
    if (!dateString) return "Not set";
    try {
      return format(parseISO(dateString), "dd MMM yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Deal priorities for dropdown
  const DEAL_PRIORITIES: DealPriority[] = ["Low", "Medium", "High"];

  // Helper to render field with estimate link indicator
  const renderFieldValue = (value: any, fieldName: keyof Deal, formatter?: (val: any) => string) => {
    const displayValue = formatter ? formatter(value) : (value || "Not set");
    const isControlled = isFieldControlledByEstimate(deal, fieldName);
    
    return (
      <div className="flex items-center gap-2">
        <span className="text-primary">{displayValue}</span>
        {isControlled && (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-primary-light/30 text-primary-color">
            <LinkIcon className="w-3 h-3 mr-1" />
            Linked
          </span>
        )}
      </div>
    );
  };

  return (
    <div className="content-section-card">
      <div className="deal-section-header">
        <div className="deal-section-title">
          <div className="deal-section-icon">
            <InformationCircleIcon className="w-5 h-5 text-accent" />
          </div>
          <h3>Deal Information</h3>
        </div>
      </div>
      
      <div className="p-6">
        {isEditing ? (
          <FormGrid columns={2} className="gap-6">
            {/* Expected Close Date */}
            <FormField
              label="Expected Close Date"
              icon={<CalendarDaysIcon className="w-4 h-4" />}
            >
              <Input
                type="date"
                value={formData.expectedCloseDate ? formData.expectedCloseDate.split("T")[0] : ""}
                onChange={(e) => onChange("expectedCloseDate", e.target.value)}
                placeholder="Select date"
              />
            </FormField>

            {/* Project Start Date */}
            <FormField
              label="Project Start Date"
              icon={<CalendarDaysIcon className="w-4 h-4" />}
              hint={isFieldControlledByEstimate(deal, "startDate", fieldOwnership) ? "Linked to estimate" : undefined}
            >
              <Input
                type="date"
                value={formData.startDate ? formData.startDate.split("T")[0] : ""}
                onChange={(e) => onChange("startDate", e.target.value)}
                disabled={isFieldControlledByEstimate(deal, "startDate", fieldOwnership)}
                variant={isFieldControlledByEstimate(deal, "startDate", fieldOwnership) ? "disabled" : "default"}
              />
            </FormField>

            {/* Project End Date */}
            <FormField
              label="Project End Date"
              icon={<CalendarDaysIcon className="w-4 h-4" />}
              hint={isFieldControlledByEstimate(deal, "endDate", fieldOwnership) ? "Linked to estimate" : undefined}
            >
              <Input
                type="date"
                value={formData.endDate ? formData.endDate.split("T")[0] : ""}
                onChange={(e) => onChange("endDate", e.target.value)}
                disabled={isFieldControlledByEstimate(deal, "endDate", fieldOwnership)}
                variant={isFieldControlledByEstimate(deal, "endDate", fieldOwnership) ? "disabled" : "default"}
              />
            </FormField>

            {/* Priority */}
            <FormField
              label="Priority"
              icon={<FlagIcon className="w-4 h-4" />}
            >
              <Select
                value={formData.priority || ""}
                onChange={(e) => onChange("priority", e.target.value)}
                options={[
                  { value: "", label: "Select Priority" },
                  ...DEAL_PRIORITIES.map((priority) => ({
                    value: priority,
                    label: priority,
                  })),
                ]}
              />
            </FormField>

            {/* Source */}
            <FormField
              label="Source"
              icon={<SparklesIcon className="w-4 h-4" />}
            >
              <Input
                type="text"
                value={formData.source || ""}
                onChange={(e) => onChange("source", e.target.value)}
                placeholder="Where did this deal come from?"
              />
            </FormField>

            {/* Owner */}
            <FormField
              label="Owner"
              icon={<UserIcon className="w-4 h-4" />}
            >
              <Input
                type="text"
                value={formData.owner || ""}
                onChange={(e) => onChange("owner", e.target.value)}
                placeholder="Who is responsible for this deal?"
              />
            </FormField>

            {/* Invoice Frequency */}
            <FormField
              label="Invoice Frequency"
              icon={<ClockIcon className="w-4 h-4" />}
              hint={isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership) ? "Linked to estimate" : undefined}
            >
              <Select
                value={formData.invoiceFrequency || ""}
                onChange={(e) => onChange("invoiceFrequency", e.target.value)}
                disabled={isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership)}
                variant={isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership) ? "disabled" : "default"}
                options={[
                  { value: "", label: "Select Frequency" },
                  ...INVOICE_FREQUENCY_OPTIONS.map((option) => ({
                    value: option.value,
                    label: option.label,
                  })),
                ]}
              />
            </FormField>

            {/* Payment Terms */}
            <FormField
              label="Payment Terms"
              icon={<BanknotesIcon className="w-4 h-4" />}
              hint={isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership) ? "Linked to estimate" : undefined}
            >
              <Select
                value={formData.paymentTerms?.toString() || ""}
                onChange={(e) => onChange("paymentTerms", parseInt(e.target.value))}
                disabled={isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership)}
                variant={isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership) ? "disabled" : "default"}
                options={[
                  { value: "", label: "Select Payment Terms" },
                  ...PAYMENT_TERMS_OPTIONS.map((option) => ({
                    value: option.value.toString(),
                    label: option.label,
                  })),
                ]}
              />
            </FormField>

            {/* Description - Full width */}
            <div className="col-span-2">
              <FormField
                label="Description"
                icon={<DocumentTextIcon className="w-4 h-4" />}
              >
                <Textarea
                  value={formData.description || ""}
                  onChange={(e) => onChange("description", e.target.value)}
                  placeholder="Add a description of the deal..."
                  rows={4}
                />
              </FormField>
            </div>
          </FormGrid>
        ) : (
          /* View Mode - Modern Card Layout */
          <div className="space-y-6">
            {/* Date Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="info-card">
                <div className="info-card-icon bg-accent-light/30">
                  <CalendarDaysIcon className="w-4 h-4 text-accent" />
                </div>
                <div>
                  <p className="info-label">Expected Close</p>
                  <p className="info-value">{formatDate(deal.expectedCloseDate)}</p>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-card-icon bg-success-light/30">
                  <CalendarDaysIcon className="w-4 h-4 text-success" />
                </div>
                <div>
                  <p className="info-label">Project Start</p>
                  {renderFieldValue(deal.startDate, "startDate", formatDate)}
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-card-icon bg-warning-light/30">
                  <CalendarDaysIcon className="w-4 h-4 text-warning" />
                </div>
                <div>
                  <p className="info-label">Project End</p>
                  {renderFieldValue(deal.endDate, "endDate", formatDate)}
                </div>
              </div>
            </div>

            {/* Deal Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="info-card">
                <div className="info-card-icon bg-error-light/30">
                  <FlagIcon className="w-4 h-4 text-error" />
                </div>
                <div>
                  <p className="info-label">Priority</p>
                  <p className="info-value">
                    <span className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      deal.priority === "High" ? "bg-error-light text-error/30" :
                      deal.priority === "Medium" ? "bg-warning-light text-warning/30" :
                      deal.priority === "Low" ? "bg-success-light text-success/30" :
                      "bg-surface-alt text-primary"
                    }`}>
                      {deal.priority || "Not set"}
                    </span>
                  </p>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-card-icon bg-primary-light/30">
                  <UserIcon className="w-4 h-4 text-primary-color" />
                </div>
                <div>
                  <p className="info-label">Owner</p>
                  <p className="info-value">{deal.owner || "Not assigned"}</p>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-card-icon bg-primary-light/30">
                  <SparklesIcon className="w-4 h-4 text-primary-color" />
                </div>
                <div>
                  <p className="info-label">Source</p>
                  <p className="info-value">{deal.source || "Not specified"}</p>
                </div>
              </div>
            </div>

            {/* Financial Terms */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="info-card">
                <div className="info-card-icon bg-success-light/30">
                  <ClockIcon className="w-4 h-4 text-success" />
                </div>
                <div>
                  <p className="info-label">Invoice Frequency</p>
                  {renderFieldValue(
                    deal.invoiceFrequency,
                    "invoiceFrequency",
                    (val) => INVOICE_FREQUENCY_OPTIONS.find(opt => opt.value === val)?.label || val || "Not set"
                  )}
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-card-icon bg-cyan-100/30">
                  <BanknotesIcon className="w-4 h-4 text-info" />
                </div>
                <div>
                  <p className="info-label">Payment Terms</p>
                  {renderFieldValue(
                    deal.paymentTerms,
                    "paymentTerms",
                    (val) => val ? (PAYMENT_TERMS_OPTIONS.find(opt => opt.value === val)?.label || `Net ${val}`) : "Not set"
                  )}
                </div>
              </div>
            </div>

            {/* Description */}
            {deal.description && (
              <div className="description-card">
                <div className="flex items-start gap-3">
                  <div className="info-card-icon bg-surface-alt">
                    <DocumentTextIcon className="w-4 h-4 text-secondary" />
                  </div>
                  <div className="flex-1">
                    <p className="info-label mb-2">Description</p>
                    <p className="text-sm text-primary whitespace-pre-wrap">
                      {deal.description}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DealInfoSection;
