import React, { useState, useEffect } from "react";
import { useQueryClient } from "react-query";
import { formatCurrency } from "../../Estimate/utils/index";
import { format, parseISO } from "date-fns";
import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { getDraftEstimate } from "../../../api/estimates";

// TypeScript interfaces
import { Button } from "@/frontend/components/ui/Button";interface Deal {
  id: string;
  name: string;
  [key: string]: unknown;
}

interface HarvestEstimate {
  id: string | number;
  subject?: string;
  number?: string;
  amount?: number;
  state?: string;
  issue_date?: string;
  updated_at?: string;
  client?: {
    name?: string;
  };
  [key: string]: unknown;
}

interface TimeAllocation {
  days: number;
  [key: string]: unknown;
}

interface EstimateAllocation {
  firstName?: string;
  lastName?: string;
  rateProposedDaily: number;
  timeAllocations: TimeAllocation[];
  [key: string]: unknown;
}

interface DraftEstimate {
  id: string;
  projectName?: string;
  clientName?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  updatedAt?: string;
  allocations?: EstimateAllocation[];
  [key: string]: unknown;
}

type EstimateDetails = DraftEstimate | HarvestEstimate;

interface ApiError extends Error {
  message: string;
  [key: string]: unknown;
}

interface LinkedEstimateModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: Deal;
  estimate: HarvestEstimate | DraftEstimate;
  estimateType: "internal" | "harvest";
}

/**
 * Modal for viewing and managing a linked estimate
 */
const LinkedEstimateModal: React.FC<LinkedEstimateModalProps> = ({
  isOpen,
  onClose,
  deal,
  estimate,
  estimateType
}) => {
  const [estimateDetails, setEstimateDetails] = useState<EstimateDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch full estimate details when modal opens
  useEffect(() => {
    if (isOpen && estimate) {
      fetchEstimateDetails();
    }
  }, [isOpen, estimate]);

  // Fetch estimate details
  const fetchEstimateDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      if (estimateType === "internal") {
        const draftDetails = await getDraftEstimate(estimate.id);
        setEstimateDetails(draftDetails);
      } else {
        // For Harvest estimates, we already have most of the details needed
        setEstimateDetails(estimate);
      }
    } catch (err) {
      const apiError = err as ApiError;
      console.error("Error fetching estimate details:", err);
      setError(apiError.message || "Failed to fetch estimate details");
    } finally {
      setLoading(false);
    }
  };

  // Unlinking is no longer supported - linking is permanent

  // Handle viewing the full estimate
  const handleViewEstimate = () => {
    let url;
    if (estimateType === "internal") {
      url = `/estimates/${estimate.id}`;
    } else {
      // For Harvest estimates
      url = `https://onbord.harvestapp.com/estimates/${estimate.id}`;
    }
    window.open(url, "_blank");
  };

  if (!isOpen) return null;

  // Format date safely
  const formatDateSafely = (dateString: string | undefined | null) => {
    if (!dateString) return "Not set";
    try {
      if (typeof dateString === 'string' && dateString.includes('T")) {
        return format(parseISO(dateString), "MMM d, yyyy");
      }
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (e) {
      return "Invalid date";
    }
  };

  // Calculate estimate total
  const calculateTotal = () => {
    if (estimateType === "internal" && estimateDetails?.allocations) {
      let total = 0;
      (estimateDetails as DraftEstimate).allocations?.forEach((allocation: EstimateAllocation) => {
        const totalDays = allocation.timeAllocations.reduce(
          (sum: number, ta: TimeAllocation) => sum + ta.days,
          0
        );
        total += allocation.rateProposedDaily * totalDays;
      });
      return total;
    } else if (estimateType === "harvest") {
      return (estimateDetails as HarvestEstimate)?.amount || 0;
    }
    return 0;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-surface-card rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">
            {estimateType === "internal" ? "Draft Estimate" : "Harvest Estimate"}
          </h2>
          <Button variant="ghost"
          onClick={onClose}>


            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12" />

            </svg>
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {error &&
          <div className="mb-4 p-3 bg-error-light/20 border border-error text-error rounded-md">
              {error}
            </div>
          }

          {loading ?
          <div className="flex justify-center items-center py-8">
              <svg
              className="animate-spin h-8 w-8 text-primary-color"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24">

                <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4">
              </circle>
                <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
              </svg>
            </div> :
          estimateDetails ?
          <div className="bg-primary-light/20 border border-primary rounded-md p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-medium text-primary">
                    {estimateType === "internal" ?
                  estimateDetails.projectName || "Unnamed Project" :
                  estimateDetails.subject || estimateDetails.number || "Unnamed Estimate"}
                  </h3>
                  <p className="text-sm text-secondary">
                    {estimateType === "internal" ?
                  estimateDetails.clientName :
                  estimateDetails.client?.name || "No Client"}
                  </p>
                </div>
                <span className={`text-xs px-2 py-0.5 rounded-full capitalize ${
              estimateType === "internal" ?
              "bg-primary-light text-primary" :
              getStatusColorClass(estimateDetails.state)}`
              }>
                  {estimateType === "internal" ?
                estimateDetails.status :
                estimateDetails.state}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div>
                  <p className="text-sm font-medium text-muted">
                    Total
                  </p>
                  <p className="text-sm text-primary">
                    {formatCurrency(calculateTotal())}
                  </p>
                </div>
                
                {estimateType === "internal" &&
              <div>
                    <p className="text-sm font-medium text-muted">
                      Time Allocation
                    </p>
                    <p className="text-sm text-primary">
                      {calculateTotalDays(estimateDetails)} days
                    </p>
                  </div>
              }
                
                <div>
                  <p className="text-sm font-medium text-muted">
                    {estimateType === "internal" ? "Date Range" : "Issue Date"}
                  </p>
                  <p className="text-sm text-primary">
                    {estimateType === "internal" ?
                  `${formatDateSafely(estimateDetails.startDate)} - ${formatDateSafely(estimateDetails.endDate)}` :
                  formatDateSafely(estimateDetails.issue_date)}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-muted">
                    Last Updated
                  </p>
                  <p className="text-sm text-primary">
                    {formatDateSafely(estimateDetails.updatedAt || estimateDetails.updated_at)}
                  </p>
                </div>
              </div>

              {estimateType === "internal" && estimateDetails.allocations &&
            <div className="mb-4">
                  <p className="text-sm font-medium text-muted mb-1">
                    Staff Allocation
                  </p>
                  <div className="space-y-1">
                    {(estimateDetails as DraftEstimate).allocations?.map((allocation: EstimateAllocation, index: number) =>
                <div key={index} className="flex justify-between text-xs text-secondary">
                        <span>{allocation.firstName} {allocation.lastName}</span>
                        <span>{getAllocationDays(allocation)} days</span>
                      </div>
                )}
                  </div>
                </div>
            }

              {/* Fields Controlled by This Estimate */}
              <div className="mb-4 p-3 bg-warning-light/20 border border-warning rounded-md">
                <p className="text-sm font-medium text-warning mb-2">
                  This estimate controls the following deal fields:
                </p>
                <ul className="text-xs text-warning space-y-1">
                  <li className="flex items-center gap-1">
                    <span className="text-warning">•</span>
                    <span>Deal Value: {formatCurrency(calculateTotal())}</span>
                  </li>
                  {estimateType === "internal" && estimateDetails.startDate &&
                <li className="flex items-center gap-1">
                      <span className="text-warning">•</span>
                      <span>Start Date: {formatDateSafely(estimateDetails.startDate)}</span>
                    </li>
                }
                  {estimateType === "internal" && estimateDetails.endDate &&
                <li className="flex items-center gap-1">
                      <span className="text-warning">•</span>
                      <span>End Date: {formatDateSafely(estimateDetails.endDate)}</span>
                    </li>
                }
                  {estimateType === "internal" && estimateDetails.invoiceFrequency &&
                <li className="flex items-center gap-1">
                      <span className="text-warning">•</span>
                      <span>Invoice Frequency: {estimateDetails.invoiceFrequency.charAt(0).toUpperCase() + estimateDetails.invoiceFrequency.slice(1)}</span>
                    </li>
                }
                  {estimateType === "internal" && estimateDetails.paymentTerms &&
                <li className="flex items-center gap-1">
                      <span className="text-warning">•</span>
                      <span>Payment Terms: Net {estimateDetails.paymentTerms}</span>
                    </li>
                }
                </ul>
                <p className="text-xs text-warning mt-2">
                  These fields cannot be edited directly while this estimate is linked.
                </p>
              </div>

              <div className="flex justify-between items-center mt-4 pt-3 border-t border-primary">
                <div className="text-xs text-muted">
                  Linked to deal: {deal.name}
                </div>
                <div className="flex space-x-2">
                  <Button variant="ghost"
                onClick={handleViewEstimate}>


                    <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                    View Estimate
                  </Button>
                </div>
              </div>
            </div> :

          <div className="text-center py-8 text-muted">
              No estimate information found.
            </div>
          }
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-default flex justify-end">
          <Button variant="secondary"
          onClick={onClose}>


            Close
          </Button>
        </div>
      </div>
    </div>);

};

// Helper function to calculate total days for an allocation
const getAllocationDays = (allocation: EstimateAllocation): number => {
  if (!allocation.timeAllocations) return 0;
  return allocation.timeAllocations.reduce(
    (sum: number, ta: TimeAllocation) => sum + ta.days,
    0
  );
};

// Helper function to calculate total days for an estimate
const calculateTotalDays = (estimate: EstimateDetails): number => {
  const draftEstimate = estimate as DraftEstimate;
  if (!draftEstimate.allocations) return 0;
  let totalDays = 0;
  draftEstimate.allocations.forEach((allocation: EstimateAllocation) => {
    totalDays += getAllocationDays(allocation);
  });
  return totalDays;
};

// Helper function to get status color class
const getStatusColorClass = (status: string) => {
  switch (status) {
    case "draft":
      return "bg-surface-alt text-primary";
    case "sent":
      return "bg-primary-light text-primary/30";
    case "accepted":
      return "bg-success-light text-success/30";
    case "declined":
      return "bg-error-light text-error/30";
    default:
      return "bg-surface-alt text-primary";
  }
};

export default LinkedEstimateModal;