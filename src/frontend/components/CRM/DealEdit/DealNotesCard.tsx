import React, { useState } from "react";
import { Note } from "../../../types/crm-types";
import { Card } from "../../ui";
import DealNotesSection from "./DealNotesSection";
import { ConversationThread } from "../Conversations";
import { DocumentTextIcon, ChatBubbleLeftRightIcon } from "@heroicons/react/24/outline";
import { Button } from "@/frontend/components/ui/Button";

interface DealNotesCardProps {
  dealId: string;
  notes: Note[];
}

/**
 * Card wrapper for the DealNotesSection component with conversation threading support
 */
const DealNotesCard: React.FC<DealNotesCardProps> = ({ dealId, notes }) => {
  const [viewMode, setViewMode] = useState<'notes' | "conversations">('notes');

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default dark:border-default">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg leading-6 font-medium text-primary dark:text-primary">
              Notes & Conversations
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-muted dark:text-subtle">
              {viewMode === 'notes' ?'Simple notes and comments about this deal.' : "Threaded conversations with participants and status tracking."}
            </p>
          </div>
          <div className="flex bg-surface-alt dark:bg-surface-alt rounded-lg p-1">
            <Button variant="secondary"
            onClick={() => setViewMode('notes')}
            className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
            viewMode === 'notes' ?'bg-surface-card dark:bg-surface-card text-primary dark:text-primary shadow-sm' : "text-secondary dark:text-subtle hover:text-primary dark:hover:text-subtle"}`
            }>

              <DocumentTextIcon className="w-4 h-4" />
              Notes
            </Button>
            <Button variant="secondary"
            onClick={() => setViewMode('conversations')}
            className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
            viewMode === 'conversations' ?'bg-surface-card dark:bg-surface-card text-primary dark:text-primary shadow-sm' : "text-secondary dark:text-subtle hover:text-primary dark:hover:text-subtle"}`
            }>

              <ChatBubbleLeftRightIcon className="w-4 h-4" />
              Conversations
            </Button>
          </div>
        </div>
      </div>
      <div className="px-4 py-5 sm:p-6">
        {viewMode === 'notes' ?
        <DealNotesSection dealId={dealId} notes={notes} /> :

        <ConversationThread
          entityType="deal"
          entityId={dealId}
          currentUserId="system" // TODO: Get from user context
        />
        }
      </div>
    </Card>);

};

export default DealNotesCard;