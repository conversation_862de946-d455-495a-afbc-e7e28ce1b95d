import React, { useState, useCallback } from "react";
import { Contact } from "../../../types/crm-types";
import { useQuery, useMutation, useQueryClient } from "react-query";
import {
  getContacts,
  associateContactWithDeal,
  disassociateContactFromDeal,
} from "../../../api/crm";
import {
  UserCircleIcon,
  MagnifyingGlassIcon,
  PlusCircleIcon,
  EnvelopeIcon,
  BriefcaseIcon,
  BuildingOfficeIcon,
} from "@heroicons/react/24/outline";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";

interface DealContactsSectionProps {
  dealId: string;
  contacts: Contact[];
}

/**
 * Component for managing contacts associated with a deal
 */
const DealContactsSection: React.FC<DealContactsSectionProps> = ({
  dealId,
  contacts,
}) => {
  const [selectedContactId, setSelectedContactId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const queryClient = useQueryClient();

  // Fetch all contacts
  const { data: allContacts = [], isLoading } = useQuery(
    "contacts",
    getContacts,
  );

  // Filter contacts that are not already associated with the deal
  const availableContacts = allContacts.filter(
    (contact) =>
      contact && contact.id && !contacts.some((c) => c?.id === contact.id),
  );

  // Filter contacts based on search term
  const filteredAvailableContacts = availableContacts.filter(
    (contact) =>
      contact &&
      contact.firstName &&
      contact.lastName &&
      (contact.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (contact.email &&
          contact.email.toLowerCase().includes(searchTerm.toLowerCase()))),
  );

  // Mutation for associating a contact with a deal
  const associateContactMutation = useMutation(
    () => associateContactWithDeal(dealId, selectedContactId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["deal", dealId]);
        setSelectedContactId("");
      },
    },
  );

  // Mutation for disassociating a contact from a deal
  const disassociateContactMutation = useMutation(
    (contactId: string) => disassociateContactFromDeal(dealId, contactId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["deal", dealId]);
      },
    },
  );

  // Handle associating a contact
  const handleAssociateContact = () => {
    if (selectedContactId) {
      console.log("Associating contact:", {
        dealId,
        selectedContactId,
        selectedContact: allContacts.find((c) => c.id === selectedContactId),
      });
      associateContactMutation.mutate();
    }
  };

  // Handle disassociating a contact
  const handleDisassociateContact = useCallback(
    (contactId: string) => {
      if (
        window.confirm(
          "Are you sure you want to remove this contact from the deal?",
        )
      ) {
        disassociateContactMutation.mutate(contactId);
      }
    },
    [disassociateContactMutation],
  );

  return (
    <div className="bg-surface-card rounded-lg overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default">
        <h3 className="text-lg leading-6 font-medium text-primary">
          Associated Contacts
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-muted">
          People involved in this deal.
        </p>
      </div>

      <div className="px-4 py-5 sm:p-6">
        {/* Add contact form */}
        <div className="mb-6 pb-6 border-b border-default">
          <h4 className="text-sm font-medium text-primary mb-4">
            Add Contact to Deal
          </h4>

          {/* Search and select interface */}
          <div className="space-y-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon
                  className="h-5 w-5 text-subtle"
                  aria-hidden="true"
                />
              </div>
              <Input
                type="text"
                id="contactSearch"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md transition-colors duration-200"
                placeholder="Search contacts by name or email"
              />
            </div>

            <div className="flex flex-col sm:flex-row sm:items-end gap-4">
              <div className="flex-1">
                <label
                  htmlFor="contactSelect"
                  className="block text-sm font-medium text-primary mb-1"
                >
                  Select Contact
                </label>
                <Select
                  id="contactSelect"
                  value={selectedContactId}
                  onChange={(e) => setSelectedContactId(e.target.value)}
                  className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md transition-colors duration-200"
                >
                  <option value="">Select a contact</option>
                  {filteredAvailableContacts
                    .filter((contact) => {
                      // Only show contacts that have valid IDs
                      const hasValidId = contact.id && contact.id.length > 10;
                      if (!hasValidId) {
                        console.warn("Skipping contact with invalid ID:", {
                          id: contact.id,
                          firstName: contact.firstName,
                          lastName: contact.lastName,
                          email: contact.email,
                        });
                      }
                      return hasValidId;
                    })
                    .map((contact) => (
                      <option key={contact.id} value={contact.id}>
                        {contact.firstName} {contact.lastName}{" "}
                        {contact.email ? `(${contact.email})` : ""}
                      </option>
                    ))}
                </Select>
              </div>
              <Button
                variant="primary"
                type="button"
                onClick={handleAssociateContact}
                disabled={
                  !selectedContactId || associateContactMutation.isLoading
                }
                className="min-w-[120px]"
              >
                {associateContactMutation.isLoading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Adding...
                  </>
                ) : (
                  <>
                    <PlusCircleIcon
                      className="h-5 w-5 mr-1"
                      aria-hidden="true"
                    />
                    Add Contact
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Associated contacts list */}
        <div>
          <h4 className="text-sm font-medium text-primary mb-4">
            Current Contacts
          </h4>
          {contacts.length === 0 ? (
            <div className="text-sm text-muted bg-surface-page/50 rounded-md p-4 text-center">
              <UserCircleIcon className="h-8 w-8 mx-auto mb-2 text-subtle" />
              No contacts associated with this deal yet.
            </div>
          ) : (
            <ul className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
              {contacts.map((contact) => (
                <li
                  key={contact.id}
                  className="bg-surface-card border border-default rounded-lg p-4 flex items-start justify-between group hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <UserCircleIcon
                        className="h-10 w-10 text-subtle"
                        aria-hidden="true"
                      />
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-primary">
                        {contact.firstName} {contact.lastName}
                      </h5>
                      {contact.email && (
                        <p className="text-sm text-muted flex items-center mt-1">
                          <EnvelopeIcon
                            className="h-4 w-4 mr-1 flex-shrink-0"
                            aria-hidden="true"
                          />

                          {contact.email}
                        </p>
                      )}
                      {contact.jobTitle && (
                        <p className="text-sm text-muted flex items-center mt-1">
                          <BriefcaseIcon
                            className="h-4 w-4 mr-1 flex-shrink-0"
                            aria-hidden="true"
                          />

                          {contact.jobTitle}
                        </p>
                      )}
                      {contact.company && (
                        <p className="text-sm text-muted flex items-center mt-1">
                          <BuildingOfficeIcon
                            className="h-4 w-4 mr-1 flex-shrink-0"
                            aria-hidden="true"
                          />

                          {contact.company.name}
                        </p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="secondary"
                    type="button"
                    onClick={() => handleDisassociateContact(contact.id)}
                    disabled={disassociateContactMutation.isLoading}
                    title="Remove contact"
                  >
                    <svg
                      className="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </Button>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default DealContactsSection;
