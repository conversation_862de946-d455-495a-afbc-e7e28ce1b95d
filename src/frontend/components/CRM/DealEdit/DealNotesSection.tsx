import React, { useState } from "react";
import { Note } from "../../../types/crm-types";
import { useMutation, useQueryClient } from "react-query";
import { addNoteToDeal, deleteNote } from "../../../api/crm";
import { format, parseISO } from "date-fns";
import { Button } from "@/frontend/components/ui/Button";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface DealNotesSectionProps {
  dealId: string;
  notes: Note[];
}

/**
 * Component for managing notes associated with a deal
 */
const DealNotesSection: React.FC<DealNotesSectionProps> = ({
  dealId,
  notes,
}) => {
  const [newNote, setNewNote] = useState("");
  const queryClient = useQueryClient();

  // Mutation for adding a note
  const addNoteMutation = useMutation(
    () => addNoteToDeal({ dealId, content: newNote }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["deal", dealId]);
        setNewNote("");
      },
    },
  );

  // Mutation for deleting a note
  const deleteNoteMutation = useMutation(
    (noteId: string) => deleteNote(noteId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["deal", dealId]);
      },
    },
  );

  // Handle adding a note
  const handleAddNote = (e: React.FormEvent) => {
    e.preventDefault();
    if (newNote.trim()) {
      addNoteMutation.mutate();
    }
  };

  // Handle deleting a note
  const handleDeleteNote = (noteId: string) => {
    if (window.confirm("Are you sure you want to delete this note?")) {
      deleteNoteMutation.mutate(noteId);
    }
  };

  // Format date to Australian format (DD/MM/YYYY HH:MM)
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy HH:mm");
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <div className="bg-surface-card shadow rounded-lg overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default">
        <h3 className="text-lg leading-6 font-medium text-primary">
          Notes & Activity
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-muted">
          Record important information and track deal activity.
        </p>
      </div>

      <div className="px-4 py-5 sm:p-6">
        {/* Add note form */}
        <form
          onSubmit={handleAddNote}
          className="mb-6 pb-6 border-b border-default"
        >
          <div>
            <label
              htmlFor="newNote"
              className="block text-sm font-medium text-primary"
            >
              Add a Note
            </label>
            <div className="mt-1">
              <Textarea
                id="newNote"
                rows={4}
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md"
                placeholder="Add a note about this deal..."
              />
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="primary"
              type="submit"
              disabled={!newNote.trim() || addNoteMutation.isLoading}
            >
              {addNoteMutation.isLoading ? "Adding..." : "Add Note"}
            </Button>
          </div>
        </form>

        {/* Notes timeline */}
        <div className="flow-root">
          {notes.length === 0 ? (
            <div className="text-sm text-muted">
              No notes have been added yet.
            </div>
          ) : (
            <ul className="-mb-8">
              {notes.map((note, noteIdx) => (
                <li key={note.id}>
                  <div className="relative pb-8">
                    {noteIdx !== notes.length - 1 ? (
                      <span
                        className="absolute top-5 left-5 -ml-px h-full w-0.5 bg-surface-alt"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex items-start space-x-3">
                      <div className="relative">
                        <div className="h-10 w-10 rounded-full bg-surface-alt flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                          <svg
                            className="h-5 w-5 text-muted"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div>
                          <div className="text-sm">
                            <span className="font-medium text-primary">
                              {note.createdBy || "User"}
                            </span>
                          </div>
                          <p className="mt-0.5 text-sm text-muted">
                            {formatDate(note.createdAt)}
                          </p>
                        </div>
                        <div className="mt-2 text-sm text-primary">
                          <p className="whitespace-pre-wrap">{note.content}</p>
                        </div>
                        <div className="mt-2 flex justify-end">
                          <Button
                            variant="danger"
                            type="button"
                            onClick={() => handleDeleteNote(note.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default DealNotesSection;
