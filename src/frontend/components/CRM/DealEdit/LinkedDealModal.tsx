import React, { useState, useEffect } from "react";
import { useQueryClient } from "react-query";
import { getLinkedDealsForEstimate } from "../../../api/crm";
import { formatCurrency } from "../../Estimate/utils/index";
import { format } from "date-fns";
import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { Button } from "@/frontend/components/ui/Button";

interface LinkedDealModalProps {
  isOpen: boolean;
  onClose: () => void;
  estimateId: string;
  estimateType: "internal" | "harvest";
}

/**
 * Modal for viewing and managing a linked deal
 */
const LinkedDealModal: React.FC<LinkedDealModalProps> = ({
  isOpen,
  onClose,
  estimateId,
  estimateType,
}) => {
  const [linkedDeal, setLinkedDeal] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch linked deal when modal opens
  useEffect(() => {
    if (isOpen && estimateId) {
      fetchLinkedDeal();
    }
  }, [isOpen, estimateId]);

  // Fetch linked deal data
  const fetchLinkedDeal = async () => {
    setLoading(true);
    setError(null);

    try {
      const linkedDeals = await getLinkedDealsForEstimate(
        estimateId,
        estimateType,
      );
      if (linkedDeals && linkedDeals.length > 0) {
        setLinkedDeal(linkedDeals[0]);
      } else {
        setError("No linked deal found");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch linked deal");
    } finally {
      setLoading(false);
    }
  };

  // Unlinking is no longer supported - linking is permanent

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-surface-card rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">Linked Deal</h2>
          <Button variant="ghost" onClick={onClose}>
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {error && (
            <div className="mb-4 p-3 bg-error-light/20 border border-error text-error rounded-md">
              {error}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <svg
                className="animate-spin h-8 w-8 text-primary-color"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : linkedDeal ? (
            <div className="bg-success-light/20 border border-success rounded-md p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-medium text-primary">
                    {linkedDeal.name}
                  </h3>
                  {linkedDeal.company && (
                    <p className="text-sm text-secondary">
                      {linkedDeal.company.name}
                    </p>
                  )}
                </div>
                <span className="text-xs bg-success-light text-success px-2 py-0.5 rounded-full capitalize">
                  {linkedDeal.stage}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div>
                  <p className="text-sm font-medium text-muted">Value</p>
                  <p className="text-sm text-primary">
                    {linkedDeal.value
                      ? formatCurrency(linkedDeal.value, linkedDeal.currency)
                      : "Not set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted">Probability</p>
                  <p className="text-sm text-primary">
                    {linkedDeal.probability !== undefined
                      ? `${Math.round(linkedDeal.probability * 100)}%`
                      : "Not set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted">
                    Expected Close Date
                  </p>
                  <p className="text-sm text-primary">
                    {linkedDeal.expectedCloseDate
                      ? format(
                          new Date(linkedDeal.expectedCloseDate),
                          "MMM d, yyyy",
                        )
                      : "Not set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted">Created</p>
                  <p className="text-sm text-primary">
                    {format(new Date(linkedDeal.createdAt), "MMM d, yyyy")}
                  </p>
                </div>
              </div>

              {linkedDeal.description && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-muted">Description</p>
                  <p className="text-sm text-primary whitespace-pre-line">
                    {linkedDeal.description}
                  </p>
                </div>
              )}

              <div className="flex justify-between items-center mt-4 pt-3 border-t border-success">
                <div className="text-xs text-muted">
                  Linked on{" "}
                  {linkedDeal.linkedAt
                    ? format(new Date(linkedDeal.linkedAt), "MMM d, yyyy")
                    : "Unknown date"}
                </div>
                <div className="flex space-x-2">
                  <a
                    href={`/crm/deals/${linkedDeal.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-xs text-success hover:text-success dark:hover:text-success"
                  >
                    <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                    View Deal
                  </a>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted">
              No linked deal found.
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-default flex justify-end">
          <Button variant="secondary" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LinkedDealModal;
