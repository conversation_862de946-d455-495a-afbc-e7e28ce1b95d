import React, { useState } from "react";
import { Deal, DealUpdate } from "../../../types/crm-types";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface DealCustomFieldsSectionProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
}

/**
 * Component for displaying and editing custom fields
 */
const DealCustomFieldsSection: React.FC<DealCustomFieldsSectionProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
}) => {
  const [newFieldKey, setNewFieldKey] = useState("");
  const [newFieldValue, setNewFieldValue] = useState("");

  // Get custom fields from form data or deal
  const customFields = formData.customFields || deal.customFields || {};

  // Add a new custom field
  const handleAddCustomField = () => {
    if (!newFieldKey.trim()) return;

    const updatedCustomFields = {
      ...customFields,
      [newFieldKey]: newFieldValue,
    };

    onChange("customFields", updatedCustomFields);
    setNewFieldKey("");
    setNewFieldValue("");
  };

  // Update an existing custom field
  const handleUpdateCustomField = (key: string, value: string) => {
    const updatedCustomFields = {
      ...customFields,
      [key]: value,
    };

    onChange("customFields", updatedCustomFields);
  };

  // Remove a custom field
  const handleRemoveCustomField = (key: string) => {
    const updatedCustomFields = { ...customFields };
    delete updatedCustomFields[key];

    onChange("customFields", updatedCustomFields);
  };

  // Check if there are any custom fields
  const hasCustomFields = Object.keys(customFields).length > 0;

  return (
    <div className="bg-surface-card shadow rounded-lg overflow-hidden mt-6">
      <div className="px-4 py-5 sm:px-6 border-b border-default flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-primary">
            Custom Fields
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-muted">
            Additional information about the deal.
          </p>
        </div>
        {isEditing && (
          <div className="flex items-center">
            <span className="text-sm text-muted mr-2">
              These fields are preserved during HubSpot imports
            </span>
          </div>
        )}
      </div>

      <div className="px-4 py-5 sm:p-6">
        {/* Display custom fields */}
        {hasCustomFields ? (
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            {Object.entries(customFields).map(([key, value]) => (
              <div key={key} className="sm:col-span-3">
                <label className="block text-sm font-medium text-primary">
                  {key}
                </label>
                <div className="mt-1 flex">
                  {isEditing ? (
                    <>
                      <Input
                        type="text"
                        value={value as string}
                        onChange={(e) =>
                          handleUpdateCustomField(key, e.target.value)
                        }
                        className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md"
                      />

                      <Button
                        variant="danger"
                        type="button"
                        onClick={() => handleRemoveCustomField(key)}
                        className="inline-flex flex items-center"
                      >
                        <svg
                          className="h-4 w-4"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </Button>
                    </>
                  ) : (
                    <div className="text-sm text-primary">
                      {value as string}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm text-muted">
            No custom fields have been added yet.
          </div>
        )}

        {/* Add new custom field form */}
        {isEditing && (
          <div className="mt-6 border-t border-default pt-6">
            <h4 className="text-sm font-medium text-primary mb-4">
              Add New Custom Field
            </h4>
            <div className="flex items-end space-x-4">
              <div className="flex-1">
                <label
                  htmlFor="newFieldKey"
                  className="block text-sm font-medium text-primary"
                >
                  Field Name
                </label>
                <Input
                  type="text"
                  id="newFieldKey"
                  value={newFieldKey}
                  onChange={(e) => setNewFieldKey(e.target.value)}
                  className="mt-1 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md"
                  placeholder="e.g., Project Code"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="newFieldValue"
                  className="block text-sm font-medium text-primary"
                >
                  Field Value
                </label>
                <Input
                  type="text"
                  id="newFieldValue"
                  value={newFieldValue}
                  onChange={(e) => setNewFieldValue(e.target.value)}
                  className="mt-1 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md"
                  placeholder="e.g., PRJ-123"
                />
              </div>
              <Button
                variant="primary"
                type="button"
                onClick={handleAddCustomField}
                disabled={!newFieldKey.trim()}
              >
                Add Field
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DealCustomFieldsSection;
