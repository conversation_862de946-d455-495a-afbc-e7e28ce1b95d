import React from "react";
import { Deal, DealUpdate } from "../../../types/crm-types";
import { Card } from "../../ui";
import { format, parseISO } from "date-fns";
import { LinkIcon } from "@heroicons/react/24/outline";
import { isFieldControlledByEstimate } from "./utils";
import { Input } from "@/frontend/components/ui/Input";

interface DealTimelineSectionProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
  fieldOwnership?: Record<string, string>;
}

/**
 * Component for displaying and editing deal timeline information
 */
const DealTimelineSection: React.FC<DealTimelineSectionProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
  fieldOwnership,
}) => {
  // Format date to Australian format (DD/MM/YYYY)
  const formatDate = (dateString?: string): string => {
    if (!dateString) return "No date set";
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-default">
        <h3 className="text-lg leading-6 font-medium text-primary">
          Timeline Information
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-muted">
          Important dates for this deal.
        </p>
      </div>
      <div className="px-4 py-5 sm:p-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          {/* Expected Close Date */}
          <div className="sm:col-span-2">
            <label
              htmlFor="expectedCloseDate"
              className="block text-sm font-medium text-primary"
            >
              Expected Close Date
            </label>
            <div className="mt-1">
              {isEditing ? (
                <Input
                  type="date"
                  id="expectedCloseDate"
                  value={
                    formData.expectedCloseDate
                      ? formData.expectedCloseDate.split("T")[0]
                      : ""
                  }
                  onChange={(e) =>
                    onChange("expectedCloseDate", e.target.value)
                  }
                  className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md"
                />
              ) : (
                <div className="text-sm text-primary">
                  {formatDate(deal.expectedCloseDate)}
                </div>
              )}
            </div>
          </div>

          {/* Project Start Date */}
          <div className="sm:col-span-2">
            <label
              htmlFor="startDate"
              className="text-sm font-medium text-primary flex items-center"
            >
              Project Start Date
              {isFieldControlledByEstimate(
                deal,
                "startDate",
                fieldOwnership,
              ) && (
                <span
                  className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-light text-primary"
                  title="This field is controlled by the linked estimate"
                >
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Estimate
                </span>
              )}
            </label>
            <div className="mt-1">
              {isEditing ? (
                <div className="relative">
                  <Input
                    type="date"
                    id="startDate"
                    value={
                      formData.startDate ? formData.startDate.split("T")[0] : ""
                    }
                    onChange={(e) => onChange("startDate", e.target.value)}
                    className={`shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md ${
                      isFieldControlledByEstimate(
                        deal,
                        "startDate",
                        fieldOwnership,
                      )
                        ? "border-primary"
                        : ""
                    }`}
                    disabled={isFieldControlledByEstimate(
                      deal,
                      "startDate",
                      fieldOwnership,
                    )}
                  />

                  {isFieldControlledByEstimate(
                    deal,
                    "startDate",
                    fieldOwnership,
                  ) && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span
                        className="text-primary-color"
                        title="This field is controlled by the linked estimate"
                      >
                        <LinkIcon className="w-4 h-4" />
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-primary flex items-center">
                  {formatDate(deal.startDate)}
                  {isFieldControlledByEstimate(
                    deal,
                    "startDate",
                    fieldOwnership,
                  ) && (
                    <span
                      className="ml-2 text-primary-color"
                      title="This field is controlled by the linked estimate"
                    >
                      <LinkIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              )}
              {isFieldControlledByEstimate(deal, "startDate", fieldOwnership) &&
                isEditing && (
                  <p className="mt-1 text-xs text-primary-color">
                    This field is controlled by the linked estimate. Changes may
                    be overwritten.
                  </p>
                )}
            </div>
          </div>

          {/* Project End Date */}
          <div className="sm:col-span-2">
            <label
              htmlFor="endDate"
              className="text-sm font-medium text-primary flex items-center"
            >
              Project End Date
              {isFieldControlledByEstimate(deal, "endDate", fieldOwnership) && (
                <span
                  className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-light text-primary"
                  title="This field is controlled by the linked estimate"
                >
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Estimate
                </span>
              )}
            </label>
            <div className="mt-1">
              {isEditing ? (
                <div className="relative">
                  <Input
                    type="date"
                    id="endDate"
                    value={
                      formData.endDate ? formData.endDate.split("T")[0] : ""
                    }
                    onChange={(e) => onChange("endDate", e.target.value)}
                    className={`shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-strong rounded-md ${
                      isFieldControlledByEstimate(
                        deal,
                        "endDate",
                        fieldOwnership,
                      )
                        ? "border-primary"
                        : ""
                    }`}
                    disabled={isFieldControlledByEstimate(
                      deal,
                      "endDate",
                      fieldOwnership,
                    )}
                  />

                  {isFieldControlledByEstimate(
                    deal,
                    "endDate",
                    fieldOwnership,
                  ) && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span
                        className="text-primary-color"
                        title="This field is controlled by the linked estimate"
                      >
                        <LinkIcon className="w-4 h-4" />
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-primary flex items-center">
                  {formatDate(deal.endDate)}
                  {isFieldControlledByEstimate(
                    deal,
                    "endDate",
                    fieldOwnership,
                  ) && (
                    <span
                      className="ml-2 text-primary-color"
                      title="This field is controlled by the linked estimate"
                    >
                      <LinkIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
              )}
              {isFieldControlledByEstimate(deal, "endDate", fieldOwnership) &&
                isEditing && (
                  <p className="mt-1 text-xs text-primary-color">
                    This field is controlled by the linked estimate. Changes may
                    be overwritten.
                  </p>
                )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DealTimelineSection;
