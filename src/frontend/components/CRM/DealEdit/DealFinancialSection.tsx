import React from "react";
import { Deal, DealUpdate } from "../../../types/crm-types";
import {
  LinkIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  ChartBarIcon,
  BanknotesIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  UserGroupIcon,
  CalculatorIcon,
} from "@heroicons/react/24/outline";
import { isFieldControlledByEstimate } from "./utils";
import { useQuery } from "react-query";
import { getDraftEstimate } from "../../../api/estimates";
import { getEstimate } from "../../../api/harvest";
import { formatPercentage } from "../../../utils/format";
import { Select, FormField, FormGrid } from "../../shared/forms";

// Define invoice frequency options
const INVOICE_FREQUENCY_OPTIONS = [
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Every 2 Weeks" },
  { value: "monthly", label: "Monthly" },
  { value: "custom", label: "Custom" },
];

// Define payment terms options
const PAYMENT_TERMS_OPTIONS = [
  { value: 7, label: "Net 7" },
  { value: 14, label: "Net 14" },
  { value: 20, label: "Net 20" },
  { value: 30, label: "Net 30" },
];

interface DealFinancialSectionProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
  fieldOwnership?: Record<string, string>;
}

/**
 * Component for displaying and editing deal financial information
 */
const DealFinancialSection: React.FC<DealFinancialSectionProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
  fieldOwnership,
}) => {
  // Get linked estimates
  const linkedEstimates = deal.estimates || [];
  const linkedDraftEstimate = linkedEstimates.find((e) => e.type === "internal");
  const linkedHarvestEstimate = linkedEstimates.find(
    (e) => e.type === "harvest"
  );

  // Fetch draft estimate details if available
  const { data: draftEstimateDetails, isLoading: isDraftLoading } = useQuery(
    ["draftEstimate", linkedDraftEstimate?.id],
    () => getDraftEstimate(linkedDraftEstimate!.id),
    {
      enabled: !!linkedDraftEstimate,
      staleTime: 60000, // 1 minute
    }
  );

  // Fetch Harvest estimate details if available
  const { data: harvestEstimateDetails, isLoading: isHarvestLoading } =
    useQuery(
      ["harvestEstimate", linkedHarvestEstimate?.id],
      () => getEstimate(parseInt(linkedHarvestEstimate!.id)),
      {
        enabled: !!linkedHarvestEstimate,
        staleTime: 60000, // 1 minute
      }
    );

  // Format currency
  const formatCurrency = (value?: number, currency = "AUD") => {
    if (value === undefined || isNaN(value)) return "—";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format percentage
  const formatPercentageLocal = (value?: number) => {
    if (value === undefined || isNaN(value)) return "—";
    return formatPercentage(value, { decimals: 1 });
  };

  // Get margin health class
  const getMarginHealthClass = (marginPercentage?: number) => {
    if (marginPercentage === undefined)
      return "text-muted";
    if (marginPercentage >= 30) return "text-success";
    if (marginPercentage >= 20) return "text-warning";
    return "text-error";
  };

  // Calculate financial metrics from draft estimate - using the same approach as the estimates table
  const calculateDraftEstimateFinancials = (draftEstimate: any) => {
    if (!draftEstimate || !draftEstimate.allocations) return null;

    // Calculate totals from allocations
    let totalDays = 0;
    let totalCost = 0;
    let totalRevenue = 0;
    const staffInitials: string[] = [];

    // Use the allocations to calculate totals - same approach as EstimatesList.tsx
    draftEstimate.allocations.forEach((allocation: any) => {
      // Add staff initials for reference
      const initial = `${allocation.firstName.charAt(0)}${
        allocation.lastName ? allocation.lastName.charAt(0) : ""
      }`;
      if (!staffInitials.includes(initial)) {
        staffInitials.push(initial);
      }

      // Calculate days for this allocation
      const allocationDays =
        allocation.timeAllocations?.reduce(
          (sum: number, ta: any) => sum + (ta.days || 0),
          0
        ) || 0;

      totalDays += allocationDays;
      totalCost += allocation.onbordCostRateDaily * allocationDays;
      totalRevenue += allocation.rateProposedDaily * allocationDays;
    });

    // Calculate margin
    const marginAmount = totalRevenue - totalCost;
    const marginPercentage =
      totalRevenue > 0 ? (marginAmount / totalRevenue) * 100 : 0;

    // Calculate average daily rate
    const averageDailyRate = totalDays > 0 ? totalRevenue / totalDays : 0;

    console.log("Calculated financials from draft estimate:", {
      totalRevenue,
      totalCost,
      marginAmount,
      marginPercentage,
      totalDays,
      averageDailyRate,
      staffInitials,
    });

    return {
      totalRevenue,
      totalCost,
      marginAmount,
      marginPercentage,
      totalDays,
      averageDailyRate,
      staffInitials,
      currency: deal.currency || "AUD",
      source: "draft",
    };
  };

  // Extract financial metrics from estimate
  const estimateFinancials = React.useMemo(() => {
    const dealCurrency = deal.currency || "AUD";

    if (draftEstimateDetails) {
      console.log("Draft estimate details:", draftEstimateDetails);

      // If the API returns totalFees directly, use it
      if (typeof draftEstimateDetails.totalFees === "number") {
        console.log(
          "Using pre-calculated totalFees:",
          draftEstimateDetails.totalFees
        );

        // If we have totalFees but not other metrics, calculate them
        if (
          draftEstimateDetails.allocations &&
          (!draftEstimateDetails.totalCost ||
            !draftEstimateDetails.marginAmount)
        ) {
          console.log("Calculating missing metrics from allocations");
          return calculateDraftEstimateFinancials(draftEstimateDetails);
        }

        return {
          totalRevenue: draftEstimateDetails.totalFees,
          totalCost: draftEstimateDetails.totalCost,
          marginAmount: draftEstimateDetails.marginAmount,
          marginPercentage: draftEstimateDetails.marginPercentage,
          totalDays: draftEstimateDetails.totalDays,
          averageDailyRate: draftEstimateDetails.averageDailyRate,
          currency: dealCurrency,
          source: "draft",
        };
      }

      // If the API doesn"t return calculated fields, calculate them ourselves
      console.log("Calculating all metrics from allocations");
      const calculatedFinancials =
        calculateDraftEstimateFinancials(draftEstimateDetails);
      if (calculatedFinancials) {
        return calculatedFinancials;
      }
    }

    if (harvestEstimateDetails) {
      // Extract data from Harvest estimate
      const amount = harvestEstimateDetails.amount;
      const currency = harvestEstimateDetails.currency || dealCurrency;

      return {
        totalRevenue: amount,
        // We don"t have these values for Harvest estimates
        totalCost: undefined,
        marginAmount: undefined,
        marginPercentage: undefined,
        totalDays: undefined,
        currency: currency,
        source: "harvest",
      };
    }

    return null;
  }, [draftEstimateDetails, harvestEstimateDetails, deal.currency]);

  // Loading state
  const isLoading =
    (linkedDraftEstimate && isDraftLoading) ||
    (linkedHarvestEstimate && isHarvestLoading);

  return (
    <div className="content-section-card">
      <div className="deal-section-header">
        <div className="deal-section-title">
          <div className="deal-section-icon">
            <CurrencyDollarIcon className="w-5 h-5 text-accent" />
          </div>
          <h3>Financial Details</h3>
        </div>
      </div>

      <div className="p-6">
        {/* Financial Summary from Estimate */}
        {(linkedDraftEstimate || linkedHarvestEstimate) && (
          <div className="financial-summary-section">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <DocumentTextIcon className="w-4 h-4 text-primary" />
                </div>
                <h4 className="text-sm font-semibold text-primary">
                  Estimate Financial Summary
                </h4>
              </div>
              {estimateFinancials && (
                <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                  estimateFinancials.source === "internal" 
                    ? "bg-primary-light text-primary-color/30"
                    : "bg-success-light text-success/30"
                }`}>
                  {estimateFinancials.source === "internal" ? "Internal Estimate" : "Harvest Estimate"}
                </span>
              )}
            </div>

            {isLoading ? (
              <div className="animate-pulse">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-20 bg-surface-alt rounded-lg"></div>
                  ))}
                </div>
              </div>
            ) : estimateFinancials ? (
              <div className="financial-metrics-container">
                <div className="financial-metrics-grid">
                  {/* Total Revenue */}
                  <div className="financial-metric-card">
                    <div className="metric-icon-wrapper bg-success-light/30">
                      <CurrencyDollarIcon className="w-5 h-5 text-success" />
                    </div>
                    <div className="metric-details">
                      <p className="metric-label">Total Revenue</p>
                      <p className="metric-value text-2xl">
                        {formatCurrency(
                          estimateFinancials.totalRevenue,
                          estimateFinancials.currency
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Gross Margin - Only show if available */}
                  {estimateFinancials.marginPercentage !== undefined && (
                    <div className="financial-metric-card">
                      <div className={`metric-icon-wrapper ${
                        estimateFinancials.marginPercentage >= 30 
                          ? "bg-success-light/30"
                          : estimateFinancials.marginPercentage >= 20
                          ? "bg-warning-light/30"
                          : "bg-error-light/30"
                      }`}>
                        {estimateFinancials.marginPercentage >= 20 ? (
                          <ArrowTrendingUpIcon className={`w-5 h-5 ${
                            estimateFinancials.marginPercentage >= 30
                              ? "text-success"
                              : "text-warning"
                          }`} />
                        ) : (
                          <ArrowTrendingDownIcon className="w-5 h-5 text-error" />
                        )}
                      </div>
                      <div className="metric-details">
                        <p className="metric-label">Gross Margin</p>
                        <p className={`metric-value text-2xl ${getMarginHealthClass(
                          estimateFinancials.marginPercentage
                        )}`}>
                          {formatPercentageLocal(estimateFinancials.marginPercentage)}
                        </p>
                        <p className="text-xs text-secondary mt-0.5">
                          {formatCurrency(
                            estimateFinancials.marginAmount,
                            estimateFinancials.currency
                          )}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Total Cost - Only show if available */}
                  {estimateFinancials.totalCost !== undefined && (
                    <div className="financial-metric-card">
                      <div className="metric-icon-wrapper bg-surface-alt">
                        <CalculatorIcon className="w-5 h-5 text-secondary" />
                      </div>
                      <div className="metric-details">
                        <p className="metric-label">Total Cost</p>
                        <p className="metric-value text-xl text-primary">
                          {formatCurrency(
                            estimateFinancials.totalCost,
                            estimateFinancials.currency
                          )}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Total Days - Only show if available */}
                  {estimateFinancials.totalDays !== undefined && (
                    <div className="financial-metric-card">
                      <div className="metric-icon-wrapper bg-primary-light/30">
                        <ClockIcon className="w-5 h-5 text-primary-color" />
                      </div>
                      <div className="metric-details">
                        <p className="metric-label">Total Days</p>
                        <p className="metric-value text-xl">
                          {estimateFinancials.totalDays.toFixed(1)}
                        </p>
                        {estimateFinancials.staffInitials &&
                          estimateFinancials.staffInitials.length > 0 && (
                            <div className="flex items-center gap-1 mt-1">
                              <UserGroupIcon className="w-3 h-3 text-muted" />
                              <span className="text-xs text-secondary">
                                {estimateFinancials.staffInitials.join(", ")}
                              </span>
                            </div>
                          )}
                      </div>
                    </div>
                  )}

                  {/* Average Daily Rate - Only show if available */}
                  {estimateFinancials.averageDailyRate !== undefined && (
                    <div className="financial-metric-card">
                      <div className="metric-icon-wrapper bg-accent-light/30">
                        <ChartBarIcon className="w-5 h-5 text-accent" />
                      </div>
                      <div className="metric-details">
                        <p className="metric-label">Avg. Daily Rate</p>
                        <p className="metric-value text-xl">
                          {formatCurrency(
                            estimateFinancials.averageDailyRate,
                            estimateFinancials.currency
                          )}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="empty-state-card">
                <div className="empty-state-icon">
                  <ChartBarIcon className="w-8 h-8 text-subtle" />
                </div>
                <p className="text-sm text-muted">
                  No financial data available. Link an estimate to see detailed metrics.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Invoice and Payment Settings */}
        <div className="mt-6">
          <div className="section-divider">
            <div className="section-divider-content">
              <div className="flex items-center gap-2">
                <CalendarIcon className="w-4 h-4 text-subtle" />
                <span className="text-sm font-medium text-primary">
                  Invoice & Payment Settings
                </span>
              </div>
            </div>
          </div>

          {isEditing ? (
            <FormGrid columns={2} className="gap-6 mt-4">
              {/* Invoice Frequency */}
              <FormField
                label="Invoice Frequency"
                icon={<ClockIcon className="w-4 h-4" />}
                hint={isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership) ? "Linked to estimate" : undefined}
              >
                <Select
                  value={formData.invoiceFrequency || ""}
                  onChange={(e) => onChange("invoiceFrequency", e.target.value)}
                  disabled={isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership)}
                  variant={isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership) ? "disabled" : "default"}
                  options={[
                    { value: "", label: "Select Frequency" },
                    ...INVOICE_FREQUENCY_OPTIONS.map((option) => ({
                      value: option.value,
                      label: option.label,
                    })),
                  ]}
                />
              </FormField>

              {/* Payment Terms */}
              <FormField
                label="Payment Terms"
                icon={<BanknotesIcon className="w-4 h-4" />}
                hint={isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership) ? "Linked to estimate" : undefined}
              >
                <Select
                  value={formData.paymentTerms?.toString() || ""}
                  onChange={(e) => onChange("paymentTerms", parseInt(e.target.value))}
                  disabled={isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership)}
                  variant={isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership) ? "disabled" : "default"}
                  options={[
                    { value: "", label: "Select Payment Terms" },
                    ...PAYMENT_TERMS_OPTIONS.map((option) => ({
                      value: option.value.toString(),
                      label: option.label,
                    })),
                  ]}
                />
              </FormField>
            </FormGrid>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="info-card">
                <div className="info-card-icon bg-accent-light/30">
                  <ClockIcon className="w-4 h-4 text-accent" />
                </div>
                <div>
                  <p className="info-label">Invoice Frequency</p>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-primary">
                      {deal.invoiceFrequency
                        ? INVOICE_FREQUENCY_OPTIONS.find(
                            (opt) => opt.value === deal.invoiceFrequency
                          )?.label || deal.invoiceFrequency
                        : "Not set"}
                    </span>
                    {isFieldControlledByEstimate(deal, "invoiceFrequency", fieldOwnership) && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-primary-light/30 text-primary-color">
                        <LinkIcon className="w-3 h-3 mr-1" />
                        Linked
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-card-icon bg-primary-light/30">
                  <BanknotesIcon className="w-4 h-4 text-primary-color" />
                </div>
                <div>
                  <p className="info-label">Payment Terms</p>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-primary">
                      {deal.paymentTerms
                        ? PAYMENT_TERMS_OPTIONS.find(
                            (opt) => opt.value === deal.paymentTerms
                          )?.label || `Net ${deal.paymentTerms}`
                        : "Not set"}
                    </span>
                    {isFieldControlledByEstimate(deal, "paymentTerms", fieldOwnership) && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-primary-light/30 text-primary-color">
                        <LinkIcon className="w-3 h-3 mr-1" />
                        Linked
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DealFinancialSection;
