import React, { useState } from "react";
import { Deal, DealUpdate, DealStage } from "../../../types/crm-types";
import {
  ChevronLeftIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon,
  SparklesIcon,
  DocumentTextIcon,
  PlusIcon,
  ArrowTopRightOnSquareIcon } from
"@heroicons/react/24/outline";
import { LinkIcon } from "@heroicons/react/24/solid";
import DealStageProgress from "./DealStageProgress";
import { isFieldControlledByEstimate, getFirstLinkedEstimate, getEstimateUrl } from "./utils";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "react-query";
import { linkDealEstimate } from "../../../api/crm";
import EstimateLinkModal from "./EstimateLinkModal";
import { useQuery } from "react-query";
import { getDraftEstimates } from "../../../api/estimates";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Card } from "@/frontend/components/ui/Card";

interface DealHeaderCardProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
  onEdit: () => void;
  onSave: (e: React.FormEvent) => void;
  onCancel: () => void;
  onBack: () => void;
  isLoading: boolean;
  fieldOwnership?: Record<string, string>;
}

/**
 * Modern header component for the Deal Edit Page
 */
const DealHeaderCard: React.FC<DealHeaderCardProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
  onEdit,
  onSave,
  onCancel,
  onBack,
  isLoading,
  fieldOwnership
}) => {
  // Fetch draft estimates for the modal
  const { data: draftEstimates = [] } = useQuery(
    "draftEstimates",
    getDraftEstimates
  );

  // Deal stages for dropdown
  const DEAL_STAGES: DealStage[] = [
  "Identified",
  "Qualified",
  "Solution proposal",
  "Solution presentation",
  "Objection handling",
  "Finalising terms",
  "Closed won",
  "Closed lost",
  "Abandoned"];


  // Format currency value
  const formatCurrency = (value?: number, currency?: string): string => {
    if (value === undefined) return "$0";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: currency || "AUD",
      maximumFractionDigits: 0
    }).format(value);
  };

  // Get stage color
  const getStageColor = (stage: string) => {
    if (stage === "Closed won") return "emerald";
    if (stage === "Closed lost" || stage === "Abandoned") return "red";
    return "purple";
  };

  const stageColor = getStageColor(deal.stage);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const linkedEstimate = getFirstLinkedEstimate(deal);
  const [showEstimateLinkModal, setShowEstimateLinkModal] = useState(false);

  // Mutation for linking estimates
  const linkEstimateMutation = useMutation(
    (params: {estimateId: string;estimateType: "internal" | "harvest";}) =>
    linkDealEstimate(deal.id, params.estimateId, params.estimateType),
    {
      onSuccess: (result) => {
        if (result.success) {
          setShowEstimateLinkModal(false);
          // Refresh deal data
          queryClient.invalidateQueries(["deal", deal.id]);
          queryClient.invalidateQueries("deals");
          queryClient.invalidateQueries(["dealFieldOwnership", deal.id]);
          // IMPORTANT: Also invalidate the batch estimates query used by EnhancedDealBoard
          queryClient.invalidateQueries(['all-deal-estimates-batch']);
        }
      }
    }
  );

  // Handle linking an estimate
  const handleLinkEstimate = (estimateId: string, type: "internal" | "harvest") => {
    linkEstimateMutation.mutate({ estimateId, estimateType: type });
  };

  // Handle estimate card click
  const handleEstimateCardClick = () => {
    if (linkedEstimate) {
      // Navigate to the estimate
      const estimateUrl = getEstimateUrl(linkedEstimate);
      if (estimateUrl.startsWith('http')) {
        // External URL (Harvest)
        window.open(estimateUrl, '_blank");
      } else {
        // Internal URL (Draft estimate)
        navigate(estimateUrl);
      }
    } else {
      // Open the estimate link modal
      setShowEstimateLinkModal(true);
    }
  };

  return (
    <div className="deal-header-card">
      {/* Top Navigation Bar */}
      <div className="px-6 py-4 border-b border-default">
        <div className="flex items-center justify-between">
          <Button variant="ghost"
          onClick={onBack}
          className="group">

            <ChevronLeftIcon className="w-4 h-4 mr-1 group-hover:-translate-x-0.5 transition-transform duration-200" />
            Back to Deals
          </Button>

          <div className="flex items-center gap-3">
            {isEditing ?
            <>
                <Button variant="secondary"
              onClick={onCancel}

              disabled={isLoading}>

                  <XMarkIcon className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button variant="primary"
              onClick={onSave}

              disabled={isLoading}>

                  {isLoading ?
                <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                      Saving...
                    </> :

                <>
                      <CheckIcon className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                }
                </Button>
              </> :

            <Button variant="primary"
            onClick={onEdit}>


                <PencilIcon className="w-4 h-4 mr-2" />
                Edit Deal
              </Button>
            }
          </div>
        </div>
      </div>

      {/* Main Header Content */}
      <div className="px-6 py-8">
        {/* Deal Name */}
        <div className="mb-6">
          {isEditing ?
          <div>
              {deal.hubspotId &&
            <label className="block text-sm font-medium text-primary mb-1">
                  Deal Name
                  <span className="ml-2 inline-flex items-center text-xs text-primary-color">
                    <LinkIcon className="w-3 h-3 mr-1" />
                    Synced from HubSpot
                  </span>
                </label>
            }
              <Input
              type="text"
              value={formData.name || ""}
              onChange={(e) => onChange("name", e.target.value)}
              className={`text-3xl font-bold w-full px-4 py-2 rounded-lg border border-strong bg-surface-card text-primary focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-200 ${
              deal.hubspotId ? "bg-surface-page cursor-not-allowed" : ""}`
              }
              placeholder="Deal Name"
              disabled={!!deal.hubspotId} />

              {deal.hubspotId && deal.hubspot_name && deal.hubspot_name !== deal.name &&
            <p className="mt-1 text-sm text-muted">
                  HubSpot name: {deal.hubspot_name}
                </p>
            }
            </div> :

          <div>
              <h1 className="text-3xl font-bold text-primary">
                {deal.name}
                {deal.hubspotId &&
              <span className="ml-3 inline-flex items-center text-sm text-primary-color">
                    <LinkIcon className="w-4 h-4 mr-1" />
                    HubSpot
                  </span>
              }
              </h1>
              {deal.hubspotId && deal.hubspot_name && deal.hubspot_name !== deal.name &&
            <p className="mt-1 text-sm text-muted">
                  HubSpot name: {deal.hubspot_name}
                </p>
            }
            </div>
          }
        </div>

        {/* Key Metrics Row */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          {/* Deal Value */}
          <div className="metric-highlight-card">
            <div className="metric-icon bg-success-light">
              <CurrencyDollarIcon className="w-5 h-5 text-success" />
            </div>
            <div className="metric-content">
              <p className="metric-label">Deal Value</p>
              <div>
                <p className="metric-value">
                  {formatCurrency(deal.value, deal.currency)}
                  {isFieldControlledByEstimate(deal, "value", fieldOwnership) &&
                  <LinkIcon className="inline-block w-3 h-3 ml-1 text-primary-color" title="Controlled by estimate" />
                  }
                  {!isFieldControlledByEstimate(deal, "value", fieldOwnership) && deal.hubspotId &&
                  <LinkIcon className="inline-block w-3 h-3 ml-1 text-primary-color" title="Synced from HubSpot" />
                  }
                </p>
                {deal.hubspotId && deal.hubspot_value !== undefined && deal.hubspot_value !== deal.value &&
                <p className="text-xs text-muted mt-0.5">
                    HubSpot: {formatCurrency(deal.hubspot_value, deal.currency)}
                  </p>
                }
              </div>
            </div>
          </div>

          {/* Probability */}
          <div className="metric-highlight-card">
            <div className="metric-icon bg-primary-light">
              <ChartBarIcon className="w-5 h-5 text-primary-color" />
            </div>
            <div className="metric-content">
              <p className="metric-label">Probability</p>
              <p className="metric-value">{deal.probability ? Math.round(deal.probability * 100) : 0}%</p>
            </div>
          </div>

          {/* Weighted Value */}
          <div className="metric-highlight-card">
            <div className="metric-icon bg-accent-light">
              <SparklesIcon className="w-5 h-5 text-accent" />
            </div>
            <div className="metric-content">
              <p className="metric-label">Weighted Value</p>
              <p className="metric-value">
                {formatCurrency((deal.value || 0) * (deal.probability || 0), deal.currency)}
              </p>
            </div>
          </div>

          {/* Expected Close */}
          <div className="metric-highlight-card">
            <div className="metric-icon bg-warning-light">
              <CalendarIcon className="w-5 h-5 text-warning" />
            </div>
            <div className="metric-content">
              <p className="metric-label">Expected Close</p>
              <p className="metric-value">
                {deal.expectedCloseDate ?
                new Date(deal.expectedCloseDate).toLocaleDateString("en-AU", {
                  month: "short",
                  day: "numeric",
                  year: "numeric"
                }) :
                "Not set"}
              </p>
            </div>
          </div>

          {/* Estimate Link Card */}
          <div
            className="metric-highlight-card cursor-pointer hover:scale-105 transition-transform duration-200"
            onClick={handleEstimateCardClick}>

            <div className={`metric-icon ${
            linkedEstimate ?
            "bg-success-light" :
            "bg-surface-alt"}`
            }>
              {linkedEstimate ?
              <DocumentTextIcon className="w-5 h-5 text-success" /> :

              <PlusIcon className="w-5 h-5 text-secondary" />
              }
            </div>
            <div className="metric-content">
              <p className="metric-label">Estimate</p>
              <div className="flex items-center gap-1">
                <p className="metric-value text-sm">
                  {linkedEstimate ?
                  <>
                      {linkedEstimate.type === 'internal' ? "Internal" : "Harvest"}
                      {linkedEstimate.name && ` #${linkedEstimate.name}`}
                    </> :

                  "Link Estimate"
                  }
                </p>
                {linkedEstimate &&
                <ArrowTopRightOnSquareIcon className="w-3 h-3 text-muted" />
                }
              </div>
            </div>
          </div>
        </div>

        {/* Stage Progress or Edit Form */}
        {isEditing ?
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Stage Selection */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Deal Stage
              </label>
              <Select
              value={formData.stage || ""}
              onChange={(e) => onChange("stage", e.target.value)}
              className="w-full">

                {DEAL_STAGES.map((stage) =>
              <option key={stage} value={stage}>
                    {stage}
                  </option>
              )}
              </Select>
            </div>

            {/* Value & Currency */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Deal Value
                {isFieldControlledByEstimate(deal, "value", fieldOwnership) &&
              <span className="ml-2 text-xs text-primary-color">
                    <LinkIcon className="inline w-3 h-3 mr-1" />
                    Linked to estimate
                  </span>
              }
              </label>
              {isFieldControlledByEstimate(deal, "value", fieldOwnership) ?
            <div className="px-4 py-2.5 rounded-lg border border-primary bg-primary-light/20 text-primary">
                  {formatCurrency(deal.value, deal.currency)}
                </div> :

            <div className="flex gap-2">
                  <Select
                value={formData.currency || "AUD"}
                onChange={(e) => onChange("currency", e.target.value)}>

                    <option value="AUD">AUD</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </Select>
                  <Input
                type="number"
                value={formData.value || ""}
                onChange={(e) =>
                onChange("value", e.target.value === "" ? undefined : parseFloat(e.target.value))
                }
                placeholder="0" />

                </div>
            }
            </div>

            {/* Probability */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Probability
              </label>
              <div className="relative">
                <Input
                type="number"
                min="0"
                max="100"
                value={
                formData.probability !== undefined ?
                Math.round(formData.probability * 100) :
                ""
                }
                onChange={(e) =>
                onChange(
                  "probability",
                  e.target.value === "" ? undefined : parseFloat(e.target.value) / 100
                )
                }
                className="w-full"
                placeholder="0" />

                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <span className="text-muted">%</span>
                </div>
              </div>
            </div>

            {/* Expected Close Date */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Expected Close Date
              </label>
              <Input
              type="date"
              value={formData.expectedCloseDate || ""}
              onChange={(e) => onChange("expectedCloseDate", e.target.value || undefined)}
              className="w-full" />

            </div>
          </div> :

        <>
            {/* Additional Info */}
            <div className="flex items-center justify-end mb-4">
              <div className="flex items-center gap-4 text-sm text-muted">
                {deal.company &&
              <div className="flex items-center gap-1">
                    <BuildingOfficeIcon className="w-4 h-4" />
                    <span>{deal.company.name}</span>
                  </div>
              }
                {deal.owner &&
              <div className="flex items-center gap-1">
                    <UserIcon className="w-4 h-4" />
                    <span>{deal.owner}</span>
                  </div>
              }
              </div>
            </div>
            
            {/* Stage Progress */}
            <DealStageProgress currentStage={deal.stage} />
          </>
        }
      </div>

      {/* Estimate Link Modal */}
      <EstimateLinkModal
        isOpen={showEstimateLinkModal}
        onClose={() => {
          setShowEstimateLinkModal(false);
        }}
        estimateType="internal"
        draftEstimates={draftEstimates}
        onLinkEstimate={handleLinkEstimate}
        isLoading={linkEstimateMutation.isLoading} />

    </div>);

};

export default DealHeaderCard;