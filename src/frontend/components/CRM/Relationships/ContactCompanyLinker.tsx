import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { Contact, Company, ContactRole } from "../../../types/crm-types";
import { getContacts, getCompanies, linkContactToCompany, unlinkContactFromCompany } from "../../../api/crm";
import { Card } from "../../ui";
import { Badge } from "../../ui";
import {
  BuildingOffice2Icon,
  UserIcon,
  LinkIcon,
  TrashIcon,
  PlusIcon } from "@heroicons/react/24/outline";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Checkbox } from "@/frontend/components/ui/Checkbox";

interface ContactCompanyLinkerProps {
  mode: "contact" | "company";
  entityId: string;
  onClose?: () => void;
}

const ContactCompanyLinker: React.FC<ContactCompanyLinkerProps> = ({ mode, entityId, onClose }) => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<ContactRole>('user');
  const [isPrimary, setIsPrimary] = useState(false);

  // Fetch data based on mode
  const { data: contacts, isLoading: contactsLoading } = useQuery('contacts', getContacts, {
    enabled: mode ==="company'
  });

  const { data: companies, isLoading: companiesLoading } = useQuery('companies', getCompanies, {
    enabled: mode ==="contact'
  });

  // Get current entity details
  const currentEntity = mode ==="contact' ?
  contacts?.find((c) => c.id === entityId) :
  companies?.find((c) => c.id === entityId);

  // Mutation for linking
  const linkMutation = useMutation(
    async ({ contactId, companyId, role, isPrimary




    }: {contactId: string;companyId: string;role: ContactRole;isPrimary: boolean;}) => {
      return linkContactToCompany(contactId, companyId, role, isPrimary);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['contact', entityId]);
        queryClient.invalidateQueries(['company', entityId]);
        queryClient.invalidateQueries('contacts');
        queryClient.invalidateQueries('companies');
        setSelectedEntity(null);
        setSearchTerm('');
      }
    }
  );

  // Mutation for unlinking
  const unlinkMutation = useMutation(
    async ({ contactId, companyId }: {contactId: string;companyId: string;}) => {
      return unlinkContactFromCompany(contactId, companyId);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['contact', entityId]);
        queryClient.invalidateQueries(['company', entityId]);
        queryClient.invalidateQueries('contacts');
        queryClient.invalidateQueries('companies');
      }
    }
  );

  // Filter entities based on search
  const filteredEntities = mode ==="contact' ?
  companies?.filter((c) =>
  c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
  c.industry?.toLowerCase().includes(searchTerm.toLowerCase())
  ) :
  contacts?.filter((c) =>
  `${c.firstName} ${c.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
  c.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get existing relationships
  const existingRelationships = mode ==="contact' ?
  (currentEntity as Contact)?.companies || [] :
  (currentEntity as Company)?.contacts || [];

  const handleLink = () => {
    if (!selectedEntity) return;

    if (mode ==="contact') {
      linkMutation.mutate({
        contactId: entityId,
        companyId: selectedEntity,
        role: selectedRole,
        isPrimary
      });
    } else {
      linkMutation.mutate({
        contactId: selectedEntity,
        companyId: entityId,
        role: selectedRole,
        isPrimary
      });
    }
  };

  const handleUnlink = (contactId: string, companyId: string) => {
    if (confirm('Are you sure you want to remove this relationship?')) {
      unlinkMutation.mutate({ contactId, companyId });
    }
  };

  const roleOptions: {value: ContactRole;label: string;}[] = [
  { value: "decision_maker", label: "Decision Maker" },
  { value: "influencer", label: "Influencer" },
  { value: "champion", label: "Champion" },
  { value: "user", label: "User" },
  { value: "technical", label: "Technical" },
  { value: "executive", label: "Executive" },
  { value: "other", label: "Other" }];


  return (
    <div className="space-y-6">
      {/* Current Relationships */}
      <Card>
        <div className="px-4 py-5 sm:px-6 border-b border-default">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Current {mode ==="contact' ?'Company' : "Contact"} Relationships
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-muted">
            {mode ==="contact' ?
            `Companies this contact is associated with` :
            `Contacts associated with this company`}
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6">
          {existingRelationships.length === 0 ?
          <p className="text-muted text-center py-4">
              No relationships yet
            </p> :

          <div className="space-y-3">
              {existingRelationships.map((rel: any) => {
              const entity = mode ==="contact' ? rel.company : rel;
              const role = mode ==="contact' ? rel.role : rel.role;
              const isPrimary = mode ==="contact' ? rel.isPrimary : rel.isPrimary;

              return (
                <div
                  key={entity.id}
                  className="flex items-center justify-between p-3 bg-surface-page rounded-lg">

                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {mode ==="contact' ?
                      <BuildingOffice2Icon className="h-5 w-5 text-subtle" /> :

                      <UserIcon className="h-5 w-5 text-subtle" />
                      }
                      </div>
                      <div>
                        <p className="font-medium text-primary">
                          {mode ==="contact' ? entity.name : `${entity.firstName} ${entity.lastName}`}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" size="sm">
                            {roleOptions.find((r) => r.value === role)?.label || role}
                          </Badge>
                          {isPrimary &&
                        <Badge variant="primary" size="sm">Primary</Badge>
                        }
                        </div>
                      </div>
                    </div>
                    
                    <Button variant="danger"
                  onClick={() => handleUnlink(
                    mode ==="contact' ? entityId : entity.id,
                    mode ==="contact' ? entity.id : entityId
                  )}

                  title="Remove relationship">

                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>);

            })}
            </div>
          }
        </div>
      </Card>

      {/* Add New Relationship */}
      <Card>
        <div className="px-4 py-5 sm:px-6 border-b border-default">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Add New {mode ==="contact' ?'Company' : "Contact"} Relationship
          </h3>
        </div>
        
        <div className="px-4 py-5 sm:p-6 space-y-4">
          {/* Debug info */}
          {process.env.NODE_ENV ==="development' &&
          <div className="text-xs text-muted mb-2">
              Mode: {mode}, Entity ID: {entityId}, 
              {mode ==="company' ? `Contacts loaded: ${contacts?.length || 0}` : `Companies loaded: ${companies?.length || 0}`}
            </div>
          }
          
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Search {mode ==="contact' ?'Companies' : "Contacts"}
              {(contactsLoading || companiesLoading) &&" (Loading...)'}
            </label>
            <Input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={mode ==="contact' ?'Search companies...' : "Search contacts..."}
              className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" />

          </div>

          {/* Entity Selection */}
          {searchTerm &&
          <>
              {contactsLoading || companiesLoading ?
            <div className="text-center py-4 text-muted">
                  Loading {mode ==="contact' ?'companies' : "contacts"}...
                </div> :
            filteredEntities && filteredEntities.length > 0 ?
            <>
                  <div>
                    <label className="block text-sm font-medium text-primary mb-1">
                      Select {mode ==="contact' ?'Company' : "Contact"}
                    </label>
                    <div className="border border-strong rounded-md max-h-48 overflow-y-auto">
                      {filteredEntities.map((entity, index) => {
                    const entityName = mode ==="contact' ?
                    entity.name :
                    `${(entity as Contact).firstName} ${(entity as Contact).lastName}`;
                    const entityId = entity.id;
                    const isSelected = selectedEntity === entityId;

                    return (
                      <Button variant="secondary"
                      key={entityId}
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        console.log('Selecting entity:', entityId, entityName);
                        setSelectedEntity(entityId);
                      }}
                      className={`w-full text-left px-3 py-2 transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary ${
                      index === 0 ? "rounded-t-md" : ""} ${

                      index === filteredEntities.length - 1 ? "rounded-b-md" : ""} ${

                      isSelected ? "bg-primary-light" : "hover:bg-surface-page dark:hover:bg-surface-elevated"}`
                      }>

                            <p className="font-medium text-primary">
                              {entityName}
                            </p>
                            {mode ==="contact' && (entity as Company).industry &&
                        <p className="text-sm text-muted">
                                {(entity as Company).industry}
                              </p>
                        }
                            {mode ==="company' && (entity as Contact).email &&
                        <p className="text-sm text-muted">
                                {(entity as Contact).email}
                              </p>
                        }
                          </Button>);

                  })}
                    </div>
                  </div>
                  {selectedEntity &&
              <div className="mt-2 p-2 bg-success-light/20 text-success rounded text-sm">
                      Selected: {filteredEntities.find((e) => e.id === selectedEntity)?.name ||
                `${(filteredEntities.find((e) => e.id === selectedEntity) as Contact)?.firstName} ${(filteredEntities.find((e) => e.id === selectedEntity) as Contact)?.lastName}`
                }
                    </div>
              }
                </> :
            searchTerm ?
            <div className="text-center py-4 text-muted">
                  No {mode ==="contact' ?'companies' : "contacts"} found matching"{searchTerm}'
                </div> :
            null}
            </>
          }

          {/* Role Selection */}
          {selectedEntity &&
          <>
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Role
                </label>
                <Select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value as ContactRole)}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">

                  {roleOptions.map((option) =>
                <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                )}
                </Select>
              </div>

              {/* Primary Toggle */}
              <div>
                <Checkbox
                  checked={isPrimary}
                  onChange={(e) => setIsPrimary(e.target.checked)}
                  label={`Mark as primary ${mode ==="contact' ?'company' : "contact"}`}
                />
              </div>

              {/* Add Button */}
              <Button variant="primary"
            onClick={handleLink}
            disabled={!selectedEntity || linkMutation.isLoading}
            className="w-full flex items-center justify-center gap-2">

                <LinkIcon className="h-4 w-4" />
                {linkMutation.isLoading ? "Adding..." : "Add Relationship"}
              </Button>
            </>
          }
        </div>
      </Card>
    </div>);

};

export default ContactCompanyLinker;