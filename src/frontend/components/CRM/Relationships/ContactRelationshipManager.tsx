import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { Contact } from "../../../types/crm-types";
import { getContacts, createContactRelationship, deleteContactRelationship } from "../../../api/crm";
import { fetchFromApi } from "../../../api/utils";
import { Card } from "../../ui";
import { Badge } from "../../ui";
import { Checkbox } from "../../ui";
import {
  UserIcon,
  UserGroupIcon,
  LinkIcon,
  TrashIcon,
  ArrowRightIcon } from "@heroicons/react/24/outline";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface ContactRelationshipManagerProps {
  contactId: string;
  onClose?: () => void;
}

type RelationshipType = 'knows' | "reports_to" | "introduced_by" | "worked_with" | "colleague";

interface ContactRelationship {
  id: string;
  sourceContactId: string;
  targetContactId: string;
  relationshipType: RelationshipType;
  strength: number;
  context?: string;
  sourceContact?: Contact;
  targetContact?: Contact;
}

const ContactRelationshipManager: React.FC<ContactRelationshipManagerProps> = ({ contactId, onClose }) => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContact, setSelectedContact] = useState<string | null>(null);
  const [relationshipType, setRelationshipType] = useState<RelationshipType>('knows');
  const [strength, setStrength] = useState(3);
  const [context, setContext] = useState('');
  const [isReversed, setIsReversed] = useState(false);

  // Fetch all contacts
  const { data: contacts } = useQuery('contacts', getContacts);

  // Fetch relationships for this contact
  const { data: relationships } = useQuery(
    ['contact-relationships', contactId],
    async () => {
      const response = await fetchFromApi(`/api/crm/contacts/${contactId}/relationships`);
      return response.data as ContactRelationship[];
    }
  );

  // Get current contact details
  const currentContact = contacts?.find((c) => c.id === contactId);

  // Filter contacts based on search (excluding current contact)
  const filteredContacts = contacts?.filter((c) =>
  c.id !== contactId && (
  `${c.firstName} ${c.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
  c.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
  c.company?.name?.toLowerCase().includes(searchTerm.toLowerCase()))

  );

  // Mutation for creating relationship
  const createMutation = useMutation(
    async (data: {
      sourceContactId: string;
      targetContactId: string;
      relationshipType: RelationshipType;
      strength: number;
      context?: string;
    }) => {
      return createContactRelationship(data);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['contact-relationships', contactId]);
        setSelectedContact(null);
        setSearchTerm('');
        setContext('');
        setStrength(3);
      }
    }
  );

  // Mutation for deleting relationship
  const deleteMutation = useMutation(
    async (relationshipId: string) => {
      return deleteContactRelationship(relationshipId);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['contact-relationships', contactId]);
      }
    }
  );

  const handleCreateRelationship = () => {
    if (!selectedContact) return;

    createMutation.mutate({
      sourceContactId: isReversed ? selectedContact : contactId,
      targetContactId: isReversed ? contactId : selectedContact,
      relationshipType,
      strength,
      context: context.trim() || undefined
    });
  };

  const handleDeleteRelationship = (relationshipId: string) => {
    if (confirm('Are you sure you want to remove this relationship?')) {
      deleteMutation.mutate(relationshipId);
    }
  };

  const relationshipOptions: {value: RelationshipType;label: string;reversedLabel?: string;}[] = [
  { value: "knows", label: "Knows" },
  { value: 'reports_to', label:'Reports to", reversedLabel: "Manages" },
  { value: 'introduced_by', label:'Introduced by", reversedLabel: "Introduced" },
  { value: "worked_with", label: "Worked with" },
  { value: "colleague", label: "Colleague" }];


  const getRelationshipLabel = (type: RelationshipType, reversed: boolean = false) => {
    const option = relationshipOptions.find((o) => o.value === type);
    if (reversed && option?.reversedLabel) {
      return option.reversedLabel;
    }
    return option?.label || type;
  };

  const getRelationshipColor = (type: RelationshipType) => {
    switch (type) {
      case"reports_to':return'primary';
      case"introduced_by':return'secondary';
      case"worked_with':return'success';
      case"colleague':return'neutral';
      default:return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Relationships */}
      <Card>
        <div className="px-4 py-5 sm:px-6 border-b border-default">
          <div className="flex items-center gap-2">
            <UserGroupIcon className="h-5 w-5 text-subtle" />
            <h3 className="text-lg leading-6 font-medium text-primary">
              Contact Relationships
            </h3>
          </div>
          <p className="mt-1 max-w-2xl text-sm text-muted">
            How {currentContact?.firstName} {currentContact?.lastName} is connected to other contacts
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6">
          {!relationships || relationships.length === 0 ?
          <p className="text-muted text-center py-4">
              No relationships yet
            </p> :

          <div className="space-y-3">
              {relationships.map((rel) => {
              const isSource = rel.sourceContactId === contactId;
              const otherContact = isSource ? rel.targetContact : rel.sourceContact;

              return (
                <div
                  key={rel.id}
                  className="flex items-center justify-between p-3 bg-surface-page rounded-lg">

                    <div className="flex items-center gap-3">
                      <UserIcon className="h-5 w-5 text-subtle flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-primary">
                            {currentContact?.firstName}
                          </p>
                          <ArrowRightIcon className="h-4 w-4 text-subtle" />
                          <Badge
                          variant={getRelationshipColor(rel.relationshipType)}
                          size="sm">

                            {getRelationshipLabel(rel.relationshipType, !isSource)}
                          </Badge>
                          <ArrowRightIcon className="h-4 w-4 text-subtle" />
                          <p className="font-medium text-primary">
                            {otherContact?.firstName} {otherContact?.lastName}
                          </p>
                        </div>
                        {rel.context &&
                      <p className="text-sm text-muted mt-1">
                            {rel.context}
                          </p>
                      }
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-xs text-muted">
                            Strength: {rel.strength}/5
                          </span>
                          {otherContact?.company &&
                        <span className="text-xs text-muted">
                              {otherContact.company.name}
                            </span>
                        }
                        </div>
                      </div>
                    </div>
                    
                    <Button variant="danger"
                  onClick={() => handleDeleteRelationship(rel.id)}

                  title="Remove relationship">

                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>);

            })}
            </div>
          }
        </div>
      </Card>

      {/* Add New Relationship */}
      <Card>
        <div className="px-4 py-5 sm:px-6 border-b border-default">
          <h3 className="text-lg leading-6 font-medium text-primary">
            Add New Relationship
          </h3>
        </div>
        
        <div className="px-4 py-5 sm:p-6 space-y-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-primary mb-1">
              Search Contacts
            </label>
            <Input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name, email, or company..."
              className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" />

          </div>

          {/* Contact Selection */}
          {searchTerm && filteredContacts && filteredContacts.length > 0 &&
          <div>
              <label className="block text-sm font-medium text-primary mb-1">
                Select Contact
              </label>
              <div className="max-h-48 overflow-y-auto border border-strong rounded-md">
                {filteredContacts.map((contact) =>
              <Button variant="secondary"
              key={contact.id}
              onClick={() => setSelectedContact(contact.id)}
              className={`w-full text-left px-3 py-2 hover:bg-surface-page dark:hover:bg-surface-elevated ${
              selectedContact === contact.id ? "bg-primary-light" : ""}`
              }>

                    <p className="font-medium text-primary">
                      {contact.firstName} {contact.lastName}
                    </p>
                    <p className="text-sm text-muted">
                      {contact.email} • {contact.company?.name || "No company"}
                    </p>
                  </Button>
              )}
              </div>
            </div>
          }

          {/* Relationship Configuration */}
          {selectedContact &&
          <>
              {/* Relationship Type */}
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Relationship Type
                </label>
                <Select
                value={relationshipType}
                onChange={(e) => setRelationshipType(e.target.value as RelationshipType)}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">

                  {relationshipOptions.map((option) =>
                <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                )}
                </Select>
              </div>

              {/* Direction Toggle for asymmetric relationships */}
              {relationshipOptions.find((o) => o.value === relationshipType)?.reversedLabel &&
            <div className="flex items-center">
                  <Checkbox
                    id="isReversed"
                    checked={isReversed}
                    onChange={(e) => setIsReversed(e.target.checked)}
                    label={`Reverse relationship (${getRelationshipLabel(relationshipType, true)} instead)`}
                  />
                </div>
            }

              {/* Relationship Strength */}
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Relationship Strength
                </label>
                <div className="flex items-center gap-4">
                  <input
                  type="range"
                  min="1"
                  max="5"
                  value={strength}
                  onChange={(e) => setStrength(parseInt(e.target.value))}
                  className="flex-1" />

                  <span className="text-sm font-medium text-primary w-8">
                    {strength}/5
                  </span>
                </div>
              </div>

              {/* Context */}
              <div>
                <label className="block text-sm font-medium text-primary mb-1">
                  Context (Optional)
                </label>
                <Textarea
                value={context}
                onChange={(e) => setContext(e.target.value)}
                placeholder="How do they know each other? Any additional context..."
                rows={2}
                className="w-full px-3 py-2 border border-strong rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" />

              </div>

              {/* Add Button */}
              <Button variant="primary"
            onClick={handleCreateRelationship}
            disabled={!selectedContact || createMutation.isLoading}
            className="w-full flex items-center justify-center gap-2">

                <LinkIcon className="h-4 w-4" />
                {createMutation.isLoading ? "Adding..." : "Add Relationship"}
              </Button>
            </>
          }
        </div>
      </Card>
    </div>);

};

export default ContactRelationshipManager;