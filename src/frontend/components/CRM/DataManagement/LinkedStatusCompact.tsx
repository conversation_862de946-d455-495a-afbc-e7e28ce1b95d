import React from "react";
import {
  LinkIcon,
  CheckCircleIcon,
  XMarkIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon as CheckCircleSolid } from "@heroicons/react/24/solid";
import { But<PERSON> } from "@/frontend/components/ui/Button";

interface LinkedStatusCompactProps {
  system:"hubspot" |"harvest";
  isLinked: boolean;
  externalId?: string | number | null;
  onLink?: () => void;
  onUnlink?: () => void;
  disabled?: boolean;
}

/**
 * Compact linked status component for table cells
 * Features a modern, streamlined design perfect for data tables
 */
const LinkedStatusCompact: React.FC<LinkedStatusCompactProps> = ({
  system,
  isLinked,
  externalId,
  onLink,
  onUnlink,
  disabled = false
}) => {
  const systemConfig = {
    hubspot: {
      name:"HubSpot",
      bgColor:"bg-warning-light dark:bg-warning-dark/20",
      borderColor:"border-orange-200 dark:border-orange-800",
      textColor:"text-warning-dark dark:text-orange-300",
      iconColor:"text-warning dark:text-warning-light",
      hoverBg:"hover:bg-warning-light dark:hover:bg-warning-dark/30"
    },
    harvest: {
      name:"Harvest",
      bgColor:"bg-success-light dark:bg-success-dark/20",
      borderColor:"border-success dark:border-green-800",
      textColor:"text-success dark:text-green-300",
      iconColor:"text-success dark:text-success-light",
      hoverBg:"hover:bg-success-light dark:hover:bg-success-dark/30"
    }
  };

  const config = systemConfig[system];

  if (isLinked) {
    return (
      <div className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-md border transition-all duration-200
        ${config.bgColor} ${config.borderColor}
        ${!disabled && onUnlink ? config.hoverBg : ""}
        ${disabled ? "opacity-50" : ""}`}>
        {/* Success Icon */}
        <CheckCircleSolid className={`w-4 h-4 ${config.iconColor}`} />
        
        {/* System Name */}
        <span className={`text-xs font-medium ${config.textColor}`}>
          {config.name}
        </span>
        
        {/* External ID */}
        {externalId &&
        <>
            <span className="text-subtle dark:text-muted">•</span>
            <span className="text-xs font-mono text-secondary dark:text-subtle">
              {externalId}
            </span>
          </>
        }
        
        {/* Unlink Action */}
        {onUnlink &&
        <Button variant="secondary"
        onClick={(e) => {
          e.stopPropagation();
          onUnlink();
        }}
        disabled={disabled}
        className={`ml-auto -mr-1 p-0.5 rounded transition-colors duration-150
              ${disabled ? "cursor-not-allowed" : "cursor-pointer"}
              text-muted hover:text-error dark:text-subtle dark:hover:text-error-light`}
        title={`Unlink from ${config.name}`}>

            <XMarkIcon className="w-3.5 h-3.5" />
          </Button>
        }
      </div>);

  }

  // Unlinked state
  return (
    <Button variant="secondary"
    onClick={onLink}
    disabled={disabled || !onLink}
    className={`inline-flex items-center gap-2`}
    title={`Link to ${config.name}`}>

      <LinkIcon className="w-4 h-4" />
      <span className="text-xs font-medium">
        Link {config.name}
      </span>
    </Button>);

};

export default LinkedStatusCompact;