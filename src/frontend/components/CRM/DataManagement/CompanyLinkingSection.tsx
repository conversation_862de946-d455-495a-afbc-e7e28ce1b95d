import React from "react";
import { Card } from "../../ui";
import { Button } from "../../ui";
import type { Company } from "../../../types/crm-types";

interface CompanyLinkingSectionProps {
  title: string;
  subtitle: string;
  companies: Company[];
  linkType: 'hubspot' | 'harvest' | 'both';
  onLinkClick: (company: Company, linkType: 'hubspot' | 'harvest') => void;
  onUnlinkClick: (companyId: string) => void;
  isLoading: boolean;
}

/**
 * Section for displaying companies that need linking
 */
const CompanyLinkingSection: React.FC<CompanyLinkingSectionProps> = ({
  title,
  subtitle,
  companies,
  linkType,
  onLinkClick,
  onUnlinkClick,
  isLoading
}) => {
  const getSourceBadge = (company: Company) => {
    const source = company.source || 'Unknown';
    const colors = {
      HubSpot: 'bg-primary-light text-primary',
      Harvest: 'bg-success-light text-success',
      Manual: 'bg-warning-light text-warning',
      Unknown: 'bg-surface-alt text-primary'
    };

    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${colors[source] || colors.Unknown}`}>
        {source}
      </span>
    );
  };

  const getLinkButtons = (company: Company) => {
    if (linkType === 'both') {
      return (
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onLinkClick(company, 'hubspot')}
            disabled={isLoading}
          >
            Link HubSpot
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onLinkClick(company, 'harvest')}
            disabled={isLoading}
          >
            Link Harvest
          </Button>
        </div>
      );
    }

    const buttonText = linkType === 'hubspot' ? 'Link HubSpot' : 'Link Harvest';
    
    return (
      <Button
        size="sm"
        variant="outline"
        onClick={() => onLinkClick(company, linkType)}
        disabled={isLoading}
      >
        {buttonText}
      </Button>
    );
  };

  const getLinkedSystemInfo = (company: Company) => {
    const links = [];
    
    if (company.hubspotId) {
      links.push(
        <span key="hubspot" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-light text-primary">
          HubSpot: {company.hubspotId}
        </span>
      );
    }
    
    if (company.harvestId) {
      links.push(
        <span key="harvest" className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-success-light text-success">
          Harvest: {company.harvestId}
        </span>
      );
    }

    return links.length > 0 ? (
      <div className="flex gap-1 mt-2">
        {links}
      </div>
    ) : null;
  };

  if (companies.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-primary mb-2">
          {title}
        </h3>
        <p className="text-secondary mb-4">
          {subtitle}
        </p>
        <div className="text-center py-8 text-muted">
          <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-sm">All companies in this category are properly linked!</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-primary">
            {title}
          </h3>
          <p className="text-secondary">
            {subtitle}
          </p>
        </div>
        <div className="text-sm text-muted">
          {companies.length} {companies.length === 1 ? 'company' : 'companies'}
        </div>
      </div>

      <div className="space-y-3">
        {companies.map((company) => (
          <div
            key={company.id}
            className="flex items-center justify-between p-4 border border-default rounded-lg hover:bg-surface-page dark:hover:bg-surface-page/50 transition-colors"
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-primary truncate">
                    {company.name}
                  </h4>
                  <div className="flex items-center gap-2 mt-1">
                    {getSourceBadge(company)}
                    {company.industry && (
                      <span className="text-xs text-muted">
                        {company.industry}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              {getLinkedSystemInfo(company)}
            </div>

            <div className="flex items-center gap-2 ml-4">
              {getLinkButtons(company)}
              
              {/* Show unlink option if company has links */}
              {(company.hubspotId || company.harvestId) && linkType !== 'both' && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onUnlinkClick(company.id)}
                  disabled={isLoading}
                  className="text-error hover:text-error dark:hover:text-error"
                >
                  Unlink
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default CompanyLinkingSection;