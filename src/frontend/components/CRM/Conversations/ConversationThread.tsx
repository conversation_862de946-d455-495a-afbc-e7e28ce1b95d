/**
 * Conversation Thread Component
 * Shows threaded conversations for an entity (deal, contact, company)
 */

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import {
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  VideoCameraIcon,
  EnvelopeIcon,
  HashtagIcon,
  PlusIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
  CheckCircleIcon,
  PauseCircleIcon,
  ArchiveBoxIcon,
  UserCircleIcon,
  XMarkIcon } from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import type { ThreadedNote, ConversationThread, ConversationType, NoteStatus } from "../../../../api/repositories/note-repository";
import { getContacts } from "../../../api/crm";
import { Badge } from "../../ui";
import { Button } from "@/frontend/components/ui/Button";
import { Select } from "@/frontend/components/ui/Select";
import { Textarea } from "@/frontend/components/ui/Textarea";

interface ConversationThreadProps {
  entityType: "deal" | "contact" |"company";
  entityId: string;
  currentUserId?: string;
  onContactClick?: (contactId: string) => void;
}

const conversationTypeIcons: Record<ConversationType, React.ComponentType<any>> = {
  email: EnvelopeIcon,
  call: PhoneIcon,
  meeting: VideoCameraIcon,
  slack: HashtagIcon,
  internal: ChatBubbleLeftRightIcon
};

const statusColors: Record<NoteStatus, string> = {
  open: "blue",
  resolved: "green",
  parked: "yellow",
  archived: "gray"
};

const ConversationThreadComponent: React.FC<ConversationThreadProps> = ({
  entityType,
  entityId,
  currentUserId = 'system",
  onContactClick
}) => {
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set());
  const [showNewThread, setShowNewThread] = useState(false);
  const [newThreadData, setNewThreadData] = useState({
    content:"",
    conversationType: "internal" as ConversationType,
    participants: [] as string[],
    status: "open" as NoteStatus
  });
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  const queryClient = useQueryClient();

  // Fetch all contacts for participant selection
  const { data: contacts = [] } = useQuery('contacts', getContacts, {
    staleTime: 5 * 60 * 1000
  });

  // Fetch conversation threads
  const { data: threads = [], isLoading } = useQuery<ConversationThread[]>(
    ['conversation-threads", entityType, entityId],
    async () => {
      const response = await fetch(`/api/crm/notes/threads?entityType=${entityType}&entityId=${entityId}`);
      if (!response.ok) throw new Error('Failed to fetch conversation threads');
      const result = await response.json();
      return result.data || [];
    },
    {
      refetchInterval: 30 * 1000 // Refresh every 30 seconds
    }
  );

  // Fetch thread details when expanded
  const fetchThreadNotes = async (threadId: string): Promise<ThreadedNote[]> => {
    const response = await fetch(`/api/crm/notes/thread/${threadId}`);
    if (!response.ok) throw new Error('Failed to fetch thread notes');
    const result = await response.json();
    return result.data || [];
  };

  // Create new thread mutation
  const createThreadMutation = useMutation(
    async (data: typeof newThreadData) => {
      const response = await fetch('/api/crm/notes", {
        method: "POST",
        headers: {"Content-Type": "application/json" },
        body: JSON.stringify({
          dealId: entityId, // Legacy field name
          content: data.content,
          conversationType: data.conversationType,
          participants: data.participants,
          status: data.status,
          createdBy: currentUserId
        })
      });
      if (!response.ok) throw new Error('Failed to create thread');
      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['conversation-threads", entityType, entityId]);
        setShowNewThread(false);
        setNewThreadData({
          content:"",
          conversationType: "internal",
          participants: [],
          status: "open"
        });
      }
    }
  );

  // Create reply mutation
  const createReplyMutation = useMutation(
    async ({ parentNoteId, content }: {parentNoteId: string;content: string;}) => {
      const response = await fetch("/api/crm/notes", {
        method: "POST",
        headers: {"Content-Type": "application/json" },
        body: JSON.stringify({
          dealId: entityId,
          content,
          parentNoteId,
          createdBy: currentUserId
        })
      });
      if (!response.ok) throw new Error('Failed to create reply');
      return response.json();
    },
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries(['thread-notes', variables.parentNoteId]);
        queryClient.invalidateQueries(['conversation-threads", entityType, entityId]);
        setReplyingTo(null);
        setReplyContent('");
      }
    }
  );

  // Update thread status mutation
  const updateStatusMutation = useMutation(
    async ({ noteId, status }: {noteId: string;status: NoteStatus;}) => {
      const response = await fetch(`/api/crm/notes/${noteId}/status`, {
        method: "PATCH",
        headers: {"Content-Type": "application/json" },
        body: JSON.stringify({ status })
      });
      if (!response.ok) throw new Error('Failed to update status');
      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['conversation-threads", entityType, entityId]);
      }
    }
  );

  // Toggle thread expansion
  const toggleThread = (threadId: string) => {
    setExpandedThreads((prev) => {
      const next = new Set(prev);
      if (next.has(threadId)) {
        next.delete(threadId);
      } else {
        next.add(threadId);
      }
      return next;
    });
  };

  // Get contact name by ID
  const getContactName = (contactId: string) => {
    const contact = contacts.find((c) => c.id === contactId);
    return contact ? `${contact.firstName} ${contact.lastName}` : "Unknown";
  };

  // Render conversation icon
  const ConversationIcon = ({ type }: {type?: ConversationType;}) => {
    const Icon = type ? conversationTypeIcons[type] : ChatBubbleLeftRightIcon;
    return <Icon className="w-4 h-4" />;
  };

  // Render thread status badge
  const ThreadStatusBadge = ({ status }: {status: NoteStatus;}) => {
    const colors = {
      open: "blue",
      resolved: "green",
      parked: "yellow",
      archived: "gray"
    };

    const icons = {
      open: null,
      resolved: CheckCircleIcon,
      parked: PauseCircleIcon,
      archived: ArchiveBoxIcon
    };

    const Icon = icons[status];

    return (
      <Badge variant={colors[status] as any} size="sm">
        {Icon && <Icon className="w-3 h-3 mr-1" />}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>);

  };

  // Render a single note
  const NoteItem: React.FC<{note: ThreadedNote;isReply?: boolean;}> = ({ note, isReply = false }) => {
    const [showReplies, setShowReplies] = useState(true);

    return (
      <div className={`${isReply ? "ml-12" : ""}`}>
        <div className="flex gap-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-surface-alt rounded-full flex items-center justify-center">
              <UserCircleIcon className="w-5 h-5 text-muted" />
            </div>
          </div>
          <div className="flex-1">
            <div className="bg-surface-page/50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-primary">
                    {note.createdBy}
                  </span>
                  <span className="text-xs text-muted">
                    {formatDistanceToNow(new Date(note.createdAt), { addSuffix: true })}
                  </span>
                  {note.conversationType &&
                  <Badge variant="neutral" size="sm">
                      <ConversationIcon type={note.conversationType} />
                      <span className="ml-1">{note.conversationType}</span>
                    </Badge>
                  }
                </div>
              </div>
              <p className="text-sm text-primary whitespace-pre-wrap">
                {note.content}
              </p>
              {note.participants && note.participants.length > 0 &&
              <div className="mt-2 flex items-center gap-2">
                  <span className="text-xs text-muted">Participants:</span>
                  <div className="flex gap-1">
                    {note.participants.map((participantId) =>
                  <Button variant="ghost"
                  key={participantId}
                  onClick={() => onContactClick?.(participantId)}>


                        {getContactName(participantId)}
                      </Button>
                  )}
                  </div>
                </div>
              }
              <div className="mt-2 flex items-center gap-3">
                <Button variant="ghost"
                onClick={() => setReplyingTo(note.id)}>


                  Reply
                </Button>
                {note.replies && note.replies.length > 0 &&
                <Button variant="ghost"
                onClick={() => setShowReplies(!showReplies)}>


                    {showReplies ? <ChevronDownIcon className="w-3 h-3" /> : <ChevronRightIcon className="w-3 h-3" />}
                    {note.replies.length} {note.replies.length === 1 ? "reply" : "replies"}
                  </Button>
                }
              </div>
            </div>
            
            {/* Reply form */}
            {replyingTo === note.id &&
            <div className="mt-2 ml-8">
                <Textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="Write a reply..."
                className="w-full px-3 py-2 text-sm border border-strong rounded-lg focus:outline-none focus:ring-2 focus:ring-accent"
                rows={3} />

                <div className="mt-2 flex justify-end gap-2">
                  <Button variant="ghost"
                onClick={() => {
                  setReplyingTo(null);
                  setReplyContent('');
                }}>


                    Cancel
                  </Button>
                  <Button variant="primary"
                onClick={() => createReplyMutation.mutate({ parentNoteId: note.id, content: replyContent })}
                disabled={!replyContent.trim() || createReplyMutation.isLoading}>


                    {createReplyMutation.isLoading ? "Sending..." : "Send"}
                  </Button>
                </div>
              </div>
            }
            
            {/* Nested replies */}
            {showReplies && note.replies && note.replies.length > 0 &&
            <div className="mt-3 space-y-3">
                {note.replies.map((reply) =>
              <NoteItem key={reply.id} note={reply} isReply />
              )}
              </div>
            }
          </div>
        </div>
      </div>);

  };

  // Render a conversation thread
  const ThreadItem: React.FC<{thread: ConversationThread;}> = ({ thread }) => {
    const isExpanded = expandedThreads.has(thread.threadId);
    const { data: notes = [], isLoading: loadingNotes } = useQuery(
      ["thread-notes", thread.threadId],
      () => fetchThreadNotes(thread.threadId),
      {
        enabled: isExpanded,
        staleTime: 5 * 60 * 1000
      }
    );

    return (
      <div className="bg-surface-card rounded-lg shadow-sm border border-default">
        <div
          className="p-4 cursor-pointer hover:bg-surface-page dark:hover:bg-surface-elevated/50"
          onClick={() => toggleThread(thread.threadId)}>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
              thread.conversationType === 'email' ?'bg-primary-light/30' :
              thread.conversationType === 'call' ?'bg-success-light/30' :
              thread.conversationType === 'meeting' ?'bg-accent-light/30' :
              thread.conversationType === 'slack' ?'bg-primary-light/30' : "bg-surface-alt"}`
              }>
                <ConversationIcon type={thread.conversationType} />
              </div>
              <div>
                <h4 className="font-medium text-primary line-clamp-1">
                  {thread.topic}
                </h4>
                <div className="flex items-center gap-3 mt-1 text-xs text-muted">
                  <span>{thread.messageCount} messages</span>
                  {thread.participantCount > 0 &&
                  <span>{thread.participantCount} participants</span>
                  }
                  <span>
                    <ClockIcon className="w-3 h-3 inline mr-1" />
                    {formatDistanceToNow(new Date(thread.lastActivity), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <ThreadStatusBadge status={thread.status} />
              {isExpanded ? <ChevronDownIcon className="w-5 h-5 text-subtle" /> : <ChevronRightIcon className="w-5 h-5 text-subtle" />}
            </div>
          </div>
        </div>
        
        {isExpanded &&
        <div className="border-t border-default p-4">
            {loadingNotes ?
          <div className="animate-pulse space-y-3">
                <div className="h-16 bg-surface-alt rounded-lg"></div>
                <div className="h-16 bg-surface-alt rounded-lg ml-12"></div>
              </div> :

          <div className="space-y-4">
                {notes.map((note) =>
            <NoteItem key={note.id} note={note} />
            )}
              </div>
          }
            
            {/* Quick status update */}
            <div className="mt-4 pt-4 border-t border-default">
              <div className="flex items-center justify-between">
                <span className="text-sm text-secondary">Update thread status:</span>
                <div className="flex gap-2">
                  {(['open','resolved','parked",'archived"] as NoteStatus[]).map((status) =>
                <Button variant="secondary"
                key={status}
                onClick={() => notes[0] && updateStatusMutation.mutate({ noteId: notes[0].id, status })}
                disabled={thread.status === status || updateStatusMutation.isLoading}
                className={`px-3 py-1.5 text-xs rounded-lg transition-colors ${
                thread.status === status ? "bg-surface-alt text-muted cursor-not-allowed" : "bg-surface-alt text-primary hover:bg-surface-alt dark:hover:bg-surface-alt"}`
                }>

                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Button>
                )}
                </div>
              </div>
            </div>
          </div>
        }
      </div>);

  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-24 bg-surface-alt rounded-lg"></div>
        </div>
      </div>);

  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-primary">
          Conversations
        </h3>
        <Button variant="primary"
        onClick={() => setShowNewThread(!showNewThread)}>


          <PlusIcon className="w-4 h-4" />
          New Conversation
        </Button>
      </div>

      {/* New thread form */}
      {showNewThread &&
      <div className="bg-surface-card rounded-lg shadow-sm border border-default p-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Conversation Type
              </label>
              <div className="flex gap-2">
                {(['internal','email','call','meeting","slack"] as ConversationType[]).map((type) =>
              <Button variant="secondary"
              key={type}
              onClick={() => setNewThreadData((prev) => ({ ...prev, conversationType: type }))}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
              newThreadData.conversationType === type ? "bg-accent-light/30 text-accent-dark border border-accent" : "bg-surface-alt text-primary hover:bg-surface-alt dark:hover:bg-surface-alt"}`
              }>

                    <ConversationIcon type={type} />
                    <span className="text-sm capitalize">{type}</span>
                  </Button>
              )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Participants
              </label>
              <Select
              multiple
              value={newThreadData.participants}
              onChange={(e) => {
                const selected = Array.from(e.target.selectedOptions).map((o) => o.value);
                setNewThreadData((prev) => ({ ...prev, participants: selected }));
              }}
              className="w-full px-3 py-2 border border-strong rounded-lg focus:outline-none focus:ring-2 focus:ring-accent"
              size={4}>

                {contacts.map((contact) =>
              <option key={contact.id} value={contact.id}>
                    {contact.firstName} {contact.lastName} {contact.company ? `(${contact.company})` : ""}
                  </option>
              )}
              </Select>
              <p className="mt-1 text-xs text-muted">
                Hold Ctrl/Cmd to select multiple participants
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                Message
              </label>
              <Textarea
              value={newThreadData.content}
              onChange={(e) => setNewThreadData((prev) => ({ ...prev, content: e.target.value }))}
              placeholder="Start the conversation..."
              className="w-full px-3 py-2 border border-strong rounded-lg focus:outline-none focus:ring-2 focus:ring-accent"
              rows={4} />

            </div>

            <div className="flex justify-end gap-3">
              <Button variant="ghost"
            onClick={() => {
              setShowNewThread(false);
              setNewThreadData({
                content:"",
                conversationType: "internal",
                participants: [],
                status: "open"
              });
            }}>


                Cancel
              </Button>
              <Button variant="primary"
            onClick={() => createThreadMutation.mutate(newThreadData)}
            disabled={!newThreadData.content.trim() || createThreadMutation.isLoading}>


                {createThreadMutation.isLoading ? "Creating..." : "Start Conversation"}
              </Button>
            </div>
          </div>
        </div>
      }

      {/* Thread list */}
      {threads.length === 0 ?
      <div className="text-center py-8 text-muted">
          <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-3 text-subtle" />
          <p>No conversations yet</p>
          <p className="text-sm mt-1">Start a conversation to track discussions</p>
        </div> :

      <div className="space-y-3">
          {threads.map((thread) =>
        <ThreadItem key={thread.threadId} thread={thread} />
        )}
        </div>
      }
    </div>);

};

export default ConversationThreadComponent;