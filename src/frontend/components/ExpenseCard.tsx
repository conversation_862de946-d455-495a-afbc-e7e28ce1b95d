import React from "react";
import { formatCurrency, formatDate } from "../utils/format";
import { Card } from "./ui";
import { Button } from "@/frontend/components/ui/Button";

interface ExpenseCardProps {
  title: string;
  amount: number;
  date: Date;
  isAdded: boolean;
  onSync: () => void;
  description: string;
  isLoading?: boolean;
  hasRecurringExpense?: boolean;
}

const ExpenseCard: React.FC<ExpenseCardProps> = ({
  title,
  amount,
  date,
  isAdded,
  onSync,
  description,
  isLoading = false,
  hasRecurringExpense = false
}) => {
  // Determine border color based on state
  const borderColor = isAdded ? "border-l-success" :
  hasRecurringExpense ? "border-l-warning" :'border-l-primary';

  return (
    <Card className={`border-l-4 ${borderColor} hover:shadow-md transition-all duration-200`}>
      <h3 className="text-lg font-semibold text-primary dark:text-gray-100">{title}</h3>

      <div className="mt-2">
        <div className="text-2xl font-bold text-accent">
          {formatCurrency(amount)}
        </div>

        <div className="mt-1 text-sm text-secondary dark:text-subtle flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          Payment date: {formatDate(date)}
        </div>

        <div className="mt-2 text-sm text-secondary dark:text-subtle">
          {description}
        </div>
      </div>

      <div className="mt-4">
        <Button variant="secondary"
        onClick={onSync}
        disabled={isAdded || isLoading}
        className={`w-full ${
        isLoading ? "--processing" :
        isAdded ? "--success" :
        hasRecurringExpense ? "--warning" :'--primary'}`
        }>

          {isLoading ? "Syncing..." :
          isAdded ? "Updated ✓" :
          hasRecurringExpense ? "Update" :'Sync with Upstream'
          }
        </Button>
      </div>
    </Card>);

};

export default ExpenseCard;