import React from "react";

/**
 * Troubleshooting section content for the help page
 */
export const TroubleshootingSection: React.FC = () => {
  return (
    <>
      <p>
        Common issues and solutions for using the Upstream platform effectively.
      </p>
      
      <h3 className="text-md font-medium text-primary mt-4">
        Authentication Issues
      </h3>
      <div className="space-y-3 mt-2">
        <div className="p-3 border border-warning/40 rounded-lg bg-warning-light/50/10">
          <h4 className="font-medium text-warning text-sm">Xero Connection Problems</h4>
          <p className="text-xs mt-1">
            If Xero authentication fails, try clearing your browser cache and re-authenticating.
            Ensure you have the necessary permissions in your Xero organization.
          </p>
        </div>
        <div className="p-3 border border-warning/40 rounded-lg bg-warning-light/50/10">
          <h4 className="font-medium text-warning text-sm">Harvest API Limits</h4>
          <p className="text-xs mt-1">
            If you see rate limiting errors, the system will automatically retry.
            Large data imports may take several minutes to complete.
          </p>
        </div>
      </div>

      <h3 className="text-md font-medium text-primary mt-6">
        Data Sync Issues
      </h3>
      <ul className="list-disc pl-6 space-y-1 mt-2 text-sm">
        <li>Refresh the page if data appears stale</li>
        <li>Check your internet connection for sync issues</li>
        <li>Verify API credentials are still valid</li>
        <li>Some reports may take time to process large datasets</li>
      </ul>

      <h3 className="text-md font-medium text-primary mt-6">
        Performance Tips
      </h3>
      <ul className="list-disc pl-6 space-y-1 mt-2 text-sm">
        <li>Use shorter date ranges for faster report generation</li>
        <li>Clear browser cache if the app feels slow</li>
        <li>Avoid having multiple tabs open simultaneously</li>
        <li>Close unused sections to improve page performance</li>
      </ul>

      <div className="mt-6 p-4 bg-primary-light/20 rounded-lg border border-primary">
        <h4 className="font-medium text-primary mb-2">
          📞 Need Help?
        </h4>
        <p className="text-sm text-primary-color">
          If you're still experiencing issues, use the feedback button in the app
          to report problems or request assistance.
        </p>
      </div>
    </>
  );
};