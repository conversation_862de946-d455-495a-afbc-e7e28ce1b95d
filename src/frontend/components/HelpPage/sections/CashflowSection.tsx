import React from "react";

/**
 * Cashflow section content for the help page
 */
export const CashflowSection: React.FC = () => {
  return (
    <>
      <p>
        The Cashflow section provides a comprehensive view of your company's
        financial position over time, with projections up to 90 days in the
        future.
      </p>

      <h3 className="text-md font-medium text-primary dark:text-subtle mt-4">
        Key Features
      </h3>
      <ul className="list-disc pl-6 space-y-2 mt-2">
        <li>Interactive cashflow chart with daily projections</li>
        <li>Current and projected balance tracking</li>
        <li>Transaction list with filtering options</li>
        <li>Customizable threshold alerts</li>
        <li>Integration with Xero and Harvest data</li>
      </ul>

      <div className="mt-6 p-4 bg-primary-light dark:bg-primary-dark/20 rounded-lg border border-primary dark:border-blue-800">
        <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
          💡 Pro Tip
        </h4>
        <p className="text-sm text-primary-color dark:text-blue-300">
          Use the scenario toggle to view different cashflow projections
          (worst-case, expected, best-case) to better plan for various business
          outcomes.
        </p>
      </div>
    </>
  );
};
