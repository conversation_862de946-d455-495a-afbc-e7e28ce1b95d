import React from "react";

/**
 * Reports section content for the help page
 */
export const ReportsSection: React.FC = () => {
  return (
    <>
      <p>
        Access detailed financial reports and analytics to understand your
        business performance across all connected systems.
      </p>

      <h3 className="text-md font-medium text-primary dark:text-subtle mt-4">
        Available Reports
      </h3>
      <div className="grid md:grid-cols-2 gap-4 mt-3">
        <div className="p-3 border border-default dark:border-default rounded-lg">
          <h4 className="font-medium text-primary dark:text-primary">
            Utilization Reports
          </h4>
          <p className="text-sm mt-1">Team utilization and capacity analysis</p>
        </div>
        <div className="p-3 border border-default dark:border-default rounded-lg">
          <h4 className="font-medium text-primary dark:text-primary">
            Time Tracking
          </h4>
          <p className="text-sm mt-1">Detailed time analysis from Harvest</p>
        </div>
        <div className="p-3 border border-default dark:border-default rounded-lg">
          <h4 className="font-medium text-primary dark:text-primary">
            Financial Summary
          </h4>
          <p className="text-sm mt-1">Revenue, expenses, and profit analysis</p>
        </div>
        <div className="p-3 border border-default dark:border-default rounded-lg">
          <h4 className="font-medium text-primary dark:text-primary">
            Xero Integration
          </h4>
          <p className="text-sm mt-1">
            Direct access to Xero accounting reports
          </p>
        </div>
      </div>

      <h3 className="text-md font-medium text-primary dark:text-subtle mt-6">
        Customization
      </h3>
      <p className="text-sm mt-2">
        Most reports support date range filtering and can be exported for
        further analysis. Time-based reports automatically adjust for your
        selected period.
      </p>
    </>
  );
};
