import React from "react";
import { TOCItem } from "../constants/helpContent";
import { Button } from "@/frontend/components/ui/Button";

interface TableOfContentsProps {
  sections: TOCItem[];
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
}

/**
 * Table of contents sidebar component
 */
export const TableOfContents: React.FC<TableOfContentsProps> = ({
  sections,
  activeSection,
  onSectionClick,
}) => {
  return (
    <div className="md:w-1/4 bg-surface-card dark:bg-surface-card rounded-lg shadow p-4 h-fit sticky top-4">
      <h3 className="text-xs font-medium text-primary dark:text-subtle mb-3">
        Contents
      </h3>
      <nav>
        <ul className="space-y-2">
          {sections.map((section) => (
            <li key={section.id}>
              <Button
                variant="secondary"
                onClick={() => onSectionClick(section.id)}
                className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeSection === section.id
                    ? "bg-secondary/10 text-secondary dark:bg-secondary/20 dark:text-secondary-light"
                    : "text-secondary dark:text-subtle hover:bg-surface-alt dark:hover:bg-surface-elevated"
                }`}
              >
                {section.title}
              </Button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};
