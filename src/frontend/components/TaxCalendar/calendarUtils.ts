import { CalendarData, GSTQuarter, QuarterInfo } from "./types";

// Helper function to get month name
export const getMonthName = (month: number): string => {
  return new Date(2000, month, 1).toLocaleString("default", { month: "short" });
};

// Helper function to get quarter information for a given month
export const getQuarterInfo = (month: number): QuarterInfo => {
  // Australian financial year quarters (July-June)
  // Q1: Jul-Sep, Q2: Oct-Dec, Q3: Jan-Mar, Q4: Apr-Jun
  if (month >= 0 && month <= 2)
    return { quarter: 3, label: "Q3", colorIndex: 0 }; // Jan-Mar
  if (month >= 3 && month <= 5)
    return { quarter: 4, label: "Q4", colorIndex: 1 }; // Apr-Jun
  if (month >= 6 && month <= 8)
    return { quarter: 1, label: "Q1", colorIndex: 2 }; // Jul-Sep
  return { quarter: 2, label: "Q2", colorIndex: 3 }; // Oct-Dec
};

// Get current quarter information
export const getCurrentQuarterInfo = (): QuarterInfo => {
  const today = new Date();
  const currentMonth = today.getMonth(); // 0-indexed (0 = January)
  return getQuarterInfo(currentMonth);
};

// Generate calendar data for the entire year
export const generateCalendarData = (
  displayYear: number,
  currentDate: Date = new Date(),
): CalendarData => {
  const months = [];

  for (let month = 0; month < 12; month++) {
    // Calculate days in month
    const daysInMonth = new Date(displayYear, month + 1, 0).getDate();
    const quarterInfo = getQuarterInfo(month);

    // Generate day data for this month
    const days = [];

    // Get current date information
    const currentDay = currentDate.getDate();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    for (let day = 1; day <= daysInMonth; day++) {
      // Determine if this day is a payment date
      const isPAYGWDueDate = day === 21;
      const isGSTDueDate = day === 26;

      // Check if this is the current day
      const isCurrentDay =
        day === currentDay &&
        month === currentMonth &&
        displayYear === currentYear;

      // Determine which month's PAYGW payment is due on this day
      const paygwPaymentForMonth =
        month > 0 && isPAYGWDueDate ? month - 1 : null;

      // Determine which quarter's GST payment is due on this day
      let gstPaymentForQuarter = null;
      if (isGSTDueDate) {
        if (month === 4) gstPaymentForQuarter = 0; // May 26 - Q3 payment (Jan-Mar)
        if (month === 7) gstPaymentForQuarter = 1; // August 26 - Q4 payment (Apr-Jun)
        if (month === 10) gstPaymentForQuarter = 2; // November 26 - Q1 payment (Jul-Sep)
        if (month === 1) gstPaymentForQuarter = 3; // February 26 - Q2 payment (Oct-Dec)
      }

      days.push({
        day,
        isPAYGWDueDate,
        isGSTDueDate,
        isCurrentDay,
        paygwPaymentForMonth,
        gstPaymentForQuarter,
      });
    }

    const monthData = {
      index: month,
      name: getMonthName(month),
      days,
      daysInMonth,
      quarter: quarterInfo,
      // For PAYGW, payment is due on the 21st of the following month
      paygwDueDate: new Date(displayYear, (month + 1) % 12, 21),
      // If December, payment is due in January of next year
      paygwDueYear: month === 11 ? displayYear + 1 : displayYear,
    };

    months.push(monthData);
  }

  // Calculate GST/BAS quarters and due dates
  const gstQuarters: GSTQuarter[] = [
    {
      label: "Q3",
      colorIndex: 0,
      startMonth: 0, // January
      endMonth: 2, // March
      dueMonth: 4, // May (month after April)
      dueDay: 26,
      dueYear: displayYear,
    },
    {
      label: "Q4",
      colorIndex: 1,
      startMonth: 3, // April
      endMonth: 5, // June
      dueMonth: 7, // August (month after July)
      dueDay: 26,
      dueYear: displayYear,
    },
    {
      label: "Q1",
      colorIndex: 2,
      startMonth: 6, // July
      endMonth: 8, // September
      dueMonth: 10, // November (month after October)
      dueDay: 26,
      dueYear: displayYear,
    },
    {
      label: "Q2",
      colorIndex: 3,
      startMonth: 9, // October
      endMonth: 11, // December
      dueMonth: 1, // February (month after January of next year)
      dueDay: 26,
      dueYear: displayYear + 1,
    },
  ];

  return { months, gstQuarters };
};
