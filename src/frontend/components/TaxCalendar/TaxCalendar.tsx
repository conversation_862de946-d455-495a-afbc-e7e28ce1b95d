import React, { useState, useEffect } from "react";
import { Card } from "../ui";
import { ThemeProvider } from "./ThemeContext";
import { generateCalendarData } from "./calendarUtils";
import { TooltipData } from "./types";
import Header from "./Header";
import Legend from "./Legend";
import Month from "./Month";
import Footer from "./Footer";
import Tooltip from "./Tooltip";
import '../../styles/components/tax-calendar.css";

interface TaxCalendarProps {
  currentDate?: Date;
}

const TaxCalendar: React.FC<TaxCalendarProps> = ({
  currentDate = new Date()
}) => {
  // State for year navigation
  const [displayYear, setDisplayYear] = useState(currentDate.getFullYear());

  // State for tooltip
  const [tooltip, setTooltip] = useState<TooltipData>({
    visible: false,
    title: "",
    content: "",
    x: 0,
    y: 0
  });

  // State for highlighted elements
  const [highlightedElements, setHighlightedElements] = useState<Set<string>>(new Set());

  // Generate calendar data
  const { months, gstQuarters } = generateCalendarData(displayYear, currentDate);

  // Handle year navigation
  const handlePreviousYear = () => setDisplayYear(prev => prev - 1);
  const handleNextYear = () => setDisplayYear(prev => prev + 1);

  // Handle mouse movement for tooltip positioning
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (tooltip.visible) {
        // Update tooltip position with mouse position
        setTooltip(prev => ({
          ...prev,
          x: e.clientX,
          y: e.clientY
        }));
      }
    };

    const handleScroll = () => {
      // Hide tooltip when scrolling
      setTooltip(prev => ({
        ...prev,
        visible: false
      }));
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);

    // Clean up event listeners on unmount
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll", handleScroll);
    };
  }, [tooltip.visible]);

  return (
    <ThemeProvider>
      <Card className="tax-calendar-visualization">
        <Header
          displayYear={displayYear}
          onPreviousYear={handlePreviousYear}
          onNextYear={handleNextYear}
        />

        <Legend />

        <div className="tax-calendar-grid">
          {months.map((month) => (
            <Month
              key={`month-${month.index}`}
              month={month}
              gstQuarters={gstQuarters}
              setTooltip={setTooltip}
              setHighlightedElements={setHighlightedElements}
              highlightedElements={highlightedElements}
            />
          ))}
        </div>

        <Footer />

        <Tooltip tooltip={tooltip} />
      </Card>
    </ThemeProvider>
  );
};

export default TaxCalendar;
