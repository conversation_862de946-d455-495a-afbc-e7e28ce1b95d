import React, { useState } from "react";
import { Button } from "@/frontend/components/ui/Button";

const Legend: React.FC = () => {
  const [isGuideVisible, setIsGuideVisible] = useState(false);

  const toggleGuide = () => {
    setIsGuideVisible(!isGuideVisible);
  };

  return (
    <>
      <div className="tax-calendar-legend flex items-center justify-between flex-wrap mb-4 pb-3 border-b border-default">
        <div className="flex items-center flex-wrap gap-4">
          <div className="tax-calendar-legend-item">
            <div className="tax-calendar-legend-color-group">
              <div className="tax-calendar-legend-color bg-primary-light"></div>
              <div className="tax-calendar-legend-color bg-success-light"></div>
              <div className="tax-calendar-legend-color bg-accent-light"></div>
            </div>
            <span>PAYGW (Monthly)</span>
          </div>
          <div className="tax-calendar-legend-item">
            <div className="tax-calendar-legend-color-group">
              <div className="tax-calendar-legend-color bg-warning-light"></div>
              <div className="tax-calendar-legend-color bg-error-light"></div>
              <div className="tax-calendar-legend-color bg-accent-light"></div>
            </div>
            <span>GST/BAS (Quarterly)</span>
          </div>
          <div className="tax-calendar-legend-item">
            <div className="tax-calendar-legend-color bg-primary-light border-2 border-primary"></div>
            <span>Payment Due Date</span>
          </div>
        </div>

        <div className="tax-calendar-guide mt-2 sm:mt-0">
          <Button
            variant="ghost"
            className="flex items-center gap-1"
            onClick={toggleGuide}
            aria-expanded={isGuideVisible}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>How to read this calendar</span>
          </Button>
        </div>
      </div>

      {isGuideVisible && (
        <div className="tax-calendar-guide-panel mb-6 p-4 bg-surface-page/50 rounded-md border border-default shadow-sm dark:shadow-black/20">
          <h4 className="text-sm font-medium text-primary mb-3">
            How to Read This Calendar
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center h-full gap-3 p-3 bg-surface-card rounded-md border border-default">
              <div className="flex-shrink-0 w-10">
                <div className="tax-calendar-day tax-calendar-day-split w-10 h-10">
                  <div className="tax-calendar-day-half-wrapper tax-calendar-day-paygw-wrapper">
                    <div className="tax-calendar-day-paygw bg-primary-light"></div>
                  </div>
                  <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper">
                    <div className="tax-calendar-day-gst bg-warning-light"></div>
                  </div>
                  <span className="tax-calendar-day-number">15</span>
                </div>
              </div>
              <div className="text-xs text-secondary">
                <div className="font-medium text-primary mb-1">
                  Calendar Day
                </div>
                <p>
                  • <span className="font-medium">Top:</span> PAYGW (monthly)
                  accrual
                </p>
                <p>
                  • <span className="font-medium">Bottom:</span> GST/BAS
                  (quarterly) accrual
                </p>
                <p>
                  • <span className="font-medium">Number:</span> Day of month
                </p>
              </div>
            </div>

            <div className="flex items-center h-full gap-3 p-3 bg-surface-card rounded-md border border-default">
              <div className="flex-shrink-0 w-10">
                <div className="tax-calendar-day tax-calendar-current-day tax-calendar-day-split w-10 h-10 border-2 border-success">
                  <div className="tax-calendar-day-half-wrapper tax-calendar-day-paygw-wrapper">
                    <div className="tax-calendar-day-paygw bg-primary-light"></div>
                  </div>
                  <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper">
                    <div className="tax-calendar-day-gst bg-warning-light"></div>
                  </div>
                  <span className="tax-calendar-day-number">15</span>
                </div>
              </div>
              <div className="text-xs text-secondary">
                <div className="font-medium text-primary mb-1">Current Day</div>
                <p>• Today's date (highlighted with green border)</p>
                <p>• Shows your position in the tax cycle</p>
              </div>
            </div>

            <div className="flex items-center h-full gap-3 p-3 bg-surface-card rounded-md border border-default">
              <div className="flex-shrink-0 w-24 flex justify-center">
                <div className="flex items-center gap-2">
                  <div className="tax-calendar-day tax-calendar-payment-day w-10 h-10 bg-primary-light border-2 border-primary">
                    <span className="tax-calendar-day-number">21</span>
                  </div>
                  <div className="tax-calendar-day tax-calendar-payment-day w-10 h-10 bg-warning-light border-2 border-warning">
                    <span className="tax-calendar-day-number">26</span>
                  </div>
                </div>
              </div>
              <div className="text-xs text-secondary">
                <div className="font-medium text-primary mb-1">
                  Payment Due Dates
                </div>
                <p>
                  • <span className="font-medium">PAYGW:</span> 21st of
                  following month
                </p>
                <p>
                  • <span className="font-medium">GST/BAS:</span> 26th of month
                  after next
                </p>
                <p>• Highlighted with colored borders</p>
              </div>
            </div>

            <div className="flex items-center h-full gap-3 p-3 bg-surface-card rounded-md border border-default">
              <div className="flex-shrink-0 w-24 flex justify-center">
                <div className="flex items-center gap-1">
                  <div className="tax-calendar-day tax-calendar-day-split w-10 h-10">
                    <div className="tax-calendar-day-half-wrapper">
                      <div className="tax-calendar-day-paygw bg-primary-light"></div>
                    </div>
                    <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper">
                      <div className="tax-calendar-day-gst bg-warning-light"></div>
                    </div>
                    <span className="tax-calendar-day-number">15</span>
                  </div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-subtle"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div className="tax-calendar-day tax-calendar-day-split w-10 h-10">
                    <div className="tax-calendar-day-half-wrapper">
                      <div className="tax-calendar-day-paygw bg-primary-light0"></div>
                    </div>
                    <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper">
                      <div className="tax-calendar-day-gst bg-warning-light"></div>
                    </div>
                    <span className="tax-calendar-day-number">15</span>
                  </div>
                </div>
              </div>
              <div className="text-xs text-secondary">
                <div className="font-medium text-primary mb-1">
                  Interactive Features
                </div>
                <p>
                  • <span className="font-medium">Hover:</span> See detailed
                  information
                </p>
                <p>
                  • <span className="font-medium">Highlighting:</span> Shows
                  related accrual periods and payment dates
                </p>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted mt-4 pt-3 border-t border-default flex justify-end">
            <p>
              <span className="font-medium">Quarters:</span> Q1 (Jul-Sep), Q2
              (Oct-Dec), Q3 (Jan-Mar), Q4 (Apr-Jun)
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default Legend;
