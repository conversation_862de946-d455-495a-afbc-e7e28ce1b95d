import React from "react";
import { MonthData, TooltipData } from "./types";
import CalendarDay from "./CalendarDay";
import PaymentDay from "./PaymentDay";

interface MonthProps {
  month: MonthData;
  gstQuarters: any[];
  setTooltip: React.Dispatch<React.SetStateAction<TooltipData>>;
  setHighlightedElements: React.Dispatch<React.SetStateAction<Set<string>>>;
  highlightedElements: Set<string>;
}

const Month: React.FC<MonthProps> = ({
  month,
  gstQuarters,
  setTooltip,
  setHighlightedElements,
  highlightedElements
}) => {
  // Extract year from the month data
  const year = month.index === 11 && month.paygwDueYear > new Date().getFullYear() 
    ? month.paygwDueYear - 1 
    : month.paygwDueYear;
  
  // Calculate empty cells for days before the 1st of the month
  const firstDayOfMonth = new Date(year, month.index, 1).getDay();
  const emptyCells = Array.from({ length: firstDayOfMonth });
  
  return (
    <div className="tax-calendar-month" data-month-index={month.index}>
      <div className="tax-calendar-month-header">
        <span>{month.name}</span>
        <span className="tax-calendar-quarter-label">
          {month.quarter.label}
        </span>
      </div>

      <div className="tax-calendar-days-grid">
        {/* Day of week headers */}
        {['S', 'M', 'T', 'W', 'T', 'F", 'S'].map((day, i) => (
          <div key={`dow-${i}`} className="tax-calendar-dow">{day}</div>
        ))}

        {/* Empty cells for days before the 1st of the month */}
        {emptyCells.map((_, i) => (
          <div key={`empty-${i}`} className="tax-calendar-day-empty"></div>
        ))}

        {/* Actual days of the month */}
        {month.days.map((day) => {
          // Special case: PAYGW payment date (21st of each month)
          if (day.isPAYGWDueDate && day.paygwPaymentForMonth !== null) {
            return (
              <PaymentDay
                key={`day-${day.day}`}
                day={day}
                setTooltip={setTooltip}
                setHighlightedElements={setHighlightedElements}
                highlightedElements={highlightedElements}
              />
            );
          }
          
          // Special case: GST payment date (28th of specific months)
          if (day.isGSTDueDate && day.gstPaymentForQuarter !== null) {
            return (
              <PaymentDay
                key={`day-${day.day}`}
                day={day}
                gstQuarters={gstQuarters}
                setTooltip={setTooltip}
                setHighlightedElements={setHighlightedElements}
                highlightedElements={highlightedElements}
              />
            );
          }
          
          // Regular day
          return (
            <CalendarDay
              key={`day-${day.day}`}
              day={day}
              monthIndex={month.index}
              quarterInfo={month.quarter}
              setTooltip={setTooltip}
              setHighlightedElements={setHighlightedElements}
              highlightedElements={highlightedElements}
            />
          );
        })}
      </div>
    </div>
  );
};

export default Month;
