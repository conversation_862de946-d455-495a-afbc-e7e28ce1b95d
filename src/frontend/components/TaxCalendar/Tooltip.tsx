import React, { useEffect, useRef } from "react";
import { TooltipData } from "./types";
import { createPortal } from "react-dom";
import { useTheme } from "./ThemeContext";
import "../../styles/components/tax-calendar.css";

interface TooltipProps {
  tooltip: TooltipData;
}

const Tooltip: React.FC<TooltipProps> = ({ tooltip }) => {
  const tooltipRef = useRef<HTMLDivElement>(null);
  const { isDarkMode } = useTheme();

  useEffect(() => {
    if (!tooltipRef.current || !tooltip.visible) return;

    const tooltipEl = tooltipRef.current;
    const tooltipRect = tooltipEl.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Calculate position to place tooltip above cursor and much closer to it
    // Position it directly above the cursor with minimal gap
    let left = tooltip.x - tooltipRect.width / 2;
    let top = tooltip.y - tooltipRect.height - 2; // Minimal gap of 2px

    // Ensure tooltip stays within viewport boundaries
    if (left < 10) {
      left = 10;
    } else if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10;
    }

    // If tooltip would go above viewport, position it below cursor instead
    if (top < 10) {
      top = tooltip.y + 15;
    }

    // Apply the calculated position
    tooltipEl.style.left = `${left}px`;
    tooltipEl.style.top = `${top}px`;
    tooltipEl.style.opacity = "1";
  }, [tooltip]);

  if (!tooltip.visible) return null;

  // Define styles based on theme
  const tooltipStyles = {
    position: "fixed" as const,
    pointerEvents: "none" as const,
    zIndex: 10000,
    opacity: 0,
    transition: "opacity 0.15s ease-in-out",
    backgroundColor: isDarkMode
      ? "rgba(28, 27, 26, 0.98)"
      : "rgba(255, 252, 240, 0.98)" /* flexoki base-950 : paper */,
    color: isDarkMode
      ? "rgba(206, 205, 195, 0.9)"
      : "rgba(16, 15, 15, 0.9)" /* flexoki base-200 : black */,
    padding: "0.5rem 0.75rem",
    borderRadius: "0.375rem",
    fontSize: "0.75rem",
    fontWeight: 500,
    boxShadow: isDarkMode
      ? "0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1)"
      : "0 4px 6px -1px rgba(16, 15, 15, 0.1), 0 2px 4px -1px rgba(16, 15, 15, 0.06)" /* flexoki black for light mode shadows */,
    border: isDarkMode
      ? "1px solid rgba(64, 62, 60, 0.8)" /* flexoki base-800 */
      : "1px solid rgba(206, 205, 195, 0.8)" /* flexoki base-200 */,
    maxWidth: "220px",
    textAlign: "left" as const,
  };

  return createPortal(
    <div
      ref={tooltipRef}
      className="tax-calendar-tooltip"
      style={tooltipStyles}
    >
      <div
        className="tax-calendar-tooltip-title"
        style={{
          fontWeight: 600,
          marginBottom: "0.25rem",
        }}
      >
        {tooltip.title}
      </div>
      <div
        className="tax-calendar-tooltip-content"
        style={{
          fontWeight: 400,
          opacity: 0.9,
        }}
      >
        {tooltip.content}
      </div>
    </div>,
    document.body,
  );
};

export default Tooltip;
