import React from "react";
import { Link } from "react-router-dom";

/**
 * NotFound component displayed when a user navigates to an invalid route
 */
const NotFound: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <h1 className="text-4xl font-bold text-primary dark:text-primary mb-4">
        404 - Page Not Found
      </h1>
      <p className="text-lg text-secondary dark:text-subtle mb-8">
        The page you are looking for doesn't exist or has been moved.
      </p>
      <Link
        to="/"
        className="px-4 py-2 bg-primary text-primary rounded-lg hover:bg-primary-dark transition-colors"
      >
        Return to Dashboard
      </Link>
    </div>
  );
};

export default NotFound;
