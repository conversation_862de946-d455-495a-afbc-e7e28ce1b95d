import React, { useEffect, useRef } from "react";
import { createPortal } from "react-dom";

export interface TooltipData {
  visible: boolean;
  title?: string;
  content: string;
  x: number;
  y: number;
}

interface SimpleTooltipProps {
  tooltip: TooltipData;
}

/**
 * A simple, tasteful tooltip component that appears near the cursor
 */
const SimpleTooltip: React.FC<SimpleTooltipProps> = ({ tooltip }) => {
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Detect dark mode
  const isDarkMode = document.documentElement.classList.contains("dark");

  useEffect(() => {
    if (!tooltipRef.current || !tooltip.visible) return;

    const tooltipEl = tooltipRef.current;
    const tooltipRect = tooltipEl.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Position it directly above the cursor with minimal gap
    let left = tooltip.x - tooltipRect.width / 2;
    let top = tooltip.y - tooltipRect.height - 8; // Small gap above cursor

    // Ensure tooltip stays within viewport boundaries
    if (left < 10) {
      left = 10;
    } else if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10;
    }

    // If tooltip would go above viewport, position it below cursor instead
    if (top < 10) {
      top = tooltip.y + 15;
    }

    // Apply the calculated position
    tooltipEl.style.left = `${left}px`;
    tooltipEl.style.top = `${top}px`;
    tooltipEl.style.opacity = "1";
  }, [tooltip]);

  if (!tooltip.visible) return null;

  // Define styles based on theme
  const tooltipStyles = {
    position: "fixed" as const,
    pointerEvents: "none" as const,
    zIndex: 10000,
    opacity: 0,
    transition: "opacity 0.15s ease-in-out",
    backgroundColor: isDarkMode
      ? "rgba(30, 41, 59, 0.98)"
      : "rgba(255, 255, 255, 0.98)",
    color: isDarkMode ? "rgba(248, 250, 252, 0.9)" : "rgba(15, 23, 42, 0.9)",
    padding: "0.5rem 0.75rem",
    borderRadius: "0.375rem",
    fontSize: "0.75rem",
    fontWeight: 500,
    boxShadow: isDarkMode
      ? "0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1)"
      : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    border: isDarkMode
      ? "1px solid rgba(51, 65, 85, 0.8)"
      : "1px solid rgba(226, 232, 240, 0.8)",
    maxWidth: "220px",
    textAlign: "left" as const,
  };

  return createPortal(
    <div ref={tooltipRef} className="simple-tooltip" style={tooltipStyles}>
      {tooltip.title && (
        <div
          className="simple-tooltip-title"
          style={{
            fontWeight: 600,
            marginBottom: "0.25rem",
          }}
        >
          {tooltip.title}
        </div>
      )}
      <div
        className="simple-tooltip-content"
        style={{
          fontWeight: 400,
          opacity: 0.9,
        }}
      >
        {tooltip.content}
      </div>
    </div>,
    document.body,
  );
};

export default SimpleTooltip;
