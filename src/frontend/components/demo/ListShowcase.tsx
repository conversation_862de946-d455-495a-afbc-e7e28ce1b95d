import React, { useState } from "react";
import { Card } from "../ui";
import { Button } from "../ui";
import { Badge } from "../ui";
import {
  List,
  ListItem,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  DataList } from
'../shared/lists';

// Sample data for demonstrations
import { Input } from "@/frontend/components/ui/Input";const sampleContacts = [
{ id: 1, name: '<PERSON>', email: "<EMAIL>", company: 'Acme Corp', role: "Manager" },
{ id: 2, name: '<PERSON>', email: "<EMAIL>", company: 'Tech Inc', role: "Developer" },
{ id: 3, name: '<PERSON>', email: "<EMAIL>", company: 'Design Co', role: "Designer" },
{ id: 4, name: '<PERSON>', email: "<EMAIL>", company: 'Marketing Ltd', role: "Marketer" }];


const sampleExpenses = [
{ id: 1, name: 'Office Rent', amount: 2500, category: 'Facilities', date: '2024-01-15' },
{ id: 2, name: 'Software Licenses', amount: 450, category: 'Technology', date: '2024-01-10' },
{ id: 3, name: 'Marketing Campaign', amount: 1200, category: 'Marketing', date: '2024-01-08' }];


/**
 * Comprehensive showcase of the new list component system
 * Demonstrates all variants, features, and usage patterns
 */
const ListShowcase: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const simulateLoading = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <div className="space-y-8 p-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-primary dark:text-primary mb-2">
          List Components Showcase
        </h1>
        <p className="text-secondary dark:text-subtle max-w-2xl mx-auto">
          Demonstration of the modernized list component system with type-safe variants,
          responsive design, and enhanced accessibility features.
        </p>
      </div>

      {/* Basic List Component */}
      <Card>
        <h2 className="text-xl font-semibold mb-4">Basic List Component</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Stack Layout */}
          <div>
            <h3 className="text-lg font-medium mb-3">Stack Layout (Default)</h3>
            <List variant="default" layout="stack" spacing="default">
              <ListItem variant="interactive" border="left">
                <div className="flex items-center justify-between">
                  <span>Interactive List Item</span>
                  <Badge variant="primary">New</Badge>
                </div>
              </ListItem>
              <ListItem variant="default" border="left">
                <span>Regular List Item</span>
              </ListItem>
              <ListItem variant="interactive" border="left" selected>
                <div className="flex items-center justify-between">
                  <span>Selected Item</span>
                  <Badge variant="success">Active</Badge>
                </div>
              </ListItem>
            </List>
          </div>

          {/* Grid Layout */}
          <div>
            <h3 className="text-lg font-medium mb-3">Grid Layout</h3>
            <List variant="comfortable" layout="grid" spacing="md">
              <ListItem variant="interactive" border="full" size="comfortable">
                <div className="text-center">
                  <div className="text-2xl mb-2">📊</div>
                  <div className="font-medium">Analytics</div>
                </div>
              </ListItem>
              <ListItem variant="interactive" border="full" size="comfortable">
                <div className="text-center">
                  <div className="text-2xl mb-2">👥</div>
                  <div className="font-medium">Contacts</div>
                </div>
              </ListItem>
              <ListItem variant="interactive" border="full" size="comfortable">
                <div className="text-center">
                  <div className="text-2xl mb-2">💰</div>
                  <div className="font-medium">Expenses</div>
                </div>
              </ListItem>
            </List>
          </div>
        </div>
      </Card>

      {/* Table Component */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Enhanced Table Component</h2>
          <Button variant="outline" size="sm" onClick={simulateLoading}>
            Simulate Loading
          </Button>
        </div>
        
        <Table variant="striped" size="default" loading={loading}>
          <TableHeader>
            <TableRow>
              <TableCell header>Name</TableCell>
              <TableCell header>Amount</TableCell>
              <TableCell header>Category</TableCell>
              <TableCell header>Date</TableCell>
              <TableCell header align="right">Actions</TableCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sampleExpenses.map((expense) =>
            <TableRow key={expense.id} interactive>
                <TableCell>{expense.name}</TableCell>
                <TableCell>${expense.amount.toLocaleString()}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{expense.category}</Badge>
                </TableCell>
                <TableCell>{expense.date}</TableCell>
                <TableCell align="right">
                  <Button variant="ghost" size="sm">Edit</Button>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>

      {/* DataList Component */}
      <Card>
        <h2 className="text-xl font-semibold mb-4">DataList with Search & Filtering</h2>
        
        <div className="mb-4">
          <Input
            type="text"
            placeholder="Search contacts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-strong dark:border-strong rounded-md" />

        </div>

        <DataList
          variant="cards"
          density="comfortable"
          data={sampleContacts}
          searchTerm={searchTerm}
          filterFn={(contact, term) =>
          contact.name.toLowerCase().includes(term.toLowerCase()) ||
          contact.email.toLowerCase().includes(term.toLowerCase()) ||
          contact.company.toLowerCase().includes(term.toLowerCase())
          }
          keyExtractor={(contact) => contact.id}
          renderItem={(contact) =>
          <Card
            variant={selectedContact === contact.id ? "bordered" : "default"}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => setSelectedContact(
              selectedContact === contact.id ? null : contact.id
            )}>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-primary dark:text-primary">
                    {contact.name}
                  </h3>
                  <p className="text-sm text-muted dark:text-subtle">
                    {contact.email}
                  </p>
                  <p className="text-sm text-secondary dark:text-subtle">
                    {contact.role} at {contact.company}
                  </p>
                </div>
                <Badge variant={selectedContact === contact.id ? "primary" : "secondary"}>
                  {selectedContact === contact.id ? "Selected" : "Contact"}
                </Badge>
              </div>
            </Card>
          }
          emptyMessage="No contacts found matching your search"
          emptyIcon={
          <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />

            </svg>
          } />

      </Card>

      {/* Loading and Empty States */}
      <Card>
        <h2 className="text-xl font-semibold mb-4">Loading & Empty States</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Loading State</h3>
            <List loading>
              <ListItem>This won't show</ListItem>
            </List>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-3">Empty State</h3>
            <List
              empty
              emptyMessage="No items to display"
              emptyIcon={
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />

                </svg>
              }
              emptyAction={
              <Button variant="primary" size="sm">
                  Add First Item
                </Button>
              } />

          </div>
        </div>
      </Card>
    </div>);

};

export default ListShowcase;