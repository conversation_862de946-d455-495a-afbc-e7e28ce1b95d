import React, { useState } from "react";
import { Card } from "../ui";
import { Input, Select, Textarea, FormField, FormSection, FormGrid } from "../shared/forms";
import { UserIcon, EnvelopeIcon, PhoneIcon } from "@heroicons/react/24/outline";

/**
 * FormShowcase component demonstrating the new form system
 * 
 * This component showcases:
 * - All form component variants and sizes
 * - Error states and validation
 * - Loading states
 * - Icon integration
 * - Grid layouts and sections
 * - Accessibility features
 */import { Button } from "@/frontend/components/ui/Button";
export const FormShowcase: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: "",
    phone: '',
    company: '',
    role: "",
    priority: '',
    description: '',
    terms: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Simulate validation
    const newErrors: Record<string, string> = {};
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.email) newErrors.email = 'Email is required';
    if (formData.email && !formData.email.includes('@')) newErrors.email = 'Invalid email format';

    setTimeout(() => {
      setErrors(newErrors);
      setLoading(false);
      if (Object.keys(newErrors).length === 0) {
        alert('Form submitted successfully!');
      }
    }, 1500);
  };

  const selectOptions = [
  { value: 'developer', label: 'Developer' },
  { value: 'designer', label: 'Designer' },
  { value: 'manager', label: 'Manager' },
  { value: 'other', label: 'Other' }];


  const priorityOptions = [
  { value: 'low', label: 'Low Priority' },
  { value: 'medium', label: 'Medium Priority' },
  { value: 'high', label: 'High Priority' },
  { value: 'urgent', label: 'Urgent', disabled: true }];


  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-primary dark:text-gray-100 mb-2">
          Form Components Showcase
        </h1>
        <p className="text-secondary dark:text-subtle">
          Demonstrating the new type-safe form system with variants, validation, and accessibility
        </p>
      </div>

      {/* Component Variants Demo */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Component Variants & Sizes</h2>
        
        <FormGrid cols={3} gap="lg">
          {/* Input variants */}
          <div>
            <h3 className="text-sm font-medium mb-3 text-primary dark:text-subtle">Input Sizes</h3>
            <div className="space-y-3">
              <Input size="sm" placeholder="Small input" />
              <Input size="default" placeholder="Default input" />
              <Input size="lg" placeholder="Large input" />
            </div>
          </div>

          {/* Select variants */}
          <div>
            <h3 className="text-sm font-medium mb-3 text-primary dark:text-subtle">Select Sizes</h3>
            <div className="space-y-3">
              <Select size="sm" placeholder="Small select" options={selectOptions} />
              <Select size="default" placeholder="Default select" options={selectOptions} />
              <Select size="lg" placeholder="Large select" options={selectOptions} />
            </div>
          </div>

          {/* Textarea variants */}
          <div>
            <h3 className="text-sm font-medium mb-3 text-primary dark:text-subtle">Textarea Sizes</h3>
            <div className="space-y-3">
              <Textarea size="sm" placeholder="Small textarea" />
              <Textarea size="default" placeholder="Default textarea" />
              <Textarea size="lg" placeholder="Large textarea" />
            </div>
          </div>
        </FormGrid>
      </Card>

      {/* States Demo */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Component States</h2>
        
        <FormGrid cols={2} gap="lg">
          <div className="space-y-4">
            <Input
              label="Default State"
              placeholder="Normal input"
              helpText="This is help text" />

            <Input
              label="Error State"
              placeholder="Input with error"
              error="This field has an error"
              value="invalid@" />

            <Input
              label="Success State"
              variant="success"
              placeholder="Valid input"
              value="<EMAIL>" />

            <Input
              label="Loading State"
              placeholder="Loading..."
              loading={true} />

          </div>
          
          <div className="space-y-4">
            <Input
              label="With Icon"
              placeholder="Enter your name"
              icon={<UserIcon className="h-5 w-5" />} />

            <Textarea
              label="Character Count"
              placeholder="Type something..."
              maxLength={100}
              showCharCount={true}
              value="This textarea shows character count" />

            <Select
              label="Required Field"
              placeholder="Choose an option"
              options={priorityOptions}
              required={true}
              error="Please select an option" />

          </div>
        </FormGrid>
      </Card>

      {/* Complete Form Demo */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-6">Complete Form Example</h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <FormSection
            title="Personal Information"
            description="Please provide your contact details">

            <FormGrid cols={2}>
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                icon={<UserIcon className="h-5 w-5" />}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                error={errors.name}
                required
                loading={loading} />

              
              <Input
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                icon={<EnvelopeIcon className="h-5 w-5" />}
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                error={errors.email}
                required
                loading={loading} />

            </FormGrid>

            <FormGrid cols={2}>
              <Input
                label="Phone Number"
                type="tel"
                placeholder="Enter your phone"
                icon={<PhoneIcon className="h-5 w-5" />}
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                helpText="Include country code if international"
                loading={loading} />

              
              <Input
                label="Company"
                placeholder="Enter company name"
                value={formData.company}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                loading={loading} />

            </FormGrid>
          </FormSection>

          <FormSection
            title="Professional Details"
            description="Tell us about your role and priorities">

            <FormGrid cols={2}>
              <Select
                label="Role"
                placeholder="Select your role"
                options={selectOptions}
                value={formData.role}
                onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                loading={loading} />

              
              <Select
                label="Priority Level"
                placeholder="Select priority"
                options={priorityOptions}
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                helpText="Urgent priority requires approval"
                loading={loading} />

            </FormGrid>

            <Textarea
              label="Description"
              placeholder="Tell us more about your requirements..."
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              maxLength={500}
              showCharCount={true}
              helpText="Provide as much detail as possible"
              loading={loading} />

          </FormSection>

          <div className="flex justify-end space-x-3 pt-4 border-t border-default dark:border-default">
            <Button variant="secondary"
            type="button"

            disabled={loading}>

              Cancel
            </Button>
            <Button variant="primary"
            type="submit"

            disabled={loading}>

              {loading ? 'Submitting...' : 'Submit Form'}
            </Button>
          </div>
        </form>
      </Card>
    </div>);

};

export default FormShowcase;