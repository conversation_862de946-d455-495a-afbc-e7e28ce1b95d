import React, { useState, useEffect, useCallback } from "react";
import {
  AllocationWithTotals,
  ProjectTotals } from
"../../hooks/useEstimateStaffManagement";
import { formatCurrency, formatRateDisplay } from "./utils";
import { RateDisplayMode, HoursPerDay } from "./RateDisplayControls";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface TeamMembersTableProps {
  /**
   * Staff allocations with calculated totals
   */
  allocationsWithTotals: AllocationWithTotals[];

  /**
   * Project total calculations
   */
  projectTotals: ProjectTotals;

  /**
   * Handler for changing staff rate with rate type
   */
  onRateChange: (internalId: string, newRate: number, rateType: 'daily' | 'hourly', rateAsEntered: number) => void;

  /**
   * Handler for triggering staff add modal
   */
  onTriggerAddStaff: () => void;

  /**
   * Handler for removing staff
   */
  onRemoveStaff: (internalId: string) => void;

  /**
   * Whether the table is in read-only mode
   */
  isReadOnly?: boolean;

  /**
   * Rate display mode (daily or hourly)
   */
  rateDisplayMode?: RateDisplayMode;

  /**
   * Hours per day for rate calculations
   */
  hoursPerDay?: HoursPerDay;

  /**
   * Current billing type (daily or hourly)
   */
  billingType?: 'daily' | 'hourly';
}

/**
 * Displays a table of team members with their rates, costs, and financial metrics
 */
const TeamMembersTable: React.FC<TeamMembersTableProps> = ({
  allocationsWithTotals,
  projectTotals,
  onRateChange,
  onTriggerAddStaff,
  onRemoveStaff,
  isReadOnly = false,
  rateDisplayMode = "daily",
  hoursPerDay = 7.5,
  billingType = "daily"
}) => {
  // Local state to track input values while user is typing
  const [inputValues, setInputValues] = useState<Record<string, string>>({});

  // Determine display labels based on rate display mode
  const rateLabel = rateDisplayMode === "hourly" ? "Hourly" : "Daily";
  const showHourly = rateDisplayMode === "hourly";

  // Helper function to get display value for an input
  const getDisplayValue = (staff: any) => {
    // If user is currently editing this field, show their input
    if (inputValues[staff.internalId] !== undefined) {
      return inputValues[staff.internalId];
    }

    // If we have the original rate as entered, use that when display mode matches rate type
    if (staff.rateAsEntered && staff.rateType) {
      // If the display mode matches how the rate was originally entered, show the original value
      if (showHourly && staff.rateType === 'hourly' || !showHourly && staff.rateType === 'daily') {
        return staff.rateAsEntered.toString();
      }
    }

    // Otherwise, convert the stored daily rate for display
    if (showHourly) {
      // In hourly pricing mode, convert daily rate to hourly for display
      return (staff.rateProposedDaily / hoursPerDay).toFixed(2);
    } else {
      // In daily pricing mode, show daily rate directly
      return staff.rateProposedDaily.toString();
    }
  };

  // Handle input change (just track what user types)
  const handleInputChange = (internalId: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [internalId]: value }));
  };

  // Handle input blur (validate and save)
  const handleInputBlur = (internalId: string, value: string) => {
    const numericValue = parseFloat(value) || 0;

    // Determine the rate type based on current display mode
    const rateType: 'daily' | 'hourly' = showHourly ? 'hourly' : 'daily';

    // Store the rate as entered by the user
    const rateAsEntered = numericValue;

    // Always calculate and store the daily rate for backward compatibility
    const dailyRate = showHourly ? numericValue * hoursPerDay : numericValue;

    // Pass all the rate information
    onRateChange(internalId, dailyRate, rateType, rateAsEntered);

    // Clear the local input state
    setInputValues((prev) => {
      const newState = { ...prev };
      delete newState[internalId];
      return newState;
    });
  };
  return (
    <div className="w-full bg-surface-card border border-default rounded-lg shadow-sm p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-primary">
          Team Members & Rates
        </h3>
        {!isReadOnly &&
        <Button variant="outline"
        onClick={onTriggerAddStaff}>


            <svg
            className="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg">

              <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6">
            </path>
            </svg>
            Add Staff Member
          </Button>
        }
      </div>

      {allocationsWithTotals.length === 0 ?
      <div className="text-center py-8 border border-dashed border-strong rounded-lg">
          <svg
          className="w-10 h-10 mx-auto text-subtle mb-3"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24">

            <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />

          </svg>
          <h4 className="text-muted text-lg font-medium mb-2">
            No Staff Assigned
          </h4>
          <p className="text-muted max-w-md mx-auto">
            Add staff members to your estimate to begin allocating time and
            calculating project costs.
          </p>
          {!isReadOnly &&
        <Button variant="primary"
        onClick={onTriggerAddStaff}>


              Add Staff Member
            </Button>
        }
        </div> :

      <div className="overflow-x-auto">
          <table className="min-w-full table-fixed">
            <colgroup>
              <col className="w-[15%]" />
              <col className="w-[15%]" />
              <col className="w-[10%]" />
              <col className="w-[10%]" />
              <col className="w-[10%]" />
              <col className="w-[12%]" />
              <col className="w-[12%]" />
              <col className="w-[8%]" />
              <col className="w-[8%]" />
            </colgroup>
            <thead>
              <tr className="bg-surface-page">
                <th className="px-3 py-2 text-left text-xs font-medium text-muted uppercase tracking-wider">
                  Team Member
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted uppercase tracking-wider">
                  Role
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-muted uppercase tracking-wider">
                  Target Rate{" "}
                  <span className="font-normal text-subtle">
                    ({rateLabel})
                  </span>
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-muted uppercase tracking-wider">
                  Proposed Rate{" "}
                  <span className="font-normal text-subtle">
                    ({rateLabel})
                  </span>
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-muted uppercase tracking-wider">
                  Total Days
                </th>
                <th className="px-3 py-2 text-right text-xs font-medium text-muted uppercase tracking-wider">
                  Total Cost
                </th>
                <th className="px-3 py-2 text-right text-xs font-medium text-muted uppercase tracking-wider">
                  Total Fees
                </th>
                <th className="px-3 py-2 text-right text-xs font-medium text-muted uppercase tracking-wider">
                  GM %
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-muted uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
              {allocationsWithTotals.map((staff) => {
              const marginPercentage =
              staff.totalFees > 0 ?
              (staff.totalFees - staff.totalCost) / staff.totalFees *
              100 :
              0;

              return (
                <tr
                  key={staff.internalId}
                  className={`hover:bg-surface-page dark:hover:bg-surface-elevated/50 ${
                  staff.isPlaceholder ?
                  "bg-surface-page/50/20" :
                  ""}`
                  }>

                    <td className="px-3 py-2 text-sm text-primary font-medium">
                      <div className="flex items-center space-x-2">
                        {/* Avatar */}
                        {staff.avatarUrl ?
                      <img
                        className="h-8 w-8 rounded-full flex-shrink-0"
                        src={staff.avatarUrl}
                        alt={`${staff.firstName} ${staff.lastName}`} /> :


                      <div className="h-8 w-8 rounded-full bg-surface-alt flex items-center justify-center text-xs font-medium text-secondary flex-shrink-0">
                            {(staff.firstName?.[0] || '') + (staff.lastName?.[0] || '')}
                          </div>
                      }
                        <span>
                          {staff.firstName || ""} {staff.lastName || ""}
                          {staff.isPlaceholder &&
                        <span className="text-xs text-muted italic ml-1">
                              (placeholder)
                            </span>
                        }
                        </span>
                      </div>
                    </td>
                    <td className="px-3 py-2 text-sm text-secondary">
                      {staff.projectRole || "Staff"}
                    </td>
                    <td className="px-3 py-2 text-center text-sm text-secondary">
                      {formatRateDisplay(
                      staff.onbordTargetRateDaily,
                      showHourly,
                      hoursPerDay
                    )}
                    </td>
                    <td className="px-3 py-2 text-center">
                      {isReadOnly ?
                    <span className="text-sm text-secondary">
                          {formatRateDisplay(
                        staff.rateProposedDaily,
                        showHourly,
                        hoursPerDay
                      )}
                        </span> :

                    <div className="relative inline-block">
                          <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted pointer-events-none">
                            $
                          </span>
                          <Input
                        type="text"

                        value={getDisplayValue(staff)}
                        onChange={(e) =>
                        handleInputChange(
                          staff.internalId,
                          e.target.value
                        )
                        }
                        onBlur={(e) =>
                        handleInputBlur(staff.internalId, e.target.value)
                        }
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.currentTarget.blur();
                          }
                        }}
                        className="w-24 py-1 px-2 pl-6 bg-surface-card border border-strong rounded text-sm text-primary appearance-none" />

                        </div>
                    }
                    </td>
                    <td className="px-3 py-2 text-center text-sm text-primary font-medium">
                      {staff.totalDays.toFixed(1)}
                    </td>
                    <td className="px-3 py-2 text-right text-sm text-error">
                      {formatCurrency(staff.totalCost)}
                    </td>
                    <td className="px-3 py-2 text-right text-sm text-success">
                      {formatCurrency(staff.totalFees)}
                    </td>
                    <td className="px-3 py-2 text-right">
                      <span
                      className={`text-sm ${
                      marginPercentage >= 30 ?
                      "text-success" :
                      marginPercentage >= 20 ?
                      "text-warning" :
                      "text-error"}`
                      }>

                        {marginPercentage.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-3 py-2 text-right text-sm text-muted">
                      {!isReadOnly &&
                    <Button variant="danger"
                    onClick={() => onRemoveStaff(staff.internalId)}

                    title="Remove staff member">

                          <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24">

                            <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />

                          </svg>
                        </Button>
                    }
                    </td>
                  </tr>);

            })}
            </tbody>
            {allocationsWithTotals.length > 0 &&
          <tfoot className="bg-surface-page">
                <tr>
                  <td
                colSpan={2}
                className="px-3 py-2 text-sm font-medium text-right text-primary">

                    Totals
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-center text-primary">
                    {/* Average target rate, weighted by days */}
                    {projectTotals.totalDays > 0 ?
                formatRateDisplay(
                  projectTotals.totalTargetRevenue /
                  projectTotals.totalDays,
                  showHourly,
                  hoursPerDay
                ) :
                formatRateDisplay(0, showHourly, hoursPerDay)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-center text-primary">
                    {/* Average proposed rate, weighted by days */}
                    {projectTotals.totalDays > 0 ?
                formatRateDisplay(
                  projectTotals.totalRevenue / projectTotals.totalDays,
                  showHourly,
                  hoursPerDay
                ) :
                formatRateDisplay(0, showHourly, hoursPerDay)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-center text-primary">
                    {projectTotals.totalDays.toFixed(1)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-right text-primary">
                    {formatCurrency(projectTotals.totalCost)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-right text-primary">
                    {formatCurrency(projectTotals.totalRevenue)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-right">
                    <span
                  className={`${
                  projectTotals.marginPercentage >= 30 ?
                  "text-success" :
                  projectTotals.marginPercentage >= 20 ?
                  "text-warning" :
                  "text-error"}`
                  }>

                      {projectTotals.marginPercentage.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-3 py-2"></td>
                </tr>
              </tfoot>
          }
          </table>
        </div>
      }
    </div>);

};

export default TeamMembersTable;