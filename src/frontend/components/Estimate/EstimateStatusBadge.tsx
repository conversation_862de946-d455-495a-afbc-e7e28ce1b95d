import React from "react";
import { Badge } from "../ui/Badge";

interface EstimateStatusBadgeProps {
  status: string;
  className?: string;
}

/**
 * A reusable badge component for displaying estimate status
 *
 * Supported statuses: "draft", 'sent', 'accepted', 'declined'
 * Each status has appropriate styling based on its meaning
 */
const EstimateStatusBadge: React.FC<EstimateStatusBadgeProps> = ({
  status,
  className = "",
}) => {
  const getVariant = (
    status: string,
  ): "primary" | "secondary" | "success" | "warning" | "error" | "info" => {
    switch (status) {
      case "draft":
        return "secondary";
      case "sent":
      case "published":
        return "info";
      case "accepted":
        return "success";
      case "declined":
        return "error";
      case "archived":
        return "warning";
      default:
        return "secondary";
    }
  };

  const getLabel = (status: string): string => {
    switch (status) {
      case "draft":
        return "Draft";
      case "published":
        return "Published";
      case "archived":
        return "Archived";
      case "sent":
        return "Sent";
      case "accepted":
        return "Accepted";
      case "declined":
        return "Declined";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <Badge variant={getVariant(status)} className={className}>
      {getLabel(status)}
    </Badge>
  );
};

export default EstimateStatusBadge;
