import React from "react";
import { But<PERSON> } from "../ui";

interface EstimateInfoAccordionProps {
  showInformation: boolean;
  setShowInformation: (show: boolean) => void;
  variant?: "list" | "editor";
}

const EstimateInfoAccordion: React.FC<EstimateInfoAccordionProps> = ({
  showInformation,
  setShowInformation,
  variant = "editor",
}) => {
  return (
    <div className="p-4 border-b border-default bg-primary-light/50/10">
      <Button
        variant="primary"
        onClick={() => setShowInformation(!showInformation)}
        className="w-full flex items-center justify-between"
      >
        <div className="flex items-center">
          <svg
            className="w-5 h-5 text-primary-color mr-1.5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span className="font-medium text-sm text-primary-color">
            Understanding Estimates
          </span>
        </div>
        <svg
          className={`w-5 h-5 text-primary-color transition-transform duration-200 ${showInformation ? "transform rotate-180" : ""}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </Button>

      {showInformation && variant === "list" && (
        <div className="mt-3 text-sm text-secondary border-l-2 border-primary pl-4 py-0.5 ml-1.5">
          <h4 className="font-medium text-primary mb-2">Estimate Types:</h4>
          <p className="mb-3">Upstream works with two types of estimates:</p>
          <div className="grid md:grid-cols-2 gap-4 mt-2">
            <div className="p-3 border border-primary rounded-lg bg-primary-light/50/20">
              <h4 className="font-medium text-primary">Internal Estimates</h4>
              <p className="text-sm mt-1">
                Created within Upstream for project planning and pricing. These
                can be edited, linked to CRM deals for tracking, and published
                to Harvest when ready. Deal linking enables automatic syncing of
                project details and financial data.
              </p>
            </div>

            <div className="p-3 border border-primary rounded-lg bg-primary-light/50/20">
              <h4 className="font-medium text-primary">Harvest Estimates</h4>
              <p className="text-sm mt-1">
                Read-only estimates imported from Harvest for reference. These
                appear in your list but cannot be edited or linked to deals.
                They provide visibility into estimates created directly in
                Harvest.
              </p>
            </div>
          </div>

          <div className="mt-3 pt-1.5 border-t border-primary/30">
            <p className="text-xs font-medium text-secondary mb-1.5">
              <strong>Tip:</strong> Link internal estimates to deals to
              automatically sync project names and track opportunities. Only
              internal estimates can be linked to deals.
            </p>
          </div>

          <div className="mt-2 flex justify-between items-center">
            <span className="text-xs text-primary/80/80 italic">
              Create a new estimate using the button above
            </span>
            <a
              href="#estimates-section"
              className="text-xs text-primary-color hover:underline"
            >
              More details in Help docs →
            </a>
          </div>
        </div>
      )}

      {showInformation && variant === "editor" && (
        <div className="mt-3 text-sm text-secondary border-l-2 border-primary pl-4 py-0.5 ml-1.5">
          <p className="mb-3">
            Create project estimates with flexible pricing models and automatic
            financial calculations.
          </p>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="p-3 border border-primary rounded-lg bg-primary-light/50/20">
              <h4 className="font-medium text-primary mb-1">Billing Models</h4>
              <p className="text-sm mb-2">
                Choose the model that matches your pricing:
              </p>

              <div className="space-y-2 text-xs">
                <div>
                  <strong className="text-primary-color">Daily Billing:</strong>
                  <ul className="list-disc pl-4 mt-0.5 space-y-0.5">
                    <li>
                      Fixed rate per day (e.g., $1,600/day × 10 days = $16,000)
                    </li>
                    <li>
                      Hours per day doesn't affect total - you charge per day
                    </li>
                    <li>Best for: Fixed day rates, project-based pricing</li>
                  </ul>
                </div>

                <div>
                  <strong className="text-primary-color">
                    Hourly Billing:
                  </strong>
                  <ul className="list-disc pl-4 mt-0.5 space-y-0.5">
                    <li>
                      Rate × Hours × Days (e.g., $200/hr × 8h × 10d = $16,000)
                    </li>
                    <li>Changing hours/day adjusts total automatically</li>
                    <li>Best for: Time & materials, part-time resources</li>
                  </ul>
                </div>
              </div>

              <p className="text-xs mt-2 text-secondary">
                <strong>Tip:</strong> Toggle between models anytime - rates
                convert automatically to maintain daily equivalents.
              </p>
            </div>

            <div className="p-3 border border-primary rounded-lg bg-primary-light/50/20">
              <h4 className="font-medium text-primary mb-1">
                Deal Integration
              </h4>
              <p className="text-sm mb-2">Link estimates to CRM deals:</p>
              <ul className="list-disc pl-4 space-y-1 text-xs">
                <li>Deal name automatically updates estimate project name</li>
                <li>
                  Estimate controls deal's financial fields (value, dates,
                  terms)
                </li>
                <li>Linking is permanent to maintain data integrity</li>
              </ul>
              <p className="text-xs mt-2 text-secondary">
                Create estimates standalone or from existing deals.
              </p>
            </div>
          </div>

          <h4 className="font-medium text-primary mb-1 mt-3">Core Features:</h4>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            <li>
              <strong>Staff Allocation:</strong> Add team members and assign
              days across project weeks
            </li>
            <li>
              <strong>Automatic Calculations:</strong> Real-time cost, revenue,
              and margin analysis
            </li>
            <li>
              <strong>Visual Analytics:</strong> Weekly effort histogram and
              utilization metrics
            </li>
            <li>
              <strong>Flexible Discounting:</strong> Apply percentage or fixed
              amount discounts
            </li>
            <li>
              <strong>Save & Continue:</strong> Store work in progress as
              internal estimates
            </li>
          </ul>

          <div className="mt-3 pt-1.5 border-t border-primary/30">
            <p className="text-xs font-medium text-secondary">
              <strong>Note:</strong> Harvest publishing is temporarily disabled.
              All estimates are saved internally for now.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EstimateInfoAccordion;
