/**
 * Utility functions for the Price Estimation feature.
 */

import { StaffAllocation } from "../../../types/estimate-types";
// Import discount calculation from shared utilities
import { calculateDiscount } from "../../../../shared/utils/estimate-calculations";

// Constants
const HOURS_PER_DAY = 7.5; // Standard billable hours per day - DEPRECATED, use estimate's hoursPerDay instead

/**
 * Converts an hourly rate to a daily rate based on standard hours.
 */
export const convertHourlyToDailyRate = (hourlyRate: number | null | undefined): number => {
  if (hourlyRate === null || hourlyRate === undefined) {
    return 0;
  }
  return hourlyRate * HOURS_PER_DAY;
};

/**
 * Converts a daily rate to an hourly rate based on specified hours per day.
 * @param dailyRate - The daily rate.
 * @param hoursPerDay - Hours per day (default: 7.5).
 * @returns The calculated hourly rate. Returns 0 if dailyRate is null or undefined.
 */
export const convertDailyToHourlyRate = (dailyRate: number | null | undefined, hoursPerDay: number = 7.5): number => {
  if (dailyRate === null || dailyRate === undefined || hoursPerDay <= 0) {
    return 0;
  }
  return dailyRate / hoursPerDay;
};

/**
 * Formats a rate for display based on display mode and hours per day setting.
 * @param dailyRate - The daily rate (stored value).
 * @param showHourly - Whether to display as hourly rate.
 * @param hoursPerDay - Hours per day for conversion (default: 7.5).
 * @param currencyCode - Currency code for formatting (default: 'AUD').
 * @returns The formatted rate string.
 */
export const formatRateDisplay = (
  dailyRate: number,
  showHourly: boolean = false,
  hoursPerDay: number = 7.5,
  currencyCode: string = 'AUD'
): string => {
  if (showHourly) {
    const hourlyRate = convertDailyToHourlyRate(dailyRate, hoursPerDay);
    return formatCurrency(hourlyRate, currencyCode);
  }
  return formatCurrency(dailyRate, currencyCode);
};

/**
 * Calculates the daily gross margin percentage.
 */
export const calculateDailyGrossMarginPercentage = (proposedRateDaily: number, costRateDaily: number): number => {
  if (proposedRateDaily <= 0) {
    return 0;
  }
  const margin = proposedRateDaily - costRateDaily;
  return (margin / proposedRateDaily) * 100;
};

/**
 * Calculates the total allocated days for a staff member from their weekly allocation.
 */
export const calculateTotalDays = (weeklyAllocation: { [weekIdentifier: string]: number }): number => {
  // Use integer arithmetic to avoid floating point errors
  // Convert to tenths (multiply by 10), sum as integers, then convert back
  const totalTenthDays = Object.values(weeklyAllocation).reduce((sum, days) => {
    // Convert each day value to tenths and round to ensure integer
    const tenthDays = Math.round((days || 0) * 10);
    return sum + tenthDays;
  }, 0);
  
  // Convert back to days and round to 1 decimal place
  return Math.round(totalTenthDays) / 10;
};

/**
 * Calculates the total cost for a staff member based on their daily cost rate and total days.
 */
export const calculateTotalCost = (costRateDaily: number, totalDays: number): number => {
  return costRateDaily * totalDays;
};

/**
 * Calculates the total fees (revenue) for a staff member based on their proposed daily rate and total days.
 * @deprecated Use calculateTotalFeesWithBilling instead to support both daily and hourly billing
 */
export const calculateTotalFees = (proposedRateDaily: number, totalDays: number): number => {
  return proposedRateDaily * totalDays;
};

/**
 * Calculates the total fees based on billing type (daily or hourly).
 * IMPORTANT: The rate parameter is ALWAYS a daily rate as stored in the database.
 * For daily billing: dailyRate * days
 * For hourly billing: Convert to hourly rate first, then multiply by total hours
 */
export const calculateTotalFeesWithBilling = (
  rate: number, // Always a daily rate from the database
  totalDays: number,
  billingType: 'daily' | 'hourly',
  hoursPerDay: number,
  rateType?: 'daily' | 'hourly', // How the rate was originally entered
  rateAsEntered?: number // The original rate value as entered
): number => {
  // If we have the original rate information and it matches the billing type,
  // use the original rate for more accurate calculations
  if (rateType && rateAsEntered !== undefined && rateType === billingType) {
    if (billingType === 'daily') {
      return rateAsEntered * totalDays;
    } else {
      // For hourly billing with original hourly rate
      const totalHours = totalDays * hoursPerDay;
      return rateAsEntered * totalHours;
    }
  }
  
  // Otherwise fall back to the current behavior
  if (billingType === 'daily') {
    return rate * totalDays;
  } else {
    // For hourly billing with daily rate storage:
    // 1. Convert stored daily rate to hourly rate
    const hourlyRate = rate / hoursPerDay;
    // 2. Calculate total hours
    const totalHours = totalDays * hoursPerDay;
    // 3. Calculate total fees
    return hourlyRate * totalHours;
    // Note: This equals rate * totalDays, which seems odd but is correct
    // because we're storing rates that were already converted from hourly input
  }
};

/**
 * Converts a rate from one billing type to another.
 * When switching from daily to hourly: hourlyRate = dailyRate / hoursPerDay
 * When switching from hourly to daily: dailyRate = hourlyRate * hoursPerDay
 */
export const convertRate = (
  currentRate: number,
  fromType: 'daily' | 'hourly',
  toType: 'daily' | 'hourly',
  hoursPerDay: number
): number => {
  if (fromType === toType) {
    return currentRate;
  }
  
  if (toType === 'hourly') {
    // Daily → Hourly
    return currentRate / hoursPerDay;
  } else {
    // Hourly → Daily
    return currentRate * hoursPerDay;
  }
};

/**
 * Calculates the project totals from staff allocations including GST.
 * This version includes GST calculations for Australian tax compliance.
 */
export const calculateProjectTotalsWithGST = (
  staffAllocations: { 
    totalCost: number; 
    totalFees: number; 
    totalDays: number;
    onbordTargetRateDaily?: number;
  }[]
): {
  totalRevenue: number;
  totalCosts: number;
  marginAmount: number;
  marginPercentage: number;
  subTotal: number;
  gstAmount: number;
  grandTotal: number;
  totalDays: number;
  targetTotalRevenue: number;
  targetRateDifference: number;
  targetRatePercentageDifference: number;
  averageDailyRate: number;
} => {
  const totalRevenue = staffAllocations.reduce((sum, alloc) => sum + alloc.totalFees, 0);
  const totalCosts = staffAllocations.reduce((sum, alloc) => sum + alloc.totalCost, 0);
  const marginAmount = totalRevenue - totalCosts;
  const marginPercentage = totalRevenue > 0 ? (marginAmount / totalRevenue) * 100 : 0;
  
  // Calculate total days
  const totalDays = staffAllocations.reduce((sum, alloc) => sum + alloc.totalDays, 0);
  
  // Calculate average daily rate
  const averageDailyRate = totalDays > 0 ? totalRevenue / totalDays : 0;
  
  // Calculate target revenue based on target rates
  const targetTotalRevenue = staffAllocations.reduce((sum, alloc) => {
    const targetRate = alloc.onbordTargetRateDaily || 0;
    return sum + (targetRate * alloc.totalDays);
  }, 0);
  
  // Calculate rate difference from target
  const targetRateDifference = totalRevenue - targetTotalRevenue;
  const targetRatePercentageDifference = targetTotalRevenue > 0 
    ? (targetRateDifference / targetTotalRevenue) * 100 
    : 0;
  
  // Australian GST calculation (10%)
  const subTotal = totalRevenue;
  const gstAmount = subTotal * 0.1; // 10% GST
  const grandTotal = subTotal + gstAmount;

  return {
    totalRevenue,
    totalCosts,
    marginAmount,
    marginPercentage,
    subTotal,
    gstAmount,
    grandTotal,
    totalDays,
    targetTotalRevenue,
    targetRateDifference,
    targetRatePercentageDifference,
    averageDailyRate
  };
};

// Define a structure for calculated totals per staff member
interface StaffTotals {
  totalDays: number;
  totalCost: number;
  totalFees: number;
}

/**
 * Calculates the overall project totals by summing up totals from each staff allocation.
 * This version includes discount calculations.
 * @param staffAllocationsWithTotals - Array of staff allocations including their calculated totals.
 * @returns An object containing project metrics such as:
 *   - totalRevenue: Total revenue from all staff allocations
 *   - totalCost: Total cost from all staff allocations
 *   - marginAmount: Difference between revenue and cost
 *   - marginPercentage: Margin as a percentage of revenue
 *   - totalDays: Total days allocated across all staff
 *   - averageDailyRate: Average rate per day
 *   - targetTotalRevenue: What the revenue would be if all staff were at target rates
 *   - targetRateDifference: Difference between proposed revenue and target revenue
 *   - targetRatePercentageDifference: Percentage difference from target rates
 */
export const calculateProjectTotals = (
  staffAllocationsWithTotals: (StaffTotals & { [key: string]: any })[],
  discountType: 'percentage' | 'amount' | 'none' = 'none',
  discountValue: number = 0
): {
  totalRevenue: number;
  totalCost: number;
  marginAmount: number;
  marginPercentage: number;
  totalDays: number;
  averageDailyRate: number;
  targetTotalRevenue: number;
  targetRateDifference: number;
  targetRatePercentageDifference: number;
  discountType: 'percentage' | 'amount' | 'none';
  discountValue: number;
  discountAmount: number;
  discountedRevenue: number;
} => {
  const totalDays = staffAllocationsWithTotals.reduce((sum, alloc) => sum + alloc.totalDays, 0);
  const totalRevenue = staffAllocationsWithTotals.reduce((sum, alloc) => sum + alloc.totalFees, 0);
  const totalCost = staffAllocationsWithTotals.reduce((sum, alloc) => sum + alloc.totalCost, 0);

  // Calculate discount
  const { discountAmount, discountedTotal: discountedRevenue } =
    calculateDiscount(totalRevenue, discountType, discountValue);

  // Calculate margins based on discounted revenue
  const marginAmount = discountedRevenue - totalCost;
  const marginPercentage = discountedRevenue > 0 ? (marginAmount / discountedRevenue) * 100 : 0;

  // Other calculations
  const averageDailyRate = totalDays > 0 ? discountedRevenue / totalDays : 0;

  // Calculate target revenue and deviations
  const targetTotalRevenue = staffAllocationsWithTotals.reduce(
    (sum, alloc) => sum + ((alloc.onbordTargetRateDaily || 0) * alloc.totalDays),
    0
  );
  const targetRateDifference = discountedRevenue - targetTotalRevenue;
  const targetRatePercentageDifference = targetTotalRevenue > 0
    ? (targetRateDifference / targetTotalRevenue) * 100
    : 0;

  return {
    totalRevenue,
    totalCost,
    marginAmount,
    marginPercentage,
    totalDays,
    averageDailyRate,
    targetTotalRevenue,
    targetRateDifference,
    targetRatePercentageDifference,

    // Discount fields
    discountType,
    discountValue,
    discountAmount,
    discountedRevenue
  };
};

// Import centralized formatting utilities
import { formatCurrencyLegacy as formatCurrency } from "../../../utils/format";

/**
 * Formats a number as currency (e.g., $1,234.56).
 * @deprecated Use centralized formatCurrency from utils/format.ts instead
 */
export { formatCurrency };

/**
 * Formats a date in a consistent way for the estimate feature.
 */
export const formatEstimateDate = (date: Date | string): string => {
  // Convert string dates to Date objects
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Format the date as DD/MM/YYYY
  return dateObj.toLocaleDateString('en-AU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

/**
 * Get CSS class and text for status badges.
 */
export const getStatusStyles = (status: string): { className: string; label: string } => {
  switch (status.toLowerCase()) {
    case 'draft':
      return {
        className: 'bg-surface-alt text-primary dark:bg-surface-elevated dark:text-gray-300',
        label: 'Draft',
      };
    case 'sent':
      return {
        className: 'bg-primary-light text-blue-800 dark:bg-primary-dark/30 dark:text-blue-300',
        label: 'Sent',
      };
    case 'accepted':
      return {
        className: 'bg-success-light text-green-800 dark:bg-success-dark/30 dark:text-green-300',
        label: 'Accepted',
      };
    case 'declined':
      return {
        className: 'bg-error-light text-red-800 dark:bg-error-dark/30 dark:text-red-300',
        label: 'Declined',
      };
    default:
      return {
        className: 'bg-surface-alt text-primary dark:bg-surface-elevated dark:text-gray-300',
        label: status.charAt(0).toUpperCase() + status.slice(1),
      };
  }
};

// Re-export for backwards compatibility
export { calculateDiscount };

// formatCurrency is already exported above
