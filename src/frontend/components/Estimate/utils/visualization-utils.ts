/**
 * Utilities for data visualization in estimate components
 */

/**
 * Generates a pastel color with gradient for staff visualization
 * @param index The index used to generate a unique color
 * @returns Object containing base color, gradient and text color 
 */
export const generatePastelColor = (index: number) => {
  // Check if dark mode is enabled by checking document class
  // This works with Tailwind dark mode approach
  const isDarkMode = typeof document !== 'undefined' && document.documentElement.classList.contains('dark');
  
  // Different color palettes for light and dark mode
  const lightModeColors = [
    // Soft blues/teals
    { hue: 195, sat: 35, light: 75, textColor: "#100F0F" }, /* Flexoki black */
    // Muted greens
    { hue: 145, sat: 30, light: 78, textColor: "#100F0F" }, /* Flexoki black */
    // Soft purples
    { hue: 265, sat: 25, light: 80, textColor: "#100F0F" }, /* Flexoki black */
    // Warm oranges
    { hue: 30, sat: 35, light: 75, textColor: "#100F0F" }, /* Flexoki black */
    // Soft reds
    { hue: 355, sat: 25, light: 80, textColor: "#100F0F" }, /* Flexoki black */
    // Calm yellows
    { hue: 45, sat: 35, light: 80, textColor: "#100F0F" }, /* Flexoki black */
    // Cool grays with tint
    { hue: 210, sat: 15, light: 85, textColor: "#100F0F" }, /* Flexoki black */
    // Soft pinks
    { hue: 335, sat: 25, light: 82, textColor: "#100F0F" }, /* Flexoki black */
    // Muted aqua
    { hue: 175, sat: 30, light: 78, textColor: "#100F0F" }, /* Flexoki black */
    // Light lavender
    { hue: 280, sat: 20, light: 83, textColor: "#100F0F" }, /* Flexoki black */
  ];
  
  const darkModeColors = [
    // Darker blues with more saturation
    { hue: 195, sat: 40, light: 40, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Deep greens
    { hue: 145, sat: 30, light: 35, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Rich purples
    { hue: 265, sat: 35, light: 40, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Deep oranges
    { hue: 30, sat: 45, light: 40, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Muted reds
    { hue: 355, sat: 35, light: 40, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Deep gold
    { hue: 45, sat: 45, light: 45, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Steel blue
    { hue: 210, sat: 25, light: 45, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Magentas
    { hue: 335, sat: 30, light: 45, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Deep teals
    { hue: 175, sat: 40, light: 35, textColor: "#FFFCF0" }, /* Flexoki paper */
    // Deep lavender
    { hue: 280, sat: 30, light: 40, textColor: "#FFFCF0" }, /* Flexoki paper */
  ];
  
  // Select the appropriate color palette based on mode
  const colorPalettes = isDarkMode ? darkModeColors : lightModeColors;
  
  // Get color palette based on index (cycle through the palettes)
  const palette = colorPalettes[index % colorPalettes.length];
  
  // Apply small variation to avoid identical colors when cycling
  const cycleOffset = Math.floor(index / colorPalettes.length) * 8;
  const hue = (palette.hue + cycleOffset) % 360;
  
  // Create base and gradient colors
  const baseColor = `hsl(${hue}, ${palette.sat}%, ${palette.light}%)`;
  const gradientLight = isDarkMode
    ? `hsl(${hue}, ${palette.sat - 5}%, ${palette.light + 10}%)`
    : `hsl(${hue}, ${palette.sat - 5}%, ${palette.light + 5}%)`;
  
  return {
    base: baseColor,
    gradient: `linear-gradient(135deg, ${baseColor} 0%, ${gradientLight} 100%)`,
    text: palette.textColor
  };
};

/**
 * Prepares weekly data from staff allocations for histogram visualization
 * @param weeklyData Raw weekly data with allocation information
 * @param staffMembers Array of staff member information
 * @returns Processed data ready for visualization
 */
export interface StaffMember {
  internalId: string;
  name: string;
  initials: string;
  role: string;
  color?: string;
  avatarUrl?: string;
}

// This interface is specific to visualization needs and different from the main StaffAllocation type
export interface StaffAllocationForChart {
  internalId: string;
  days: number;
  initials: string;
  [key: string]: any;
}

export interface WeekData {
  week: string;
  identifier: string;
  hasAlternatingBackground: boolean; // Renamed from isWeekend for clarity
  total: number;
  staffAllocations: StaffAllocationForChart[];
}

export interface WeeklyHistogramData {
  weeklyData: WeekData[];
  staffMembers: StaffMember[];
}

/**
 * Calculates the height needed for the histogram based on maximum values
 * @param weeklyData The weekly data to analyze
 * @param boxSize The size of each allocation box in pixels
 * @returns Height in pixels for the histogram
 */
export const calculateHistogramHeight = (weeklyData: WeekData[], boxSize: number): number => {
  // Find maximum number of days in any week
  const maxDaysPerWeek = Math.max(...weeklyData.map(week => week.total), 1);
  // Calculate height needed (days × box size + some padding)
  return Math.min(Math.ceil(maxDaysPerWeek) * boxSize + 10, 400);
};

/**
 * Extracts week number and date from a week label
 * @param weekLabel The week label to parse (format: "W1 01/1")
 * @returns Object containing weekNum and dateStr
 */
export const parseWeekLabel = (weekLabel: string): { weekNum: string, dateStr: string } => {
  const weekParts = weekLabel.match(/W(\d+)\s+(.+)/);
  const weekNum = weekParts ? `W${weekParts[1]}` : "";
  const dateStr = weekParts ? weekParts[2] : weekLabel;
  return { weekNum, dateStr };
};