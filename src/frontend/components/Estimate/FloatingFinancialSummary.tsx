import React, { useState, useEffect } from "react";
import { ProjectTotals } from "../../hooks/useEstimateStaffManagement";
import { formatCurrency } from "./utils";
import { Button } from "@/frontend/components/ui/Button";

interface FloatingFinancialSummaryProps {
  projectTotals: ProjectTotals;
}

const FloatingFinancialSummary: React.FC<FloatingFinancialSummaryProps> = ({
  projectTotals,
}) => {
  // Initialize state from localStorage or default to true
  const [isExpanded, setIsExpanded] = useState(() => {
    const savedState = localStorage.getItem("financialSummaryPanelExpanded");
    return savedState === null ? true : savedState === "true";
  });

  // Save expanded state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(
      "financialSummaryPanelExpanded",
      isExpanded.toString(),
    );
  }, [isExpanded]);

  // Helper function to determine margin health indicator
  const getMarginHealthClass = (percentage: number) => {
    if (percentage >= 30) return "bg-success-light text-success/20";
    if (percentage >= 20) return "bg-warning-light text-warning/20";
    return "bg-error-light text-error/20";
  };

  return (
    <div className="fixed flex flex-col floating-finance-panel">
      {/* Collapsed state shows only an icon button */}
      {!isExpanded ? (
        <Button
          variant="primary"
          onClick={() => setIsExpanded(true)}
          aria-label="Show Financial Summary"
          aria-expanded="false"
          title="Show Financial Summary"
        >
          <svg
            className="w-5 h-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </Button>
      ) : (
        <div className="bg-surface-card rounded-lg border border-default p-3 w-full origin-top-right transition-all duration-300 ease-in-out scale-100 opacity-100">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-sm font-medium text-primary">
              Financial Summary
            </h3>
            <Button
              variant="secondary"
              onClick={() => setIsExpanded(false)}
              aria-label="Collapse Financial Summary"
              aria-expanded="true"
            >
              <svg
                className="w-3.5 h-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </Button>
          </div>

          {/* Redesigned metrics display with Fees as primary */}
          <div className="space-y-4">
            {/* Fees (ex. GST) - Primary metric with more prominence */}
            <div className="bg-primary/5 -mx-3 px-3 py-2 border-b border-primary/20">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-primary-dark">
                  Fees (ex. GST)
                </span>
                <span className="text-[9px] text-muted">
                  {projectTotals.totalDays.toFixed(1)} days
                </span>
              </div>
              <div className="mt-0.5">
                <span className="text-base font-bold text-primary">
                  {formatCurrency(
                    isNaN(projectTotals.discountedRevenue)
                      ? isNaN(projectTotals.totalRevenue)
                        ? 0
                        : projectTotals.totalRevenue
                      : projectTotals.discountedRevenue,
                  )}
                </span>
                {projectTotals.discountAmount > 0 && (
                  <span className="ml-2 text-xs text-muted line-through">
                    {formatCurrency(projectTotals.totalRevenue)}
                  </span>
                )}
              </div>
            </div>

            {/* GM and Total Cost on same row */}
            <div className="grid grid-cols-2 gap-2">
              {/* Gross Margin */}
              <div>
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-muted">GM</span>
                  <div
                    className={`px-1 py-0.5 rounded text-[9px] font-medium ${getMarginHealthClass(
                      projectTotals.marginPercentage,
                    )}`}
                  >
                    {projectTotals.marginPercentage >= 30
                      ? "Good"
                      : projectTotals.marginPercentage >= 20
                        ? "OK"
                        : "Low"}
                  </div>
                </div>
                <div className="mt-1">
                  <div className="flex flex-col">
                    <span
                      className={`text-sm font-bold ${
                        projectTotals.marginPercentage >= 30
                          ? "text-success"
                          : projectTotals.marginPercentage >= 20
                            ? "text-warning"
                            : "text-error"
                      }`}
                    >
                      {projectTotals.marginPercentage.toFixed(1)}%
                    </span>
                    <span className="text-[10px] text-secondary">
                      {formatCurrency(projectTotals.marginAmount)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Total Cost */}
              <div>
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-muted">Cost</span>
                  <span className="text-[9px] text-muted">Pre-OH</span>
                </div>
                <div className="mt-1">
                  <span className="text-sm font-bold text-primary">
                    {formatCurrency(
                      isNaN(projectTotals.totalCost)
                        ? 0
                        : projectTotals.totalCost,
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloatingFinancialSummary;
