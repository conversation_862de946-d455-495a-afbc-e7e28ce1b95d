import React, { useState } from "react";
import {
  AllocationWithTotals,
  ProjectTotals,
} from "../../hooks/useEstimateStaffManagement"; // Adjust path as needed
import { formatCurrency } from "./utils";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface BudgetSummaryProps {
  allocationsWithTotals: AllocationWithTotals[];
  projectTotals: ProjectTotals;
  onDiscountTypeChange?: (type: "percentage" | "amount" | "none") => void;
  onDiscountValueChange?: (value: number) => void;
}

const BudgetSummary: React.FC<BudgetSummaryProps> = ({
  allocationsWithTotals,
  projectTotals,
  onDiscountTypeChange,
  onDiscountValueChange,
}) => {
  const [showMargin, setShowMargin] = useState(false); // Internal state for GM toggle - default to collapsed

  // Calculate final totals for display with validation to prevent NaN
  const totalRevenue = isNaN(projectTotals.totalRevenue)
    ? 0
    : projectTotals.totalRevenue;

  // Ensure discountedRevenue is valid
  const discountedRevenue = isNaN(projectTotals.discountedRevenue)
    ? totalRevenue
    : projectTotals.discountedRevenue;

  // Calculate GST and grand total
  const gstAmount = discountedRevenue * 0.1;
  const grandTotal = discountedRevenue + gstAmount;

  // Use totalCost from props directly with validation
  const totalCost = isNaN(projectTotals.totalCost)
    ? 0
    : projectTotals.totalCost;

  // Log values for debugging
  console.log("BudgetSummary - Totals:", {
    totalRevenue,
    discountedRevenue,
    gstAmount,
    grandTotal,
    totalCost,
    projectTotals,
  });

  return (
    <div className="bg-surface-card rounded-lg shadow-sm border border-default p-4 mt-4">
      <div className="flex justify-between items-center mb-3">
        <h4 className="font-medium text-primary">Budget Summary</h4>
        <div className="flex items-center gap-2">
          {!showMargin && (
            <span className="text-xs text-muted italic">
              Click GM to show gross margin
            </span>
          )}
          <Button
            variant="secondary"
            onClick={() => setShowMargin(!showMargin)}
            className={`px-3 py-1 text-xs font-medium rounded flex items-center transition-colors ${
              showMargin
                ? "bg-accent-light text-accent-dark/30"
                : "bg-surface-alt text-secondary hover:bg-surface-alt dark:hover:bg-surface-alt"
            }`}
          >
            <span className="mr-1">GM</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={showMargin ? "M15 12H9" : "M12 6v6m0 0v6m0-6h6m-6 0H6"}
              />
            </svg>
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full table-fixed border-separate border-spacing-0">
          <colgroup>
            <col className="w-[15%]" /> {/* Staff name */}
            <col className="w-[13%]" /> {/* Role */}
            <col className="w-[6%]" /> {/* Days */}
            <col className="w-[8%]" /> {/* Daily Cost */}
            <col className="w-[12%]" /> {/* Total Cost */}
            <col className="w-[8%]" /> {/* Daily Revenue */}
            <col className="w-[16%]" /> {/* Total Revenue */}
            {showMargin && (
              <>
                <col className="w-[12%]" /> {/* Margin $ */}
                <col className="w-[8%]" /> {/* Margin % */}
              </>
            )}
          </colgroup>

          <thead>
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-muted uppercase tracking-wider border-b-2 border-default">
                Team Member
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-muted uppercase tracking-wider border-b-2 border-default">
                Role
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-muted uppercase tracking-wider border-b-2 border-default">
                Days
              </th>
              <th
                colSpan={2}
                className="px-4 py-1 text-center text-xs font-medium bg-error-light/10 text-error uppercase tracking-wider border-b border-default"
              >
                Cost
              </th>
              <th
                colSpan={2}
                className="px-4 py-1 text-center text-xs font-medium bg-success-light/10 text-success uppercase tracking-wider border-b border-default"
              >
                Revenue (Fees)
              </th>
              {showMargin && (
                <th
                  colSpan={2}
                  className="px-4 py-1 text-center text-xs font-medium bg-accent-light/10 text-accent-dark uppercase tracking-wider border-b border-default"
                >
                  GM
                </th>
              )}
            </tr>
            <tr>
              <th className="border-b-2 border-default"></th>
              <th className="border-b-2 border-default"></th>
              <th className="border-b-2 border-default"></th>
              <th className="px-4 py-2 text-center text-xs font-medium bg-error-light/10 text-error uppercase tracking-wider border-b-2 border-default">
                Daily
              </th>
              <th className="px-4 py-2 text-right text-xs font-medium bg-error-light/20 text-error uppercase tracking-wider border-b-2 border-default">
                Total
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium bg-success-light/10 text-success uppercase tracking-wider border-b-2 border-default">
                Daily
              </th>
              <th className="px-4 py-2 text-right text-xs font-medium bg-success-light/20 text-success uppercase tracking-wider border-b-2 border-default">
                Total
              </th>
              {showMargin && (
                <>
                  <th className="px-4 py-2 text-right text-xs font-medium bg-accent-light/10 text-accent uppercase tracking-wider border-b-2 border-default">
                    $
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium bg-accent-light/20 text-accent-dark uppercase tracking-wider border-b-2 border-default">
                    %
                  </th>
                </>
              )}
            </tr>
          </thead>

          <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
            {allocationsWithTotals.map((staff, index) => {
              const staffMargin = staff.totalFees - staff.totalCost;
              const staffMarginPercent =
                staff.totalFees > 0 ? (staffMargin / staff.totalFees) * 100 : 0;

              return (
                <tr
                  key={index}
                  className="hover:bg-surface-page dark:hover:bg-surface-elevated/50 border-b border-default"
                >
                  <td className="px-4 py-2 text-sm text-primary font-medium">
                    {staff.firstName || ""} {staff.lastName || ""}
                  </td>
                  <td className="px-4 py-2 text-sm text-secondary">
                    {staff.projectRole || "Staff"}
                  </td>
                  <td className="px-4 py-2 text-center text-sm text-secondary">
                    {staff.totalDays.toFixed(1)}
                  </td>
                  <td className="px-4 py-2 text-center text-sm text-error bg-error-light/50/5">
                    {formatCurrency(staff.onbordCostRateDaily)}
                  </td>
                  <td className="px-4 py-2 text-right text-sm text-error bg-error-light/80/10">
                    {formatCurrency(staff.totalCost)}
                  </td>
                  <td className="px-4 py-2 text-center text-sm text-success bg-success-light/50/5">
                    {formatCurrency(staff.rateProposedDaily)}
                  </td>
                  <td className="px-4 py-2 text-right text-sm text-success bg-success-light/80/10">
                    {formatCurrency(staff.totalFees)}
                  </td>
                  {showMargin && (
                    <>
                      <td className="px-4 py-2 text-right text-sm bg-accent-light/50/5">
                        <span className="text-accent-dark">
                          {formatCurrency(staffMargin)}
                        </span>
                      </td>
                      <td className="px-4 py-2 text-right text-sm bg-accent-light/80/10">
                        <span className="text-accent-dark">
                          {staffMarginPercent.toFixed(1)}%
                        </span>
                      </td>
                    </>
                  )}
                </tr>
              );
            })}
          </tbody>

          <tfoot>
            {/* Subtotal Row */}
            <tr className="bg-surface-page/50 font-medium border-t-2 border-strong">
              <td
                colSpan={2}
                className="px-4 py-2 text-sm text-primary text-right font-semibold"
              >
                Subtotals
              </td>
              <td className="px-4 py-2 text-center text-sm text-primary font-semibold">
                {projectTotals.totalDays.toFixed(1)}
              </td>
              <td className="px-4 py-2 text-center text-sm text-error bg-error-light/50/5 font-semibold">
                {projectTotals.totalDays > 0
                  ? formatCurrency(totalCost / projectTotals.totalDays)
                  : formatCurrency(0)}
              </td>
              <td className="px-4 py-2 text-right text-sm text-error font-semibold bg-error-light/20">
                {formatCurrency(totalCost)}
              </td>
              <td className="px-4 py-2 text-center text-sm text-success bg-success-light/50/5 font-semibold">
                {projectTotals.totalDays > 0
                  ? formatCurrency(totalRevenue / projectTotals.totalDays)
                  : formatCurrency(0)}
              </td>
              <td className="px-4 py-2 text-right text-sm text-success font-semibold bg-success-light/20">
                {formatCurrency(totalRevenue)}
              </td>
              {showMargin && (
                <>
                  <td className="px-4 py-2 text-right text-sm bg-accent-light/20 font-semibold">
                    <span className="text-accent-dark font-semibold">
                      {formatCurrency(projectTotals.marginAmount)}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-right text-sm bg-accent-light/20 font-semibold">
                    <span className="text-accent-dark font-semibold">
                      {projectTotals.marginPercentage.toFixed(1)}%
                    </span>
                  </td>
                </>
              )}
            </tr>

            {/* Separator */}
            <tr>
              <td
                colSpan={showMargin ? 9 : 7}
                className="py-2 border-b-2 border-strong"
              ></td>
            </tr>

            {/* Discount Row */}
            <tr className="bg-primary-light/10">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-2 text-sm text-primary text-right"
              >
                <div className="flex items-center justify-end gap-3 w-full">
                  <span className="font-medium text-primary-color">
                    Discount
                  </span>

                  <div className="flex items-center gap-1">
                    <Input
                      type="text"
                      value={
                        projectTotals.discountType === "percentage"
                          ? isNaN(projectTotals.discountValue)
                            ? ""
                            : projectTotals.discountValue
                          : totalRevenue > 0 && projectTotals.discountAmount > 0
                            ? parseFloat(
                                (
                                  (projectTotals.discountAmount /
                                    totalRevenue) *
                                  100
                                ).toFixed(2),
                              )
                            : ""
                      }
                      onChange={(e) => {
                        // Allow empty string
                        if (e.target.value === "") {
                          onDiscountTypeChange?.("percentage");
                          onDiscountValueChange?.(0);
                          return;
                        }

                        // Only allow numbers and decimal points
                        const numericValue = e.target.value.replace(
                          /[^0-9.]/g,
                          "",
                        );

                        // Handle multiple decimal points
                        const decimalCount = (numericValue.match(/\./g) || [])
                          .length;
                        let sanitizedValue = numericValue;
                        if (decimalCount > 1) {
                          const parts = numericValue.split(".");
                          sanitizedValue =
                            parts[0] + "." + parts.slice(1).join("");
                        }

                        // Try to parse as float
                        const value = parseFloat(sanitizedValue);

                        // Update if it's a valid number
                        if (!isNaN(value)) {
                          onDiscountTypeChange?.("percentage");
                          onDiscountValueChange?.(value);
                        }
                      }}
                      className="text-sm rounded-md border border-strong bg-surface-card px-2 py-1.5 w-16 text-right focus:ring-2 focus:ring-primary focus:border-primary dark:focus:ring-blue-600 dark:focus:border-primary appearance-none shadow-sm"
                      placeholder="0"
                    />

                    <span className="text-muted">%</span>
                  </div>

                  <span className="text-primary-color font-medium">or</span>

                  <div className="flex items-center gap-1">
                    <span className="text-muted">$</span>
                    <Input
                      type="text"
                      value={
                        projectTotals.discountType === "amount"
                          ? isNaN(projectTotals.discountValue)
                            ? ""
                            : projectTotals.discountValue
                          : projectTotals.discountAmount > 0
                            ? parseFloat(
                                projectTotals.discountAmount.toFixed(2),
                              )
                            : ""
                      }
                      onChange={(e) => {
                        // Allow empty string
                        if (e.target.value === "") {
                          onDiscountTypeChange?.("amount");
                          onDiscountValueChange?.(0);
                          return;
                        }

                        // Only allow numbers and decimal points
                        const numericValue = e.target.value.replace(
                          /[^0-9.]/g,
                          "",
                        );

                        // Handle multiple decimal points
                        const decimalCount = (numericValue.match(/\./g) || [])
                          .length;
                        let sanitizedValue = numericValue;
                        if (decimalCount > 1) {
                          const parts = numericValue.split(".");
                          sanitizedValue =
                            parts[0] + "." + parts.slice(1).join("");
                        }

                        // Try to parse as float
                        const value = parseFloat(sanitizedValue);

                        // Update if it's a valid number
                        if (!isNaN(value)) {
                          onDiscountTypeChange?.("amount");
                          onDiscountValueChange?.(value);
                        }
                      }}
                      className="text-sm rounded-md border border-strong bg-surface-card px-2 py-1.5 w-24 focus:ring-2 focus:ring-primary focus:border-primary dark:focus:ring-blue-600 dark:focus:border-primary appearance-none shadow-sm"
                      placeholder="0"
                    />
                  </div>
                </div>
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-2 text-right text-sm font-medium bg-primary-light/20"
              >
                {projectTotals.discountAmount > 0 ? (
                  <span className="text-primary-color">
                    -{formatCurrency(projectTotals.discountAmount)}
                  </span>
                ) : (
                  <span className="text-muted">-{formatCurrency(0)}</span>
                )}
              </td>
            </tr>

            {/* Total Fees (ex. GST) Row */}
            <tr className="bg-success-light/20 font-semibold">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-3 text-sm text-success text-right font-semibold"
              >
                Total Fees (ex. GST)
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-3 text-right text-lg text-success bg-success-light/30 font-semibold"
              >
                {formatCurrency(discountedRevenue)}
              </td>
            </tr>

            {/* GST Row */}
            <tr className="bg-surface-page/80/30">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-2 text-sm text-secondary text-right"
              >
                GST (10%)
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-2 text-right text-sm text-secondary bg-surface-alt/50"
              >
                {formatCurrency(gstAmount)}
              </td>
            </tr>

            {/* Total Row */}
            <tr className="bg-surface-alt font-bold">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-3 text-sm text-primary text-right font-bold"
              >
                TOTAL (Inc. GST)
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-3 text-right text-lg text-primary bg-surface-alt font-bold"
              >
                {formatCurrency(grandTotal)}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div className="mt-4 text-xs text-muted border-t border-default pt-2">
        <p>
          All prices are in Australian Dollars (AUD). GST is applied at the
          standard rate of 10%.
        </p>
      </div>
    </div>
  );
};

export default BudgetSummary;
