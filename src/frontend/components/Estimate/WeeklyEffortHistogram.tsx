import React, { useState, useMemo } from "react";
import { 
  generatePastelColor, 
  parseWeekLabel,
  StaffMember,
  WeekData
} from "./utils/visualization-utils";

interface WeeklyEffortHistogramProps {
  weeklyData: WeekData[];
  staffMembers: StaffMember[];
}

const WeeklyEffortHistogram: React.FC<WeeklyEffortHistogramProps> = ({
  weeklyData,
  staffMembers,
}) => {
  const [highlightedStaffId, setHighlightedStaffId] = useState<string | null>(null);
  
  // Create staff ordering map
  const staffOrder = useMemo(() => 
    Object.fromEntries(staffMembers.map((staff, index) => [staff.internalId, index])),
    [staffMembers]
  );
  
  // Sort weekly data with staff members visible first
  const processedWeeklyData = useMemo(() => 
    weeklyData.map(week => ({
      ...week,
      staffAllocations: [...week.staffAllocations]
        .filter(alloc => alloc.days > 0)
    })),
    [weeklyData]
  );

  // Determine y-axis scale - round to nearest 5
  const maxTotal = Math.max(...processedWeeklyData.map(week => week.total), 5);
  const yAxisMax = Math.ceil(maxTotal / 5) * 5;
  
  // Define chart dimensions
  const CHART_HEIGHT = 300; // Fixed chart height in pixels
  const WEEK_LABELS_HEIGHT = 60; // Height for the labels below chart (reduced from 100)
  
  // Calculate pixels-per-day based on yAxisMax and fixed chart height
  // This ensures the y-axis scale matches exactly with the column heights
  const PIXELS_PER_DAY = CHART_HEIGHT / yAxisMax;
  
  // Use fixed height to ensure consistent scaling
  const graphHeight = CHART_HEIGHT;
  
  // Simple stacked bar chart
  return (
    <div className="w-full">
      <div className="flex" style={{ height: `${graphHeight + WEEK_LABELS_HEIGHT}px` }}>
        {/* Y Axis */}
        <div className="pr-2 text-right">
          <div className="flex flex-col justify-between" style={{ height: `${graphHeight}px` }}>
            {[...Array(6)].map((_, i) => {
              const value = Math.round((5-i) * yAxisMax / 5);
              return (
                <div key={i} className="text-xs text-muted dark:text-subtle">
                  {value}d
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Chart Area */}
        <div className="flex-1 border-l border-b border-strong dark:border-strong relative" style={{ height: `${graphHeight}px` }}>
          {/* Grid Lines */}
          {[0, 1, 2, 3, 4, 5].map((i) => (
            <div 
              key={i} 
              className={`absolute left-0 right-0 border-t ${
                i === 5 
                  ? 'border-strong dark:border-strong' 
                  : 'border-default dark:border-default'
              }`}
              style={{ bottom: `${(i / 5) * graphHeight}px` }}
            />
          ))}
          
          {/* Week Columns - using flex layout */}
          <div className="absolute inset-0 flex">
            {processedWeeklyData.map((week) => {
              const { weekNum, dateStr } = parseWeekLabel(week.week);
              
              // Get width based on weeks count
              const columnWidth = processedWeeklyData.length > 15 ? '90%' : 
                                 processedWeeklyData.length > 10 ? '85%' : '75%';
              
              // Prepare staff data
              const staffDataForWeek = [...week.staffAllocations]
                .sort((a, b) => (staffOrder[a.internalId] || 0) - (staffOrder[b.internalId] || 0));
                
              return (
                <div key={week.identifier} className="flex-1 relative group flex items-center h-full">
                  {/* Stacked bars for each staff member with ABSOLUTE heights */}
                  <div className="w-full h-full relative">
                    {staffDataForWeek.map((staffAlloc) => {
                      if (staffAlloc.days <= 0) return null;
                      
                      const staffIndex = staffMembers.findIndex(s => s.internalId === staffAlloc.internalId);
                      const staffColor = generatePastelColor(staffIndex);
                      
                      // Calculate EXACT pixel height based on days - maintains perfect scale
                      // This ensures 1.5 days is proportionally taller than 1.2 days
                      let barHeight = staffAlloc.days * PIXELS_PER_DAY;
                      
                      // Apply a very small minimum (only for tiny values < 0.25 days)
                      // to ensure labels remain visible
                      if (staffAlloc.days > 0 && barHeight < 8) {
                        barHeight = 8;
                      }
                      
                      // Calculate position from bottom based on earlier staff blocks
                      const bottomPosition = staffDataForWeek
                        .slice(0, staffDataForWeek.indexOf(staffAlloc))
                        .reduce((total, staff) => {
                          if (staff.days <= 0) return total;
                          
                          // Use the exact same height calculation as above for consistency
                          let height = staff.days * PIXELS_PER_DAY;
                          if (staff.days > 0 && height < 8) {
                            height = 8;
                          }
                          
                          return total + height; // No gap between blocks
                        }, 0);
                      
                      // Determine font size based on days and height
                      const fontSize = 
                        staffAlloc.days < 0.5 ? '8px' : 
                        staffAlloc.days < 1.0 ? '8px' : 
                        staffAlloc.days < 2.0 ? '9px' : '9px';
                      
                      // Show text only for blocks that are big enough
                      const showText = staffAlloc.days >= 0.4;
                      
                      return (
                        <div
                          key={staffAlloc.internalId}
                          className={`absolute rounded-sm ${
                            highlightedStaffId === staffAlloc.internalId ? 'ring-1 ring-slate-400 dark:ring-slate-300 shadow-md z-10 transition-all duration-200' : 'transition-all duration-200'
                          }`}
                          style={{
                            height: `${barHeight}px`,
                            bottom: `${bottomPosition}px`,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: columnWidth,
                            background: staffColor.gradient
                          }}
                          onMouseEnter={() => setHighlightedStaffId(staffAlloc.internalId)}
                          onMouseLeave={() => setHighlightedStaffId(null)}
                        >
                          {/* More prominent pattern for days less than 5.0 */}
                          {staffAlloc.days < 5.0 && (
                            <div 
                              className="absolute inset-0 opacity-50" 
                              style={{ 
                                backgroundImage: document.documentElement.classList.contains('dark')
                                  ? 'repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(255,252,240,0.25) 5px, rgba(255,252,240,0.25) 9px)' /* Flexoki paper with opacity */
                                  : 'repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(255,252,240,0.4) 5px, rgba(255,252,240,0.4) 9px)' /* Flexoki paper with opacity */
                              }}
                            />
                          )}
                          
                          {/* Staff label - only show if box is big enough */}
                          {showText && (
                            <div className="h-full flex items-center justify-center px-0.5">
                              <span 
                                className="truncate whitespace-nowrap font-medium text-center w-full" 
                                style={{ 
                                  color: staffColor.text,
                                  fontSize: fontSize
                                }}
                              >
                                {staffAlloc.initials},{staffAlloc.days.toFixed(1)}
                              </span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                  
                  {/* Week label - all grouped together with proper spacing (reduced negative spacing) */}
                  <div className="absolute w-full text-center" style={{ bottom: '-55px' }}>
                    <div className="pt-2 pb-0.5 text-xs font-medium dark:text-subtle">{weekNum}</div>
                    <div className="text-xs text-muted dark:text-subtle pb-0.5">{dateStr}</div>
                    <div className="text-xs text-secondary dark:text-subtle">
                      {week.total > 0 ? `${week.total.toFixed(1)}d` : '-'}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* Team Members Legend */}
      <div className="mt-6 pt-3 border-t border-default dark:border-default">
        <h4 className="text-xs font-medium text-primary dark:text-subtle mb-2">Team Members</h4>
        <div className="flex flex-wrap gap-2">
          {staffMembers
            .filter(staff => processedWeeklyData.some(week => 
              week.staffAllocations.some(allocation => 
                allocation.internalId === staff.internalId && allocation.days > 0
              )
            ))
            .sort((a, b) => (staffOrder[a.internalId] || 0) - (staffOrder[b.internalId] || 0))
            .map((staff) => {
              const staffIndex = staffMembers.findIndex(s => s.internalId === staff.internalId);
              const staffColor = generatePastelColor(staffIndex);
              
              return (
                <div 
                  key={staff.internalId}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-xs transition-all duration-200 ${
                    highlightedStaffId === staff.internalId 
                      ? 'bg-surface-page dark:bg-surface-alt shadow-sm ring-1 ring-slate-200 dark:ring-slate-600' 
                      : 'hover:bg-surface-page/50 dark:hover:bg-surface-elevated/50'
                  }`}
                  onMouseEnter={() => setHighlightedStaffId(staff.internalId)}
                  onMouseLeave={() => setHighlightedStaffId(null)}
                >
                  <div 
                    className="w-5 h-5 rounded-md flex items-center justify-center font-medium text-xs shadow-sm"
                    style={{ 
                      background: staffColor.gradient,
                      color: staffColor.text
                    }}
                  >
                    {staff.initials}
                  </div>
                  <span className="font-medium dark:text-subtle">{staff.name}</span>
                  <span className="text-muted dark:text-subtle text-[10px]">({staff.role})</span>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default WeeklyEffortHistogram;