import React, { useState, useEffect } from "react";
import { EstimateFormState } from "../../hooks/useEstimateFormState";
import { getLinkedDealsForEstimate } from "../../api/crm";
import DealLinkModal from "../CRM/DealEdit/DealLinkModal";
import LinkedDealModal from "../CRM/DealEdit/LinkedDealModal";
import DealSelectionModal from "../CRM/DealEdit/DealSelectionModal";
import { Deal } from "../../types/crm-types"; // Adjust path as needed
import { format } from "date-fns"; // Import date-fns format

// Constants for dropdown options
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";

const INVOICE_FREQUENCY_OPTIONS = [
{ value:"weekly", label:"Weekly" },
{ value:"biweekly", label:"Every 2 Weeks" },
{ value:"monthly", label:"Monthly" },
{ value:"quarterly", label:"Quarterly" },
{ value:"milestone", label:"Milestone-based" },
{ value:"completion", label:"On Completion" }];


const PAYMENT_TERMS_OPTIONS = [
{ value: 7, label:"Net 7" },
{ value: 14, label:"Net 14" },
{ value: 20, label:"Net 20" },
{ value: 30, label:"Net 30" },
{ value: 45, label:"Net 45" },
{ value: 60, label:"Net 60" }];


// Define props based on what the form needs from the useEstimateFormState hook
interface EstimateConfigurationFormProps {
  formState: EstimateFormState;
  isReadOnly: boolean; // Combined read-only state (e.g., saved successfully or no permission)
  className?: string; // Add optional className prop
  draftUuid?: string; // Draft UUID for deal linking (optional)
  onDealSelected?: (deal: Deal | null) => void; // Callback when a deal is selected
}

const EstimateConfigurationForm: React.FC<EstimateConfigurationFormProps> = ({
  formState,
  isReadOnly,
  className ="w-[75%]", // Default to original width
  draftUuid,
  onDealSelected
}) => {
  // State for deal linking
  const [linkedDeal, setLinkedDeal] = useState<Deal | null>(null);

  // State for deal selection (new estimates)
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);

  // Modal states
  const [showDealLinkModal, setShowDealLinkModal] = useState(false);
  const [showLinkedDealModal, setShowLinkedDealModal] = useState(false);
  const [showDealSelectionModal, setShowDealSelectionModal] = useState(false);

  // Track if we've already notified parent about the linked deal
  const [hasNotifiedDealSelection, setHasNotifiedDealSelection] = useState(false);

  // Fetch linked deal when draftUuid changes (only if estimate is already saved)
  useEffect(() => {
    const fetchLinkedDeal = async () => {
      if (!draftUuid) {
        // If no draftUuid, we're in"new estimate' mode - no linked deal yet
        setLinkedDeal(null);
        setHasNotifiedDealSelection(false);
        return;
      }

      try {
        const deals = await getLinkedDealsForEstimate(draftUuid,"internal");
        const foundDeal = deals.length > 0 ? deals[0] : null;
        setLinkedDeal(foundDeal);

        // If a linked deal is found and we haven't notified yet, notify parent component
        if (foundDeal && onDealSelected && !hasNotifiedDealSelection) {
          onDealSelected(foundDeal);
          setHasNotifiedDealSelection(true);
        }
      } catch (error) {
        console.error("Failed to fetch linked deal:", error);
        setLinkedDeal(null);
      }
    };

    fetchLinkedDeal();
  }, [draftUuid]); // Remove onDealSelected from dependencies to prevent re-fetches

  // Notify parent when linked deal is found
  useEffect(() => {
    if (linkedDeal && onDealSelected && !hasNotifiedDealSelection) {
      onDealSelected(linkedDeal);
      setHasNotifiedDealSelection(true);
    }
  }, [linkedDeal, onDealSelected, hasNotifiedDealSelection]);

  // Handle deal link modal close with refresh
  const handleDealLinkModalClose = () => {
    setShowDealLinkModal(false);

    // Refresh linked deal data after potential linking
    if (draftUuid) {
      getLinkedDealsForEstimate(draftUuid,"internal").
      then((deals) => {
        setLinkedDeal(deals.length > 0 ? deals[0] : null);
      }).
      catch((error) => {
        console.error("Failed to refresh linked deal:", error);
      });
    }
  };

  // Handle linked deal modal close with refresh
  const handleLinkedDealModalClose = () => {
    setShowLinkedDealModal(false);

    // Refresh linked deal data after potential unlinking
    if (draftUuid) {
      getLinkedDealsForEstimate(draftUuid,"internal").
      then((deals) => {
        setLinkedDeal(deals.length > 0 ? deals[0] : null);
      }).
      catch((error) => {
        console.error("Failed to refresh linked deal:", error);
      });
    }
  };

  // Handle deal selection for new estimates
  const handleDealSelection = (deal: Deal) => {
    console.log("🔍 DEAL SELECTED:", deal);
    setSelectedDeal(deal);

    // Populate form fields from deal
    formState.setFormDataFromDeal(deal);

    console.log("🔍 AFTER DEAL SELECTION - isDealLinked will be:", !!deal);

    // Notify parent component about deal selection
    if (onDealSelected) {
      onDealSelected(deal);
    }
  };

  // Use deal linking state from form hook
  const isDealLinked = formState.isDealLinked;

  // Determine step completion states
  const isStep1Complete = !!selectedDeal || !!linkedDeal;
  const isStep2Complete = !!(formState.startDateStr && formState.endDateStr);
  const isStep3Complete = !!(formState.invoiceFrequency && formState.paymentTerms);

  return (
    <div className={`${className} space-y-4`}>
      {/* Estimate Details Form */}
      <div className="flex gap-4">
        {/* Left: Deal Selection & Project Info */}
        <div className="w-[40%] bg-surface-card rounded-lg shadow-sm border border-default p-4">
          <div className="space-y-3">
            {/* Deal Selection (only for new estimates) */}
            {!draftUuid &&
            <div className="mb-4">
                <h4 className="text-sm font-medium text-primary mb-2 flex items-center">
                  <span className="flex items-center justify-center w-6 h-6 rounded-full bg-primary text-white text-xs font-bold mr-2">
                    1
                  </span>
                  Select Deal
                </h4>
                <div
                  className={`relative py-4 px-3 border-2 rounded-lg transition-all cursor-pointer ${
                  selectedDeal ?"border-success bg-success-light/20" :"border-primary border-dashed hover:border-solid hover:bg-primary-light/5 dark:hover:bg-primary-dark/5"}`
                  }
                  onClick={() => !selectedDeal && setShowDealSelectionModal(true)}>

                  <div className="flex items-center space-x-2">
                    <div className="flex-shrink-0">
                      {selectedDeal ?
                        <svg
                          className="w-5 h-5 text-success"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg> :
                        <svg
                          className="w-5 h-5 text-primary"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4v16m8-8H4" />
                        </svg>
                      }
                    </div>
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium text-primary text-sm">
                        {selectedDeal ?
                          selectedDeal.name :"Choose a deal to estimate"}
                      </h5>
                      <p className="text-xs text-secondary truncate">
                        {selectedDeal ?
                          `${selectedDeal.company?.name} • ${selectedDeal.stage}` :"Required to create an estimate"}
                      </p>
                    </div>
                    {selectedDeal &&
                      <Button variant="secondary"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowDealSelectionModal(true);
                        }}>
                        Change
                      </Button>
                    }
                  </div>
                </div>
              </div>
            }
            {/* Client field - always populated from deal */}
            <div>
              <label
                htmlFor="clientName"
                className="block text-sm font-medium text-primary mb-1">
                Client
              </label>
              <Input
                type="text"
                id="clientName"
                value={formState.clientSearchTerm}
                placeholder="Select a deal first"
                className="w-full p-2 border rounded-lg bg-surface-alt border-default text-secondary cursor-not-allowed"
                disabled={true}
                readOnly />
            </div>
            {/* Project Name field - always populated from deal */}
            <div>
              <label
                htmlFor="projectName"
                className="block text-sm font-medium text-primary mb-1">
                Project Name
                <span className="ml-2 text-xs text-success">
                  (from deal)
                </span>
              </label>
              <Input
                type="text"
                id="projectName"
                value={formState.projectName}
                placeholder="Select a deal first"
                className="w-full p-2 border rounded-lg bg-surface-alt border-default text-secondary cursor-not-allowed"
                disabled={true}
                readOnly />
            </div>

            {/* View Deal Button - only show when deal is linked */}
            {(selectedDeal || linkedDeal) &&
            <div className="mt-3">
                <Button
                href={`/crm/deals/${(selectedDeal || linkedDeal)?.id}`}
                target="_blank"
                rel="noopener noreferrer"
                variant="success" className="">

                  <svg
                  className="w-3.5 h-3.5 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">

                    <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />

                  </svg>
                  View Deal
                </Button>
              </div>
            }
          </div>
        </div>
        {/* Right: Dates/Duration */}
        <div className={`w-[60%] bg-surface-card rounded-lg shadow-sm border border-default p-4 transition-all ${
          !isStep1Complete ? "opacity-50" : ""
        }`}>
          {/* Step 2 Header */}
          <h4 className="text-sm font-medium text-primary mb-3 flex items-center">
            <span className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold mr-2 transition-all ${
              isStep2Complete ? "bg-success text-white" : 
              isStep1Complete ? "bg-primary text-white" : "bg-surface-page text-secondary"
            }`}>
              {isStep2Complete ? "✓" : "2"}
            </span>
            Set Project Dates
            {!isStep1Complete && <span className="ml-2 text-xs text-muted">(select deal first)</span>}
          </h4>
          <div className={`grid grid-cols-2 gap-x-4 gap-y-3 ${!isStep1Complete ? "pointer-events-none" : ""}`}>
            <div className="space-y-3 flex flex-col justify-between h-full">
              <div>
                <label
                  htmlFor="startDate"
                  className="block text-sm font-medium text-primary mb-1">

                  Start Date <span className="text-error">*</span>
                </label>
                <Input
                  type="date"
                  id="startDate"
                  value={formState.startDateStr}
                  onChange={(e) =>
                  formState.handleDateChange(e.target.value, true)
                  }
                  className="w-[85%] p-2 border border-strong rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-sm"
                  disabled={isReadOnly || !isStep1Complete} />

              </div>
              <div>
                <label
                  htmlFor="endDate"
                  className="block text-sm font-medium text-primary mb-1">

                  End Date <span className="text-error">*</span>
                </label>
                <Input
                  type="date"
                  id="endDate"
                  value={formState.endDateStr}
                  onChange={(e) =>
                  formState.handleDateChange(e.target.value, false)
                  }
                  className="w-[85%] p-2 border border-strong rounded-lg focus:ring-2 focus:ring-primary focus:border-primary text-sm"
                  disabled={isReadOnly || !isStep1Complete} />

              </div>
            </div>
            <div className="flex flex-col h-full">
              <label className="block text-sm font-medium text-primary mb-1">
                Duration
              </label>
              <div className="flex flex-col space-y-1.5 h-full justify-between py-1">
                {/* Duration buttons */}
                <div className="grid grid-cols-4 gap-1.5">
                  {[
                  { label:"1W", value:"1week" },
                  { label:"2W", value:"2weeks" },
                  { label:"3W", value:"3weeks" },
                  { label:"4W/1M", value:"1month" }].
                  map((d) =>
                  <Button variant="secondary"
                  key={d.value}
                  type="button"
                  onClick={() => formState.handleDurationSelect(d.value)}
                  className={`py-1 px-1 rounded border text-xs ${
                  formState.activeDuration === d.value ?"border-primary bg-primary bg-opacity-10 text-primary" :"border-strong text-muted"}`
                  }
                  disabled={isReadOnly || !isStep1Complete}>

                      {d.label}
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-4 gap-1.5">
                  {[
                  { label:"6W", value:"6weeks" },
                  { label:"2M/8W", value:"2months" },
                  { label:"3M/12W", value:"3months" },
                  { label:"6M/24W", value:"6months" }].
                  map((d) =>
                  <Button variant="secondary"
                  key={d.value}
                  type="button"
                  onClick={() => formState.handleDurationSelect(d.value)}
                  className={`py-1 px-1 rounded border text-xs ${
                  formState.activeDuration === d.value ?"border-primary bg-primary bg-opacity-10 text-primary" :"border-strong text-muted"}`
                  }
                  disabled={isReadOnly || !isStep1Complete}>

                      {d.label}
                    </Button>
                  )}
                </div>
                <div className="border-t border-default my-1.5"></div>
                <div className="grid grid-cols-2 gap-1.5">
                  <Button variant="ghost"
                  type="button"
                  onClick={() => {
                    const d = new Date(formState.endDateStr);
                    d.setDate(d.getDate() - 7);
                    formState.handleDateChange(
                      format(d,"yyyy-MM-dd"),
                      false
                    );
                  }}

                  disabled={isReadOnly || !isStep1Complete}>

                    - 1 Week
                  </Button>
                  <Button variant="ghost"
                  type="button"
                  onClick={() => {
                    const d = new Date(formState.endDateStr);
                    d.setDate(d.getDate() + 7);
                    formState.handleDateChange(
                      format(d,"yyyy-MM-dd"),
                      false
                    );
                  }}

                  disabled={isReadOnly || !isStep1Complete}>

                    + 1 Week
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Invoice & Payment Terms Section */}
          <div className={`mt-4 pt-4 border-t border-default transition-all ${
            !isStep2Complete ? "opacity-50" : ""
          }`}>
            {/* Step 3 Header */}
            <div className="mb-3">
              <h4 className="text-sm font-medium text-primary flex items-center">
                <span className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold mr-2 transition-all ${
                  isStep3Complete ? "bg-success text-white" : 
                  isStep2Complete ? "bg-primary text-white" : "bg-surface-page text-secondary"
                }`}>
                  {isStep3Complete ? "✓" : "3"}
                </span>
                Invoice & Payment Terms
                {!isStep2Complete ? (
                  <span className="ml-2 text-xs text-muted">(set dates first)</span>
                ) : (
                  <span className="ml-2 text-xs font-normal text-muted">
                    This is used for cashflow forecasting
                  </span>
                )}
              </h4>
            </div>
            <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${!isStep2Complete ? "pointer-events-none" : ""}`}>
              {/* Invoice Frequency */}
              <div>
                <label
                  htmlFor="invoiceFrequency"
                  className="block text-sm font-medium text-primary mb-2">

                  <div className="flex items-center space-x-2">
                    <svg
                      className="w-4 h-4 text-muted"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">

                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />

                    </svg>
                    <span>Invoice Frequency</span>
                    <span className="text-error">*</span>
                  </div>
                </label>
                <div className="relative">
                  <Select
                    id="invoiceFrequency"
                    value={formState.invoiceFrequency}
                    onChange={(e) =>
                    formState.handleInvoiceFrequencyChange(e.target.value)
                    }
                    className={`w-full p-3 pr-10 border rounded-lg text-sm transition-all duration-200 appearance-none bg-surface-card ${
                    formState.invoiceFrequency ?"border-primary bg-primary-light/20" :"border-strong"} ${

                    !formState.invoiceFrequency && !isReadOnly ?"border-error" :""} focus:ring-2 focus:ring-primary focus:border-primary`
                    }
                    disabled={isReadOnly || !isStep2Complete}>

                    <option value="" disabled>
                      Choose how often to invoice...
                    </option>
                    {INVOICE_FREQUENCY_OPTIONS.map((option) =>
                    <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    )}
                  </Select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    {formState.invoiceFrequency ?
                    <svg
                      className="w-4 h-4 text-primary-color"
                      fill="currentColor"
                      viewBox="0 0 20 20">

                        <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd" />

                      </svg> :

                    <svg
                      className="w-4 h-4 text-subtle"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">

                        <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7" />

                      </svg>
                    }
                  </div>
                </div>
                {!formState.invoiceFrequency && !isReadOnly &&
                <p className="mt-1 text-xs text-error">
                    Please select an invoice frequency
                  </p>
                }
              </div>

              {/* Payment Terms */}
              <div>
                <label
                  htmlFor="paymentTerms"
                  className="block text-sm font-medium text-primary mb-2">

                  <div className="flex items-center space-x-2">
                    <svg
                      className="w-4 h-4 text-muted"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">

                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />

                    </svg>
                    <span>Payment Terms</span>
                    <span className="text-error">*</span>
                  </div>
                </label>
                <div className="relative">
                  <Select
                    id="paymentTerms"
                    value={formState.paymentTerms ||""}
                    onChange={(e) =>
                    formState.handlePaymentTermsChange(
                      e.target.value ? Number(e.target.value) : null
                    )
                    }
                    className={`w-full p-3 pr-10 border rounded-lg text-sm transition-all duration-200 appearance-none bg-surface-card ${
                    formState.paymentTerms ?"border-primary bg-primary-light/20" :"border-strong"} ${

                    !formState.paymentTerms && !isReadOnly ?"border-error" :""} focus:ring-2 focus:ring-primary focus:border-primary`
                    }
                    disabled={isReadOnly || !isStep2Complete}>

                    <option value="" disabled>
                      Choose payment terms...
                    </option>
                    {PAYMENT_TERMS_OPTIONS.map((option) =>
                    <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    )}
                  </Select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    {formState.paymentTerms ?
                    <svg
                      className="w-4 h-4 text-primary-color"
                      fill="currentColor"
                      viewBox="0 0 20 20">

                        <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd" />

                      </svg> :

                    <svg
                      className="w-4 h-4 text-subtle"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">

                        <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7" />

                      </svg>
                    }
                  </div>
                </div>
                {!formState.paymentTerms && !isReadOnly &&
                <p className="mt-1 text-xs text-error">
                    Please select payment terms
                  </p>
                }
              </div>
            </div>
          </div>
        </div>

        {/* Deal Link Modal */}
        {draftUuid &&
        <DealLinkModal
          isOpen={showDealLinkModal}
          onClose={handleDealLinkModalClose}
          estimateId={draftUuid}
          estimateType="internal" />

        }

        {/* Linked Deal Modal */}
        {draftUuid && linkedDeal &&
        <LinkedDealModal
          isOpen={showLinkedDealModal}
          onClose={handleLinkedDealModalClose}
          estimateId={draftUuid}
          estimateType="internal" />

        }

        {/* Deal Selection Modal for new estimates */}
        <DealSelectionModal
          isOpen={showDealSelectionModal}
          onClose={() => setShowDealSelectionModal(false)}
          onSelectDeal={handleDealSelection}
          estimateClientName={formState.clientSearchTerm}
          estimateProjectName={formState.projectName} />

      </div>
    </div>);

};

export default EstimateConfigurationForm;