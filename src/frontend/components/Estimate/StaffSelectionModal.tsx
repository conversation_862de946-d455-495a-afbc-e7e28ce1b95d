import React, { useState, useEffect, FormEvent } from "react";
import { User, getUsers } from "../../api/harvest";
import { convertHourlyToDailyRate, formatCurrency } from "./utils";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface StaffSelectionModalProps {
  isOpen: boolean;
  onSelect: (selectedUser: {
    harvestUserId: number;
    firstName: string;
    lastName: string;
    projectRole: string;
    level: string;
    onbordTargetRateDaily: number;
    onbordCostRateDaily: number;
    rateProposedDaily: number;
    isPlaceholder?: boolean; // Flag to identify placeholder staff
    avatarUrl?: string; // Profile photo URL from Harvest
  }) => void;
  onClose: () => void;
  existingUserIds?: number[]; // Optional: To disable selection of already added users
  users?: User[] | null;
  isLoading?: boolean;
  error?: string | null;
}

const StaffSelectionModal: React.FC<StaffSelectionModalProps> = ({
  isOpen,
  onSelect,
  onClose,
  existingUserIds = [],
  users: initialUsers = null,
  isLoading: initialIsLoading = false,
  error: initialError = null
}) => {
  // State for user list
  const [users, setUsers] = useState<User[]>(initialUsers || []);
  const [filteredUsers, setFilteredUsers] = useState<User[]>(
    initialUsers || []
  );
  const [isLoading, setIsLoading] = useState(initialIsLoading);
  const [error, setError] = useState<string | null>(initialError);
  const [searchTerm, setSearchTerm] = useState("");

  // State for placeholder form
  const [showPlaceholderForm, setShowPlaceholderForm] = useState(false);
  const [placeholderName, setPlaceholderName] = useState("");
  const [placeholderRole, setPlaceholderRole] = useState("");
  const [placeholderLevel, setPlaceholderLevel] = useState("");
  const [placeholderTargetRate, setPlaceholderTargetRate] = useState<number>(0);
  const [placeholderCostRate, setPlaceholderCostRate] = useState<number>(0);
  const [placeholderFormError, setPlaceholderFormError] = useState<
    string | null>(
    null);

  // Fetch users when the modal opens
  useEffect(() => {
    if (isOpen && !initialUsers) {
      const fetchUsers = async () => {
        setIsLoading(true);
        setError(null);
        try {
          const fetchedUsers = await getUsers({ is_active: true });
          setUsers(fetchedUsers);
          setFilteredUsers(fetchedUsers);
        } catch (err: any) {
          console.error("Error fetching users:", err);
          setError(err.message ||"Failed to fetch team members");
        } finally {
          setIsLoading(false);
        }
      };

      fetchUsers();
    } else if (initialUsers) {
      setUsers(initialUsers);
      setFilteredUsers(initialUsers);
    }
  }, [isOpen, initialUsers]);

  // Filter users when search term changes
  useEffect(() => {
    if (users.length > 0) {
      const filtered = users.filter((user) => {
        const fullName = `${user.first_name ||""} ${
        user.last_name ||""}`.
        toLowerCase();
        return (
          fullName.includes(searchTerm.toLowerCase()) ||
          user.email &&
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.roles &&
          user.roles.some((role) =>
          role.toLowerCase().includes(searchTerm.toLowerCase())
          ));

      });
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  if (!isOpen) {
    return null;
  }

  const handleUserSelect = (user: User) => {
    // Ensure firstName and lastName are present
    const firstName = user.first_name ||"Unknown";
    const lastName = user.last_name ||"";

    // Determine the primary role or use first available role
    const primaryRole =
    user.roles && user.roles.length > 0 ? user.roles[0] :"Consultant";

    // Safely convert hourly rates to daily with fallbacks
    const defaultHourlyRate = user.default_hourly_rate || 0;
    const costRate = user.cost_rate || 0;

    const targetRateDaily = convertHourlyToDailyRate(defaultHourlyRate);
    const costRateDaily = convertHourlyToDailyRate(costRate);

    // Log selection to verify data
    console.log("Adding team member:", {
      name: `${firstName} ${lastName}`,
      role: primaryRole,
      targetRate: targetRateDaily,
      costRate: costRateDaily
    });

    onSelect({
      harvestUserId: user.id,
      firstName: firstName,
      lastName: lastName,
      projectRole: primaryRole,
      level: primaryRole, // Default to using role as level
      onbordTargetRateDaily: targetRateDaily,
      onbordCostRateDaily: costRateDaily,
      rateProposedDaily: targetRateDaily, // Default proposed rate to target rate
      avatarUrl: user.avatar_url // Pass through the avatar URL from Harvest
    });

    // Keep the modal open to allow multiple selections
  };

  // Handle placeholder staff form submission
  const handlePlaceholderSubmit = (e: FormEvent) => {
    e.preventDefault();
    setPlaceholderFormError(null);

    // Validate form fields
    if (!placeholderName.trim()) {
      setPlaceholderFormError("Name is required");
      return;
    }

    if (!placeholderRole.trim()) {
      setPlaceholderFormError("Role is required");
      return;
    }

    if (placeholderTargetRate <= 0) {
      setPlaceholderFormError("Target rate must be greater than 0");
      return;
    }

    if (placeholderCostRate < 0) {
      setPlaceholderFormError("Cost rate cannot be negative");
      return;
    }

    // Generate a negative ID for the placeholder to avoid conflicts with real Harvest IDs
    // Use current timestamp as part of the ID to ensure uniqueness
    const placeholderId = -1 * Math.floor(Date.now() / 1000);

    // Submit the placeholder staff
    onSelect({
      harvestUserId: placeholderId,
      firstName: placeholderName,
      lastName:"", // Leave last name empty for placeholders
      projectRole: placeholderRole,
      level: placeholderLevel || placeholderRole, // Use role as level if level not provided
      onbordTargetRateDaily: placeholderTargetRate,
      onbordCostRateDaily: placeholderCostRate,
      rateProposedDaily: placeholderTargetRate, // Default proposed rate to target rate
      isPlaceholder: true
    });

    // Reset form fields
    setPlaceholderName("");
    setPlaceholderRole("");
    setPlaceholderLevel("");
    setPlaceholderTargetRate(0);
    setPlaceholderCostRate(0);

    // Optionally close the form after submission
    setShowPlaceholderForm(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-surface-card rounded-lg shadow-xl max-w-3xl w-full max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-default flex justify-between items-center">
          <h2 className="text-xl font-semibold text-primary">
            Select Team Member
          </h2>
          <Button variant="ghost"
          onClick={onClose}

          aria-label="Close">

            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12" />

            </svg>
          </Button>
        </div>

        {/* Search bar */}
        <div className="p-4 border-b border-default">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-subtle"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">

                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />

              </svg>
            </div>
            <Input
              type="text"
              placeholder="Search by name, email, or role..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full p-2 border border-strong rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" />

          </div>
        </div>

        {/* Placeholder Staff Form */}
        {showPlaceholderForm &&
        <div className="flex-1 p-4 bg-accent-light/10">
            <form onSubmit={handlePlaceholderSubmit}>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-base font-medium text-primary">
                  Add Placeholder Staff Member
                </h3>
                <Button variant="ghost"
              type="button"
              onClick={() => setShowPlaceholderForm(false)}>


                  <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">

                    <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M10 19l-7-7m0 0l7-7m-7 7h18" />

                  </svg>
                  Back to staff list
                </Button>
              </div>
              {placeholderFormError &&
            <div className="mb-3 p-2 bg-error-light/20 border border-error rounded text-sm text-error">
                  {placeholderFormError}
                </div>
            }

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Name <span className="text-error">*</span>
                  </label>
                  <Input
                  type="text"
                  value={placeholderName}
                  onChange={(e) => setPlaceholderName(e.target.value)}
                  placeholder="e.g., Senior Developer"
                  className="w-full p-2 border border-strong rounded focus:ring-2 focus:ring-primary focus:border-primary"
                  required />

                </div>

                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Role <span className="text-error">*</span>
                  </label>
                  <Input
                  type="text"
                  value={placeholderRole}
                  onChange={(e) => setPlaceholderRole(e.target.value)}
                  placeholder="e.g., Developer"
                  className="w-full p-2 border border-strong rounded focus:ring-2 focus:ring-primary focus:border-primary"
                  required />

                </div>

                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Level
                  </label>
                  <Input
                  type="text"
                  value={placeholderLevel}
                  onChange={(e) => setPlaceholderLevel(e.target.value)}
                  placeholder="e.g., Senior (optional)"
                  className="w-full p-2 border border-strong rounded focus:ring-2 focus:ring-primary focus:border-primary" />

                </div>

                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Target Rate (Daily) <span className="text-error">*</span>
                  </label>
                  <Input
                  type="number"
                  value={placeholderTargetRate ||""}
                  onChange={(e) =>
                  setPlaceholderTargetRate(Number(e.target.value))
                  }
                  placeholder="1500"
                  min="0"
                  step="10"
                  className="w-full p-2 border border-strong rounded focus:ring-2 focus:ring-primary focus:border-primary"
                  required />

                </div>

                <div>
                  <label className="block text-sm font-medium text-primary mb-1">
                    Cost Rate (Daily) <span className="text-error">*</span>
                  </label>
                  <Input
                  type="number"
                  value={placeholderCostRate ||""}
                  onChange={(e) =>
                  setPlaceholderCostRate(Number(e.target.value))
                  }
                  placeholder="800"
                  min="0"
                  step="10"
                  className="w-full p-2 border border-strong rounded focus:ring-2 focus:ring-primary focus:border-primary"
                  required />

                </div>
              </div>

              <div className="flex justify-end">
                <Button variant="secondary"
              type="button"
              onClick={() => setShowPlaceholderForm(false)}>


                  Cancel
                </Button>
                <Button variant="primary"
              type="submit">


                  Add Placeholder Staff
                </Button>
              </div>
            </form>
          </div>
        }

        {/* User list */}
        <div
          className={`flex-1 overflow-y-auto p-4 ${
          showPlaceholderForm ?"hidden" :"block"}`
          }>

          {isLoading ?
          <div className="flex justify-center items-center h-40">
              <svg
              className="animate-spin h-8 w-8 text-primary"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24">

                <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4">
              </circle>
                <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
              </svg>
            </div> :
          error ?
          <div className="bg-error-light/20 rounded-md p-4 text-center">
              <p className="text-error">{error}</p>
              <Button variant="ghost"
            onClick={() => window.location.reload()}>


                Retry
              </Button>
            </div> :
          filteredUsers.length === 0 ?
          <div className="text-center py-8">
              <svg
              className="mx-auto h-12 w-12 text-subtle"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor">

                <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />

              </svg>
              <p className="mt-2 text-muted">
                {searchTerm ?"No team members match your search" :"No team members available"}
              </p>
            </div> :

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {/* Placeholder Staff Box */}
              <div
              className="border border-dashed border-accent rounded-md p-3 hover:border-accent dark:hover:border-accent hover:bg-accent-light dark:hover:bg-accent-dark/10 cursor-pointer"
              onClick={() => setShowPlaceholderForm(true)}>

                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-accent">
                      Add Placeholder Staff
                    </h3>
                    <p className="text-xs text-muted mt-1">
                      Create a staff entry with your own values
                    </p>
                  </div>
                  <svg
                  className="w-6 h-6 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">

                    <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />

                  </svg>
                </div>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <div className="bg-accent-light/20 rounded p-1.5">
                    <span className="block text-xs text-accent mb-0.5">
                      Custom Rates
                    </span>
                    <span className="font-medium text-sm text-accent-dark">
                      Set your own values
                    </span>
                  </div>
                  <div className="bg-accent-light/20 rounded p-1.5">
                    <span className="block text-xs text-accent mb-0.5">
                      No Harvest ID
                    </span>
                    <span className="font-medium text-sm text-accent-dark">
                      Unnamed staff
                    </span>
                  </div>
                </div>
              </div>

              {/* Real User Boxes */}
              {filteredUsers.map((user) => {
              const isAlreadyAdded = existingUserIds.includes(user.id);
              const targetRateDaily = convertHourlyToDailyRate(
                user.default_hourly_rate || 0
              );
              const costRateDaily = convertHourlyToDailyRate(
                user.cost_rate || 0
              );

              return (
                <div
                  key={user.id}
                  className={`border rounded-md p-3 ${
                  isAlreadyAdded ?"border-strong bg-surface-page" :"border-default hover:border-primary dark:hover:border-primary-light"}`
                  }>

                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-3">
                        {/* Avatar */}
                        {user.avatar_url ?
                      <img
                        className="h-10 w-10 rounded-full flex-shrink-0"
                        src={user.avatar_url}
                        alt={`${user.first_name} ${user.last_name}`} /> :


                      <div className="h-10 w-10 rounded-full bg-surface-alt flex items-center justify-center text-sm font-medium text-secondary flex-shrink-0">
                            {(user.first_name?.[0] ||"") + (
                        user.last_name?.[0] ||"")}
                          </div>
                      }
                        <div>
                          <h3 className="font-medium text-primary">
                            {user.first_name ||"Unknown"}{""}
                            {user.last_name ||""}
                          </h3>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {user.roles && user.roles.length > 0 ?
                          user.roles.map((role, idx) =>
                          <span
                            key={idx}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-light text-primary/30">

                                  {role}
                                </span>
                          ) :

                          <span className="text-xs text-muted">
                                Consultant
                              </span>
                          }
                          </div>
                        </div>
                      </div>
                      <Button variant="secondary"
                    onClick={() => handleUserSelect(user)}
                    disabled={isAlreadyAdded}
                    className={`px-3 py-1 rounded text-sm ${
                    isAlreadyAdded ?"bg-surface-page text-secondary cursor-not-allowed" :""}`
                    }>

                        {isAlreadyAdded ?"Added" :"Add"}
                      </Button>
                    </div>
                    <div className="mt-2 grid grid-cols-2 gap-2">
                      <div className="bg-surface-page rounded p-1.5">
                        <span className="block text-xs text-muted mb-0.5">
                          Target Rate
                        </span>
                        <span className="font-medium text-sm text-primary">
                          {formatCurrency(targetRateDaily)}/day
                        </span>
                      </div>
                      <div className="bg-surface-page rounded p-1.5">
                        <span className="block text-xs text-muted mb-0.5">
                          Cost Rate
                        </span>
                        <span className="font-medium text-sm text-primary">
                          {formatCurrency(costRateDaily)}/day
                        </span>
                      </div>
                    </div>
                  </div>);

            })}
            </div>
          }
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-default flex justify-end">
          <Button variant="secondary"
          onClick={onClose}>


            Done
          </Button>
        </div>
      </div>
    </div>);

};

export default StaffSelectionModal;