import React, { useState, useCallback, useRef, useEffect } from "react";
import { formatCurrency } from "./utils";
import { AllocationWithTotals } from "../../hooks/useEstimateStaffManagement";
import { WeekInfo } from "../../types/estimate-types";
import { useMutation, useQueryClient } from "react-query";
import { fetchFromApi } from "../../api/utils";
import { Button } from "@/frontend/components/ui/Button";
import { Input } from "@/frontend/components/ui/Input";

interface TimeAllocationGridEnhancedProps {
  /**
   * Staff allocations with calculated totals
   */
  allocationsWithTotals: AllocationWithTotals[];

  /**
   * Week information for columns
   */
  weeks: WeekInfo[];

  /**
   * Handler for changing allocation values
   */
  onAllocationChange: (internalId: string, weekIdentifier: string, days: number) => void;

  /**
   * Whether the grid is in read-only mode
   */
  isReadOnly?: boolean;

  /**
   * Estimate ID for API calls
   */
  estimateId: string;

  /**
   * Display unit (hours or days)
   */
  displayUnit?: 'hours' | 'days';

  /**
   * Handler for display unit change
   */
  onDisplayUnitChange?: (unit: 'hours' | 'days') => void;

  /**
   * Whether to automatically enter edit mode when navigating to a cell
   */
  autoEditOnFocus?: boolean;

  /**
   * Handler for reordering allocations
   */
  onReorderAllocations?: (orderedAllocationIds: string[]) => void;
}

interface CellPosition {
  row: number;
  col: number;
}

interface PendingUpdate {
  allocationId: string;
  weekId: string;
  days: number;
}

/**
 * Enhanced Time Allocation Grid with spreadsheet-like features
 */
const TimeAllocationGridEnhanced: React.FC<TimeAllocationGridEnhancedProps> = ({
  allocationsWithTotals,
  weeks,
  onAllocationChange,
  isReadOnly = false,
  estimateId,
  displayUnit = 'days',
  onDisplayUnitChange,
  autoEditOnFocus = true,
  onReorderAllocations
}) => {
  // State for fullscreen mode
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const timeAllocationRef = useRef<HTMLDivElement>(null);
  const tableRef = useRef<HTMLTableElement>(null);

  // State for cell navigation and editing
  const [focusedCell, setFocusedCell] = useState<CellPosition | null>(null);
  const [editingCell, setEditingCell] = useState<CellPosition | null>(null);
  const [originalValue, setOriginalValue] = useState<string>('');

  // State for drag and drop
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTarget, setDropTarget] = useState<number | null>(null);
  const [dropPosition, setDropPosition] = useState<'before' | 'after' | null>(null);

  // State for precision hint
  const [showPrecisionHint, setShowPrecisionHint] = useState<boolean>(false);
  const precisionHintTimeout = useRef<NodeJS.Timeout>();

  // Pending updates for batch saving
  const pendingUpdates = useRef<Map<string, number>>(new Map());
  const saveTimeout = useRef<NodeJS.Timeout>();

  // Query client for cache invalidation
  const queryClient = useQueryClient();

  // Mutation for updating allocation order
  const updateOrderMutation = useMutation({
    mutationFn: async (orderedIds: string[]) => {
      return await fetchFromApi(`/api/estimates/drafts/${estimateId}/allocation-order`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderedAllocationIds: orderedIds })
      });
    },
    onSuccess: (data, orderedIds) => {
      // Notify parent component about the reorder
      if (onReorderAllocations) {
        onReorderAllocations(orderedIds);
      }
      // Also invalidate queries in case they're being used
      queryClient.invalidateQueries({ queryKey: ['estimate', estimateId] });
    }
  });

  // Handle escape key to exit fullscreen
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Lock body scroll when in fullscreen
    if (isFullscreen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isFullscreen]);

  // Cleanup precision hint timeout
  useEffect(() => {
    return () => {
      if (precisionHintTimeout.current) {
        clearTimeout(precisionHintTimeout.current);
      }
    };
  }, []);

  // Calculate project totals
  const totalDays = allocationsWithTotals.reduce((sum, staff) => sum + staff.totalDays, 0);

  // Calculate weekly totals
  const weeklyTotals = weeks.map((week) => {
    const totalTenths = allocationsWithTotals.reduce(
      (sum, staff) => {
        const days = staff.weeklyAllocation[week.identifier] || 0;
        return sum + Math.round(days * 10);
      },
      0
    );
    return Math.round(totalTenths) / 10;
  });

  const formatDisplayValue = (days: number): string => {
    // Always display in days since we removed the hours/days toggle
    if (days === 0) return '';

    // For display, show up to 3 decimal places but remove trailing zeros
    const formatted = days.toFixed(3);
    // Remove trailing zeros after decimal point
    return formatted.replace(/\.?0+$/, '');
  };

  // Navigate to a cell
  const navigateToCell = (row: number, col: number) => {
    const maxRow = allocationsWithTotals.length - 1;
    const maxCol = weeks.length - 1;

    // Clamp to valid range
    const newRow = Math.max(0, Math.min(maxRow, row));
    const newCol = Math.max(0, Math.min(maxCol, col));

    setFocusedCell({ row: newRow, col: newCol });

    // Auto-enter edit mode when navigating (spreadsheet-like behavior)
    if (!isReadOnly && autoEditOnFocus) {
      setEditingCell({ row: newRow, col: newCol });
      const allocation = allocationsWithTotals[newRow];
      const week = weeks[newCol];
      const currentDays = allocation.weeklyAllocation[week.identifier] || 0;
      setOriginalValue(formatDisplayValue(currentDays));

      // Focus the input element after it renders
      setTimeout(() => {
        const inputElement = tableRef.current?.querySelector(
          `[data-row="${newRow}"][data-col="${newCol}"] input`
        ) as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
          inputElement.select();
        }
      }, 10);
    } else {
      // If not auto-editing, just focus the cell
      setTimeout(() => {
        const cellElement = tableRef.current?.querySelector(
          `[data-row="${newRow}"][data-col="${newCol}"]`
        ) as HTMLElement;
        cellElement?.focus();
      }, 0);
    }
  };

  // Handle keyboard navigation
  const handleGridKeyDown = (e: React.KeyboardEvent) => {
    if (!focusedCell || isReadOnly) return;

    const { row, col } = focusedCell;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        navigateToCell(row - 1, col);
        break;

      case 'ArrowDown':
        e.preventDefault();
        navigateToCell(row + 1, col);
        break;

      case 'ArrowLeft':
        e.preventDefault();
        navigateToCell(row, col - 1);
        break;

      case 'ArrowRight':
        e.preventDefault();
        navigateToCell(row, col + 1);
        break;

      case 'Tab':
        e.preventDefault();
        if (e.shiftKey) {
          // Move to previous cell
          if (col > 0) {
            navigateToCell(row, col - 1);
          } else if (row > 0) {
            navigateToCell(row - 1, weeks.length - 1);
          }
        } else {
          // Move to next cell
          if (col < weeks.length - 1) {
            navigateToCell(row, col + 1);
          } else if (row < allocationsWithTotals.length - 1) {
            navigateToCell(row + 1, 0);
          }
        }
        break;

      case 'Enter':
        // Since we're auto-editing, Enter should move down
        e.preventDefault();
        navigateToCell(row + 1, col);
        break;

      case 'F2':
        // F2 can still be used to re-enter edit mode if needed
        e.preventDefault();
        if (!editingCell || editingCell.row !== row || editingCell.col !== col) {
          setEditingCell({ row, col });
          const allocation = allocationsWithTotals[row];
          const week = weeks[col];
          const currentDays = allocation.weeklyAllocation[week.identifier] || 0;
          setOriginalValue(formatDisplayValue(currentDays));
        }
        break;

      case 'Escape':
        if (editingCell && editingCell.row === row && editingCell.col === col) {
          e.preventDefault();
          setEditingCell(null);
          // Keep focus on the same cell
          setFocusedCell({ row, col });
        }
        break;
    }
  };

  // Handle cell click
  const handleCellClick = (row: number, col: number) => {
    setFocusedCell({ row, col });
    if (!isReadOnly && !editingCell) {
      setEditingCell({ row, col });
      const allocation = allocationsWithTotals[row];
      const week = weeks[col];
      const currentDays = allocation.weeklyAllocation[week.identifier] || 0;
      setOriginalValue(formatDisplayValue(currentDays));
    }
  };

  // Handle cell value change
  const handleCellChange = (allocationId: string, weekId: string, value: string) => {
    // Validate and parse the input (always in days now)
    const numValue = parseFloat(value) || 0;
    const days = numValue;

    // Clamp to valid range (0-5 days)
    const clampedDays = Math.max(0, Math.min(5, days));

    // Update local state immediately
    onAllocationChange(allocationId, weekId, clampedDays);

    // Batch for saving
    const key = `${allocationId}:${weekId}`;
    pendingUpdates.current.set(key, clampedDays);

    // Debounce save
    clearTimeout(saveTimeout.current);
    saveTimeout.current = setTimeout(() => {
      // In a real implementation, this would call an API
      // For now, the updates are handled by the parent component
      pendingUpdates.current.clear();
    }, 750);
  };

  // Handle cell blur
  const handleCellBlur = (row: number, col: number) => {
    if (editingCell?.row === row && editingCell?.col === col) {
      setEditingCell(null);
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.effectAllowed = 'move';
    setDraggedIndex(index);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    const rect = e.currentTarget.getBoundingClientRect();
    const midpoint = rect.top + rect.height / 2;

    setDropTarget(index);
    setDropPosition(e.clientY < midpoint ? 'before' : 'after');
  };

  // Handle drag leave
  const handleDragLeave = () => {
    setDropTarget(null);
    setDropPosition(null);
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      setDropTarget(null);
      setDropPosition(null);
      return;
    }

    // Calculate new order
    const newAllocations = [...allocationsWithTotals];
    const [draggedItem] = newAllocations.splice(draggedIndex, 1);

    // Adjust drop index based on position
    let insertIndex = dropIndex;
    if (dropPosition === 'after') {
      insertIndex = dropIndex + 1;
    }
    if (draggedIndex < dropIndex && dropPosition === 'before') {
      insertIndex = dropIndex - 1;
    }

    newAllocations.splice(insertIndex, 0, draggedItem);

    // Update the order via API
    const orderedIds = newAllocations.map((a) => a.internalId);
    updateOrderMutation.mutate(orderedIds);

    // Reset drag state
    setDraggedIndex(null);
    setDropTarget(null);
    setDropPosition(null);
  };

  return (
    <div
      ref={timeAllocationRef}
      className={`${
      isFullscreen ?
      'fixed inset-0 z-50 p-4 bg-surface-card overflow-auto' :
      'w-full bg-surface-card border border-default rounded-lg shadow-sm p-4'} transition-all duration-300 ease-in-out`
      }>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-4">
          <h3 className="font-semibold text-primary">Time Allocation</h3>
          

          {/* Fullscreen toggle button */}
          <Button variant="ghost"
          onClick={() => setIsFullscreen(!isFullscreen)}

          title={isFullscreen ? "Exit full screen" : "View in full screen"}>

            {isFullscreen ?
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg> :

            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
              </svg>
            }
          </Button>
        </div>
        <div className="flex items-center">
          {isFullscreen &&
          <span className="text-xs text-muted">
              Press ESC to exit fullscreen
            </span>
          }
        </div>
      </div>

      {/* Enhanced Time Allocation Table */}
      <div className="border border-subtle rounded-md">
        <div className="overflow-x-auto relative">
          <table
            ref={tableRef}
            className="w-full allocation-table table-fixed"
            role="grid"
            onKeyDown={handleGridKeyDown}
            tabIndex={0}>

            <colgroup>
              <col className="w-[30px]" /> {/* Drag handle */}
              <col className="w-[180px]" /> {/* Team member */}
              {weeks.map((week) =>
              <col key={week.identifier} className="w-[80px] min-w-[80px]" />
              )}
              <col className="w-[80px]" /> {/* Total days */}
              <col className="w-[100px]" /> {/* Total cost */}
              <col className="w-[100px]" /> {/* Total fees */}
            </colgroup>
            <thead>
              <tr className="bg-surface-page">
                {/* Drag handle header */}
                <th className="sticky left-0 z-40 bg-surface-page px-1 py-2 shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  <span className="sr-only">Reorder</span>
                </th>

                {/* Team member header */}
                <th className="sticky left-[30px] z-40 bg-surface-page px-3 py-2 text-left text-xs font-medium text-muted uppercase tracking-wider shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  <div className="whitespace-nowrap">Team</div>
                  <div className="text-[0.65rem] font-normal">Member</div>
                </th>

                {/* Week headers */}
                {weeks.map((week) => {
                  const parts = week.shortLabel.split(' ');
                  const weekNum = parts[0];
                  const dateStr = parts.length > 1 ? parts[1] : '';

                  return (
                    <th
                      key={week.identifier}
                      className={`px-2 py-2 text-center text-xs font-medium text-muted uppercase tracking-wider ${
                      week.hasAlternatingBackground ? 'bg-surface-alt/80' : ''}`
                      }>

                      <div className="whitespace-nowrap">{weekNum}</div>
                      <div className="text-[0.65rem] font-normal">{dateStr}</div>
                    </th>);

                })}

                {/* Total columns */}
                <th className="sticky right-[200px] z-30 bg-surface-page px-3 py-2 text-right text-xs font-medium text-muted uppercase tracking-wider shadow-[-2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  <div className="whitespace-nowrap">Total</div>
                  <div className="text-[0.65rem] font-normal">Days</div>
                </th>
                <th className="sticky right-[100px] z-30 bg-surface-page px-3 py-2 text-right text-xs font-medium text-muted uppercase tracking-wider">
                  <div className="whitespace-nowrap">Total</div>
                  <div className="text-[0.65rem] font-normal">Cost</div>
                </th>
                <th className="sticky right-0 z-30 bg-surface-page px-3 py-2 text-right text-xs font-medium text-muted uppercase tracking-wider shadow-[-2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  <div className="whitespace-nowrap">Total</div>
                  <div className="text-[0.65rem] font-normal">Fees</div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
              {allocationsWithTotals.map((staff, rowIndex) =>
              <tr
                key={staff.internalId}
                className={`
                    hover:bg-surface-page dark:hover:bg-surface-elevated/50 
                    ${staff.isPlaceholder ? 'bg-surface-page/50/20' : ''}
                    ${draggedIndex === rowIndex ? 'opacity-50' : ''}
                    ${dropTarget === rowIndex && dropPosition === 'before' ? 'border-t-2 border-success' : ''}
                    ${dropTarget === rowIndex && dropPosition === 'after' ? 'border-b-2 border-success' : ''}
                    transition-all duration-200
                  `}
                draggable={!isReadOnly}
                onDragStart={(e) => handleDragStart(e, rowIndex)}
                onDragOver={(e) => handleDragOver(e, rowIndex)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, rowIndex)}>

                  {/* Drag handle */}
                  <td className="sticky left-0 z-40 bg-surface-card px-1 py-2 shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                    {!isReadOnly &&
                  <div className="drag-handle cursor-move text-subtle hover:text-secondary">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8h16M4 16h16" />
                        </svg>
                      </div>
                  }
                  </td>

                  {/* Team member */}
                  <td className="sticky left-[30px] z-40 bg-surface-card px-3 py-2 text-sm text-primary whitespace-nowrap shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                    {staff.firstName || ""} {staff.lastName || ''}
                    {staff.isPlaceholder && <span className="text-xs text-muted italic ml-1">(placeholder)</span>}
                  </td>

                  {/* Week data cells */}
                  {weeks.map((week, colIndex) => {
                  const allocation = staff.weeklyAllocation[week.identifier] || 0;
                  const isEditing = editingCell?.row === rowIndex && editingCell?.col === colIndex;
                  const isFocused = focusedCell?.row === rowIndex && focusedCell?.col === colIndex;

                  return (
                    <td
                      key={`${staff.internalId}-${week.identifier}`}
                      className={`
                          px-2 py-2 text-center z-0 relative
                          ${week.hasAlternatingBackground ? 'bg-surface-page/40' : ''}
                          ${isFocused && !isEditing ? 'ring-2 ring-success ring-inset' : ''}
                          ${isReadOnly ? '' : 'cursor-text hover:bg-surface-alt dark:hover:bg-surface-elevated/60'}
                        `}
                      onClick={() => handleCellClick(rowIndex, colIndex)}
                      data-row={rowIndex}
                      data-col={colIndex}
                      tabIndex={-1}
                      role="gridcell">

                        {isReadOnly ?
                      <span className="inline-block text-sm text-primary text-center">
                            {formatDisplayValue(allocation) || '-'}
                          </span> :
                      isEditing ?
                      <Input
                        type="text"

                        defaultValue={formatDisplayValue(allocation)}
                        onChange={(e) => handleCellChange(staff.internalId, week.identifier, e.target.value)}
                        onBlur={() => handleCellBlur(rowIndex, colIndex)}
                        onFocus={(e) => e.target.select()}
                        onKeyDown={(e) => {
                          e.stopPropagation(); // Prevent the table's key handler from interfering

                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleCellBlur(rowIndex, colIndex);
                            navigateToCell(rowIndex + 1, colIndex);
                          } else if (e.key === 'Escape') {
                            e.preventDefault();
                            e.currentTarget.value = originalValue;
                            handleCellBlur(rowIndex, colIndex);
                          } else if (e.key === 'Tab') {
                            e.preventDefault();
                            handleCellBlur(rowIndex, colIndex);
                            if (e.shiftKey) {
                              navigateToCell(rowIndex, colIndex - 1);
                            } else {
                              navigateToCell(rowIndex, colIndex + 1);
                            }
                          } else if (e.key === 'ArrowUp' || e.key === 'ArrowDown' || e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                            // Also handle arrow keys in the input
                            e.preventDefault();
                            handleCellBlur(rowIndex, colIndex);

                            switch (e.key) {
                              case 'ArrowUp':
                                navigateToCell(rowIndex - 1, colIndex);
                                break;
                              case 'ArrowDown':
                                navigateToCell(rowIndex + 1, colIndex);
                                break;
                              case 'ArrowLeft':
                                navigateToCell(rowIndex, colIndex - 1);
                                break;
                              case 'ArrowRight':
                                navigateToCell(rowIndex, colIndex + 1);
                                break;
                            }
                          }
                        }}
                        className="w-full text-center py-0.5 text-sm text-primary bg-surface-card border border-success rounded px-1 focus:outline-none focus:ring-2 focus:ring-success" /> :


                      <span
                        className="inline-block text-sm text-primary text-center w-full"
                        contentEditable={false}>

                            {formatDisplayValue(allocation) || '-'}
                          </span>
                      }
                      </td>);

                })}

                  {/* Total columns */}
                  <td className="sticky right-[200px] z-30 bg-surface-card px-3 py-2 text-right text-sm text-primary font-medium whitespace-nowrap shadow-[-2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                    {formatDisplayValue(staff.totalDays)}
                  </td>
                  <td className="sticky right-[100px] z-30 bg-surface-card px-3 py-2 text-right text-sm text-error whitespace-nowrap">
                    {formatCurrency(staff.totalCost)}
                  </td>
                  <td className="sticky right-0 z-30 bg-surface-card px-3 py-2 text-right text-sm text-success whitespace-nowrap shadow-[-2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                    {formatCurrency(staff.totalFees)}
                  </td>
                </tr>
              )}
            </tbody>
            <tfoot className="bg-surface-page">
              <tr>
                {/* Empty drag handle column */}
                <td className="sticky left-0 z-40 bg-surface-page px-1 py-2 shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]"></td>

                {/* Weekly totals header */}
                <td className="sticky left-[30px] z-40 bg-surface-page px-3 py-2 text-sm text-primary font-semibold whitespace-nowrap shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  Weekly Totals
                </td>

                {/* Weekly totals */}
                {weeks.map((week, index) =>
                <td
                  key={`total-${week.identifier}`}
                  className={`px-2 py-2 text-center text-sm text-primary font-semibold z-0 ${
                  week.isWeekend ? 'bg-surface-alt/50' : ''}`
                  }>

                    {formatDisplayValue(weeklyTotals[index])}
                  </td>
                )}

                {/* Total columns */}
                <td className="sticky right-[200px] z-30 bg-surface-page px-3 py-2 text-right text-sm text-primary font-semibold whitespace-nowrap shadow-[-2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  {formatDisplayValue(totalDays)}
                </td>
                <td className="sticky right-[100px] z-30 bg-surface-page px-3 py-2 text-right text-sm text-primary font-semibold whitespace-nowrap">
                  {formatCurrency(allocationsWithTotals.reduce((sum, staff) => sum + staff.totalCost, 0))}
                </td>
                <td className="sticky right-0 z-30 bg-surface-page px-3 py-2 text-right text-sm text-primary font-semibold whitespace-nowrap shadow-[-2px_0_4px_-1px_rgba(0,0,0,0.1)]">
                  {formatCurrency(allocationsWithTotals.reduce((sum, staff) => sum + staff.totalFees, 0))}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>);

};

export default TimeAllocationGridEnhanced;