import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getEstimates } from "../../api/harvest";
import {
  getDraftEstimates,
  getDraftEstimate,
  deleteDraftEstimate } from
"../../api/estimates";
import { format, parseISO } from "date-fns";
import { formatCurrency } from "./utils";
import { DraftEstimateSummary } from "../../../types/api";
import EstimateInfoAccordion from "./EstimateInfoAccordion";
import { getLinkedDealsForEstimate } from "../../api/crm";
import DealLinkModal from "../CRM/DealEdit/DealLinkModal";
import LinkedDealModal from "../CRM/DealEdit/LinkedDealModal";
import { useIsDesktop } from "../../hooks/useMediaQuery";
import { DraftEstimateCard } from "./DraftEstimateCard";
import { HarvestEstimateCard } from "./HarvestEstimateCard";
import EstimateStatusBadge from "./EstimateStatusBadge";
import { Button } from "../ui";

// Type for Harvest estimate data
import { Input } from "@/frontend/components/ui/Input";interface HarvestEstimate {
  id: number;
  number: string;
  client: {
    id: number;
    name: string;
  };
  subject?: string;
  state: "draft" | "sent" | "accepted" | "declined";
  amount: number;
  issue_date: string;
}

// Enhanced draft estimate with calculated totals
interface EnhancedDraftEstimate extends DraftEstimateSummary {
  totalFees?: number;
  grossMarginAmount?: number;
  grossMarginPercentage?: number;
  staffInitials?: string[];
  isLoading?: boolean;
}

// Import the EstimateStatusBadge component instead of defining a local one

// Staff initials circles component
const StaffInitialsCircles: React.FC<{initials: string[];}> = ({
  initials
}) => {
  // Define colors for the circles (using Tailwind colors)
  const colors = [
  "bg-primary-light0 text-primary",
  "bg-accent-light0 text-primary",
  "bg-success-light0 text-primary",
  "bg-warning-light0 text-primary",
  "bg-magenta-500 text-primary",
  "bg-primary-light0 text-primary",
  "bg-error-light0 text-primary",
  "bg-info-light0 text-primary"];


  return (
    <div className="flex -space-x-2 overflow-hidden">
      {initials.map((initial, index) =>
      <div
        key={index}
        className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-medium ${
        colors[index % colors.length]} ring-2 ring-white dark:ring-gray-800`
        }
        title={`Staff member: ${initial}`}>

          {initial}
        </div>
      )}
    </div>);

};

// Props interface
interface EstimatesListProps {}

// Main EstimatesList component
const EstimatesList: React.FC<EstimatesListProps> = () => {
  const navigate = useNavigate();
  const isDesktop = useIsDesktop();
  // Harvest estimates state
  const [harvestEstimates, setHarvestEstimates] = useState([]);
  const [filteredHarvestEstimates, setFilteredHarvestEstimates] = useState([]);

  // Draft estimates state
  const [draftEstimates, setDraftEstimates] = useState([]);
  const [filteredDraftEstimates, setFilteredDraftEstimates] = useState([]);

  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInformation, setShowInformation] = useState(true);
  const [linkToDealModalOpen, setLinkToDealModalOpen] = useState<{
    isOpen: boolean;
    estimateId: string;
    estimateType: "internal" | "harvest";
  }>({ isOpen: false, estimateId: "", estimateType: "internal" });

  const [linkedDealModalOpen, setLinkedDealModalOpen] = useState<{
    isOpen: boolean;
    estimateId: string;
    estimateType: "internal" | "harvest";
  }>({ isOpen: false, estimateId: "", estimateType: "internal" });

  // Store linked deals information for each estimate
  const [linkedDealsMap, setLinkedDealsMap] = useState<Record<string, any>>({});

  // Function to load full draft data and calculate totals
  const loadFullDraftData = async (
  draftSummary: DraftEstimateSummary)
  : Promise<EnhancedDraftEstimate> => {
    try {
      // Fetch full draft data
      const fullDraft = await getDraftEstimate(draftSummary.uuid);

      // Calculate totals
      let totalFees = 0;
      let totalCost = 0;
      const staffInitials: string[] = [];

      // Process allocations
      if (fullDraft.allocations && fullDraft.allocations.length > 0) {
        fullDraft.allocations.forEach((allocation) => {
          // Calculate days for this allocation
          const totalDays = allocation.timeAllocations.reduce(
            (sum, ta) => sum + ta.days,
            0
          );

          // Calculate fees and cost
          const fees = allocation.rateProposedDaily * totalDays;
          const cost = allocation.onbordCostRateDaily * totalDays;

          totalFees += fees;
          totalCost += cost;

          // Get initials
          const firstInitial =
          allocation.firstName && allocation.firstName.length > 0 ?
          allocation.firstName.charAt(0) :
          "";
          const lastInitial =
          allocation.lastName && allocation.lastName.length > 0 ?
          allocation.lastName.charAt(0) :
          "";
          const initials = (firstInitial + lastInitial).toUpperCase();

          if (initials && !staffInitials.includes(initials)) {
            staffInitials.push(initials);
          }
        });
      }

      // Calculate gross margin
      const grossMarginAmount = totalFees - totalCost;
      const grossMarginPercentage =
      totalFees > 0 ? grossMarginAmount / totalFees * 100 : 0;

      // Return enhanced draft
      return {
        ...draftSummary,
        totalFees,
        grossMarginAmount,
        grossMarginPercentage,
        staffInitials,
        isLoading: false
      };
    } catch (error) {
      console.error(
        `Error loading full data for draft ${draftSummary.uuid}:`,
        error
      );
      // Return original with error flag
      return {
        ...draftSummary,
        isLoading: false
      };
    }
  };

  // Function to fetch linked deals for an estimate
  const fetchLinkedDealsForEstimate = async (
  estimateId: string,
  estimateType: "internal" | "harvest") =>
  {
    try {
      const linkedDeals = await getLinkedDealsForEstimate(
        estimateId,
        estimateType
      );
      return linkedDeals;
    } catch (err) {
      console.error(
        `Failed to fetch linked deals for estimate ${estimateId}:`,
        err
      );
      return [];
    }
  };

  // Fetch both Harvest and draft estimates from the API
  useEffect(() => {
    console.log("EstimatesList component mounted, fetching estimates...");

    const fetchAllEstimates = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch both types of estimates in parallel
        const [harvestData, draftData] = await Promise.all([
        getEstimates(),
        getDraftEstimates()]
        );

        console.log("Harvest estimates data received:", harvestData);
        console.log("Draft estimates data received:", draftData);

        setHarvestEstimates(harvestData);
        setFilteredHarvestEstimates(harvestData);

        // Initialize enhanced drafts with loading state
        const enhancedDrafts: EnhancedDraftEstimate[] = draftData.map(
          (draft) => ({
            ...draft,
            isLoading: true
          })
        );

        setDraftEstimates(enhancedDrafts);
        setFilteredDraftEstimates(enhancedDrafts);

        // Load full data for each draft
        const fullDraftsPromises = draftData.map((draft) =>
        loadFullDraftData(draft)
        );
        const fullDrafts = await Promise.all(fullDraftsPromises);

        setDraftEstimates(fullDrafts);
        setFilteredDraftEstimates(fullDrafts);

        // Fetch linked deals for each draft estimate
        const linkedDealsMap: Record<string, any> = {};
        for (const draft of draftData) {
          const linkedDeals = await fetchLinkedDealsForEstimate(
            draft.uuid,
            "internal"
          );
          if (linkedDeals && linkedDeals.length > 0) {
            linkedDealsMap[draft.uuid] = linkedDeals[0]; // Store the first linked deal
          }
        }
        setLinkedDealsMap(linkedDealsMap);
      } catch (err) {
        console.error("Failed to fetch estimates:", err);
        setError("Could not load estimates. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchAllEstimates();
  }, []);

  // Filter estimates based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredHarvestEstimates(harvestEstimates);
      setFilteredDraftEstimates(draftEstimates);
      return;
    }

    const lowerCaseSearchTerm = searchTerm.toLowerCase();

    // Filter Harvest estimates
    const filteredHarvest = harvestEstimates.filter((estimate) => {
      return (
        estimate.number?.toLowerCase().includes(lowerCaseSearchTerm) ||
        estimate.client?.name?.toLowerCase().includes(lowerCaseSearchTerm) ||
        estimate.subject?.toLowerCase().includes(lowerCaseSearchTerm) ||
        estimate.state?.toLowerCase().includes(lowerCaseSearchTerm) ||
        estimate.amount?.toString().includes(lowerCaseSearchTerm) ||
        estimate.issue_date &&
        format(parseISO(estimate.issue_date), "MMM d, yyyy").
        toLowerCase().
        includes(lowerCaseSearchTerm));

    });

    // Filter draft estimates
    const filteredDrafts = draftEstimates.filter((draft) => {
      return (
        draft.uuid?.toLowerCase().includes(lowerCaseSearchTerm) ||
        draft.clientName?.toLowerCase().includes(lowerCaseSearchTerm) ||
        draft.projectName?.toLowerCase().includes(lowerCaseSearchTerm) ||
        draft.status?.toLowerCase().includes(lowerCaseSearchTerm) ||
        draft.startDate &&
        format(new Date(draft.startDate), "MMM d, yyyy").
        toLowerCase().
        includes(lowerCaseSearchTerm) ||
        draft.endDate &&
        format(new Date(draft.endDate), "MMM d, yyyy").
        toLowerCase().
        includes(lowerCaseSearchTerm));

    });

    setFilteredHarvestEstimates(filteredHarvest);
    setFilteredDraftEstimates(filteredDrafts);
  }, [searchTerm, harvestEstimates, draftEstimates]);

  // Handler for creating a new estimate
  const handleCreateNew = () => {
    navigate("/estimates/new");
  };

  // Handler for viewing a draft estimate
  const handleViewDraftEstimate = (uuid: string) => {
    navigate(`/estimates/${uuid}`);
  };

  // Handler for search input change
  const onSearchChange = (e: {target: {value: string;};}) => {
    setSearchTerm(e.target.value);
  };

  // Helper to check if there are any estimates to display
  const hasEstimates =
  filteredHarvestEstimates.length > 0 || filteredDraftEstimates.length > 0;

  return (
    <div className="full-width-container px-4 py-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-primary mb-4 sm:mb-0">
          Estimates
        </h1>
        <Button
          onClick={handleCreateNew}
          variant="primary"
          className="flex items-center">

          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor">

            <path
              fillRule="evenodd"
              d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z"
              clipRule="evenodd" />

          </svg>
          Create New Estimate
        </Button>
      </div>

      {/* Information Accordion */}
      <div className="mb-6 bg-surface-card shadow rounded-lg overflow-hidden">
        <EstimateInfoAccordion
          showInformation={showInformation}
          setShowInformation={setShowInformation}
          variant="list" />

      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-5 w-5 text-subtle"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">

              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />

            </svg>
          </div>
          <Input
            type="text"
            value={searchTerm}
            onChange={onSearchChange}
            className="block w-full pl-10 pr-3 py-2 border border-strong rounded-md leading-5 bg-surface-card placeholder-gray-500 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
            placeholder="Search estimates..." />

        </div>
      </div>

      {/* Loading State */}
      {loading ?
      <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <svg
            className="animate-spin h-10 w-10 text-primary mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24">

              <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4">
            </circle>
              <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
            </svg>
            <p className="text-secondary">
              Loading estimates...
            </p>
          </div>
        </div> :
      // Error State
      error ?
      <div className="bg-error-light/20 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
              className="h-5 w-5 text-error-light"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor">

                <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />

              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-error">
                Error
              </h3>
              <div className="mt-2 text-sm text-error">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div> :
      // Empty State (after filtering or initial load)
      !hasEstimates ?
      <div className="bg-surface-card overflow-hidden shadow rounded-lg p-6 text-center">
          <svg
          className="mx-auto h-12 w-12 text-subtle"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true">

            <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />

          </svg>
          <h3 className="mt-2 text-sm font-medium text-primary">
            {searchTerm ?
          "No estimates match your search" :
          "No estimates found"}
          </h3>
          <p className="mt-1 text-sm text-muted">
            {searchTerm ?
          "Try adjusting your search term." :
          "Get started by creating a new estimate."}
          </p>
          {!searchTerm &&
        <div className="mt-6">
              <Button
            onClick={handleCreateNew}
            variant="primary"
            className="flex items-center">

                <svg
              className="-ml-1 mr-2 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true">

                  <path
                fillRule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clipRule="evenodd" />

                </svg>
                Create New Estimate
              </Button>
            </div>
        }
        </div> :

      <div className="space-y-8">
          {/* Draft Estimates */}
          {filteredDraftEstimates.length > 0 &&
        <div>
              <h2 className="text-lg font-medium text-primary mb-4">
                Draft Estimates
              </h2>
              {!isDesktop ?
          // Mobile view - cards
          <div className="grid grid-cols-1 gap-4">
                  {filteredDraftEstimates.map((draft) =>
            <DraftEstimateCard
              key={draft.uuid}
              draft={draft}
              onView={handleViewDraftEstimate}
              onDelete={async (uuid) => {
                try {
                  await deleteDraftEstimate(uuid);
                  setDraftEstimates((prevDrafts) =>
                  prevDrafts.filter((d) => d.uuid !== uuid)
                  );
                  setFilteredDraftEstimates((prevFiltered) =>
                  prevFiltered.filter((d) => d.uuid !== uuid)
                  );
                  setError(null);
                } catch (err: any) {
                  console.error('Failed to delete draft: ", err);

                  // If it's a 404 error, the estimate was already deleted
                  if (
                  err?.message?.includes("404") ||
                  err?.message?.includes("not found"))
                  {
                    // Remove from UI since it"s already deleted
                    setDraftEstimates((prevDrafts) =>
                    prevDrafts.filter((d) => d.uuid !== uuid)
                    );
                    setFilteredDraftEstimates((prevFiltered) =>
                    prevFiltered.filter((d) => d.uuid !== uuid)
                    );
                  } else {
                    // For other errors, show an error message
                    setError(
                      "Failed to delete estimate. Please try again."
                    );
                  }
                }
              }}
              onLinkDeal={() =>
              setLinkToDealModalOpen({
                isOpen: true,
                estimateId: draft.uuid,
                estimateType: "internal"
              })
              }
              onViewLinkedDeal={() =>
              setLinkedDealModalOpen({
                isOpen: true,
                estimateId: draft.uuid,
                estimateType: "internal"
              })
              }
              hasLinkedDeal={!!linkedDealsMap[draft.uuid]}
              StaffInitialsCircles={StaffInitialsCircles} />

            )}
                </div> :

          // Desktop view - table
          <div className="shadow rounded-lg overflow-hidden">
                  <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-surface-page">
                      <tr>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider w-1/4 max-w-[200px]">

                          Project / Client
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Date Range
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Staff
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-right text-xs font-medium text-muted uppercase tracking-wider">

                          Total Fees
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-right text-xs font-medium text-muted uppercase tracking-wider">

                          Gross Margin
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Last Updated
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredDraftEstimates.map((draft) =>
                <React.Fragment key={draft.uuid}>
                          {/* Main information row */}
                          <tr className="hover:bg-surface-page dark:hover:bg-surface-elevated">
                            <td className="px-4 py-3 text-sm w-1/4 max-w-[200px]">
                              <div className="space-y-1">
                                <div className="font-medium text-primary truncate">
                                  {draft.projectName || "Untitled Project"}
                                </div>
                                <div className="text-xs text-muted truncate">
                                  {draft.clientName}
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary">
                              {`${format(
                        new Date(draft.startDate),
                        "MMM d, yyyy"
                      )} - ${format(
                        new Date(draft.endDate),
                        "MMM d, yyyy"
                      )}`}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary">
                              {draft.isLoading ?
                      <div className="flex items-center">
                                  <svg
                          className="animate-spin h-4 w-4 text-primary mr-2"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24">

                                    <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4">
                          </circle>
                                    <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                          </path>
                                  </svg>
                                  <span>Loading...</span>
                                </div> :
                      draft.staffInitials &&
                      draft.staffInitials.length > 0 ?
                      <StaffInitialsCircles
                        initials={draft.staffInitials} /> :


                      <span className="text-subtle">
                                  No staff
                                </span>
                      }
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary text-right">
                              {draft.isLoading ?
                      <div className="flex justify-end">
                                  <svg
                          className="animate-spin h-4 w-4 text-primary"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24">

                                    <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4">
                          </circle>
                                    <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                          </path>
                                  </svg>
                                </div> :
                      draft.totalFees !== undefined ?
                      formatCurrency(draft.totalFees) :

                      <span className="text-subtle">
                                  -
                                </span>
                      }
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                              {draft.isLoading ?
                      <div className="flex justify-end">
                                  <svg
                          className="animate-spin h-4 w-4 text-primary"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24">

                                    <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4">
                          </circle>
                                    <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                          </path>
                                  </svg>
                                </div> :
                      draft.grossMarginAmount !== undefined &&
                      draft.grossMarginPercentage !== undefined ?
                      <span
                        className={
                        draft.grossMarginPercentage >= 50 ?
                        "text-success" :
                        draft.grossMarginPercentage >= 30 ?
                        "text-warning" :
                        "text-error"
                        }>

                                  {formatCurrency(draft.grossMarginAmount)} |{" "}
                                  {draft.grossMarginPercentage.toFixed(1)}%
                                </span> :

                      <span className="text-subtle">
                                  -
                                </span>
                      }
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary">
                              {format(new Date(draft.updatedAt), "MMM d, yyyy")}
                            </td>
                          </tr>

                          {/* Actions row */}
                          <tr className="bg-surface-page/30/20 border-b border-default">
                            <td colSpan={6} className="px-4 py-2">
                              <div className="flex items-center gap-2 justify-start">
                                <Button
                          onClick={() =>
                          handleViewDraftEstimate(draft.uuid)
                          }
                          variant="outline"
                          size="sm"
                          title="View or edit this estimate"
                          className="flex items-center">

                                  <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor">

                                    <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />

                                  </svg>
                                  View/Edit
                                </Button>

                                {linkedDealsMap[draft.uuid] ?
                        <Button
                          onClick={() =>
                          setLinkedDealModalOpen({
                            isOpen: true,
                            estimateId: draft.uuid,
                            estimateType: "internal"
                          })
                          }
                          variant="success"
                          size="sm"
                          title="View or edit linked deal"
                          className="flex items-center">

                                    <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor">

                                      <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />

                                    </svg>
                                    Linked Deal
                                  </Button> :

                        <Button
                          onClick={() =>
                          setLinkToDealModalOpen({
                            isOpen: true,
                            estimateId: draft.uuid,
                            estimateType: "internal"
                          })
                          }
                          variant="success"
                          size="sm"
                          title="Link this estimate to a deal"
                          className="flex items-center">

                                    <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor">

                                      <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />

                                    </svg>
                                    Link to Deal
                                  </Button>
                        }

                                <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (
                            window.confirm(
                              "Are you sure you want to delete this draft estimate? This action cannot be undone."
                            ))
                            {
                              // Call deleteDraftEstimate API and refresh the list
                              const deleteDraft = async () => {
                                try {
                                  await deleteDraftEstimate(draft.uuid);
                                  // Remove from the current list rather than refetching
                                  setDraftEstimates((prevDrafts) =>
                                  prevDrafts.filter(
                                    (d) => d.uuid !== draft.uuid
                                  )
                                  );
                                  setFilteredDraftEstimates(
                                    (prevFiltered) =>
                                    prevFiltered.filter(
                                      (d) => d.uuid !== draft.uuid
                                    )
                                  );
                                  // Show a brief success message
                                  const successMsg =
                                  "Draft deleted successfully";
                                  console.log(successMsg);

                                  // Clear any previous error
                                  setError(null);
                                } catch (err: any) {
                                  console.error('Failed to delete draft: ",
                                    err
                                  );

                                  // If it's a 404 error, the estimate was already deleted
                                  if (
                                  err?.message?.includes("404") ||
                                  err?.message?.includes("not found"))
                                  {
                                    // Remove from UI since it"s already deleted
                                    setDraftEstimates((prevDrafts) =>
                                    prevDrafts.filter(
                                      (d) => d.uuid !== draft.uuid
                                    )
                                    );
                                    setFilteredDraftEstimates(
                                      (prevFiltered) =>
                                      prevFiltered.filter(
                                        (d) => d.uuid !== draft.uuid
                                      )
                                    );

                                    // Don"t set an error message since the UI was updated
                                    console.log(
                                      "Updated UI anyway despite error"
                                    );
                                  } else {
                                    // For other errors, show an error message
                                    setError(
                                      "Failed to delete estimate. Please try again."
                                    );
                                  }
                                }
                              };
                              deleteDraft();
                            }
                          }}
                          variant="danger"
                          size="sm"
                          title="Delete this estimate">

                                  <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor">

                                    <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />

                                  </svg>
                                </Button>
                              </div>
                            </td>
                          </tr>
                        </React.Fragment>
                )}
                    </tbody>
                  </table>
                </div>
          }
            </div>
        }

          {/* Harvest Estimates Table */}
          {filteredHarvestEstimates.length > 0 &&
        <div>
              <h2 className="text-lg font-medium text-primary mb-4">
                Harvest Estimates
              </h2>
              {!isDesktop ?
          // Mobile view - cards
          <div className="space-y-4">
                  {filteredHarvestEstimates.map((estimate: HarvestEstimate) =>
            <HarvestEstimateCard
              key={estimate.id}
              estimate={estimate}
              StatusBadge={EstimateStatusBadge} />

            )}
                </div> :

          // Desktop view - table
          <div className="shadow rounded-lg">
                  <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-surface-page">
                      <tr>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Number
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Client
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Subject
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-right text-xs font-medium text-muted uppercase tracking-wider">

                          Amount
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Issue Date
                        </th>
                        <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-muted uppercase tracking-wider">

                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-surface-card divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredHarvestEstimates.map((estimate) =>
                <React.Fragment key={estimate.id}>
                          {/* Main information row */}
                          <tr className="hover:bg-surface-page dark:hover:bg-surface-elevated">
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-primary">
                              {estimate.number}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary">
                              {estimate.client?.name || "N/A"}
                            </td>
                            <td className="px-4 py-3 text-sm text-primary">
                              <div className="truncate max-w-[200px]">
                                {estimate.subject || "-"}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary text-right">
                              {formatCurrency(estimate.amount)}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-primary">
                              {estimate.issue_date ?
                      format(
                        parseISO(estimate.issue_date),
                        "MMM d, yyyy"
                      ) :
                      "-"}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm">
                              <EstimateStatusBadge status={estimate.state} />
                            </td>
                          </tr>

                          {/* Actions row */}
                          <tr className="bg-surface-page/30/20 border-b border-default">
                            <td colSpan={6} className="px-4 py-2">
                              <div className="flex items-center gap-2 justify-start">
                                <a
                          href={`https://onbord.harvestapp.com/estimates/${estimate.id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-3 py-1.5 rounded text-xs font-medium border border-warning text-warning-light hover:bg-warning-light dark:hover:bg-warning-dark/20 transition-colors"
                          title="Open in Harvest">

                                  <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor">

                                    <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />

                                  </svg>
                                  View in Harvest
                                </a>
                              </div>
                            </td>
                          </tr>
                        </React.Fragment>
                )}
                    </tbody>
                  </table>
                </div>
          }
            </div>
        }
        </div>
      }

      {/* Deal Link Modal */}
      <DealLinkModal
        isOpen={linkToDealModalOpen.isOpen}
        onClose={() => {
          setLinkToDealModalOpen({
            isOpen: false,
            estimateId: "",
            estimateType: "internal"
          });
          // Refresh linked deals data after linking
          if (linkToDealModalOpen.estimateId) {
            fetchLinkedDealsForEstimate(
              linkToDealModalOpen.estimateId,
              linkToDealModalOpen.estimateType
            ).then((deals) => {
              if (deals && deals.length > 0) {
                setLinkedDealsMap((prev) => ({
                  ...prev,
                  [linkToDealModalOpen.estimateId]: deals[0]
                }));
              }
            });
          }
        }}
        estimateId={linkToDealModalOpen.estimateId}
        estimateType={linkToDealModalOpen.estimateType} />


      {/* Linked Deal Modal */}
      <LinkedDealModal
        isOpen={linkedDealModalOpen.isOpen}
        onClose={() => {
          setLinkedDealModalOpen({
            isOpen: false,
            estimateId: "",
            estimateType: "internal"
          });
          // Refresh linked deals data after unlinking
          if (linkedDealModalOpen.estimateId) {
            fetchLinkedDealsForEstimate(
              linkedDealModalOpen.estimateId,
              linkedDealModalOpen.estimateType
            ).then((deals) => {
              if (deals && deals.length > 0) {
                setLinkedDealsMap((prev) => ({
                  ...prev,
                  [linkedDealModalOpen.estimateId]: deals[0]
                }));
              } else {
                // Remove the deal from the map if it was unlinked
                setLinkedDealsMap((prev) => {
                  const newMap = { ...prev };
                  delete newMap[linkedDealModalOpen.estimateId];
                  return newMap;
                });
              }
            });
          }
        }}
        estimateId={linkedDealModalOpen.estimateId}
        estimateType={linkedDealModalOpen.estimateType} />

    </div>);

};

export default EstimatesList;