/* Foundation CSS - Tailwind-first component patterns and base styles */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* === BASE LAYER - Global styles and resets === */
@layer base {
  /* Enhanced CSS reset using Tailwind approach */
  *,
  ::before,
  ::after {
    /* TODO: removed @apply */
  }

  html {
    /* TODO: removed @apply */
  }

  body {
    /* TODO: removed @apply */
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    /* TODO: removed @apply */
  }

  /* === SEMANTIC DESIGN TOKENS === */
  :root {
    /* Button semantic tokens */
    --btn-primary-bg: var(--color-primary-600);
    --btn-primary-text: var(--color-white);
    --btn-primary-hover: var(--color-primary-700);
    --btn-primary-border: transparent;
    
    --btn-secondary-bg: var(--color-secondary-100);
    --btn-secondary-text: var(--color-secondary-900);
    --btn-secondary-hover: var(--color-secondary-200);
    --btn-secondary-border: var(--color-secondary-300);
    
    --btn-danger-bg: var(--color-error-600);
    --btn-danger-text: var(--color-white);
    --btn-danger-hover: var(--color-error-700);
    --btn-danger-border: transparent;
    
    --btn-ghost-bg: transparent;
    --btn-ghost-text: var(--color-gray-700);
    --btn-ghost-hover: var(--color-gray-100);
    --btn-ghost-border: transparent;

    --btn-outline-bg: transparent;
    --btn-outline-text: var(--color-primary-600);
    --btn-outline-hover: var(--color-primary-50);
    --btn-outline-border: var(--color-primary-600);
    
    /* Input semantic tokens */
    --input-bg: var(--color-white);
    --input-border: var(--color-gray-300);
    --input-text: var(--color-gray-900);
    --input-placeholder: var(--color-gray-500);
    --input-focus-border: var(--color-primary-500);
    --input-focus-ring: rgba(32, 94, 166, 0.2);
    --input-error-border: var(--color-error-500);
    --input-success-border: var(--color-success-500);
    
    /* Surface semantic tokens */
    --surface-primary: var(--color-white);
    --surface-secondary: var(--color-gray-50);
    --surface-elevated: var(--color-white);
    --surface-overlay: rgba(0, 0, 0, 0.5);

    /* Badge semantic tokens */
    --badge-primary-bg: var(--color-primary-100);
    --badge-primary-text: var(--color-primary-700);
    --badge-secondary-bg: var(--color-gray-100);
    --badge-secondary-text: var(--color-gray-700);
    --badge-success-bg: var(--color-success-100);
    --badge-success-text: var(--color-success-700);
    --badge-warning-bg: var(--color-warning-100);
    --badge-warning-text: var(--color-warning-700);
    --badge-error-bg: var(--color-error-100);
    --badge-error-text: var(--color-error-700);
    
    /* Alert semantic tokens */
    --alert-info-bg: var(--color-info-bg);
    --alert-info-border: rgba(36, 131, 123, 0.3);
    --alert-info-text: var(--color-info-text);
    --alert-success-bg: var(--color-success-bg);
    --alert-success-border: rgba(102, 128, 11, 0.3);
    --alert-success-text: var(--color-success-text);
    --alert-warning-bg: var(--color-warning-bg);
    --alert-warning-border: rgba(188, 82, 21, 0.3);
    --alert-warning-text: var(--color-warning-text);
    --alert-error-bg: var(--color-error-bg);
    --alert-error-border: rgba(175, 48, 41, 0.3);
    --alert-error-text: var(--color-error-text);
  }

  /* Dark mode semantic token overrides */
  @media (prefers-color-scheme: dark) {
    :root {
      /* Button tokens - dark mode */
      --btn-secondary-bg: var(--color-gray-800);
      --btn-secondary-text: var(--color-gray-100);
      --btn-secondary-hover: var(--color-gray-700);
      --btn-secondary-border: var(--color-gray-600);
      
      --btn-ghost-text: var(--color-gray-300);
      --btn-ghost-hover: var(--color-gray-800);

      --btn-outline-text: var(--color-primary-400);
      --btn-outline-hover: rgba(32, 94, 166, 0.1);
      --btn-outline-border: var(--color-primary-400);
      
      /* Input tokens - dark mode */
      --input-bg: var(--color-gray-900);
      --input-border: var(--color-gray-700);
      --input-text: var(--color-gray-100);
      --input-placeholder: var(--color-gray-500);
      --input-focus-ring: rgba(49, 113, 178, 0.2);
      
      /* Surface tokens - dark mode */
      --surface-primary: var(--color-gray-900);
      --surface-secondary: var(--color-gray-800);
      --surface-elevated: var(--color-gray-850);
      --surface-overlay: rgba(0, 0, 0, 0.7);

      /* Badge tokens - dark mode */
      --badge-primary-bg: rgba(32, 94, 166, 0.2);
      --badge-primary-text: var(--color-primary-300);
      --badge-secondary-bg: rgba(87, 86, 83, 0.2);
      --badge-secondary-text: var(--color-gray-300);
      --badge-success-bg: rgba(102, 128, 11, 0.2);
      --badge-success-text: var(--color-success-300);
      --badge-warning-bg: rgba(188, 82, 21, 0.2);
      --badge-warning-text: var(--color-warning-300);
      --badge-error-bg: rgba(175, 48, 41, 0.2);
      --badge-error-text: var(--color-error-300);

      /* Alert tokens - dark mode */
      --alert-info-bg: rgba(36, 131, 123, 0.15);
      --alert-info-border: rgba(36, 131, 123, 0.5);
      --alert-success-bg: rgba(102, 128, 11, 0.15);
      --alert-success-border: rgba(102, 128, 11, 0.5);
      --alert-warning-bg: rgba(188, 82, 21, 0.15);
      --alert-warning-border: rgba(188, 82, 21, 0.5);
      --alert-error-bg: rgba(175, 48, 41, 0.15);
      --alert-error-border: rgba(175, 48, 41, 0.5);
    }
  }
}

/* === COMPONENT LAYER - Reusable component patterns === */
@layer components {
  /* === ADAPTIVE CARD COMPONENT === */
  /* Replaces .adaptive-card with container query support */
  .card {
    container-type: inline-size;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    color: var(--color-text);
    padding: theme("spacing.fluid-md");
  }

  /* Container query variants for cards */
  @container (min-width: 300px) {
    .card {
      padding: theme("spacing.fluid-lg");
    }
  }

  @container (min-width: 500px) {
    .card {
      padding: theme("spacing.fluid-xl");
    }
  }

  /* Card variants */
  .card--compact {
    padding: theme("spacing.fluid-sm");
  }

  .card--comfortable {
    padding: theme("spacing.fluid-lg");
  }

  .card--interactive {
    /* TODO: removed @apply */
  }

  .card--bordered {
    /* TODO: removed @apply */
  }

  /* Card state variants */
  .card-info {
    /* TODO: removed @apply */
  }

  .card-success {
    /* TODO: removed @apply */
  }

  .card-warning {
    /* TODO: removed @apply */
  }

  .card-error {
    /* TODO: removed @apply */
  }

  .card-hover {
    /* TODO: removed @apply */
  }

  /* === RESPONSIVE CONTAINER COMPONENT === */
  /* Replaces .responsive-container */
  .responsive-layout {
    container-type: inline-size;
    /* TODO: removed @apply */
  }

  @container (min-width: 600px) {
    .responsive-layout {
      /* TODO: removed @apply */
    }
  }

  /* === BUTTON SYSTEM REMOVED === */
  /* All buttons now use btn-modern from modern-design-system.css */
  /* See docs/button-consistency-strategy.md for migration guide */

  /* === BADGE COMPONENT SYSTEM === */
  .badge {
    /* TODO: removed @apply */
  }

  .badge-primary {
    background-color: var(--badge-primary-bg);
    color: var(--badge-primary-text);
  }

  .badge-secondary {
    background-color: var(--badge-secondary-bg);
    color: var(--badge-secondary-text);
  }

  .badge-success {
    background-color: var(--badge-success-bg);
    color: var(--badge-success-text);
  }

  .badge-warning {
    background-color: var(--badge-warning-bg);
    color: var(--badge-warning-text);
  }

  .badge-error {
    background-color: var(--badge-error-bg);
    color: var(--badge-error-text);
  }

  /* Alias for compatibility */
  .badge-danger {
    background-color: var(--badge-error-bg);
    color: var(--badge-error-text);
  }

  .badge-info {
    background-color: var(--color-info-bg);
    color: var(--color-info-text);
  }

  .badge-outline {
    background-color: transparent;
    border: 1px solid currentColor;
  }

  /* Dark mode styles handled by semantic tokens - see :root[data-theme="dark"] */

  /* === ALERT COMPONENT SYSTEM === */
  .alert {
    /* TODO: removed @apply */
  }

  .alert-info {
    background-color: var(--alert-info-bg);
    border: 1px solid;
    border-color: var(--alert-info-border);
  }

  .alert-info .alert-icon {
    color: var(--color-info);
  }

  .alert-info .alert-title {
    color: var(--alert-info-text);
  }

  .alert-info .alert-body {
    color: var(--alert-info-text);
    opacity: 0.9;
  }

  .alert-success {
    background-color: var(--alert-success-bg);
    border: 1px solid;
    border-color: var(--alert-success-border);
  }

  .alert-success .alert-icon {
    color: var(--color-success);
  }

  .alert-success .alert-title {
    color: var(--alert-success-text);
  }

  .alert-success .alert-body {
    color: var(--alert-success-text);
    opacity: 0.9;
  }

  .alert-warning {
    background-color: var(--alert-warning-bg);
    border: 1px solid;
    border-color: var(--alert-warning-border);
  }

  .alert-warning .alert-icon {
    color: var(--color-warning);
  }

  .alert-warning .alert-title {
    color: var(--alert-warning-text);
  }

  .alert-warning .alert-body {
    color: var(--alert-warning-text);
    opacity: 0.9;
  }

  .alert-error {
    background-color: var(--alert-error-bg);
    border: 1px solid;
    border-color: var(--alert-error-border);
  }

  .alert-error .alert-icon {
    color: var(--color-error);
  }

  .alert-error .alert-title {
    color: var(--alert-error-text);
  }

  .alert-error .alert-body {
    color: var(--alert-error-text);
    opacity: 0.9;
  }

  /* Dark mode styles handled by semantic tokens - see :root[data-theme="dark"] */

  /* === FORM COMPONENT SYSTEM === */
  .form-input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--input-border);
    border-radius: 0.375rem;
    box-shadow: var(--shadow-sm);
    background-color: var(--input-bg);
    color: var(--input-text);
    transition: all 0.2s ease;
  }

  .form-input::placeholder {
    color: var(--input-placeholder);
  }

  .form-input:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px var(--input-focus-ring);
  }

  .form-input--error {
    border-color: var(--input-error-border);
  }

  .form-input--error:focus {
    border-color: var(--input-error-border);
    box-shadow: 0 0 0 3px rgba(175, 48, 41, 0.1);
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text);
    margin-bottom: 0.25rem;
  }

  .form-error {
    font-size: 0.75rem;
    color: var(--color-error);
    margin-top: 0.25rem;
  }

  .form-help {
    font-size: 0.75rem;
    color: var(--color-text-muted);
    margin-top: 0.25rem;
  }

  /* Form field wrapper */
  .form-field {
    /* TODO: removed @apply */
  }

  /* Form section grouping */
  .form-section {
    /* TODO: removed @apply */
  }

  .form-section__title {
    /* TODO: removed @apply */
  }

  .form-section__description {
    /* TODO: removed @apply */
  }

  /* Form grid layouts */
  .form-grid {
    /* TODO: removed @apply */
  }

  .form-grid--cols-1 {
    /* TODO: removed @apply */
  }

  .form-grid--cols-2 {
    /* TODO: removed @apply */
  }

  .form-grid--cols-3 {
    /* TODO: removed @apply */
  }

  .form-grid--cols-4 {
    /* TODO: removed @apply */
  }

  /* Input icons */
  .form-input-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    padding-left: 0.75rem;
    display: flex;
    align-items: center;
    pointer-events: none;
  }

  .form-input-icon svg {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--color-text-muted);
  }

  /* Error icons */
  .form-error-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    padding-right: 0.75rem;
    display: flex;
    align-items: center;
    pointer-events: none;
  }

  .form-error-icon svg {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--color-error);
  }

  /* Success state */
  .form-input--success {
    border-color: var(--input-success-border);
  }

  .form-input--success:focus {
    border-color: var(--input-success-border);
    box-shadow: 0 0 0 3px rgba(102, 128, 11, 0.1);
  }

  /* Loading state */
  .form-input--loading {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Required field indicator */
  .form-required {
    color: var(--color-error);
    margin-left: 0.25rem;
  }

  /* Select styles */
  .form-select {
    display: block;
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 0.75rem;
    border: 1px solid var(--input-border);
    border-radius: 0.375rem;
    box-shadow: var(--shadow-sm);
    background-color: var(--input-bg);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236f6e69' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    color: var(--input-text);
    appearance: none;
    transition: all 0.2s ease;
  }

  .form-select:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px var(--input-focus-ring);
  }

  .form-select--error {
    border-color: var(--input-error-border);
  }

  .form-select--error:focus {
    border-color: var(--input-error-border);
    box-shadow: 0 0 0 3px rgba(175, 48, 41, 0.1);
  }

  .form-select--success {
    border-color: var(--input-success-border);
  }

  .form-select--success:focus {
    border-color: var(--input-success-border);
    box-shadow: 0 0 0 3px rgba(102, 128, 11, 0.1);
  }

  /* Textarea styles */
  .form-textarea {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--input-border);
    border-radius: 0.375rem;
    box-shadow: var(--shadow-sm);
    background-color: var(--input-bg);
    color: var(--input-text);
    resize: vertical;
    min-height: 5rem;
    transition: all 0.2s ease;
  }

  .form-textarea::placeholder {
    color: var(--input-placeholder);
  }

  .form-textarea:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px var(--input-focus-ring);
  }

  .form-textarea--error {
    border-color: var(--input-error-border);
  }

  .form-textarea--error:focus {
    border-color: var(--input-error-border);
    box-shadow: 0 0 0 3px rgba(175, 48, 41, 0.1);
  }

  .form-textarea--success {
    border-color: var(--input-success-border);
  }

  .form-textarea--success:focus {
    border-color: var(--input-success-border);
    box-shadow: 0 0 0 3px rgba(102, 128, 11, 0.1);
  }

  /* Dark mode styles handled by semantic tokens - see :root[data-theme="dark"] */
  @media (prefers-color-scheme: dark) {
    /* Update select arrow color for dark mode */
    .form-select {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239f9d96' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    }
  }

  /* === LIST COMPONENT SYSTEM === */
  .list {
    container-type: inline-size;
    /* TODO: removed @apply */
  }

  /* List variants */
  .list--compact {
    /* TODO: removed @apply */
  }

  .list--comfortable {
    /* TODO: removed @apply */
  }

  /* List layouts */
  .list--stack {
    /* TODO: removed @apply */
  }

  .list--grid {
    /* TODO: removed @apply */
  }

  @container (min-width: 640px) {
    .list--grid {
      /* TODO: removed @apply */
    }
  }

  @container (min-width: 1024px) {
    .list--grid {
      /* TODO: removed @apply */
    }
  }

  .list--table {
    /* TODO: removed @apply */
  }

  .list--cards {
    /* TODO: removed @apply */
  }

  @container (min-width: 640px) {
    .list--cards {
      /* TODO: removed @apply */
    }
  }

  @container (min-width: 1024px) {
    .list--cards {
      /* TODO: removed @apply */
    }
  }

  /* List item component */
  .list-item {
    /* TODO: removed @apply */
  }

  .list-item--interactive {
    /* TODO: removed @apply */
  }

  .list-item--selected {
    /* TODO: removed @apply */
  }

  .list-item--disabled {
    /* TODO: removed @apply */
  }

  /* List item sizes */
  .list-item--compact {
    /* TODO: removed @apply */
  }

  .list-item--comfortable {
    /* TODO: removed @apply */
  }

  /* List item borders */
  .list-item--bordered {
    /* TODO: removed @apply */
  }

  .list-item--border-left {
    /* TODO: removed @apply */
  }

  .list-item--border-full {
    /* TODO: removed @apply */
  }

  /* === TABLE COMPONENT SYSTEM === */
  .table-container {
    /* TODO: removed @apply */
    -webkit-overflow-scrolling: touch; /* Enable smooth scrolling on iOS */
  }

  /* Mobile-specific table adjustments */
  @media (max-width: 767px) {
    .table-container {
      /* TODO: removed @apply */ /* Remove side borders on mobile */
      margin-left: -1rem;
      margin-right: -1rem;
      width: calc(100% + 2rem);
    }

    /* Hide less important columns on mobile */
    .table .hide-mobile {
      display: none;
    }

    /* Reduce padding on mobile */
    .table th,
    .table td {
      /* TODO: removed @apply */
    }
  }

  .table {
    /* TODO: removed @apply */
  }

  /* Enhanced table variants */
  .table--striped tbody tr:nth-child(even) {
    /* TODO: removed @apply */
  }

  .table--bordered {
    /* TODO: removed @apply */
  }

  .table--bordered th,
  .table--bordered td {
    /* TODO: removed @apply */
  }

  .table--compact th,
  .table--compact td {
    /* TODO: removed @apply */
  }

  .table--sm th,
  .table--sm td {
    /* TODO: removed @apply */
  }

  .table--lg th,
  .table--lg td {
    /* TODO: removed @apply */
  }

  .table--responsive {
    container-type: inline-size;
  }

  @container (max-width: 640px) {
    .table--responsive {
      /* TODO: removed @apply */
    }
  }

  .table-header {
    /* TODO: removed @apply */
  }

  .table-header-cell {
    /* TODO: removed @apply */
  }

  .table-body {
    /* TODO: removed @apply */
  }

  .table-row {
    /* TODO: removed @apply */
  }

  .table-cell {
    /* TODO: removed @apply */
  }

  /* === DATA LIST COMPONENT SYSTEM === */
  .data-list {
    container-type: inline-size;
    /* TODO: removed @apply */
  }

  .data-list--cards {
    /* TODO: removed @apply */
  }

  @container (min-width: 640px) {
    .data-list--cards {
      /* TODO: removed @apply */
    }
  }

  @container (min-width: 1024px) {
    .data-list--cards {
      /* TODO: removed @apply */
    }
  }

  .data-list--table {
    /* TODO: removed @apply */
  }

  .data-list--grid {
    /* TODO: removed @apply */
  }

  @container (min-width: 640px) {
    .data-list--grid {
      /* TODO: removed @apply */
    }
  }

  @container (min-width: 1024px) {
    .data-list--grid {
      /* TODO: removed @apply */
    }
  }

  /* Data list density */
  .data-list--compact .list-item {
    /* TODO: removed @apply */
  }

  .data-list--comfortable .list-item {
    /* TODO: removed @apply */
  }

  /* Data list loading state */
  .data-list--loading {
    /* TODO: removed @apply */
  }

  .data-list--loading::after {
    content: "";
    /* TODO: removed @apply */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='10' stroke='currentColor' stroke-width='4' class='opacity-25'/%3E%3Cpath fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z' class='opacity-75'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 2rem;
  }

  /* === EXPENSE LIST COMPONENT SYSTEM === */
  .expense-list-item {
    container-type: inline-size;
  }

  .expense-item-responsive {
    /* TODO: removed @apply */
  }

  /* Default: Show card layout */
  .expense-card-layout {
    /* TODO: removed @apply */
  }

  .expense-table-layout {
    /* TODO: removed @apply */
  }

  /* Container query: Switch to table layout for wider containers */
  @container (min-width: 768px) {
    .expense-card-layout {
      /* TODO: removed @apply */
    }

    .expense-table-layout {
      /* TODO: removed @apply */
    }
  }

  /* === TRANSACTION LIST COMPONENT SYSTEM === */
  .transaction-list-item {
    container-type: inline-size;
  }

  .transaction-item-responsive {
    /* TODO: removed @apply */
  }

  /* Default: Show card layout */
  .transaction-card-layout {
    /* TODO: removed @apply */
  }

  .transaction-table-layout {
    /* TODO: removed @apply */
  }

  /* Container query: Switch to table layout for wider containers */
  @container (min-width: 768px) {
    .transaction-card-layout {
      /* TODO: removed @apply */
    }

    .transaction-table-layout {
      /* TODO: removed @apply */
    }
  }

  /* Transaction table wrapper for proper structure */
  .transaction-table-wrapper {
    container-type: inline-size;
  }

  /* Transaction table headers - sync with list item container queries */
  .transaction-headers-wrapper {
    container-type: inline-size;
  }

  .transaction-table-headers {
    /* TODO: removed @apply */
  }

  @container (min-width: 768px) {
    .transaction-table-headers {
      /* TODO: removed @apply */
    }
  }

  /* Fix DataList alignment for transaction tables */
  .transactions-container .data-list {
    /* TODO: removed @apply */
  }

  /* Remove DataList item wrapper divs in table mode - CRITICAL FIX */
  @container (min-width: 768px) {
    .transaction-table-content .data-list > div[role="listitem"] {
      display: contents !important;
    }

    /* Also ensure the transaction list items are visible */
    .transaction-list-item {
      display: contents !important;
    }

    /* But keep the table layout visible */
    .transaction-list-item .transaction-table-layout {
      display: block !important;
    }

    /* Hide the card layout */
    .transaction-list-item .transaction-card-layout {
      display: none !important;
    }
  }

  /* Ensure list items don't add extra padding in table mode */
  .transaction-table-layout .list-item,
  .decision-table-layout .list-item {
    padding: 0 !important;
  }

  /* SIMPLIFIED Transaction table grid layout */
  .transaction-table-grid {
    display: grid !important;
    grid-template-columns: 3rem 7rem 1fr 1fr auto 10rem;
    gap: 1rem;
    align-items: center;
    width: 100%;
    padding: 0 1rem;
  }

  /* Headers need the exact same structure */
  .transaction-headers-wrapper .transaction-table-grid {
    padding: 0.625rem 1rem;
  }

  /* Content rows */
  .transaction-table-layout .transaction-table-grid {
    padding: 0.75rem 1rem;
  }

  /* === DECISION LIST COMPONENT SYSTEM === */
  .decision-list-item {
    container-type: inline-size;
  }

  .decision-item-responsive {
    /* TODO: removed @apply */
  }

  /* Default: Show card layout */
  .decision-card-layout {
    /* TODO: removed @apply */
  }

  .decision-table-layout {
    /* TODO: removed @apply */
  }

  /* Container query: Switch to table layout for wider containers */
  @container (min-width: 768px) {
    .decision-card-layout {
      /* TODO: removed @apply */
    }

    .decision-table-layout {
      /* TODO: removed @apply */
    }
  }

  /* Decision table wrapper for proper structure */
  .decision-table-wrapper {
    container-type: inline-size;
  }

  /* Decision table headers - sync with list item container queries */
  .decision-headers-wrapper {
    container-type: inline-size;
  }

  .decision-table-headers {
    /* TODO: removed @apply */
  }

  @container (min-width: 768px) {
    .decision-table-headers {
      /* TODO: removed @apply */
    }
  }

  /* Fix DataList alignment for decision tables */
  .decision-container .data-list {
    /* TODO: removed @apply */
  }

  /* Remove DataList item wrapper divs in table mode - CRITICAL FIX */
  @container (min-width: 768px) {
    .decision-table-content .data-list > div[role="listitem"] {
      display: contents !important;
    }

    /* Also ensure the decision list items are visible */
    .decision-list-item {
      display: contents !important;
    }

    /* But keep the table layout visible */
    .decision-list-item .decision-table-layout {
      display: block !important;
    }

    /* Hide the card layout */
    .decision-list-item .decision-card-layout {
      display: none !important;
    }
  }

  /* Remove default list item hover states in table mode */
  .transaction-table-layout .list-item--interactive:hover,
  .decision-table-layout .list-item--interactive:hover {
    background-color: transparent;
  }

  /* SIMPLIFIED Decision table grid layout */
  .decision-table-grid {
    display: grid !important;
    grid-template-columns: 4rem 1fr 7rem 6rem 8rem 10rem;
    gap: 1rem;
    align-items: center;
    width: 100%;
    padding: 0 1rem;
  }

  /* Headers need the exact same structure */
  .decision-headers-wrapper .decision-table-grid {
    padding: 0.625rem 1rem;
  }

  /* Content rows */
  .decision-table-layout .decision-table-grid {
    padding: 0.75rem 1rem;
  }

  /* === FLOATING PANEL SYSTEM === */
  .floating-panel {
    /* TODO: removed @apply */
  }

  .floating-actions {
    /* TODO: removed @apply */
    width: 220px;
  }

  .floating-finance {
    /* TODO: removed @apply */
    width: 220px;
  }

  /* === LOADING STATES === */
  .loading-spinner {
    /* TODO: removed @apply */
  }

  .loading-dots {
    /* TODO: removed @apply */
  }

  .loading-dot {
    /* TODO: removed @apply */
  }

  .loading-dot:nth-child(1) {
    /* TODO: removed @apply */
  }

  .loading-dot:nth-child(2) {
    /* TODO: removed @apply */
  }

  .loading-dot:nth-child(3) {
    /* TODO: removed @apply */
  }

  /* === UTILITY PATTERNS === */
  .text-gradient {
    /* TODO: removed @apply */
  }

  /* Info box for documentation/notes */
  .info-box {
    background-color: rgba(32, 94, 166, 0.05); /* var(--color-primary) with opacity */
    border: 1px solid var(--color-border);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: var(--color-text-muted);
  }

  @media (prefers-color-scheme: dark) {
    .info-box {
      background-color: rgba(49, 113, 178, 0.08); /* var(--color-primary-500) with opacity */
      border-color: var(--color-border-strong);
    }
  }

  /* Page background */
  .page-background {
    background-color: var(--color-bg);
  }

  /* Primary text */
  .text-primary {
    color: var(--color-text);
  }

  /* Muted text */
  .text-muted {
    color: var(--color-text-muted);
  }

  /* Subtle text */
  .text-subtle {
    color: var(--color-text-subtle);
  }

  /* Border styling */
  .border-default {
    border-color: var(--color-border);
  }

  /* Hover states */
  .hover-surface:hover {
    background-color: var(--color-surface-alt);
  }

  @media (prefers-color-scheme: dark) {
    .hover-surface:hover {
      background-color: rgba(134, 133, 128, 0.1); /* slightly lighter surface */
    }
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.400") theme("colors.gray.100");
  }

  .scrollbar-thin::-webkit-scrollbar {
    /* TODO: removed @apply */
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    /* TODO: removed @apply */
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    /* TODO: removed @apply */
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    /* TODO: removed @apply */
  }

  /* === LEGACY COMPATIBILITY === */
  /* Keep existing classes during transition */
  .adaptive-card {
    /* TODO: removed @apply */
  }

  .responsive-container {
    /* TODO: removed @apply */
  }

  /* .default-button removed - use btn-modern btn-modern--primary instead */
}

/* === UTILITY LAYER - Additional utilities === */
@layer utilities {
  /* Container query utilities for better responsive design */
  .container-responsive {
    container-type: inline-size;
  }

  /* Fluid typography utilities */
  .text-fluid-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
    line-height: 1.6;
  }

  /* Enhanced focus utilities */
  .focus-ring-inset {
    /* TODO: removed @apply */
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Animation utilities */
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-in {
    animation: animate-in 0.3s ease-out;
  }

  .fade-in-0 {
    opacity: 0;
    animation-fill-mode: forwards;
  }

  .zoom-in-95 {
    transform: scale(0.95);
    animation-fill-mode: forwards;
  }

  .animate-fade-in-up {
    animation: fadeIn 0.5s ease-out, slideInLeft 0.5s ease-out;
  }

  /* Accessibility utilities */
  .sr-only-focusable:focus {
    /* TODO: removed @apply */
  }

  /* Full width utility for estimates feature */
  .full-width-container {
    /* TODO: removed @apply */
  }
}
