/* Xero Integration Components Styling */

/* Shared styles for Xero integration components */
.xero-info-panel {
  background-color: rgba(var(--color-primary-rgb), 0.05);
  border-left: 3px solid rgba(var(--color-primary-rgb), 0.8);
  padding: var(--space-md);
  border-radius: 0.5rem;
  margin-bottom: var(--space-md);
  transition: background-color 0.2s ease;
}

.dark .xero-info-panel {
  background-color: rgba(var(--color-primary-rgb), 0.08);
  border-left-color: rgba(var(--color-primary-rgb), 0.6);
}

.xero-info-panel:hover {
  background-color: rgba(var(--color-primary-rgb), 0.08);
}

.dark .xero-info-panel:hover {
  background-color: rgba(var(--color-primary-rgb), 0.12);
}

/* Filter bar styles */
.xero-filter-bar {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: 0.5rem;
  background-color: rgba(242, 240, 229, 0.5);
  border-radius: 0.375rem;
  margin-bottom: var(--space-md);
  flex-wrap: wrap;
}

.dark .xero-filter-bar {
  background-color: rgba(40, 39, 38, 0.3);
}

.xero-filter-group {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.xero-filter-label {
  font-size: 0.75rem;
  color: rgba(135, 133, 128, 1);
  white-space: nowrap;
}

.dark .xero-filter-label {
  color: rgba(159, 157, 150, 1);
}

/* Bills table styles */
.xero-bills-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  table-layout: fixed; /* Use fixed layout for better column control */
}

.xero-bills-table th {
  background-color: rgba(242, 240, 229, 0.8);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: rgba(135, 133, 128, 1);
  padding: var(--space-sm) var(--space-md);
  text-align: left;
  border-bottom: 1px solid rgba(230, 228, 217, 1);
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .xero-bills-table th {
  background-color: rgba(40, 39, 38, 0.9);
  color: rgba(159, 157, 150, 1);
  border-bottom-color: rgba(64, 62, 60, 1);
}

.xero-bills-table td {
  padding: var(--space-sm) var(--space-md);
  border-bottom: 1px solid rgba(230, 228, 217, 0.5);
  transition: background-color 0.15s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.dark .xero-bills-table td {
  border-bottom-color: rgba(64, 62, 60, 0.5);
}

.xero-bills-table tr:hover td {
  background-color: rgba(242, 240, 229, 0.5);
}

.dark .xero-bills-table tr:hover td {
  background-color: rgba(64, 62, 60, 0.3);
}

.xero-bills-table tr:last-child td {
  border-bottom: none;
}

/* Column width optimization */
.xero-bills-table th.reference-column,
.xero-bills-table td.reference-column {
  width: 15%;
}

.xero-bills-table th.vendor-column,
.xero-bills-table td.vendor-column {
  width: 15%;
}

.xero-bills-table th.date-column,
.xero-bills-table td.date-column {
  width: 10%;
}

.xero-bills-table th.due-date-column,
.xero-bills-table td.due-date-column {
  width: 10%;
}

.xero-bills-table th.amount-column,
.xero-bills-table td.amount-column {
  width: 10%;
  text-align: right;
  font-weight: 600;
}

.xero-bills-table th.status-column,
.xero-bills-table td.status-column {
  width: 10%;
}

.xero-bills-table th.type-column,
.xero-bills-table td.type-column {
  width: 18%;
}

.xero-bills-table th.action-column,
.xero-bills-table td.action-column {
  width: 12%;
  text-align: center;
}

/* Action button styling - REMOVED: Migrated to btn-modern--xero 
 * All Xero buttons now use the unified btn-modern system
 */

/* Expense card enhancements */
.xero-expense-card {
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.xero-expense-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .xero-expense-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.25),
    0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

.xero-expense-card--net-pay {
  border-top: 3px solid rgba(var(--color-primary-rgb), 0.8);
}

.xero-expense-card--tax {
  border-top: 3px solid rgba(154, 122, 160, 0.8);
}

.xero-expense-card--super {
  border-top: 3px solid rgba(var(--color-success-rgb), 0.8);
}

.xero-expense-card__header {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-xs);
}

.xero-expense-card__icon {
  width: 1.75rem; /* 28px */
  height: 1.75rem; /* 28px */
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.xero-expense-card__icon--net-pay {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: rgba(var(--color-primary-rgb), 1);
}

.xero-expense-card__icon--tax {
  background-color: rgba(154, 122, 160, 0.1);
  color: rgba(154, 122, 160, 1);
}

.xero-expense-card__icon--super {
  background-color: rgba(var(--color-success-rgb), 0.1);
  color: rgba(var(--color-success-rgb), 1);
}

.dark .xero-expense-card__icon--net-pay {
  background-color: rgba(var(--color-primary-rgb), 0.2);
  color: rgba(var(--color-primary-rgb), 1);
}

.dark .xero-expense-card__icon--tax {
  background-color: rgba(154, 122, 160, 0.2);
  color: rgba(154, 122, 160, 1);
}

.dark .xero-expense-card__icon--super {
  background-color: rgba(var(--color-success-rgb), 0.2);
  color: rgba(var(--color-success-rgb), 1);
}

.xero-expense-card__title {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(40, 39, 38, 1);
}

.dark .xero-expense-card__title {
  color: rgba(242, 240, 229, 1);
}

.xero-expense-card__amount {
  font-size: 1.5rem;
  font-weight: 700;
  margin: var(--space-xs) 0;
}

.xero-expense-card__details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-xs);
  margin: var(--space-sm) 0;
  font-size: 0.875rem;
}

.xero-expense-card__detail {
  display: flex;
  flex-direction: column;
}

.xero-expense-card__detail-label {
  color: rgba(135, 133, 128, 0.8);
  font-size: 0.75rem;
  margin-bottom: 0.125rem;
}

.dark .xero-expense-card__detail-label {
  color: rgba(159, 157, 150, 0.8);
}

.xero-expense-card__detail-value {
  color: rgba(64, 62, 60, 1);
  font-weight: 500;
}

.dark .xero-expense-card__detail-value {
  color: rgba(230, 228, 217, 1);
}

.xero-expense-card__description {
  font-size: 0.875rem;
  color: rgba(135, 133, 128, 1);
  margin-bottom: var(--space-sm);
  line-height: 1.4;
}

.dark .xero-expense-card__description {
  color: rgba(159, 157, 150, 1);
}

.xero-expense-card__button {
  width: 100%;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
}

.xero-expense-card__button--sync {
  background-color: transparent;
  color: rgba(var(--color-primary-rgb), 1);
  border: 1px solid rgba(var(--color-primary-rgb), 0.5);
}

.xero-expense-card__button--sync:hover:not(:disabled) {
  background-color: rgba(var(--color-primary-rgb), 1);
  color: white;
}

.xero-expense-card__button--syncing {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: rgba(var(--color-primary-rgb), 0.7);
  cursor: wait;
}

.xero-expense-card__button--success {
  background-color: rgba(var(--color-success-rgb), 0.1);
  color: rgba(var(--color-success-rgb), 1);
  border: 1px solid rgba(var(--color-success-rgb), 0.5);
}

.xero-expense-card__button--update {
  background-color: rgba(154, 122, 160, 0.1);
  color: rgba(154, 122, 160, 1);
  border: 1px solid rgba(154, 122, 160, 0.5);
}

.xero-expense-card__button--update:hover:not(:disabled) {
  background-color: rgba(154, 122, 160, 1);
  color: white;
}

.dark .xero-expense-card__button--sync {
  color: rgba(var(--color-primary-rgb), 1);
  border-color: rgba(var(--color-primary-rgb), 0.5);
}

.dark .xero-expense-card__button--sync:hover:not(:disabled) {
  background-color: rgba(var(--color-primary-rgb), 1);
  color: white;
}

.dark .xero-expense-card__button--syncing {
  background-color: rgba(var(--color-primary-rgb), 0.2);
  color: rgba(var(--color-primary-rgb), 0.7);
}

.dark .xero-expense-card__button--success {
  background-color: rgba(var(--color-success-rgb), 0.2);
  color: rgba(var(--color-success-rgb), 1);
  border-color: rgba(var(--color-success-rgb), 0.5);
}

.dark .xero-expense-card__button--update {
  background-color: rgba(154, 122, 160, 0.2);
  color: rgba(154, 122, 160, 1);
  border-color: rgba(154, 122, 160, 0.5);
}

.dark .xero-expense-card__button--update:hover:not(:disabled) {
  background-color: rgba(154, 122, 160, 1);
  color: white;
}

/* Spinner animation for loading states */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.xero-spinner {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .xero-filter-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .xero-filter-group {
    width: 100%;
  }

  .xero-expense-card__details {
    grid-template-columns: 1fr;
  }
}
