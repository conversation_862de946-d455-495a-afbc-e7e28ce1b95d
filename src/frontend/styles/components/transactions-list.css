/*
 * TransactionsList Component Styles
 * Uses desktop-first approach with responsive adaptations
 */

/* Container for the entire transactions list */
.transactions-container {
  container-type: inline-size;
  container-name: transactions;
  width: 100%;
}

/* Grid layout for transaction cards */
.transactions-grid {
  display: grid;
  gap: var(--space-sm);
  width: 100%;
}

/* REMOVED: Legacy media queries - now using container queries in foundation.css */
/* Container queries provide better responsive behavior for component-level responsiveness */

/* Table styles */
.table-view {
  width: 100%;
  border-collapse: collapse;
}

.table-view th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--color-gray-50);
  padding: var(--space-sm);
  font-weight: 600;
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200);
}

.table-view td {
  padding: var(--space-sm);
  vertical-align: middle;
}

.table-view tr {
  border-bottom: 1px solid var(--color-gray-200);
  transition: background-color 150ms ease;
}

.table-view tr:hover {
  background-color: var(--color-gray-50);
}

/* Dark mode adjustments */
.dark .table-view th {
  background-color: rgba(55, 65, 81, 0.5);
  border-color: var(--color-gray-700);
}

.dark .table-view tr {
  border-color: var(--color-gray-700);
}

.dark .table-view tr:hover {
  background-color: rgba(55, 65, 81, 0.5);
}

/* Highlighted row */
.table-view tr.highlighted {
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.dark .table-view tr.highlighted {
  background-color: rgba(37, 99, 235, 0.2);
}

/* Container query adaptations */
@container transactions (width < 1200px) {
  .table-view th,
  .table-view td {
    padding: var(--space-xs);
  }
}

/* Mobile-specific styles */
@media (max-width: 640px) {
  .transactions-grid {
    gap: var(--space-xs);
  }
}
