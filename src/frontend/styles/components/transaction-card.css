/* src/frontend/styles/components/transaction-card.css */
.transaction-card {
  container-type: inline-size; /* Enable container queries */
  border-radius: 0.5rem;
  transition: background-color 150ms ease;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm); /* Use fluid gap */
  border-width: 1px; /* Ensure border is always present for consistency */
  overflow: hidden; /* Prevent content overflow */
}

.transaction-card--highlighted {
  background-color: rgba(var(--color-primary-rgb), 0.1); /* Flexoki blue-400 with 10% opacity */
}

.dark .transaction-card--highlighted {
  background-color: rgba(var(--color-primary-rgb), 0.2); /* Flexoki blue-400 with 20% opacity */
}

.transaction-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-card__date-container {
  display: flex;
  align-items: center;
  gap: var(--space-xs); /* Use fluid gap */
}

.transaction-card__icon {
  width: 1em; /* Scale with font size */
  height: 1em; /* Scale with font size */
  flex-shrink: 0;
}

.transaction-card__date {
  /* Font size handled by text-fluid-sm utility */
  white-space: nowrap;
}

.transaction-card__amount-container {
  display: flex;
  align-items: center;
  gap: var(--space-xs); /* Use fluid gap */
  text-align: right;
}

.transaction-card__amount {
  /* Font size handled by text-fluid-base utility */
  white-space: nowrap;
}

.transaction-card__percent {
  /* Styles for PercentageChange component */
  flex-shrink: 0;
}

.transaction-card__description {
  /* Allow wrapping */
  overflow: hidden;
  /* Consider adding max lines / text overflow ellipsis if needed */
}

.transaction-card__desc-primary {
  /* Font size handled by text-fluid-base utility */
  display: block; /* Ensure it takes full width */
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap; */ /* REMOVED - Allow wrapping by default */
}

.transaction-card__desc-secondary {
  /* Font size handled by text-fluid-sm utility */
  display: block; /* Ensure it takes full width */
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap; */ /* REMOVED - Allow wrapping by default */
}

.transaction-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-sm); /* Use fluid gap */
}

.transaction-card__badge {
  display: inline-flex; /* Use inline-flex for proper alignment */
  align-items: center;
  padding: 0.125rem 0.375rem; /* Base padding */
  border-radius: 0.25rem;
  border-width: 1px;
  font-size: 0.75rem; /* Base font size */
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; /* Prevent overflow */
  /* Specific type colors are applied via Tailwind classes in the component */
}

/* Ensure anchor tags within badges behave correctly */
.transaction-card__badge a {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: inherit; /* Inherit color from parent badge */
}

.transaction-card__badge-icon {
  width: 0.75em; /* Scale with font size */
  height: 0.75em; /* Scale with font size */
  margin-right: 0.25em;
  flex-shrink: 0;
}

.transaction-card__badge-dot {
  width: 0.5em;
  height: 0.5em;
  background-color: var(--color-primary); /* Flexoki blue-400 */
  border-radius: 9999px;
  margin-right: 0.375em;
  flex-shrink: 0;
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}

.transaction-card__badge-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.transaction-card__badge-link-icon {
  width: 0.875em; /* Scale with font size */
  height: 0.875em; /* Scale with font size */
  margin-left: 0.375em;
  flex-shrink: 0;
  opacity: 0.7;
}

.transaction-card__badge:hover .transaction-card__badge-link-icon {
  opacity: 1;
}


.transaction-card__balance {
  /* Font size handled by text-fluid-sm utility */
  white-space: nowrap;
  text-align: right;
  flex-shrink: 0;
}

/* Responsive adaptations using container queries */

/* Very small containers (e.g., narrow columns, very small phones) */
@container (width < 320px) {
  .transaction-card {
    gap: var(--space-xs); /* Tighter gap */
  }

  .transaction-card__header {
    /* Stack date and amount vertically */
    flex-direction: column;
    align-items: flex-start;
    gap: 0.125rem;
  }

  .transaction-card__amount-container {
    align-self: flex-end; /* Align amount to the right */
  }

  .transaction-card__description {
    line-height: 1.3;
  }

  .transaction-card__footer {
    /* Stack badge and balance */
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .transaction-card__balance {
    align-self: flex-end; /* Align balance to the right */
  }

  .transaction-card__badge {
    font-size: 0.65rem; /* Even smaller badge text */
    padding: 0.1rem 0.25rem;
  }
}

/* Small to medium containers */
@container (width >= 320px) and (width < 450px) {
  .transaction-card__badge {
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
  }
  .transaction-card__desc-primary {
     white-space: normal; /* Allow wrapping on slightly larger */
  }
   .transaction-card__desc-secondary {
     /* white-space: normal; */ /* No longer needed here */
  }
}

/* Medium containers and up */
@container (width >= 450px) {
  .transaction-card {
    gap: var(--space-sm); /* Slightly larger gap */
  }

  .transaction-card__badge {
    font-size: 0.75rem; /* Slightly larger badge text */
    padding: 0.2rem 0.4rem;
  }
   .transaction-card__desc-primary {
     white-space: normal; /* Allow wrapping */
  }
   .transaction-card__desc-secondary {
     /* white-space: normal; */ /* No longer needed here */
  }
}
