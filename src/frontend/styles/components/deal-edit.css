/* Deal Edit Page - Modern Professional Design */

/* Main Container */
.deal-edit-page {
  /* TODO: removed @apply */
  position: relative;
}

/* Header Wrapper with Gradient */
.deal-header-wrapper {
  /* TODO: removed @apply */
  background: linear-gradient(135deg, rgb(242 240 229), rgb(230 228 217)); /* Flexoki base-50 to base-100 */
  border-bottom: 1px solid rgb(230 228 217); /* Flexoki base-100 */
}

.dark .deal-header-wrapper {
  background: linear-gradient(135deg, rgb(64 62 60), rgb(40 39 38)); /* Flexoki base-800 to base-900 */
  border-bottom-color: rgb(87 86 83); /* Flexoki base-700 */
}

.deal-header-container {
  /* TODO: removed @apply */
}

.deal-header-gradient {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    ellipse at top right,
    rgb(154 122 160 / 0.1), /* Flexoki purple-400 with 10% opacity */
    transparent 50%
  );
  pointer-events: none;
}

.dark .deal-header-gradient {
  background: radial-gradient(
    ellipse at top right,
    rgb(154 122 160 / 0.2), /* Flexoki purple-400 with 20% opacity */
    transparent 50%
  );
}

/* Content Wrapper */
.deal-content-wrapper {
  /* TODO: removed @apply */
}

/* Sidebar Navigation */
.deal-sidebar {
  /* TODO: removed @apply */
}

.sidebar-sticky {
  /* TODO: removed @apply */
}

.sidebar-nav {
  /* TODO: removed @apply */
}

.sidebar-nav-item {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  position: relative;
  overflow: hidden;
}

.sidebar-nav-item.active {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.sidebar-nav-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 3px;
  height: 0;
  background: linear-gradient(to bottom, #9A7AA0, #8B7EC8); /* Flexoki purple-400 to purple-500 */
  transform: translateY(-50%);
  transition: height 0.3s ease;
}

.sidebar-nav-item.active::before {
  height: 70%;
}

.nav-icon {
  /* TODO: removed @apply */
}

.sidebar-nav-item:hover .nav-icon {
  /* TODO: removed @apply */
}

.sidebar-nav-item.active .nav-icon {
  /* TODO: removed @apply */
}

.nav-label {
  /* TODO: removed @apply */
}

.nav-indicator {
  /* TODO: removed @apply */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Sidebar Stats */
.sidebar-stats {
  /* TODO: removed @apply */
}

.sidebar-stats .stat-item {
  /* TODO: removed @apply */
}

.sidebar-stats .stat-label {
  /* TODO: removed @apply */
}

.sidebar-stats .stat-value {
  /* TODO: removed @apply */
}

/* Main Content Area */
.deal-main-content {
  /* TODO: removed @apply */
}

/* Mobile Section Tabs */
.mobile-section-tabs {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.tabs-scroll-container {
  /* TODO: removed @apply */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-scroll-container::-webkit-scrollbar {
  display: none;
}

.section-tab {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.section-tab.active {
  /* TODO: removed @apply */
}

.tab-icon {
  /* TODO: removed @apply */
}

.section-tab:hover .tab-icon {
  /* TODO: removed @apply */
}

.section-tab.active .tab-icon {
  /* TODO: removed @apply */
}

/* Content Sections */
.content-sections {
  /* TODO: removed @apply */
}

.content-section {
  /* TODO: removed @apply */
  animation: fadeIn 0.5s ease-out;
}

/* Section Cards - Modern Design */
.content-section > div {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  position: relative;
  overflow: hidden;
}

.content-section > div::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #9A7AA0, #8B7EC8, #8B7EC8); /* Flexoki purple gradient */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.content-section > div:hover::before {
  transform: scaleX(1);
}

/* Enhanced Card Headers */
.deal-section-header {
  /* TODO: removed @apply */
}

.deal-section-title {
  /* TODO: removed @apply */
}

.deal-section-title h3 {
  /* TODO: removed @apply */
}

.deal-section-icon {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.deal-section-icon svg {
  /* TODO: removed @apply */
}

/* Form Styling Enhancements */
.deal-form-grid {
  /* TODO: removed @apply */
}

.deal-form-field {
  /* TODO: removed @apply */
}

.deal-form-label {
  /* TODO: removed @apply */
}

.deal-form-input {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.deal-form-input:hover:not(:focus) {
  /* TODO: removed @apply */
}

/* Financial Metrics Card */
.financial-metrics-grid {
  /* TODO: removed @apply */
}

.metric-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.metric-label {
  /* TODO: removed @apply */
}

.metric-value {
  /* TODO: removed @apply */
}

.metric-trend {
  /* TODO: removed @apply */
}

.trend-up {
  /* TODO: removed @apply */
}

.trend-down {
  /* TODO: removed @apply */
}

/* Timeline Visualization */
.timeline-container {
  /* TODO: removed @apply */
}

.timeline-line {
  /* TODO: removed @apply */
}

.timeline-item {
  /* TODO: removed @apply */
}

.timeline-marker {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.timeline-content {
  /* TODO: removed @apply */
}

/* Contact & Estimate Cards */
.entity-grid {
  /* TODO: removed @apply */
}

.entity-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

/* Notes Timeline */
.notes-timeline {
  /* TODO: removed @apply */
}

.note-item {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.note-avatar {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

/* Loading States */
.deal-loading {
  /* TODO: removed @apply */
}

.deal-loading-spinner {
  /* TODO: removed @apply */
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 1280px) {
  .deal-header-card .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (max-width: 1024px) {
  .deal-content-wrapper {
    /* TODO: removed @apply */
  }

  .deal-sidebar {
    /* TODO: removed @apply */
  }

  .deal-header-card .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .deal-form-grid {
    /* TODO: removed @apply */
  }

  .financial-metrics-grid {
    /* TODO: removed @apply */
  }

  .entity-grid {
    /* TODO: removed @apply */
  }
}

/* Deal Header Card Styles */
.deal-header-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

/* Metric Highlight Cards */
.metric-highlight-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.metric-highlight-card.cursor-pointer {
  /* TODO: removed @apply */
}

.metric-icon {
  /* TODO: removed @apply */
}

.metric-content {
  /* TODO: removed @apply */
}

.metric-content .metric-label {
  /* TODO: removed @apply */
}

.metric-content .metric-value {
  /* TODO: removed @apply */
}

/* Content Section Card */
.content-section-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  position: relative;
  overflow: hidden;
}

.content-section-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #9A7AA0, #8B7EC8, #8B7EC8); /* Flexoki purple gradient */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.content-section-card:hover::before {
  transform: scaleX(1);
}

/* Info Cards for View Mode */
.info-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.info-card-icon {
  /* TODO: removed @apply */
}

.info-label {
  /* TODO: removed @apply */
}

.info-value {
  /* TODO: removed @apply */
}

/* Description Card */
.description-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

/* Financial Summary Section */
.financial-summary-section {
  /* TODO: removed @apply */
}

.financial-metrics-container {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.financial-metrics-grid {
  /* TODO: removed @apply */
}

.financial-metric-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  /* TODO: removed @apply */
  min-width: 0; /* Allow content to shrink */
  overflow: hidden; /* Prevent content overflow */
}

.metric-icon-wrapper {
  /* TODO: removed @apply */
}

.metric-details {
  /* TODO: removed @apply */
  overflow: hidden; /* Prevent overflow */
}

.metric-details .metric-label {
  /* TODO: removed @apply */
  white-space: nowrap; /* Prevent label wrapping */
}

.metric-details .metric-value {
  /* TODO: removed @apply */
  word-break: break-word; /* Allow long values to wrap */
  line-height: 1.2; /* Tighter line height for wrapped text */
}

/* Section Divider */
.section-divider {
  /* TODO: removed @apply */
}

.section-divider::before {
  content: "";
  /* TODO: removed @apply */
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgb(230 228 217), /* Flexoki base-100 */
    transparent
  );
}

.dark .section-divider::before {
  background: linear-gradient(
    to right,
    transparent,
    rgb(111 110 105), /* Flexoki base-600 */
    transparent
  );
}

.section-divider-content {
  /* TODO: removed @apply */
}

.section-divider-content > * {
  /* TODO: removed @apply */
}

/* Empty State Card */
.empty-state-card {
  /* TODO: removed @apply */
  /* TODO: removed @apply */
}

.empty-state-icon {
  /* TODO: removed @apply */
}

/* Dark Mode Enhancements */
.dark .content-section > div {
  background: linear-gradient(
    135deg,
    rgb(64 62 60 / 0.95), /* Flexoki base-800 */
    rgb(40 39 38 / 0.95) /* Flexoki base-900 */
  );
  border-color: rgb(87 86 83) !important; /* Flexoki base-700 - Ensure dark borders */
}

/* Container Queries for Responsive Components */
@container (min-width: 640px) {
  .deal-form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container (min-width: 768px) {
  .financial-metrics-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
