/* Enhanced Allocation Table Styles */

.allocation-table {
  --cell-padding: 8px;
  --focus-color: var(--color-success); /* Flexoki green-600 */
  --hover-color: #F2F0E5; /* Flexoki base-50 */
  --drag-handle-color: #9F9D96; /* Flexoki base-400 */
  --border-color: #E6E4D9; /* Flexoki base-100 */
  --edit-border-color: var(--color-success); /* Flexoki green-600 */
  table-layout: fixed;
}

/* Dark mode variables */
.dark .allocation-table {
  --hover-color: #403E3C; /* Flexoki base-800 */
  --drag-handle-color: #6F6E69; /* Flexoki base-600 */
  --border-color: #575653; /* Flexoki base-700 */
}

/* Cell transitions */
.allocation-table td {
  transition: background-color 0.15s ease, box-shadow 0.15s ease;
}

/* Editable cells */
.allocation-table td[role="gridcell"]:not([readonly]) {
  position: relative;
}

/* Hover effects for editable cells */
.allocation-table td[role="gridcell"]:not([readonly]):hover {
  background-color: var(--hover-color);
  cursor: text;
}

/* Edit indicator */
.allocation-table td[role="gridcell"]:not([readonly])::after {
  content: '';
  position: absolute;
  top: 2px;
  right: 2px;
  width: 4px;
  height: 4px;
  background-color: var(--drag-handle-color);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.allocation-table td[role="gridcell"]:not([readonly]):hover::after {
  opacity: 0.5;
}

/* Focus styles */
.allocation-table td[role="gridcell"]:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px var(--focus-color);
  background-color: white;
  z-index: 1;
}

.dark .allocation-table td[role="gridcell"]:focus {
  background-color: #282726; /* Flexoki base-900 */
}

/* Drag and drop styles */
.allocation-table tr[draggable="true"] {
  cursor: move;
}

.allocation-table tr.dragging {
  opacity: 0.5;
}

/* Drag handle */
.drag-handle {
  opacity: 0;
  transition: opacity 0.2s ease;
  user-select: none;
}

.allocation-table tr:hover .drag-handle {
  opacity: 0.5;
}

.drag-handle:hover {
  opacity: 1 !important;
  color: #6F6E69; /* Flexoki base-600 */
}

.dark .drag-handle:hover {
  color: #CECDC3; /* Flexoki base-200 */
}

/* Drop indicators */
.allocation-table tr.drop-target-before {
  box-shadow: 0 -2px 0 0 var(--focus-color);
}

.allocation-table tr.drop-target-after {
  box-shadow: 0 2px 0 0 var(--focus-color);
}

/* Input styles within cells */
.allocation-table input[type="text"],
.allocation-table input[type="number"] {
  font-size: inherit;
  font-family: inherit;
  line-height: 1.2;
  padding: 2px 4px;
  width: 100%;
  text-align: center;
}

/* Sticky columns must have opaque backgrounds */
.allocation-table .sticky {
  background-color: inherit;
}

/* Prevent content bleed-through */
.allocation-table th.sticky,
.allocation-table td.sticky {
  position: sticky;
  background-clip: padding-box;
}

/* Ensure proper stacking context */
.allocation-table tbody tr {
  position: relative;
}

/* Placeholder row styling */
.allocation-table tr.placeholder-row {
  font-style: italic;
  opacity: 0.7;
}

/* Hours/Days toggle button group */
.unit-toggle-group {
  display: inline-flex;
  border-radius: 0.375rem;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.unit-toggle-group button {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border: none;
  background-color: transparent;
  transition: all 0.15s ease;
}

.unit-toggle-group button:not(:last-child) {
  border-right: 1px solid var(--border-color);
}

.unit-toggle-group button.active {
  background-color: #EDEECF; /* Flexoki green-50 */
  color: #536907; /* Flexoki green-700 */
}

.dark .unit-toggle-group button.active {
  background-color: #3D4C07; /* Flexoki green-800 */
  color: #CDD597; /* Flexoki green-150 */
}

/* Fullscreen mode adjustments */
.fullscreen-container {
  background-color: white;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.dark .fullscreen-container {
  background-color: #100F0F; /* Flexoki black */
}

.fullscreen-container .allocation-table {
  flex: 1;
  overflow: auto;
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .allocation-table {
    font-size: 0.75rem;
  }
  
  .allocation-table th,
  .allocation-table td {
    padding: 0.25rem;
  }
  
  .allocation-table col {
    min-width: 60px;
  }
}

/* Print styles */
@media print {
  .allocation-table {
    font-size: 10pt;
  }
  
  .drag-handle,
  .unit-toggle-group,
  button {
    display: none !important;
  }
  
  .allocation-table tr {
    page-break-inside: avoid;
  }
}

/* Keyboard navigation indicator */
.allocation-table[data-keyboard-nav="true"] td[role="gridcell"]:focus {
  box-shadow: inset 0 0 0 2px var(--focus-color), 0 0 0 3px rgba(var(--color-success-rgb), 0.1); /* Flexoki green-600 with 10% opacity */
}

/* Loading state */
.allocation-table.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.allocation-table.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--focus-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Fade in animation for precision hint */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Smooth scrolling for keyboard navigation */
.allocation-table-wrapper {
  scroll-behavior: smooth;
}

/* Ensure overflow container works with sticky positioning */
.overflow-x-auto {
  position: relative;
  scroll-behavior: smooth;
}

/* Cell validation states */
.allocation-table td[data-invalid="true"] {
  background-color: #FFE1D5; /* Flexoki red-50 */
}

.dark .allocation-table td[data-invalid="true"] {
  background-color: #6C201C; /* Flexoki red-800 */
}

/* Totals row emphasis */
.allocation-table tfoot tr {
  font-weight: 600;
  background-color: #F2F0E5; /* Flexoki base-50 */
  border-top: 2px solid var(--border-color);
}

.dark .allocation-table tfoot tr {
  background-color: #282726; /* Flexoki base-900 */
  border-top-color: #575653; /* Flexoki base-700 */
}