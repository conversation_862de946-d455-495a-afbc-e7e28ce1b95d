/* src/frontend/styles/components/decision-card.css */

.decision-card {
  /* Base styles inherited from .adaptive-card (defined elsewhere, likely shared/card.css) */
  /* Add specific overrides or additions for decision cards if needed */
  container-type: inline-size; /* Enable container queries */
}

.decision-card__main {
  /* Styles for the main clickable area */
  /* Uses flex, gap-1 from Tailwind */
}

.decision-card__expand-indicator {
  /* Styles for the chevron icon */
  /* Uses w-4, h-4, transition-transform from Tailwind */
}

.decision-card__expanded-content {
  /* Styles for the expanded section */
  /* Uses border-t, p-fluid-sm, bg-surface-alt bg-opacity-50, dark:bg-surface-page dark:bg-opacity-20 from Tailwind */
  /* Consider adding transition for smooth expand/collapse */
  overflow: hidden;
  transition: max-height 0.3s ease-out, padding 0.3s ease-out, opacity 0.3s ease-out;
  max-height: 0; /* Collapsed by default */
  padding-top: 0;
  padding-bottom: 0;
  opacity: 0;
}

/* Apply styles when expanded (driven by React state adding/removing content) */
/* We rely on the conditional rendering in React; CSS handles the transition */
.decision-card__main[aria-expanded="true"] + .decision-card__expanded-content {
  max-height: 500px; /* Adjust as needed for max content height */
  padding-top: var(--space-sm); /* Restore padding */
  padding-bottom: var(--space-sm);
  opacity: 1;
}

/* Example Container Query (adjust breakpoint and styles as needed) */
/* @container (min-width: 400px) { */
  /* .decision-card__main { */
    /* Maybe switch to a horizontal layout? */
    /* flex-direction: row; */
    /* align-items: center; */
  /* } */
/* } */
