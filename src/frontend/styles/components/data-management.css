/**
 * Data Management Component Styles
 * Enhanced table design with modern UI patterns
 */

/* Enhanced Table Container */
.data-management-table {
  /* TODO: removed @apply */
}

/* Table Styling */
.data-management-table table {
  /* TODO: removed @apply */
}

/* Table Header */
.data-management-table thead {
  /* TODO: removed @apply */
}

.data-management-table th {
  /* TODO: removed @apply */
}

/* Table Body */
.data-management-table tbody {
  /* TODO: removed @apply */
}

.data-management-table td {
  /* TODO: removed @apply */
}

/* Row Hover Effect */
.data-management-table tbody tr {
  /* TODO: removed @apply */
}

.data-management-table tbody tr:hover {
  /* TODO: removed @apply */
}

/* Company Cell */
.company-cell {
  /* TODO: removed @apply */
}

.company-name {
  /* TODO: removed @apply */
}

.company-source {
  /* TODO: removed @apply */
}

/* Specific column widths */
.data-management-table th:nth-child(1),
.data-management-table td:nth-child(1) {
  /* TODO: removed @apply */
}

.data-management-table th:nth-child(2),
.data-management-table td:nth-child(2) {
  /* TODO: removed @apply */
}

.data-management-table th:nth-child(3),
.data-management-table td:nth-child(3) {
  /* TODO: removed @apply */
}

.data-management-table th:nth-child(4),
.data-management-table td:nth-child(4) {
  /* TODO: removed @apply */
}

/* Status Cell Enhancements */
.status-cell {
  /* TODO: removed @apply */
}

/* Link Status Cell - New Design */
.link-status-cell {
  /* TODO: removed @apply */
}

/* Compact Link Status Container */
.link-status-container {
  /* TODO: removed @apply */
}

/* Linked State */
.link-status-container.linked {
  /* TODO: removed @apply */
}

.link-status-container.linked.hubspot {
  /* TODO: removed @apply */
}

.link-status-container.linked.harvest {
  /* TODO: removed @apply */
}

/* Unlinked State */
.link-status-container.unlinked {
  /* TODO: removed @apply */
}

/* System Icon */
.link-system-icon {
  /* TODO: removed @apply */
}

.link-system-icon.linked {
  /* TODO: removed @apply */
}

.link-system-icon.unlinked {
  /* TODO: removed @apply */
}

/* Status Text */
.link-status-text {
  /* TODO: removed @apply */
}

.link-status-text.linked {
  /* TODO: removed @apply */
}

.link-status-text.unlinked {
  /* TODO: removed @apply */
}

/* External ID */
.link-external-id {
  /* TODO: removed @apply */
}

/* Link/Unlink Actions */
.link-action-button {
  /* TODO: removed @apply */
}

.link-action-button.link {
  /* TODO: removed @apply */
}

.link-action-button.unlink {
  /* TODO: removed @apply */
}

/* Responsive Design */
@container (max-width: 768px) {
  .data-management-table th,
  .data-management-table td {
    /* TODO: removed @apply */
  }
  
  .link-status-container {
    /* TODO: removed @apply */
  }
  
  .link-system-icon {
    /* TODO: removed @apply */
  }
}

/* Mobile Table Scrolling Fix */
@media (max-width: 768px) {
  .data-management-table {
    /* TODO: removed @apply */
    -webkit-overflow-scrolling: touch;
  }
  
  /* Ensure table maintains minimum width for readability */
  .data-management-table table {
    min-width: 600px;
  }
}

/* Loading State */
.link-status-container.loading {
  /* TODO: removed @apply */
}

/* Stats Cards Enhancement */
.stat-card {
  /* TODO: removed @apply */
}

.stat-card::before {
  content: '';
  /* TODO: removed @apply */
}

.stat-card.total::before {
  /* TODO: removed @apply */
}

.stat-card.linked::before {
  /* TODO: removed @apply */
}

.stat-card.hubspot::before {
  /* TODO: removed @apply */
}

.stat-card.harvest::before {
  /* TODO: removed @apply */
}

.stat-card.unlinked::before {
  /* TODO: removed @apply */
}

/* Stat Value Animation */
.stat-value {
  /* TODO: removed @apply */
}

.stat-card:hover .stat-value {
  /* TODO: removed @apply */
}