/**
 * @file Centralized color constants and utilities
 * 
 * This file provides a comprehensive color system for the application,
 * using the Flexoki theme as the single source of truth.
 * All colors are imported from flexoki-theme.ts to ensure consistency.
 */

import { flexokiPalette, flexokiChartColors, getFinancialColors as getFlexokiFinancialColors } from "../../styles/flexoki-theme";

/**
 * Core color palette using Flexoki values
 * These colors are used consistently across different features
 */
export const coreColors = {
  // Primary blues
  blue: {
    100: flexokiPalette.blue[100],
    200: flexokiPalette.blue[200], 
    300: flexokiPalette.blue[300],
    500: flexokiPalette.blue[500],
    600: flexokiPalette.blue[600],
    800: flexokiPalette.blue[800]
  },
  // Secondary colors (mapped to Flexoki)
  emerald: {
    100: flexokiPalette.green[100],
    200: flexokiPalette.green[200],
    300: flexokiPalette.green[300],
    500: flexokiPalette.green[500],
    600: flexokiPalette.green[600]
  },
  purple: {
    100: flexokiPalette.purple[100],
    200: flexokiPalette.purple[200],
    300: flexokiPalette.purple[300],
    500: flexokiPalette.purple[500],
    600: flexokiPalette.purple[600]
  },
  // Additional colors
  teal: {
    200: flexokiPalette.cyan[200],
    300: flexokiPalette.cyan[300],
    500: flexokiPalette.cyan[500],
    600: flexokiPalette.cyan[600]
  },
  amber: {
    100: flexokiPalette.orange[100],
    200: flexokiPalette.orange[200],
    500: flexokiPalette.orange[500],
    600: flexokiPalette.orange[600],
    800: flexokiPalette.orange[800]
  },
  // Status colors
  red: {
    100: flexokiPalette.red[100],
    200: flexokiPalette.red[200],
    500: flexokiPalette.red[500],
    800: flexokiPalette.red[800]
  },
  green: {
    100: flexokiPalette.green[100],
    200: flexokiPalette.green[200], 
    500: flexokiPalette.green[500],
    800: flexokiPalette.green[800]
  },
  // Neutral colors
  gray: {
    100: flexokiPalette.base[100],
    200: flexokiPalette.base[200],
    600: flexokiPalette.base[600],
    800: flexokiPalette.base[800]
  },
  slate: {
    100: flexokiPalette.base[100],
    200: flexokiPalette.base[200],
    700: flexokiPalette.base[700],
    800: flexokiPalette.base[800]
  }
};

/**
 * Task visualization colors (hex format for charts)
 * Using Flexoki chart colors for consistency
 */
export const taskColors: string[] = [
  flexokiPalette.blue[600],    // Billable tasks
  flexokiPalette.orange[600],  // Public Holiday  
  flexokiPalette.purple[600],  // Annual Leave
  flexokiPalette.cyan[600],    // Other Unpaid Leave
  flexokiPalette.yellow[600],  // Salesforce
  flexokiPalette.magenta[600], // Interviews
  flexokiPalette.green[600],   // Additional colors
  flexokiPalette.blue[400],
  flexokiPalette.orange[400],
  flexokiPalette.purple[400],
  flexokiPalette.cyan[400],
  flexokiPalette.yellow[400],
  flexokiPalette.magenta[400],
  flexokiPalette.green[400],
  flexokiPalette.red[600],
  flexokiPalette.base[500],
  flexokiPalette.base[600],
  flexokiPalette.base[700],
  flexokiPalette.red[400],
  flexokiPalette.red[700],
];

/**
 * Expense type color mapping
 * Using semantic tokens from the design system
 */
export const expenseTypeColors = {
  'Monthly Payroll": "bg-primary-light text-primary-dark border-primary/20 dark:border-primary/30",
  'Superannuation": "bg-warning-light text-warning-dark border-warning/20 dark:border-warning/30",
  'Insurances": "bg-magenta-light text-magenta-dark border-magenta/20 dark:border-magenta/30",
  'Taxes": "bg-primary-light text-primary-dark border-primary/20 dark:border-primary/30",
  'Subcontractor Fees": "bg-accent-light text-accent-dark border-accent/20 dark:border-accent/30",
  'Rent": "bg-success-light text-success-dark border-success/20 dark:border-success/30",
  'Reimbursements": "bg-info-light text-info-dark border-info/20 dark:border-info/30",
  'Professional Fees": "bg-info-light text-info-dark border-info/20 dark:border-info/30",
  'General Expenses": "bg-info-light text-info-dark border-info/20 dark:border-info/30",
  'Director Distributions": "bg-accent-light text-accent-dark border-accent/20 dark:border-accent/30",
  'Hardware": "bg-magenta-light text-magenta-dark border-magenta/20 dark:border-magenta/30",
  'Subscriptions": "bg-error-light text-error-dark border-error/20 dark:border-error/30",
  'Other Fees": "bg-warning-light text-warning-dark border-warning/20 dark:border-warning/30",
  'Other': "bg-surface-alt text-primary border-default dark:border-strong"
} as const;

/**
 * Expense frequency color mapping
 * Using semantic tokens from the design system
 */
export const expenseFrequencyColors = {
  'one-off": "bg-surface-alt text-secondary border-default dark:border-strong",
  'weekly": "bg-primary-light text-primary-dark border-primary/20 dark:border-primary/30",
  'monthly": "bg-accent-light text-accent-dark border-accent/20 dark:border-accent/30",
  'quarterly': "bg-warning-light text-warning-dark border-warning/20 dark:border-warning/30"
} as const;

/**
 * Tax calendar color schemes
 * Using semantic tokens from the design system
 */
export const taxCalendarColors = {
  paygw: {
    light: [
      'bg-primary-light', 'bg-success-light', 'bg-accent-light', 'bg-info-light',
      'bg-accent-light', 'bg-success-light', 'bg-primary-light', 'bg-info-light',
      'bg-primary-color', 'bg-success', 'bg-accent', 'bg-info'
    ],
    dark: [
      'bg-primary-light/20', 'bg-success-light/20', 'bg-accent-light/20', 'bg-info-light/20',
      'bg-accent-light/20', 'bg-success-light/20', 'bg-primary-light/20', 'bg-info-light/20',
      'bg-primary-color', 'bg-success', 'bg-accent', 'bg-info'
    ],
    payment: {
      light: [
        'bg-primary-light border-2 border-primary',
        'bg-success-light border-2 border-success',
        'bg-accent-light border-2 border-accent',
        'bg-info-light border-2 border-info',
        'bg-accent-light border-2 border-accent',
        'bg-success-light border-2 border-success',
        'bg-primary-light border-2 border-primary',
        'bg-info-light border-2 border-info',
        'bg-primary-color border-2 border-primary-dark',
        'bg-success border-2 border-success-dark',
        'bg-accent border-2 border-accent-dark',
        'bg-info border-2 border-info-dark'
      ],
      dark: [
        'bg-primary-light/20 border-2 border-primary-light',
        'bg-success-light/20 border-2 border-success-light',
        'bg-accent-light/20 border-2 border-accent-light',
        'bg-info-light/20 border-2 border-info-light',
        'bg-accent-light/20 border-2 border-accent-light',
        'bg-success-light/20 border-2 border-success-light',
        'bg-primary-light/20 border-2 border-primary-light',
        'bg-info-light/20 border-2 border-info-light',
        'bg-primary-color border-2 border-primary-light',
        'bg-success border-2 border-success-light',
        'bg-accent border-2 border-accent-light',
        'bg-info border-2 border-info-light'
      ]
    }
  },
  gst: {
    light: ['bg-warning-light', 'bg-error-light', 'bg-warning', 'bg-magenta-light'],
    dark: ['bg-warning-light/20', 'bg-error-light/20', 'bg-warning', 'bg-magenta'],
    payment: {
      light: [
        'bg-warning-light border-2 border-warning',
        'bg-error-light border-2 border-error',
        'bg-warning border-2 border-warning-dark',
        'bg-magenta-light border-2 border-magenta'
      ],
      dark: [
        'bg-warning-light/20 border-2 border-warning-light',
        'bg-error-light/20 border-2 border-error-light',
        'bg-warning border-2 border-warning-light",
        'bg-magenta border-2 border-magenta-light"
      ]
    }
  }
};

/**
 * Financial color standards
 * Using semantic tokens from the design system
 */
export const financialColors = {
  income: {
    light: "text-success",
    dark: "text-success-light",
    bg: "bg-success-light dark:bg-success-dark/30"
  },
  expense: {
    light: "text-error", 
    dark: "text-error-light",
    bg: "bg-error-light dark:bg-error-dark/30"
  },
  neutral: {
    light: "text-secondary",
    dark: "text-subtle",
    bg: "bg-surface-alt dark:bg-surface-alt/30"
  }
};

/**
 * Status color scheme
 * Using semantic tokens from the design system
 */
export const statusColors = {
  success: "bg-success-light text-success-dark border-success/20 dark:bg-success-dark/30 dark:text-success-light",
  warning: "bg-warning-light text-warning-dark border-warning/20 dark:bg-warning-dark/30 dark:text-warning-light",
  error: "bg-error-light text-error-dark border-error/20 dark:bg-error-dark/30 dark:text-error-light",
  info: "bg-info-light text-info-dark border-info/20 dark:bg-info-dark/30 dark:text-info-light",
  neutral: "bg-surface-alt text-secondary border-default dark:bg-surface-alt dark:text-subtle"
};

/**
 * Utility functions for color management
 */

/**
 * Get a color for a task based on its ID and properties
 * Ensures consistent colors across different views
 */
export const getTaskColor = (taskId: number, taskName: string, isBillable: boolean): string => {
  // Billable tasks are always blue
  if (isBillable) {
    return taskColors[0];
  }

  // Map common task names to specific colors
  const commonTaskColors: Record<string, string> = {
    'Public Holiday': taskColors[1],
    'Annual Leave': taskColors[2], 
    'Other Unpaid Leave': taskColors[3],
    'Salesforce': taskColors[4],
    'Interviews': taskColors[5],
  };

  // Check if this is a common task with a predefined color
  if (commonTaskColors[taskName]) {
    return commonTaskColors[taskName];
  }

  // For other non-billable tasks, use a hash of the task ID to pick a consistent color
  const hash = taskId % (taskColors.length - 1);
  return taskColors[hash + 1]; // +1 to skip the billable color
};

/**
 * Get CSS class for expense type
 */
export const getExpenseTypeClass = (type: keyof typeof expenseTypeColors | string): string => {
  return expenseTypeColors[type as keyof typeof expenseTypeColors] || expenseTypeColors['Other"];
};

/**
 * Get CSS class for expense frequency
 */
export const getExpenseFrequencyClass = (frequency: keyof typeof expenseFrequencyColors | string): string => {
  return expenseFrequencyColors[frequency as keyof typeof expenseFrequencyColors] || statusColors.neutral;
};

/**
 * Get tax calendar color class
 */
export const getTaxCalendarColor = (
  type: "paygw" | "gst",
  index: number,
  isPayment: boolean,
  isDarkMode: boolean,
  isHighlighted: boolean
): string => {
  const colorSet = taxCalendarColors[type];
  
  // Determine which color variant to use based on theme and highlight state
  const variant = isDarkMode 
    ? (isHighlighted ? 'light' : "dark")
    : (isHighlighted ? 'dark" : "light");
  
  if (isPayment) {
    return colorSet.payment[variant][index % colorSet.payment[variant].length];
  }
  
  return colorSet[variant][index % colorSet[variant].length];
};

// Legacy tax calendar colors for backward compatibility
export const paygwColors = {
  normal: "#4385BE",    // Flexoki blue-400
  bg_normal: "#E1ECEB", // Light blue background approximation
  important: "#DA702C", // Flexoki orange-400
  bg_important: "#FFE7CE", // Flexoki orange-50
  urgent: "#AF3029",    // Flexoki red-600
  bg_urgent: "#FFE1D5"  // Flexoki red-50
};

export const gstColors = {
  normal: "#66800B",    // Flexoki green-600
  bg_normal: "#EDEECF", // Flexoki green-50
  important: "#DA702C", // Flexoki orange-400
  bg_important: "#FFE7CE", // Flexoki orange-50
  urgent: "#AF3029",    // Flexoki red-600
  bg_urgent: "#FFE1D5"  // Flexoki red-50
};

/**
 * Legacy getColorClass function for backward compatibility
 * @deprecated Use getTaxCalendarColor instead for new code
 */
export const getColorClass = (
  type: "paygw" | "gst",
  importance: "normal" | "important" | "urgent",
  background: boolean = false
): string => {
  const colors = type === 'paygw' ? paygwColors : gstColors;
  const key = background ? `bg_${importance}` : importance;
  return colors[key as keyof typeof colors];
};

/**
 * Get financial color based on value (positive/negative)
 * Delegates to Flexoki theme for consistency
 */
export const getFinancialColor = (value: number, isDarkMode = false): string => {
  const flexokiColors = getFlexokiFinancialColors(isDarkMode);
  
  if (value > 0) {
    return isDarkMode ? 'text-success-light' : "text-success";
  } else if (value < 0) {
    return isDarkMode ? 'text-error-light' : "text-error";
  }
  return isDarkMode ? 'text-subtle' : "text-secondary";
};

/**
 * Get a set of colors for a collection of tasks
 * This ensures each task gets a unique color when possible
 */
export const getTaskColorMap = (
  tasks: Array<{ taskId: number; taskName: string; isBillable: boolean }>
): Record<number, string> => {
  const colorMap: Record<number, string> = {};
  const usedColors = new Set<string>();

  // Common task name to color mapping for consistency
  const commonTaskColors: Record<string, string> = {
    'Billable': taskColors[0],
    'Public Holiday': taskColors[1],
    'Annual Leave': taskColors[2],
    'Other Unpaid Leave': taskColors[3],
    'Salesforce': taskColors[4],
    'Interviews': taskColors[5],
  };

  // First, assign colors to billable tasks (all blue)
  tasks.filter(t => t.isBillable).forEach(task => {
    colorMap[task.taskId] = taskColors[0];
  });

  // Then, assign unique colors to non-billable tasks
  const nonBillableTasks = tasks.filter(t => !t.isBillable);

  // Sort by task ID to ensure consistent color assignment
  nonBillableTasks.sort((a, b) => a.taskId - b.taskId);

  nonBillableTasks.forEach(task => {
    // First check if this is a common task with a predefined color
    if (commonTaskColors[task.taskName]) {
      colorMap[task.taskId] = commonTaskColors[task.taskName];
      usedColors.add(commonTaskColors[task.taskName]);
      return;
    }

    // Try to get a color based on task ID
    let colorIndex = (task.taskId % (taskColors.length - 1)) + 1; // +1 to skip billable color
    let color = taskColors[colorIndex];

    // If this color is already used, find another one
    if (usedColors.has(color)) {
      // Find the first unused color
      for (let i = 1; i < taskColors.length; i++) {
        if (!usedColors.has(taskColors[i])) {
          color = taskColors[i];
          break;
        }
      }

      // If all colors are used, fall back to the original algorithm
      if (usedColors.has(color)) {
        color = taskColors[colorIndex];
      }
    }

    colorMap[task.taskId] = color;
    usedColors.add(color);
  });

  return colorMap;
};

/**
 * Company color palette (hex format for inline styles)
 * Using Flexoki colors for consistency
 */
export const companyColors = [
  flexokiPalette.blue[600],
  flexokiPalette.green[600],
  flexokiPalette.purple[600],
  flexokiPalette.orange[600],
  flexokiPalette.magenta[600],
  flexokiPalette.cyan[600],
  flexokiPalette.red[600],
  flexokiPalette.yellow[600],
];

/**
 * Generate consistent colors for a company based on its name
 * Returns an object with backgroundColor and other color properties
 */
export const generateCompanyColors = (companyName: string): { backgroundColor: string } => {
  // Simple hash function to get consistent color for the same company name
  const hash = companyName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const backgroundColor = companyColors[hash % companyColors.length];

  return {
    backgroundColor
  };
};