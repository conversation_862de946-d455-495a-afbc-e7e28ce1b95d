/**
 * Utility functions for working with Gravatar
 */

import md5 from "blueimp-md5";

/**
 * Generate a Gravatar URL for a given email address
 *
 * Following Gravatar's official documentation:
 * 1. Trim leading and trailing whitespace
 * 2. Force all characters to lower-case
 * 3. Create an MD5 hash of the email address
 *
 * @param email The email address to generate a Gravatar URL for
 * @param size The size of the Gravatar image (default: 80)
 * @param defaultImage The default image to use if no Gravatar is found
 * @returns The Gravatar URL
 */
export const getGravatarUrl = (
  email: string,
  size: number = 80,
  defaultImage: string = "identicon",
): string => {
  try {
    // Trim and lowercase the email as per Gravatar requirements
    const normalizedEmail = email.trim().toLowerCase();

    // Create an MD5 hash of the email address using the blueimp-md5 library
    const hash = md5(normalizedEmail);

    // Construct the Gravatar URL
    return `https://www.gravatar.com/avatar/${hash}?s=${size}&d=${defaultImage}`;
  } catch (error) {
    // If there's any error, return a default image
    console.error("Error generating Gravatar URL:", error);
    return `https://www.gravatar.com/avatar/00000000000000000000000000000000?s=${size}&d=${defaultImage}`;
  }
};
