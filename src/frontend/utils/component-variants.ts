/**
 * Component variant utility - Type-safe component styling system
 * Provides a consistent way to manage component variants using Tailwind classes
 */

type VariantConfig<T extends Record<string, any>> = {
  base: string;
  variants: T;
  defaultVariants?: Partial<{
    [K in keyof T]: keyof T[K];
  }>;
};

type VariantProps<T extends VariantConfig<any>> = Partial<{
  [K in keyof T['variants']]: keyof T['variants'][K];
}>;

/**
 * Creates a variant function for consistent component styling
 */
export function createVariants<T extends VariantConfig<any>>(config: T) {
  return (props: VariantProps<T> & { className?: string }) => {
    const { className, ...variantProps } = props;

    // Start with base classes
    let classes = config.base;

    // Apply default variants
    const resolvedProps = { ...config.defaultVariants, ...variantProps };

    // Apply variant classes
    Object.entries(resolvedProps).forEach(([key, value]) => {
      if (value && config.variants[key]?.[value as string]) {
        classes += ` ${config.variants[key][value as string]}`;
      }
    });

    // Add custom className if provided
    if (className) {
      classes += ` ${className}`;
    }

    return classes.trim();
  };
}

/**
 * Utility function to conditionally join classNames
 */
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ");
}

/**
 * Card component variants
 */
export const cardVariants = createVariants({
  base: "card",
  variants: {
    variant: {
      default: "",
      interactive: "card--interactive",
      bordered: "card--bordered",
    },
    size: {
      compact: "card--compact",
      default: "",
      comfortable: "card--comfortable",
    },
    shadow: {
      none: "shadow-none",
      sm: "shadow-sm",
      default: "shadow-card",
      md: "shadow-md",
      lg: "shadow-lg",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    shadow: "default",
  }
});

/**
 * Button component variants
 */
export const buttonVariants = createVariants({
  base: "btn",
  variants: {
    variant: {
      primary: "btn--primary",
      secondary: "btn--secondary",
      outline: "btn--outline",
      ghost: "btn--ghost",
      danger: "btn--danger",
      success: "btn--success",
    },
    size: {
      sm: "btn--sm",
      default: "",
      lg: "btn--lg",
    },
    state: {
      default: "",
      loading: "btn--loading",
    }
  },
  defaultVariants: {
    variant: "primary",
    size: "default",
    state: "default",
  }
});

/**
 * Badge component variants
 */
export const badgeVariants = createVariants({
  base: "badge",
  variants: {
    variant: {
      primary: "badge--primary",
      secondary: "badge--secondary",
      success: "badge--success",
      warning: "badge--warning",
      danger: "badge--danger",
      outline: "badge--outline",
      // Financial-specific badge variants
      predicted: "bg-warning-light/30 text-warning",
      accrued: "bg-accent-light/30 text-accent",
      neutral: "bg-surface-alt text-secondary",
    },
    size: {
      sm: "text-xs px-2 py-0.5",
      default: "text-xs px-2.5 py-0.5",
      lg: "text-sm px-3 py-1",
    },
    withIcon: {
      true: "flex items-center",
      false: "",
    }
  },
  defaultVariants: {
    variant: "primary",
    size: "default",
    withIcon: false,
  }
});

/**
 * Icon badge variants for logo/image badges
 */
export const iconBadgeVariants = createVariants({
  base: "inline-block flex-shrink-0",
  variants: {
    size: {
      sm: "w-4 h-4",
      default: "w-5 h-5",
      lg: "w-6 h-6",
    },
    rounded: {
      none: "",
      sm: "rounded-sm",
      default: "rounded",
      full: "rounded-full",
    }
  },
  defaultVariants: {
    size: "default",
    rounded: "default",
  }
});

// === FORM COMPONENT VARIANTS ===

/**
 * Input component variants
 */
export const inputVariants = createVariants({
  base: "form-input",
  variants: {
    variant: {
      default: "",
      error: "form-input--error",
      success: "border-success focus:border-success focus:ring-success",
    },
    size: {
      sm: "text-sm py-1 px-2 h-[30px]",
      default: "text-sm py-2 px-3 h-[38px]",
      lg: "text-base py-3 px-4 h-[46px]",
    },
    withIcon: {
      true: "pl-10",
      false: "",
    },
    withError: {
      true: "pr-10",
      false: "",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    withIcon: false,
    withError: false,
  }
});

/**
 * Select component variants
 */
export const selectVariants = createVariants({
  base: "form-input",
  variants: {
    variant: {
      default: "",
      error: "form-input--error",
      success: "border-success focus:border-success focus:ring-success",
    },
    size: {
      sm: "text-sm py-1 px-2 h-[30px]",
      default: "text-sm py-2 px-3 h-[38px]",
      lg: "text-base py-3 px-4 h-[46px]",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  }
});

/**
 * Textarea component variants
 */
export const textareaVariants = createVariants({
  base: "form-input",
  variants: {
    variant: {
      default: "",
      error: "form-input--error",
      success: "border-success focus:border-success focus:ring-success",
    },
    size: {
      sm: "text-sm py-1 px-2 min-h-[60px]",
      default: "text-sm py-2 px-3 min-h-[80px]",
      lg: "text-base py-3 px-4 min-h-[100px]",
    },
    resize: {
      none: "resize-none",
      vertical: "resize-y",
      horizontal: "resize-x",
      both: "resize",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    resize: "vertical",
  }
});

/**
 * Form field wrapper variants
 */
export const formFieldVariants = createVariants({
  base: "form-field",
  variants: {
    variant: {
      default: "",
      inline: "flex items-center space-x-3",
      stacked: "space-y-1",
    },
    size: {
      sm: "mb-3",
      default: "mb-4",
      lg: "mb-6",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  }
});

// === LIST COMPONENT VARIANTS ===

/**
 * List container variants
 */
export const listVariants = createVariants({
  base: "list",
  variants: {
    variant: {
      default: "",
      compact: "list--compact",
      comfortable: "list--comfortable",
    },
    layout: {
      stack: "list--stack",
      grid: "list--grid",
      table: "list--table",
      cards: "list--cards",
    },
    spacing: {
      none: "space-y-0",
      sm: "space-y-1",
      default: "space-y-2",
      md: "space-y-3",
      lg: "space-y-4",
      xl: "space-y-6",
    }
  },
  defaultVariants: {
    variant: "default",
    layout: "stack",
    spacing: "default",
  }
});

/**
 * List item variants
 */
export const listItemVariants = createVariants({
  base: "list-item",
  variants: {
    variant: {
      default: "",
      interactive: "list-item--interactive",
      selected: "list-item--selected",
      disabled: "list-item--disabled",
    },
    size: {
      compact: "list-item--compact",
      default: "",
      comfortable: "list-item--comfortable",
    },
    border: {
      none: "",
      default: "list-item--bordered",
      left: "list-item--border-left",
      full: "list-item--border-full",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    border: "none",
  }
});

/**
 * Table variants for enhanced table components
 */
export const tableVariants = createVariants({
  base: "table",
  variants: {
    variant: {
      default: "",
      striped: "table--striped",
      bordered: "table--bordered",
      compact: "table--compact",
    },
    size: {
      sm: "table--sm",
      default: "",
      lg: "table--lg",
    },
    responsive: {
      true: "table--responsive",
      false: "",
    }
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    responsive: true,
  }
});

/**
 * Data list variants for data-driven lists
 */
export const dataListVariants = createVariants({
  base: "data-list",
  variants: {
    variant: {
      default: "",
      cards: "data-list--cards",
      table: "data-list--table",
      grid: "data-list--grid",
    },
    density: {
      compact: "data-list--compact",
      default: "",
      comfortable: "data-list--comfortable",
    },
    loading: {
      true: "data-list--loading",
      false: "",
    }
  },
  defaultVariants: {
    variant: "default",
    density: "default",
    loading: false,
  }
});

/**
 * Type exports for component props
 */
export type CardVariantProps = VariantProps<typeof cardVariants>;
export type ButtonVariantProps = VariantProps<typeof buttonVariants>;
export type BadgeVariantProps = VariantProps<typeof badgeVariants>;
export type IconBadgeVariantProps = VariantProps<typeof iconBadgeVariants>;
export type InputVariantProps = VariantProps<typeof inputVariants>;
export type SelectVariantProps = VariantProps<typeof selectVariants>;
export type TextareaVariantProps = VariantProps<typeof textareaVariants>;
export type FormFieldVariantProps = VariantProps<typeof formFieldVariants>;
export type ListVariantProps = VariantProps<typeof listVariants>;
export type ListItemVariantProps = VariantProps<typeof listItemVariants>;
export type TableVariantProps = VariantProps<typeof tableVariants>;
export type DataListVariantProps = VariantProps<typeof dataListVariants>;