/**
 * Utility functions for working with quarters
 */

/**
 * Get information about the current quarter
 * @returns Object with quarter information
 */
export const getCurrentQuarterInfo = (): {
  quarter: number;
  quarterName: string;
  quarterLabel: string;
  financialYear: string;
  daysElapsed: number;
  daysInQuarter: number;
  percentComplete: number;
} => {
  const today = new Date();
  const currentMonth = today.getMonth(); // 0-indexed (0 = January)
  const currentYear = today.getFullYear();

  // Determine current quarter (1-4)
  let quarter: number;
  let quarterStartMonth: number;
  let quarterEndMonth: number;

  if (currentMonth >= 0 && currentMonth <= 2) {
    // Q3 of financial year (Jan-Mar)
    quarter = 3;
    quarterStartMonth = 0; // January
    quarterEndMonth = 2; // March
  } else if (currentMonth >= 3 && currentMonth <= 5) {
    // Q4 of financial year (Apr-Jun)
    quarter = 4;
    quarterStartMonth = 3; // April
    quarterEndMonth = 5; // June
  } else if (currentMonth >= 6 && currentMonth <= 8) {
    // Q1 of financial year (Jul-Sep)
    quarter = 1;
    quarterStartMonth = 6; // July
    quarterEndMonth = 8; // September
  } else {
    // Q2 of financial year (Oct-Dec)
    quarter = 2;
    quarterStartMonth = 9; // October
    quarterEndMonth = 11; // December
  }

  // Determine financial year (e.g., "2023-24")
  let financialYearStart: number;
  let financialYearEnd: number;

  if (currentMonth >= 6) {
    // July onwards
    financialYearStart = currentYear;
    financialYearEnd = currentYear + 1;
  } else {
    // Before July
    financialYearStart = currentYear - 1;
    financialYearEnd = currentYear;
  }

  const financialYear = `${financialYearStart}-${String(financialYearEnd).slice(2)}`;

  // Calculate days elapsed in the quarter
  const quarterStart = new Date(currentYear, quarterStartMonth, 1);
  const quarterEnd = new Date(currentYear, quarterEndMonth + 1, 0); // Last day of end month

  const daysElapsed = Math.floor(
    (today.getTime() - quarterStart.getTime()) / (1000 * 60 * 60 * 24),
  );
  const daysInQuarter =
    Math.floor(
      (quarterEnd.getTime() - quarterStart.getTime()) / (1000 * 60 * 60 * 24),
    ) + 1;
  const percentComplete = (daysElapsed / daysInQuarter) * 100;

  // Quarter name mapping
  const quarterNames = ["Jul-Sep", "Oct-Dec", "Jan-Mar", "Apr-Jun"];
  const quarterName = quarterNames[quarter - 1];

  // Quarter label (Q1, Q2, etc.)
  const quarterLabel = `Q${quarter}`;

  return {
    quarter,
    quarterName,
    quarterLabel,
    financialYear,
    daysElapsed,
    daysInQuarter,
    percentComplete,
  };
};
