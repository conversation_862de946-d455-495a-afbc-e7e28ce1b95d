/**
 * Feature Flags API client
 */

import { FeatureFlag } from "../../types/feature-flags";

interface FeatureFlagConfig {
  name: string;
  description: string;
  defaultValue: boolean;
  category: "experimental" | "beta" | "stable";
}

interface FeatureFlagData {
  flag: string;
  enabled: boolean;
  config: FeatureFlagConfig;
}

interface AllFeatureFlagsResponse {
  [key: string]: {
    enabled: boolean;
    config: FeatureFlagConfig;
  };
}

/**
 * Get all feature flags
 */
export async function getAllFeatureFlags(): Promise<AllFeatureFlagsResponse> {
  const response = await fetch('/api/feature-flags');
  
  if (!response.ok) {
    throw new Error(`Failed to fetch feature flags: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data;
}

/**
 * Get a specific feature flag
 */
export async function getFeatureFlag(flag: FeatureFlag): Promise<FeatureFlagData> {
  const response = await fetch(`/api/feature-flags/${flag}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch feature flag ${flag}: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data;
}

/**
 * Update a feature flag
 */
export async function updateFeatureFlag(flag: FeatureFlag, enabled: boolean): Promise<FeatureFlagData> {
  const response = await fetch(`/api/feature-flags/${flag}`, {
    method: "PUT",
    headers: {
      'Content-Type': "application/json"
    },
    body: JSON.stringify({ enabled })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to update feature flag ${flag}: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data;
}

/**
 * Toggle a feature flag
 */
export async function toggleFeatureFlag(flag: FeatureFlag): Promise<FeatureFlagData> {
  const response = await fetch(`/api/feature-flags/${flag}/toggle`, {
    method: "POST"
  });
  
  if (!response.ok) {
    throw new Error(`Failed to toggle feature flag ${flag}: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data;
}

/**
 * Reset a feature flag to its default value
 */
export async function resetFeatureFlag(flag: FeatureFlag): Promise<FeatureFlagData> {
  const response = await fetch(`/api/feature-flags/${flag}/reset`, {
    method: "POST"
  });
  
  if (!response.ok) {
    throw new Error(`Failed to reset feature flag ${flag}: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data;
}

/**
 * Reset all feature flags to their default values
 */
export async function resetAllFeatureFlags(): Promise<AllFeatureFlagsResponse> {
  const response = await fetch('/api/feature-flags/reset-all', {
    method: "POST"
  });
  
  if (!response.ok) {
    throw new Error(`Failed to reset all feature flags: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data.flags;
}

/**
 * Get feature flags by category
 */
export async function getFeatureFlagsByCategory(category: "experimental" | "beta" | "stable"): Promise<FeatureFlagData[]> {
  const response = await fetch(`/api/feature-flags/category/${category}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch feature flags by category ${category}: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data.flags;
}

/**
 * Enable or disable all experimental features
 */
export async function updateExperimentalFeatures(action: "enable" | "disable"): Promise<FeatureFlagData[]> {
  const response = await fetch(`/api/feature-flags/experimental/${action}`, {
    method: "POST"
  });
  
  if (!response.ok) {
    throw new Error(`Failed to ${action} experimental features: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data.flags;
}

// Feature flag cache for client-side performance
let featureFlagCache: AllFeatureFlagsResponse | null = null;
let cacheExpiry = 0;
const CACHE_DURATION = 60000; // 1 minute

/**
 * Check if a feature flag is enabled (with caching)
 */
export async function isFeatureEnabled(flag: FeatureFlag): Promise<boolean> {
  try {
    // Check cache first
    if (featureFlagCache && Date.now() < cacheExpiry) {
      return featureFlagCache[flag]?.enabled ?? true;
    }
    
    // Fetch fresh data
    const flags = await getAllFeatureFlags();
    
    // Update cache
    featureFlagCache = flags;
    cacheExpiry = Date.now() + CACHE_DURATION;
    
    return flags[flag]?.enabled ?? true;
  } catch (error) {
    console.error('Error checking feature flag:', error);
    // Default to enabled on error
    return true;
  }
}

/**
 * Clear the feature flag cache
 */
export function clearFeatureFlagCache(): void {
  featureFlagCache = null;
  cacheExpiry = 0;
}

/**
 * React hook for feature flags
 */
import { useState, useEffect } from "react";

export function useFeatureFlag(flag: FeatureFlag): {
  enabled: boolean;
  loading: boolean;
  error: Error | null;
  toggle: () => Promise<void>;
  refresh: () => Promise<void>;
} {
  const [enabled, setEnabled] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const refresh = async () => {
    try {
      setLoading(true);
      setError(null);
      const isEnabled = await isFeatureEnabled(flag);
      setEnabled(isEnabled);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
      // Default to enabled on error
      setEnabled(true);
    } finally {
      setLoading(false);
    }
  };
  
  const toggle = async () => {
    try {
      setLoading(true);
      setError(null);
      await toggleFeatureFlag(flag);
      clearFeatureFlagCache();
      await refresh();
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    refresh();
  }, [flag]);
  
  return { enabled, loading, error, toggle, refresh };
}