// The old eventBus is no longer used - we'll use console logging directly

// Helper function to safely stringify objects with possible circular references
const safeStringify = (obj: unknown): string => {
  if (obj === null || obj === undefined) {
    return String(obj);
  }

  if (typeof obj !== 'object') {
    return String(obj);
  }

  // Handle special objects
  if (obj instanceof Error) {
    return `Error: ${obj.message}${obj.stack ? `\n${obj.stack}` : ""}`;
  }

  try {
    // Use a set to track objects we've seen
    const seen = new WeakSet();

    // Custom replacer function to handle circular references
    const replacer = (_key: string, value: unknown) => {
      // Skip non-object values
      if (typeof value !== 'object' || value === null) {
        return value;
      }

      // Handle DOM nodes
      if (value instanceof Node) {
        return `[DOM Node: ${value.nodeName}]`;
      }

      // Check for circular reference
      if (seen.has(value)) {
        return '[Circular Reference]';
      }

      // Add this object to the set of seen objects
      seen.add(value);

      return value;
    };

    return JSON.stringify(obj, replacer, 2);
  } catch (err) {
    return `[Object - Failed to stringify: ${err instanceof Error ? err.message : String(err)}]`;
  }
};

/**
 * Fetch data from API with error handling, automatic retry for 401s, and logging
 * @param url API URL
 * @param options Fetch options
 * @returns Response data
 */
// TypeScript interfaces for API responses
interface ApiError extends Error {
  status?: number;
  isRetryable?: boolean;
  isNetworkError?: boolean;
}

interface ErrorResponse {
  message?: string;
  redirectUrl?: string;
}

// Helper function to wait for a specified duration
const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const fetchFromApi = async (url: string, options: RequestInit & { timeout?: number } = {}): Promise<unknown> => {
  // Generate request ID for correlation
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  // Retry configuration
  const MAX_RETRIES = 3;
  const INITIAL_RETRY_DELAY = 1000; // 1 second
  const MAX_RETRY_DELAY = 10000; // 10 seconds
  const REQUEST_TIMEOUT = options.timeout || 30000; // Use custom timeout or 30 seconds default
  
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
    try {
      // Only add cache-busting for auth-related endpoints to prevent rate limiting
      const isAuthEndpoint = url.includes('/auth') || url.includes('/logout');
      const finalUrl = isAuthEndpoint ? (
        url.includes('?')
          ? `${url}&_t=${Date.now()}`
          : `${url}?_t=${Date.now()}`
      ) : url;

      // Log the request
      if (attempt > 0) {
        console.log(`API Request Retry ${attempt}/${MAX_RETRIES}: ${options.method || "GET"} ${url}`, {
          requestId,
          url,
          method: options.method || "GET",
          attempt,
        });
      } else {
        console.log(`API Request: ${options.method || "GET"} ${url}`, {
          requestId,
          url,
          method: options.method || "GET",
        });
      }

      const startTime = Date.now();
      
      // Create an AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);
      
      try {
        // Extract timeout before passing to fetch
        const { timeout, ...fetchOptions } = options;
        
        const response = await fetch(finalUrl, {
          ...fetchOptions,
          signal: controller.signal,
          credentials: "include", // Include cookies for auth
          headers: {
            'Content-Type': "application/json",
            'X-Requested-With': "XMLHttpRequest",
            'Cache-Control': "no-cache, no-store",
            'Pragma': "no-cache",
            'X-Request-ID': requestId,
            ...(fetchOptions.headers || {})
          }
        });
        
        // Clear the timeout
        clearTimeout(timeoutId);

        const duration = Date.now() - startTime;

        // Clone response before reading it (because reading the body consumes it)
        const responseClone = response.clone();

        if (!response.ok) {
          // Try to parse the error message from the response
          try {
            const errorData = await response.json() as ErrorResponse;

            // Log the error response
            console.error(`API Error Response: ${response.status} ${response.statusText} for ${options.method || "GET"} ${url} (${duration}ms)`, {
              requestId,
              status: response.status,
              statusText: response.statusText,
              url,
              method: options.method || "GET",
              data: errorData,
              duration
            });

            // Enhanced handling for 401 errors, with special logic for preview deployments
            const isPreviewDeployment = window.location.hostname.includes('preview');
            if (response.status === 401) {
              console.log('Got 401 error:', errorData);

              // Check if we're in a preview deployment
              if (isPreviewDeployment) {
                console.log('In preview deployment, checking fallback auth options');

                // Perform advanced cookie diagnostics
                const allCookies = document.cookie.split(';').map(c => c.trim());
                const authCookies = allCookies.filter(c =>
                  c.startsWith('preview_auth=') ||
                  c.startsWith('preview_auth_alt=') ||
                  c.startsWith('preview_auth_api=') ||
                  c.startsWith('auth_test=')
                );

                console.log(`Found ${authCookies.length} auth-related cookies:`,
                  authCookies.map(c => c.split('=')[0])
                );

                // First check if we have a redirectUrl provided in the error response
                if (errorData?.redirectUrl) {
                  console.log('Using redirect URL from API:', errorData.redirectUrl);

                  // Add cache-busting timestamp to prevent browser cache issues
                  const redirectWithTimestamp = errorData.redirectUrl.includes('?')
                    ? `${errorData.redirectUrl}&_t=${Date.now()}`
                    : `${errorData.redirectUrl}?_t=${Date.now()}`;

                  // Only redirect if we're not already in a redirect loop
                  if (!window.location.href.includes('first_load=true')) {
                    window.location.href = redirectWithTimestamp;
                    return null; // Prevent further processing
                  } else {
                    console.log('Detected potential redirect loop, using direct auth endpoint');
                    window.location.href = `/api/xero/auth?_t=${Date.now()}`;
                    return null;
                  }
                }
                // If no redirectUrl but we have auth cookies, try refreshing the page
                else if (authCookies.length > 0 && !window.location.href.includes('refresh=true')) {
                  console.log('Auth cookies exist but not recognized by API, refreshing page');

                  // Add a special parameter to detect refresh loops
                  const currentUrl = new URL(window.location.href);
                  currentUrl.searchParams.set('refresh', 'true');
                  currentUrl.searchParams.set('_t', Date.now().toString());

                  window.location.href = currentUrl.toString();
                  return null;
                }
                // Last resort: direct auth endpoint
                else {
                  console.log('Using direct auth endpoint as last resort');
                  window.location.href = `/api/xero/auth?_t=${Date.now()}`;
                  return null;
                }
              }
              // For non-preview deployments, use simpler handling
              else if (errorData?.redirectUrl) {
                console.log('Non-preview deployment, using provided redirect URL');
                window.location.href = errorData.redirectUrl;
                return null;
              }
            }

            // Create ApiError with proper status for better error handling
            const apiError = new Error(errorData.message || `Error: ${response.status} ${response.statusText}`) as ApiError;
            apiError.status = response.status;
            apiError.isRetryable = response.status >= 500 || response.status === 429;
            throw apiError;
          } catch (e) {
            // If the error was already an ApiError, rethrow it
            if (e instanceof Error && 'status' in e) {
              throw e;
            }
            
            // Log parse error
            console.error(`API Error: Failed to parse error response for ${options.method || "GET"} ${url}`, {
              requestId,
              error: e instanceof Error ? e.message : String(e),
              status: response.status,
              statusText: response.statusText
            });

            const apiError = new Error(`Error: ${response.status} ${response.statusText}`) as ApiError;
            apiError.status = response.status;
            apiError.isRetryable = response.status >= 500 || response.status === 429;
            throw apiError;
          }
        }

        // For successful responses
        // Handle 204 No Content responses (no body to parse)
        if (response.status === 204) {
          console.log(`API Response: ${response.status} ${response.statusText} for ${options.method || "GET"} ${url} (${duration}ms)`, {
            requestId,
            status: response.status,
            statusText: response.statusText,
            url,
            method: options.method || "GET",
            duration
          });
          return null;
        }

        try {
          const responseData = await responseClone.json();

          // Log successful response (with limited data to avoid huge logs)
          // Safely get the size by using our safe stringify function
          const serializedSize = (() => {
            try {
              return safeStringify(responseData).length;
            } catch (e) {
              return -1; // Indicate we couldn't determine size
            }
          })();

          const responseInfo = {
            requestId,
            status: response.status,
            statusText: response.statusText,
            url,
            method: options.method || "GET",
            dataSize: serializedSize,
            duration,
          };

          console.log(`API Response: ${response.status} ${response.statusText} for ${options.method || "GET"} ${url} (${duration}ms)`, responseInfo);

          return responseData;
        } catch (e) {
          // Log JSON parse error but continue with original response
          console.warn(`API Warning: Could not parse JSON response for logging purposes`, {
            requestId,
            error: e instanceof Error ? e.message : String(e)
          });

          // Still try to parse the original response
          return await response.json();
        }
      } catch (fetchError) {
        // Clear timeout if fetch fails
        clearTimeout(timeoutId);
        
        // Check if it's an abort error (timeout)
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          const timeoutError = new Error(`Request timed out after ${REQUEST_TIMEOUT}ms`) as ApiError;
          timeoutError.isRetryable = true;
          timeoutError.isNetworkError = true;
          throw timeoutError;
        }
        
        throw fetchError;
      }
    } catch (error) {
      const apiError = error as ApiError;
      lastError = apiError;
      
      // Log network or other fetch errors
      console.error(`API Fetch Error: ${apiError.message || "Unknown error"} for ${options.method || "GET"} ${url}`, {
        requestId,
        url,
        method: options.method || "GET",
        error: apiError.message || "Unknown error",
        stack: apiError.stack,
        attempt,
        willRetry: attempt < MAX_RETRIES && (apiError.isRetryable || apiError.isNetworkError || !apiError.status)
      });

      // Check if we're in a preview deployment for extra debug info
      if (window.location.hostname.includes('preview')) {
        console.log('Preview deployment detected in API error handler');
        console.log('Current cookies:', document.cookie);
      }

      // Determine if we should retry
      const shouldRetry = attempt < MAX_RETRIES && (
        apiError.isRetryable || 
        apiError.isNetworkError || 
        !apiError.status || // Network errors don't have status
        apiError.status >= 500 || 
        apiError.status === 429
      );

      if (shouldRetry) {
        // Calculate delay with exponential backoff
        const baseDelay = INITIAL_RETRY_DELAY * Math.pow(2, attempt);
        const delay = Math.min(baseDelay, MAX_RETRY_DELAY);
        
        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + (Math.random() * 1000);
        
        console.log(`Retrying request in ${jitteredDelay}ms...`);
        await wait(jitteredDelay);
        continue; // Try again
      }

      // If we've exhausted retries or error is not retryable, throw
      break;
    }
  }

  // If we get here, we've exhausted all retries
  const finalError = lastError || new Error('Failed to fetch from API');
  const networkError = finalError as ApiError;
  networkError.isNetworkError = true;
  throw networkError;
};