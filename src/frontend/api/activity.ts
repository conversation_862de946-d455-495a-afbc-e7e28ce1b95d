/**
 * Activity API Client
 *
 * This module provides functions for interacting with the activity feed API.
 */

import { 
  Activity, 
  ActivityCreate, 
  ActivityUpdate, 
  ActivityFilters, 
  ActivityFeedResponse,
  ActivityStats 
} from "../types/activity-types";

const API_BASE = '/api/activity';

/**
 * Get activity feed with optional filters
 */
export async function getActivityFeed(filters: ActivityFilters = {}): Promise<ActivityFeedResponse> {
  const params = new URLSearchParams();

  // Add filters to query parameters
  if (filters.type) {
    if (Array.isArray(filters.type)) {
      filters.type.forEach(type => params.append('type', type));
    } else {
      params.append('type', filters.type);
    }
  }

  if (filters.source) {
    if (Array.isArray(filters.source)) {
      filters.source.forEach(source => params.append('source', source));
    } else {
      params.append('source', filters.source);
    }
  }

  if (filters.entityType) {
    params.append('entityType', filters.entityType);
  }

  if (filters.entityId) {
    params.append('entityId', filters.entityId);
  }

  if (filters.createdBy) {
    params.append('createdBy', filters.createdBy);
  }

  if (filters.isRead !== undefined) {
    params.append('isRead', filters.isRead.toString());
  }

  if (filters.importance) {
    params.append('importance', filters.importance);
  }

  if (filters.dateFrom) {
    params.append('dateFrom', filters.dateFrom);
  }

  if (filters.dateTo) {
    params.append('dateTo', filters.dateTo);
  }

  if (filters.search) {
    params.append('search', filters.search);
  }

  if (filters.limit) {
    params.append('limit', filters.limit.toString());
  }

  if (filters.offset) {
    params.append('offset', filters.offset.toString());
  }

  const url = params.toString() ? `${API_BASE}?${params.toString()}` : API_BASE;
  
  const response = await fetch(url, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get activity feed: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get recent activities
 */
export async function getRecentActivities(limit: number = 20, offset: number = 0): Promise<Activity[]> {
  const params = new URLSearchParams({
    limit: limit.toString(),
    offset: offset.toString(),
  });

  const response = await fetch(`${API_BASE}/recent?${params.toString()}`, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get recent activities: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get activity statistics
 */
export async function getActivityStats(): Promise<ActivityStats> {
  const response = await fetch(`${API_BASE}/stats`, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get activity stats: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get unread activity count
 */
export async function getUnreadCount(userId?: string): Promise<number> {
  const params = userId ? new URLSearchParams({ userId }) : new URLSearchParams();
  const url = params.toString() ? `${API_BASE}/unread-count?${params.toString()}` : `${API_BASE}/unread-count`;

  const response = await fetch(url, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get unread count: ${response.statusText}`);
  }

  const result = await response.json();
  return result.count;
}

/**
 * Get activities for a specific entity
 */
export async function getActivitiesForEntity(
  entityType: string, 
  entityId: string, 
  limit: number = 50
): Promise<Activity[]> {
  const params = new URLSearchParams({ limit: limit.toString() });

  const response = await fetch(`${API_BASE}/entity/${entityType}/${entityId}?${params.toString()}`, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get activities for entity: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get specific activity by ID
 */
export async function getActivity(id: string): Promise<Activity> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get activity: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Create a new activity
 */
export async function createActivity(data: ActivityCreate): Promise<Activity> {
  const response = await fetch(API_BASE, {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Failed to create activity: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Update an activity
 */
export async function updateActivity(id: string, data: ActivityUpdate): Promise<Activity> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Failed to update activity: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Mark activities as read
 */
export async function markActivitiesAsRead(activityIds: string[]): Promise<boolean> {
  const response = await fetch(`${API_BASE}/mark-read`, {
    method: 'PUT',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ activityIds }),
  });

  if (!response.ok) {
    throw new Error(`Failed to mark activities as read: ${response.statusText}`);
  }

  const result = await response.json();
  return result.success;
}

/**
 * Delete an activity
 */
export async function deleteActivity(id: string): Promise<boolean> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'DELETE',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to delete activity: ${response.statusText}`);
  }

  const result = await response.json();
  return result.success;
}
