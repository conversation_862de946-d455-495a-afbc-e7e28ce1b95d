/**
 * Harvest Invoice Cache API Client
 * 
 * Frontend utilities for managing Harvest invoice cache.
 */

export interface HarvestInvoiceCache {
  harvestClientId: number;
  totalInvoiced: number;
  invoiceCount: number;
  lastUpdated: string;
  createdAt: string;
}

export interface CacheStats {
  totalEntries: number;
  expiredEntries: number;
  validEntries: number;
}

/**
 * Get cache statistics
 */
export const getCacheStats = async (): Promise<CacheStats> => {
  const response = await fetch('/api/harvest-cache/stats');
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || "Failed to get cache stats");
  }
  
  return data.data;
};

/**
 * Get all cached invoice totals
 */
export const getAllCachedTotals = async (): Promise<HarvestInvoiceCache[]> => {
  const response = await fetch('/api/harvest-cache/totals');
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || "Failed to get cached totals");
  }
  
  return data.data;
};

/**
 * Get cached invoice total for specific client
 */
export const getCachedTotal = async (clientId: number): Promise<HarvestInvoiceCache | null> => {
  const response = await fetch(`/api/harvest-cache/client/${clientId}`);
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || "Failed to get cached total");
  }
  
  return data.data;
};

/**
 * Refresh invoice cache from Harvest API
 */
export const refreshCache = async (clientId?: number): Promise<HarvestInvoiceCache[]> => {
  const response = await fetch('/api/harvest-cache/refresh', {
    method: "POST",
    headers: {
      'Content-Type': "application/json",
    },
    body: JSON.stringify({ clientId }),
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || "Failed to refresh cache");
  }
  
  return data.data;
};

/**
 * Get fresh invoice total for specific client (bypasses cache)
 */
export const getFreshTotal = async (clientId: number): Promise<HarvestInvoiceCache | null> => {
  const response = await fetch(`/api/harvest-cache/client/${clientId}/fresh`);
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || "Failed to get fresh total");
  }
  
  return data.data;
};

/**
 * Clear expired cache entries
 */
export const clearExpiredCache = async (): Promise<{ entriesCleared: number }> => {
  const response = await fetch('/api/harvest-cache/expired', {
    method: "DELETE",
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.message || "Failed to clear expired cache");
  }
  
  return data.data;
};
