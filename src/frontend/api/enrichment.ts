/**
 * Enrichment API Client
 *
 * Frontend API client for triggering and managing data enrichment
 */

import { fetchFromApi } from "./utils";

export interface EnrichmentResult {
  source: string;
  success: boolean;
  data?: any;
  error?: string;
  confidence: number;
}

export interface CompanyEnrichmentResponse {
  company: any;
  enrichment: any[];
  results: EnrichmentResult[];
}

/**
 * Trigger enrichment for a company
 */
export async function enrichCompany(
  companyId: string,
  sources?: string[],
): Promise<CompanyEnrichmentResponse> {
  const response = await fetchFromApi(`/api/enrichment/company/${companyId}`, {
    method: "POST",
    body: JSON.stringify({ sources }),
  });

  return response as CompanyEnrichmentResponse;
}

/**
 * Get enrichment data for a company
 */
export async function getCompanyEnrichment(
  companyId: string,
  source?: string,
): Promise<any> {
  const params = new URLSearchParams();
  if (source) {
    params.append("source", source);
  }

  const url = `/api/enrichment/company/${companyId}${params.toString() ? `?${params}` : ""}`;
  const response = await fetchFromApi(url);

  return response;
}

/**
 * Check if a company needs enrichment
 */
export async function checkCompanyEnrichmentStatus(companyId: string): Promise<{
  needsEnrichment: boolean;
  lastEnrichedAt?: string;
  enrichmentStatus?: any;
}> {
  const response = await fetchFromApi(
    `/api/enrichment/company/${companyId}/check`,
  );

  return response as {
    needsEnrichment: boolean;
    lastEnrichedAt?: string;
    enrichmentStatus?: any;
  };
}

/**
 * Get enrichment statistics
 */
export async function getEnrichmentStats(): Promise<any> {
  const response = await fetchFromApi("/api/enrichment/stats");

  return response;
}
