/**
 * Tender API Client
 * 
 * Frontend API client for tender operations
 */

import { BaseTender, TenderQualificationStatus } from "../../types/shared-types";

// API base URL
const API_BASE = '/api/tenders';

/**
 * Tender filters for API requests
 */
export interface TenderFilters {
  qualificationStatus?: TenderQualificationStatus;
  includeDeleted?: boolean;
}

/**
 * Tender statistics
 */
export interface TenderStats {
  total: number;
  new: number;
  reviewing: number;
  interested: number;
  notInterested: number;
  closingSoon: number;
}

/**
 * Qualify tender request
 */
export interface QualifyTenderRequest {
  status: TenderQualificationStatus;
  reason?: string;
  qualifiedBy?: string;
}

/**
 * Tender API client
 */
export const tenderApi = {
  /**
   * Get all tenders with optional filtering
   */
  async getAllTenders(filters?: TenderFilters): Promise<BaseTender[]> {
    const params = new URLSearchParams();
    
    if (filters?.qualificationStatus) {
      params.append('qualificationStatus', filters.qualificationStatus);
    }
    
    if (filters?.includeDeleted) {
      params.append('includeDeleted', 'true');
    }
    
    const response = await fetch(`${API_BASE}?${params}`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch tenders');
    }
    
    return response.json();
  },

  /**
   * Get tender statistics
   */
  async getTenderStats(): Promise<TenderStats> {
    const response = await fetch(`${API_BASE}/stats`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch tender statistics');
    }
    
    return response.json();
  },

  /**
   * Get tenders closing soon
   */
  async getTendersClosingSoon(days: number = 7): Promise<BaseTender[]> {
    const response = await fetch(`${API_BASE}/closing-soon?days=${days}`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch tenders closing soon');
    }
    
    return response.json();
  },

  /**
   * Get a specific tender
   */
  async getTenderById(id: string): Promise<BaseTender> {
    const response = await fetch(`${API_BASE}/${id}`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch tender');
    }
    
    return response.json();
  },

  /**
   * Create a new tender
   */
  async createTender(tender: Partial<BaseTender>): Promise<BaseTender> {
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(tender)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create tender');
    }
    
    return response.json();
  },

  /**
   * Update a tender
   */
  async updateTender(id: string, updates: Partial<BaseTender>): Promise<BaseTender> {
    const response = await fetch(`${API_BASE}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(updates)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update tender');
    }
    
    return response.json();
  },

  /**
   * Qualify a tender (move through workflow)
   */
  async qualifyTender(id: string, request: QualifyTenderRequest): Promise<BaseTender> {
    const response = await fetch(`${API_BASE}/${id}/qualify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to qualify tender');
    }
    
    return response.json();
  },

  /**
   * Delete a tender (soft delete)
   */
  async deleteTender(id: string): Promise<void> {
    const response = await fetch(`${API_BASE}/${id}`, {
      method: 'DELETE',
      credentials: 'include'
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete tender');
    }
  },

  /**
   * Get tenders for a specific company
   */
  async getTendersByCompany(companyId: string): Promise<BaseTender[]> {
    const response = await fetch(`${API_BASE}/company/${companyId}`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch tenders for company');
    }
    
    return response.json();
  }
};

/**
 * React Query hooks for tender operations
 */
import { useQuery, useMutation, useQueryClient } from "react-query";

/**
 * Hook to fetch all tenders
 */
export function useTenders(filters?: TenderFilters) {
  return useQuery(
    ['tenders', filters],
    () => tenderApi.getAllTenders(filters),
    {
      staleTime: 30000, // 30 seconds
      cacheTime: 5 * 60 * 1000 // 5 minutes
    }
  );
}

/**
 * Hook to fetch tender statistics
 */
export function useTenderStats() {
  return useQuery(
    'tenderStats',
    () => tenderApi.getTenderStats(),
    {
      staleTime: 60000, // 1 minute
      cacheTime: 5 * 60 * 1000 // 5 minutes
    }
  );
}

/**
 * Hook to fetch tenders closing soon
 */
export function useTendersClosingSoon(days: number = 7) {
  return useQuery(
    ['tendersClosingSoon', days],
    () => tenderApi.getTendersClosingSoon(days),
    {
      staleTime: 60000, // 1 minute
      cacheTime: 5 * 60 * 1000 // 5 minutes
    }
  );
}

/**
 * Hook to fetch a specific tender
 */
export function useTender(id: string) {
  return useQuery(
    ['tender', id],
    () => tenderApi.getTenderById(id),
    {
      enabled: !!id,
      staleTime: 30000, // 30 seconds
      cacheTime: 5 * 60 * 1000 // 5 minutes
    }
  );
}

/**
 * Hook to qualify a tender
 */
export function useQualifyTender() {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ id, request }: { id: string; request: QualifyTenderRequest }) => 
      tenderApi.qualifyTender(id, request),
    {
      onSuccess: (updatedTender) => {
        // Invalidate and refetch tender queries
        queryClient.invalidateQueries('tenders');
        queryClient.invalidateQueries('tenderStats');
        queryClient.invalidateQueries(['tender', updatedTender.id]);
        
        // Update the tender in cache
        queryClient.setQueryData(['tender', updatedTender.id], updatedTender);
      }
    }
  );
}

/**
 * Hook to create a tender
 */
export function useCreateTender() {
  const queryClient = useQueryClient();
  
  return useMutation(
    (tender: Partial<BaseTender>) => tenderApi.createTender(tender),
    {
      onSuccess: () => {
        // Invalidate and refetch tender queries
        queryClient.invalidateQueries('tenders');
        queryClient.invalidateQueries('tenderStats');
      }
    }
  );
}

/**
 * Hook to update a tender
 */
export function useUpdateTender() {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ id, updates }: { id: string; updates: Partial<BaseTender> }) => 
      tenderApi.updateTender(id, updates),
    {
      onSuccess: (updatedTender) => {
        // Invalidate and refetch tender queries
        queryClient.invalidateQueries('tenders');
        queryClient.setQueryData(['tender', updatedTender.id], updatedTender);
      }
    }
  );
}

/**
 * Hook to delete a tender
 */
export function useDeleteTender() {
  const queryClient = useQueryClient();
  
  return useMutation(
    (id: string) => tenderApi.deleteTender(id),
    {
      onSuccess: (_, id) => {
        // Invalidate and refetch tender queries
        queryClient.invalidateQueries('tenders');
        queryClient.invalidateQueries('tenderStats');
        queryClient.removeQueries(['tender', id]);
      }
    }
  );
}