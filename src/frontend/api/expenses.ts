import axios from "axios";
import { CustomExpense } from "../../types";

/**
 * Get all custom expenses
 * @returns Custom expenses
 */
export const getExpenses = async (): Promise<CustomExpense[]> => {
  try {
    // Use relative path
    const response = await axios.get("/api/expenses", {
      withCredentials: true,
    });

    // Convert string dates back to Date objects
    const expenses = response.data.data.map((expense: any) => ({
      ...expense,
      date: new Date(expense.date),
    }));

    return expenses;
  } catch (error: any) {
    console.error("Error fetching expenses:", error);
    throw new Error(error.response?.data?.error || "Failed to fetch expenses");
  }
};

/**
 * Create a new custom expense
 * @param expense Custom expense data
 * @returns Created expense
 */
export const createExpense = async (
  expense: Omit<CustomExpense, "id">,
): Promise<CustomExpense> => {
  try {
    // Use relative path
    console.log('Sending expense create request to: ", "/api/expenses');
    console.log("Expense data:", JSON.stringify(expense));

    // Add timeout and retry logic
    const response = await axios.post("/api/expenses", expense, {
      withCredentials: true,
      timeout: 15000, // 15 second timeout
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });

    console.log(
      "Create expense response:",
      response.status,
      response.statusText,
    );

    // Convert string date back to Date object
    return {
      ...response.data.data,
      date: new Date(response.data.data.date),
    };
  } catch (error: any) {
    console.error("Error creating expense:", error);

    // More detailed error logging
    if (error.code === "ERR_NETWORK") {
      console.error("Network error details:", {
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          withCredentials: error.config?.withCredentials,
        },
      });
    }

    throw new Error(error.response?.data?.error || "Failed to create expense");
  }
};

/**
 * Update an existing custom expense
 * @param id Expense ID
 * @param expense Updated expense data
 * @returns Updated expense
 */
export const updateExpense = async (
  id: string,
  expense: Omit<CustomExpense, "id">,
): Promise<CustomExpense> => {
  try {
    console.log("Updating expense with credentials:", id);
    // Use relative path
    const response = await axios.put(`/api/expenses/${id}`, expense, {
      withCredentials: true,
    });

    console.log("Expense update successful, checking auth status");
    // Check auth status after update to detect any changes
    try {
      // Use relative path
      const authCheck = await axios.get("/api/xero/auth-status", {
        withCredentials: true,
      });
      console.log("Auth status after expense update:", authCheck.data);
    } catch (authError) {
      console.error("Auth check error after expense update:", authError);
    }

    // Convert string date back to Date object
    return {
      ...response.data.data,
      date: new Date(response.data.data.date),
    };
  } catch (error: any) {
    console.error("Error updating expense:", error);
    throw new Error(error.response?.data?.error || "Failed to update expense");
  }
};

/**
 * Delete a custom expense
 * @param id Expense ID
 */
export const deleteExpense = async (id: string): Promise<void> => {
  try {
    // Use relative path
    await axios.delete(`/api/expenses/${id}`, {
      withCredentials: true,
    });
  } catch (error: any) {
    console.error("Error deleting expense:", error);
    throw new Error(error.response?.data?.error || "Failed to delete expense");
  }
};
