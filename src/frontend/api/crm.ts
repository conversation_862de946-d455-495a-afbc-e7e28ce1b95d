import { fetchFrom<PERSON>pi } from "./utils";
import {
  Deal,
  Contact,
  Company,
  Note,
  DealCreate,
  DealUpdate,
  ContactCreate,
  ContactUpdate,
  CompanyCreate,
  CompanyUpdate,
  NoteCreate,
  DealEstimate,
  ContactRole
} from "../types/crm-types";

// TypeScript interface for API errors
interface ApiError extends Error {
  response?: {
    data?: {
      error?: string;
      message?: string;
    };
    status?: number;
  };
}

/**
 * Get all deals
 */
export const getDeals = async (): Promise<Deal[]> => {
  const response = await fetchFromApi('/api/crm/deals');
  return response.data;
};

/**
 * Get a deal by ID
 */
export const getDealById = async (id: string): Promise<Deal> => {
  const response = await fetchFromApi(`/api/crm/deals/${id}`);
  return response.data;
};

/**
 * Create a new deal
 * @deprecated Deals should be created in HubSpot only
 */
// export const createDeal = async (dealData: DealCreate): Promise<Deal> => {
//   const response = await fetchFrom<PERSON>pi('/api/crm/deals', {
//     method: "POST",
//     body: JSON.stringify(dealData),
//     headers: {
//       'Content-Type': "application/json"
//     }
//   });
//   return response.data;
// };

/**
 * Update a deal
 */
export const updateDeal = async (id: string, dealData: DealUpdate): Promise<Deal> => {
  const response = await fetchFromApi(`/api/crm/deals/${id}`, {
    method: "PUT",
    body: JSON.stringify(dealData),
    headers: {
      'Content-Type': "application/json"
    }
  });
  return response.data;
};

/**
 * Delete a deal
 */
export const deleteDeal = async (id: string): Promise<void> => {
  await fetchFromApi(`/api/crm/deals/${id}`, {
    method: "DELETE"
  });
};

/**
 * Get all contacts
 */
export const getContacts = async (): Promise<Contact[]> => {
  const response = await fetchFromApi('/api/crm/contacts');
  return response.data;
};

/**
 * Get a contact by ID
 */
export const getContactById = async (id: string): Promise<Contact> => {
  const response = await fetchFromApi(`/api/crm/contacts/${id}`);
  return response.data;
};

/**
 * Create a new contact
 */
export const createContact = async (contactData: ContactCreate): Promise<Contact> => {
  const response = await fetchFromApi('/api/crm/contacts', {
    method: "POST",
    body: JSON.stringify(contactData),
    headers: {
      'Content-Type': "application/json"
    }
  });
  return response.data;
};

/**
 * Update a contact
 */
export const updateContact = async (id: string, contactData: ContactUpdate): Promise<Contact> => {
  const response = await fetchFromApi(`/api/crm/contacts/${id}`, {
    method: "PUT",
    body: JSON.stringify(contactData),
    headers: {
      'Content-Type': "application/json"
    }
  });
  return response.data;
};

/**
 * Delete a contact
 */
export const deleteContact = async (id: string): Promise<void> => {
  await fetchFromApi(`/api/crm/contacts/${id}`, {
    method: "DELETE"
  });
};

/**
 * Get all companies
 */
export const getCompanies = async (): Promise<Company[]> => {
  const response = await fetchFromApi('/api/crm/companies');
  return response.data;
};

/**
 * Get a company by ID
 */
export const getCompanyById = async (id: string): Promise<Company> => {
  const response = await fetchFromApi(`/api/crm/companies/${id}`);
  return response.data;
};

/**
 * Create a new company
 */
export const createCompany = async (companyData: CompanyCreate): Promise<Company> => {
  const response = await fetchFromApi('/api/crm/companies', {
    method: "POST",
    body: JSON.stringify(companyData),
    headers: {
      'Content-Type': "application/json"
    }
  });
  return response.data;
};

/**
 * Update a company
 */
export const updateCompany = async (id: string, companyData: CompanyUpdate): Promise<Company> => {
  const response = await fetchFromApi(`/api/crm/companies/${id}`, {
    method: "PUT",
    body: JSON.stringify(companyData),
    headers: {
      'Content-Type': "application/json"
    }
  });
  return response.data;
};

/**
 * Delete a company
 */
export const deleteCompany = async (id: string): Promise<void> => {
  await fetchFromApi(`/api/crm/companies/${id}`, {
    method: "DELETE"
  });
};

/**
 * Associate a contact with a deal
 */
export const associateContactWithDeal = async (dealId: string, contactId: string, role?: string): Promise<void> => {
  await fetchFromApi(`/api/crm/deals/${dealId}/contacts/${contactId}`, {
    method: "POST",
    body: JSON.stringify({ role }),
    headers: {
      'Content-Type': "application/json"
    }
  });
};

/**
 * Disassociate a contact from a deal
 */
export const disassociateContactFromDeal = async (dealId: string, contactId: string): Promise<void> => {
  await fetchFromApi(`/api/crm/deals/${dealId}/contacts/${contactId}`, {
    method: "DELETE"
  });
};

/**
 * Add a note to a deal
 */
export const addNoteToDeal = async (noteData: NoteCreate): Promise<Note> => {
  const response = await fetchFromApi(`/api/crm/deals/${noteData.dealId}/notes`, {
    method: "POST",
    body: JSON.stringify({
      content: noteData.content,
      createdBy: noteData.createdBy
    }),
    headers: {
      'Content-Type': "application/json"
    }
  });
  return response.data;
};

/**
 * Delete a note
 */
export const deleteNote = async (id: string): Promise<void> => {
  await fetchFromApi(`/api/crm/notes/${id}`, {
    method: "DELETE"
  });
};

/**
 * Get estimates linked to a deal
 */
export const getDealEstimates = async (dealId: string): Promise<DealEstimate[]> => {
  const response = await fetchFromApi(`/api/crm/deals/${dealId}/estimates`);
  return response.data;
};

/**
 * Get field ownership for a deal
 */
export const getDealFieldOwnership = async (dealId: string): Promise<Record<string, string>> => {
  try {
    const response = await fetchFromApi(`/api/crm/deals/${dealId}/field-ownership`);
    return response.fieldOwnership || {};
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Failed to get field ownership:', apiError);
    return {};
  }
};

/**
 * Link an estimate to a deal
 * @returns An object with success status and optional error message
 */
export const linkDealEstimate = async (
  dealId: string,
  estimateId: string,
  estimateType: "internal" | "harvest",
  linkedBy?: string
): Promise<{ success: boolean; error?: string; deal?: Deal }> => {
  try {
    const response = await fetchFromApi(
      `/api/crm/deals/${dealId}/estimates`,
      {
        method: "POST",
        body: JSON.stringify({ estimateId, estimateType, linkedBy }),
        headers: {
          'Content-Type': "application/json"
        }
      }
    ) as { success: boolean; error?: string; message?: string; deal?: Deal };
    // Check if the response contains a success field
    if (response && response.success !== undefined) {
      return { success: response.success, error: response.error, deal: response.deal };
    }
    // Default to success if response structure is different
    return { success: true, deal: response.deal };
  } catch (error) {
    const apiError = error as ApiError;
    // Extract error message from the API response if available
    const errorMessage = apiError.response?.data?.error || "Failed to link estimate to deal";
    return { success: false, error: errorMessage };
  }
};

/**
 * Unlink an estimate from a deal
 * @returns An object with success status and optional error message
 */
export const unlinkDealEstimate = async (
  dealId: string,
  estimateId: string,
  estimateType: "internal" | "harvest"
): Promise<{ success: boolean; error?: string }> => {
  try {
    await fetchFromApi(
      `/api/crm/deals/${dealId}/estimates/${estimateId}?estimateType=${estimateType}`,
      { method: "DELETE" }
    );
    return { success: true };
  } catch (error) {
    const apiError = error as ApiError;
    // Extract error message from the API response if available
    const errorMessage = apiError.response?.data?.error || "Failed to unlink estimate from deal";
    return { success: false, error: errorMessage };
  }
};

/**
 * Get deals linked to an estimate
 * @returns An array of deals linked to the specified estimate
 */
export const getLinkedDealsForEstimate = async (
  estimateId: string,
  estimateType: "internal" | "harvest"
): Promise<Deal[]> => {
  try {
    const response = await fetchFromApi(
      `/api/crm/estimates/${estimateId}/deals?estimateType=${estimateType}`
    );
    return response.data;
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Failed to get linked deals for estimate:', apiError);
    return [];
  }
};

/**
 * Get estimates for multiple deals in a single request
 * Automatically chunks requests if more than 100 deals
 */
export const getBatchDealEstimates = async (dealIds: string[]): Promise<Record<string, DealEstimate[]>> => {
  try {
    // If we have 100 or fewer deals, make a single request
    if (dealIds.length <= 100) {
      const response = await fetchFromApi('/api/crm/deals/batch-estimates', {
        method: "POST",
        headers: {
          'Content-Type': "application/json"
        },
        body: JSON.stringify({ dealIds })
      });
      return response.data || {};
    }
    
    // For more than 100 deals, chunk the requests
    const chunkSize = 100;
    const chunks: string[][] = [];
    
    for (let i = 0; i < dealIds.length; i += chunkSize) {
      chunks.push(dealIds.slice(i, i + chunkSize));
    }
    
    // Make requests for all chunks in parallel
    const chunkPromises = chunks.map(chunk => 
      fetchFromApi('/api/crm/deals/batch-estimates', {
        method: "POST",
        headers: {
          'Content-Type': "application/json"
        },
        body: JSON.stringify({ dealIds: chunk })
      }).then(response => response.data || {})
    );
    
    // Wait for all chunks to complete
    const chunkResults = await Promise.all(chunkPromises);
    
    // Merge all results into a single object
    const mergedResult: Record<string, DealEstimate[]> = {};
    chunkResults.forEach(chunkData => {
      Object.assign(mergedResult, chunkData);
    });
    
    return mergedResult;
  } catch (error) {
    console.error('Failed to fetch batch deal estimates:', error);
    return {};
  }
};

/**
 * Get all companies with their linking status to external systems
 */
export const getCompaniesWithLinkingStatus = async (): Promise<Array<Company & {
  linkingStatus: "both" | "hubspot_only" | "harvest_only" | "none"
}>> => {
  const response = await fetchFromApi('/api/crm/companies/linking-status');
  return response.data;
};

/**
 * Get companies that need linking between systems (legacy)
 */
export const getUnlinkedCompanies = async (): Promise<{
  hubspotOnly: Company[];
  harvestOnly: Company[];
  bothMissing: Company[];
}> => {
  const response = await fetchFromApi('/api/crm/companies/unlinked');
  return response.data;
};

/**
 * Link a company to HubSpot
 */
export const linkCompanyToHubSpot = async (
  companyId: string,
  hubspotId: string
): Promise<Company> => {
  const response = await fetchFromApi(
    `/api/crm/companies/${companyId}/link-hubspot`,
    {
      method: "POST",
      body: JSON.stringify({ hubspotId }),
      headers: {
        'Content-Type': "application/json"
      }
    }
  );
  return response.data;
};

/**
 * Link a company to Harvest
 */
export const linkCompanyToHarvest = async (
  companyId: string,
  harvestId: number
): Promise<Company> => {
  const response = await fetchFromApi(
    `/api/crm/companies/${companyId}/link-harvest`,
    {
      method: "POST",
      body: JSON.stringify({ harvestId }),
      headers: {
        'Content-Type': "application/json"
      }
    }
  );
  return response.data;
};

/**
 * Unlink a company from HubSpot
 */
export const unlinkCompanyFromHubSpot = async (companyId: string): Promise<Company> => {
  const response = await fetchFromApi(
    `/api/crm/companies/${companyId}/link-hubspot`,
    {
      method: "DELETE"
    }
  );
  return response.data;
};

/**
 * Get all linked Harvest IDs
 */
export const getLinkedHarvestIds = async (): Promise<number[]> => {
  const response = await fetchFromApi('/api/crm/companies/linked-harvest-ids');
  return response.data;
};

/**
 * Unlink a company from Harvest
 */
export const unlinkCompanyFromHarvest = async (companyId: string): Promise<Company> => {
  const response = await fetchFromApi(
    `/api/crm/companies/${companyId}/link-harvest`,
    {
      method: "DELETE"
    }
  );
  return response.data;
};

/**
 * Merge two companies
 * @param sourceCompanyId Company to merge from (will be deleted)
 * @param targetCompanyId Company to merge to (will be updated)
 * @returns The merged company
 */
export const mergeCompanies = async (sourceCompanyId: string, targetCompanyId: string): Promise<Company> => {
  const response = await fetchFromApi(
    `/api/crm/companies/${sourceCompanyId}/merge/${targetCompanyId}`,
    {
      method: "POST"
    }
  );
  return response.data;
};

/**
 * Link a contact to a company
 */
export const linkContactToCompany = async (
  contactId: string,
  companyId: string,
  role: ContactRole = 'user',
  isPrimary: boolean = false
): Promise<void> => {
  await fetchFromApi(`/api/crm/contacts/${contactId}/companies`, {
    method: "POST",
    body: JSON.stringify({ companyId, role, isPrimary }),
    headers: {
      'Content-Type': "application/json"
    }
  });
};

/**
 * Unlink a contact from a company
 */
export const unlinkContactFromCompany = async (
  contactId: string,
  companyId: string
): Promise<void> => {
  await fetchFromApi(`/api/crm/contacts/${contactId}/companies/${companyId}`, {
    method: "DELETE"
  });
};

/**
 * Get relationship network for an entity
 */
export const getRelationshipNetwork = async (
  entityId: string,
  entityType: "contact" | "company",
  depth: number = 2
): Promise<{
  nodes: Array<{
    id: string;
    label: string;
    type: "contact" | "company";
    level: number;
  }>;
  links: Array<{
    source: string;
    target: string;
    type: string;
    strength: number;
    label?: string;
  }>;
}> => {
  const response = await fetchFromApi(
    `/api/crm/network/relationships/${entityId}?type=${entityType}&depth=${depth}`
  );
  return response.data;
};

/**
 * Create a contact relationship
 */
export const createContactRelationship = async (data: {
  sourceContactId: string;
  targetContactId: string;
  relationshipType: string;
  strength: number;
  context?: string;
}): Promise<void> => {
  await fetchFromApi('/api/crm/contacts/relationships', {
    method: "POST",
    body: JSON.stringify(data),
    headers: {
      'Content-Type': "application/json"
    }
  });
};

/**
 * Delete a contact relationship
 */
export const deleteContactRelationship = async (relationshipId: string): Promise<void> => {
  await fetchFromApi(`/api/crm/contacts/relationships/${relationshipId}`, {
    method: "DELETE"
  });
};

// ContactRole is now imported from crm-types


