import axios from "axios";
import { CashflowForecast, CustomExpense, ScenarioCashflowForecast } from '../../types/financial'; // Direct import from source file

// Track in-flight requests to prevent duplicates
const pendingRequests: Record<string, Promise<CashflowForecast>> = {};

// Legacy methods have been removed

/**
 * Get forward projection
 * @param daysAhead Number of days to project forward (30, 60, 90, 120, 150, 180, or 210)
 * @param customExpenses Custom expenses array
 * @returns Cashflow forecast
 */
export const getForwardProjection = async (
  daysAhead: number = 90,
  customExpenses: CustomExpense[] = []
): Promise<CashflowForecast & { filterDecisions?: any[] }> => {
  try {
    // Validate daysAhead
    if (![30, 60, 90, 120, 150, 180, 210].includes(daysAhead)) {
      console.warn(`Invalid daysAhead value: ${daysAhead}. Using 90 days as default.`);
      daysAhead = 90;
    }

    // Create a unique key for this request
    // Use expense IDs and amounts for the request key
    const expensesHash = JSON.stringify(customExpenses.map(e =>
      `${e.id}:${e.amount}:${e.frequency}`
    ));
    const requestKey = `projection_${daysAhead}_expenses_${expensesHash}`; // Combined declaration and assignment with const

    // Check if we already have a pending request with these parameters
    if (requestKey in pendingRequests) {
      console.log('🔄 Using existing in-flight projection request');
      return pendingRequests[requestKey];
    }

    // Create request options
    const requestOptions: any = {
      withCredentials: true
    };

    // Always use POST to send expenses in the body
    let requestPromise;

    console.log('Generating forward projection...');

    // Use POST to send custom expenses in the body
    // Use relative path instead of absolute API_URL
    requestPromise = axios.post('/api/cashflow/forward-projection', {
      daysAhead,
      customExpenses
    }, requestOptions);

    // Process the request
    requestPromise = requestPromise
    .then(response => {
      // Clean up after request completes
      delete pendingRequests[requestKey];

      // The API response (response.data) should now contain the complete
      // filterDecisions array directly. No need to store it globally.
      if (response.data.filterDecisions && Array.isArray(response.data.filterDecisions)) {
        console.log(`Received ${response.data.filterDecisions.length} filter decisions from server`);
        // Optional: Add minimal logging if needed during testing
        // const excludedCount = response.data.filterDecisions.filter((d: any) => d.action === 'excluded').length;
        // const keptCount = response.data.filterDecisions.filter((d: any) => d.action === 'kept').length;
        // console.log(`FILTER DECISION STATS (frontend): ${excludedCount} excluded, ${keptCount} kept, ${response.data.filterDecisions.length} total`);
      } else {
        console.warn('No filterDecisions array found in API response.');
      }

      return response.data; // Return the full response data, including filterDecisions
    })
    .catch(error => {
      // Make sure to clean up even on errors
      delete pendingRequests[requestKey];
      throw error;
    });

    // Store this request
    pendingRequests[requestKey] = requestPromise;

    // Return the data
    return requestPromise;
  } catch (error: any) {
    console.error('Error fetching forward projection:', error);
    throw new Error(error.response?.data?.error || "Failed to fetch forward projection");
  }
};

// Track in-flight scenario requests to prevent duplicates
const pendingScenarioRequests: Record<string, Promise<ScenarioCashflowForecast>> = {};

/**
 * Get forward projection with deal scenarios
 * @param daysAhead Number of days to project forward (30, 60, 90, 120, 150, 180, or 210)
 * @param customExpenses Custom expenses array
 * @returns Scenario cashflow forecast
 */
export const getForwardProjectionWithDeals = async (
  daysAhead: number = 90,
  customExpenses: CustomExpense[] = []
): Promise<ScenarioCashflowForecast> => {
  try {
    // Validate daysAhead
    if (![30, 60, 90, 120, 150, 180, 210].includes(daysAhead)) {
      console.warn(`Invalid daysAhead value: ${daysAhead}. Using 90 days as default.`);
      daysAhead = 90;
    }

    // Create a unique key for this request
    const expensesHash = JSON.stringify(customExpenses.map(e =>
      `${e.id}:${e.amount}:${e.frequency}`
    ));
    const requestKey = `scenario_projection_${daysAhead}_expenses_${expensesHash}`;

    // Check if we already have a pending request with these parameters
    if (requestKey in pendingScenarioRequests) {
      console.log('🔄 Using existing in-flight scenario projection request');
      return pendingScenarioRequests[requestKey];
    }

    // Create request options
    const requestOptions: any = {
      withCredentials: true
    };

    console.log('Generating forward projection with deals...');

    // Use POST to send custom expenses in the body
    let requestPromise = axios.post('/api/cashflow/projection/with-deals', {
      daysAhead,
      customExpenses
    }, requestOptions);

    // Process the request to return data directly
    const dataPromise = requestPromise
    .then(response => {
      // Clean up after request completes
      delete pendingScenarioRequests[requestKey];

      return response.data;
    })
    .catch(error => {
      // Make sure to clean up even on errors
      delete pendingScenarioRequests[requestKey];
      throw error;
    });

    // Store this request
    pendingScenarioRequests[requestKey] = dataPromise;

    // Return the data promise
    return dataPromise;
  } catch (error: any) {
    console.error('Error fetching forward projection with deals:', error);
    throw new Error(error.response?.data?.error || "Failed to fetch forward projection with deals");
  }
};

/**
 * Get available cashflow snapshot dates
 * @param daysAhead Projection timeframe (default: 90 days)
 * @returns Array of available dates in YYYY-MM-DD format
 */
export const getAvailableSnapshotDates = async (
  daysAhead: number = 90
): Promise<string[]> => {
  try {
    const response = await axios.get('/api/cashflow/snapshots/dates', {
      params: { daysAhead },
      withCredentials: true
    });

    if (response.data.success && Array.isArray(response.data.dates)) {
      return response.data.dates;
    }

    return [];
  } catch (error) {
    console.error('Error fetching snapshot dates:', error);
    return [];
  }
};

/**
 * Get cashflow projection snapshot for a specific date
 * @param date Snapshot date in YYYY-MM-DD format
 * @param daysAhead Projection timeframe (default: 90 days)
 * @returns Cashflow forecast from the snapshot
 */
export const getProjectionSnapshot = async (
  date: string,
  daysAhead: number = 90
): Promise<CashflowForecast | null> => {
  try {
    const response = await axios.get(`/api/cashflow/snapshots/${date}`, {
      params: { daysAhead },
      withCredentials: true
    });

    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    return null;
  } catch (error) {
    console.error('Error fetching projection snapshot:', error);
    return null;
  }
};

/**
 * Create a manual snapshot of the current cashflow projection
 * @param daysAhead Projection timeframe (default: 90 days)
 * @param customExpenses Custom expenses to include in the snapshot
 * @returns Created snapshot date or null if failed
 */
export const createManualSnapshot = async (
  daysAhead: number = 90,
  customExpenses: CustomExpense[] = []
): Promise<string | null> => {
  try {
    const response = await axios.post('/api/cashflow/snapshots/create', {
      daysAhead,
      customExpenses
    }, {
      withCredentials: true
    });

    if (response.data.success && response.data.date) {
      return response.data.date;
    }

    return null;
  } catch (error) {
    console.error('Error creating manual snapshot:', error);
    return null;
  }
};
