import axios from "axios";
import { CustomExpense } from "../../types";
import { XERO_SOURCES } from "../../constants/xero";
import { getExpenses, createExpense, updateExpense } from "./expenses";
import { isXeroExpense } from "../components/Expense/utils";
import {
  XeroBillDisplay,
  XeroExpenseBreakdown,
  XeroPayrollExpenseDisplay,
  XeroGSTData
} from "../../api/types/xero";

// TypeScript interfaces for this file
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  response?: {
    statusCode?: number;
    data?: { message?: string };
  };
}

interface XeroBillData {
  date: string;
  dueDate?: string;
  id: string;
  amount: number;
  description?: string;
  supplier?: string;
  [key: string]: unknown;
}

interface XeroSuperannuationExpense {
  id: string;
  date: string;
  amount: number;
  employee?: string;
  description?: string;
  [key: string]: unknown;
}

/**
 * Re-export interfaces for usage in components
 */
// Define interface for balance sheet response
export interface XeroBalanceSheetResponse {
  success: boolean;
  data: Record<string, unknown>; // The balance sheet report data from Xero
  date: string;
}

export type {
  XeroBillDisplay,
  XeroExpenseBreakdown,
  XeroPayrollExpenseDisplay,
  XeroGSTData
};

/**
 * Get bills from Xero
 * @param days Number of days to look back (default: 30)
 * @returns API response with bills with isAlreadyAdded flag
 */
export const getXeroBills = async (days = 30): Promise<XeroBillDisplay[]> => {
  try {
    // Fetch bills from Xero
    const response = await axios.get(`/api/xero/bills?days=${days}`, {
      withCredentials: true
    });

    if (!response.data.success) {
      console.error('Error getting bills from Xero:', response.data.message);
      return [];
    }

    // Parse dates from strings to Date objects
    const bills = response.data?.data?.map((bill: XeroBillData) => ({
      ...bill,
      date: new Date(bill.date),
      dueDate: new Date(bill.dueDate),
      isAlreadyAdded: false // Default value
    }));

    // Fetch existing expenses to check for duplicates
    try {
      const expenses = await getExpenses();

      // Mark bills that have already been added as expenses
      return bills.map((bill: XeroBillDisplay) => {
        // A bill is considered already added if one of these conditions is met:
        // 1. An expense has a name that starts with the bill reference
        // 2. The bill reference is contained within the expense name
        // 3. The invoice number is contained within the expense name
        // 4. The vendor name and amount match exactly (fallback for missing reference)
        const alreadyAdded = expenses.some(expense => {
          // Check for reference number match (most reliable)
          if (bill.reference &&
             (expense.name.startsWith(bill.reference) ||
              expense.name.includes(bill.reference))) {
            return true;
          }

          // Check for invoice number match
          if (bill.invoiceNumber &&
             (expense.name.includes(bill.invoiceNumber))) {
            return true;
          }

          // Fallback: Check for vendor name + exact amount match
          // This is less reliable but helps catch manually renamed expenses
          if (bill.vendor &&
              expense.name.includes(bill.vendor) &&
              Math.abs(expense.amount - bill.amount) < 0.01) {
            return true;
          }

          return false;
        });

        return {
          ...bill,
          isAlreadyAdded: alreadyAdded
        };
      });
    } catch (expenseError) {
      console.error('Error fetching expenses for comparison:', expenseError);
      // Continue with bills without checking for duplicates
      return bills;
    }
  } catch (error) {
    const apiError = error as ApiError;
    // Special handling for authentication errors
    if (apiError.response?.statusCode === 401) {
      console.error('Authentication error - please reconnect to Xero');
      // Could trigger a re-authentication flow here
    }

    console.error('Error getting bills from Xero:', apiError);
    throw new Error(apiError.response?.data?.message || 'Failed to fetch bills from Xero');
  }
};

/**
 * Convert a Xero bill to a custom expense
 * @param bill Bill to convert
 * @param expenseType Type of expense to create
 * @returns Created expense
 */
export const convertBillToExpense = async (bill: XeroBillDisplay, expenseType: CustomExpense['type']): Promise<CustomExpense> => {
  try {
    const response = await axios.post('/api/xero/bills/convert', { bill, expenseType }, {
      withCredentials: true,
      timeout: 15000, // 15 second timeout
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.data.success) {
      console.error('Error converting bill to expense:', response.data.message);
      throw new Error(response.data.message);
    }

    // Parse date from string to Date object
    const expense = {
      ...response.data.data,
      date: new Date(response.data.data.date),
      // Add metadata to track source
      _source: {
        type: 'xero_bill',
        id: bill.id,
        importedAt: new Date()
      }
    };

    return expense;
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error converting bill to expense:', apiError);
    throw new Error(apiError.response?.data?.message || 'Failed to convert bill to expense');
  }
};

/**
 * Get latest payroll data from Xero to use as a template for projections
 * @returns Most recent payroll information for template creation
 */
export const getXeroPayrollExpenses = async (): Promise<XeroPayrollExpenseDisplay[]> => {
  try {
    console.log('Fetching payroll data from API');
    // Use a fixed value of 90 days for lookback
    const response = await axios.get('/api/xero/payroll?days=90', {
      withCredentials: true
    });

    // If the API returned a non-success response, throw an error
    if (!response.data.success) {
      console.error('Error getting payroll expenses from Xero:', response.data.message);
      throw new Error(response.data.message || 'Failed to fetch payroll data from Xero API');
    }

    // Check if we have data
    if (!response.data?.data || !Array.isArray(response.data.data) || response.data.data.length === 0) {
      console.log('No payroll data returned from API');
      return [];
    }

    // The data should already be processed by the payroll service
    const allExpenses = response.data.data;

    // Get the most recent payroll entry
    // We're expecting these to be sorted by payment date (most recent first) already
    const mostRecentPayroll = allExpenses[0];

    console.log('Found most recent payroll:', {
      id: mostRecentPayroll.id,
      date: mostRecentPayroll.paymentDate,
      amount: mostRecentPayroll.amount,
      status: mostRecentPayroll.status
    });

    // Check if a payroll expense with this pattern already exists
    try {
      const customExpenses = await getExpenses();

      const hasExistingRecurring = customExpenses.some(expense =>
        expense.type === 'Monthly Payroll' &&
        expense.name.includes('Payroll') &&
        expense.frequency === 'monthly');

      const paySummary = {
        ...mostRecentPayroll,
        isTemplate: true,
        hasRecurringExpense: hasExistingRecurring
      };

      console.log(`Returning payroll template (has recurring expense: ${hasExistingRecurring})`);
      return [paySummary];
    } catch (error) {
      console.error('Error fetching expenses for comparison:', error);
      return [{
        ...mostRecentPayroll,
        isTemplate: true,
        hasRecurringExpense: false
      }];
    }
  } catch (error) {
    const apiError = error as ApiError & { response?: { status?: number; data?: any } };
    // Enhanced error handling for different response codes
    if (apiError.response) {
      // Handle specific status codes with appropriate messages
      const status = apiError.response.status;
      const errorData = apiError.response.data;

      console.error(`Xero API error (${status}):`, errorData);

      // Authentication errors
      if (status === 401) {
        console.error('Authentication error - please reconnect to Xero');
        throw new Error(errorData.message || 'Authentication error - please reconnect to Xero');
      }

      // Permission/scope errors
      if (status === 403) {
        console.error('Permission error - missing required scopes');
        throw new Error(errorData.message || 'Missing required permissions for Xero Payroll API');
      }

      // No data found errors
      if (status === 404) {
        console.error('No payroll data found in Xero');
        throw new Error(errorData.message || 'No payroll data found in Xero');
      }

      // API not available errors
      if (status === 501) {
        console.error('Xero Payroll API not available');
        throw new Error(errorData.message || 'Xero Payroll API not available for your account type');
      }

      // Use the most specific error message available
      throw new Error(
        errorData.details ||
        errorData.message ||
        `Failed to fetch payroll data from Xero (Status ${status})`
      );
    }

    // General error handling
    console.error('Error getting payroll data from Xero:', apiError);
    throw new Error(apiError.message || 'Failed to fetch payroll data from Xero');
  }
};

/**
 * Sync the payroll template with upstream to create a recurring expense
 * @param templateData Latest payroll information to use as a template
 * @returns Created recurring expense
 */
export const syncPayrollWithUpstream = async (templateData: XeroPayrollExpenseDisplay): Promise<CustomExpense> => {
  try {
    console.log('Creating recurring payroll expense from template:', {
      id: templateData.id,
      amount: templateData.amount,
      date: templateData.paymentDate
    });

    // Validate the template data before proceeding
    if (!templateData.id || !templateData.amount || !templateData.paymentDate) {
      throw new Error('Invalid payroll template data - missing required fields');
    }

    // Check for data integrity
    if (!templateData.id || templateData.amount === 0) {
      console.error('Invalid payroll data - cannot sync with incomplete data');
      throw new Error('Cannot sync with incomplete payroll data. Please ensure your Xero connection is properly configured with the payroll.payruns scope.');
    }

    // Calculate the next payment date based on the template's payment date
    // We want to keep the same day of month for the next payment
    const paymentDate = new Date(templateData.paymentDate);

    // Ensure we have a valid date before proceeding
    if (isNaN(paymentDate.getTime())) {
      console.error('Invalid payment date in template:', templateData.paymentDate);
      throw new Error('Invalid payment date in payroll template');
    }

    const today = new Date();

    // Check if we have a payroll run in the current month
    // This uses the period dates from the template to determine if this month's payroll has occurred
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    console.log('Using actual date for payroll sync:', {
      today: today.toISOString(),
      currentMonth,
      currentYear
    });

    // Dump the entire templateData object to see what data we're working with
    console.log('Full payroll template data:', JSON.stringify(templateData, null, 2));

    const periodStartDate = templateData.periodStartDate ? new Date(templateData.periodStartDate) : null;
    const periodEndDate = templateData.periodEndDate ? new Date(templateData.periodEndDate) : null;

    console.log('Payroll period dates after conversion:', {
      periodStartDate: periodStartDate ? periodStartDate.toISOString() : null,
      periodEndDate: periodEndDate ? periodEndDate.toISOString() : null,
      periodStartMonth: periodStartDate ? periodStartDate.getMonth() : null,
      currentMonth
    });

    const hasCurrentMonthPayroll = periodStartDate &&
      periodStartDate.getMonth() === currentMonth &&
      periodStartDate.getFullYear() === currentYear;

    console.log('Payroll payment date calculation info:', {
      today: today.toISOString(),
      currentMonth,
      currentYear,
      periodStartDate: periodStartDate ? periodStartDate.toISOString() : null,
      periodStartMonth: periodStartDate ? periodStartDate.getMonth() : null,
      periodStartYear: periodStartDate ? periodStartDate.getFullYear() : null,
      hasCurrentMonthPayroll
    });

    // Get the day of month from the template's payment date
    let nextPaymentDay = paymentDate.getDate();

    // Check if the payment date is in the future
    const paymentDayIsInFuture = nextPaymentDay >= today.getDate();

    console.log('Payroll payment day check:', {
      nextPaymentDay,
      todayDate: today.getDate(),
      paymentDayIsInFuture
    });

    // If the payment day is in the future, show it in the current month
    // Otherwise, show it in the next month
    let nextPaymentMonth;

    // Always show the next payment in the current month if the payment day hasn't passed yet
    if (paymentDayIsInFuture) {
      nextPaymentMonth = currentMonth; // Current month
      console.log('Payroll payment day is in the future, showing in current month');
    } else {
      // Payment day has passed, show in next month
      nextPaymentMonth = currentMonth + 1;
      console.log('Payroll payment day has passed, showing in next month');
    }

    let nextPaymentYear = currentYear;

    // Adjust if we need to roll over to next year
    if (nextPaymentMonth > 11) {
      nextPaymentMonth = 0; // January
      nextPaymentYear += 1;
    }

    // Handle month length differences (e.g., January 31 -> February 28)
    const daysInNextMonth = new Date(nextPaymentYear, nextPaymentMonth + 1, 0).getDate();
    if (nextPaymentDay > daysInNextMonth) {
      nextPaymentDay = daysInNextMonth;
    }

    const nextPaymentDate = new Date(nextPaymentYear, nextPaymentMonth, nextPaymentDay);

    console.log('Final calculated next payment date for payroll:', {
      nextPaymentDate: nextPaymentDate.toISOString(),
      nextPaymentDay,
      nextPaymentMonth,
      nextPaymentYear,
      localeDateString: nextPaymentDate.toLocaleDateString()
    });

    // Format an employee description
    const employeeText = templateData.employeeCount === 1
      ? '1 employee'
      : `${templateData.employeeCount} employees`;

    // Format costs breakdown
    let descriptionParts = [];
    if (templateData.wages) descriptionParts.push(`Net Pay: $${templateData.wages.toFixed(2)}`);
    if (templateData.superannuation) descriptionParts.push(`Super: $${templateData.superannuation.toFixed(2)}`);
    if (templateData.tax) descriptionParts.push(`Tax: $${templateData.tax.toFixed(2)}`);

    const detailedCosts = descriptionParts.length > 0
      ? ` (${descriptionParts.join(', ')})`
      : '';

    // Create a custom expense structure
    const expense = {
      name: `Monthly Payroll (${employeeText})`,
      type: 'Monthly Payroll' as const,
      amount: templateData.amount,
      date: nextPaymentDate,
      frequency: 'monthly' as const,
      description: `Recurring payroll expense based on Xero data${detailedCosts}`,
      source: XERO_SOURCES.PAYROLL_TEMPLATE,
      // Additional metadata
      metadata: {
        xeroPayrollId: templateData.id,
        payrollWages: templateData.wages,
        payrollSuper: templateData.superannuation,
        payrollTax: templateData.tax,
        payrollEmployeeCount: templateData.employeeCount,
        payrollSource: templateData.source || 'xero',
        isFromXero: true
      }
    };

    console.log('Creating recurring expense:', {
      name: expense.name,
      amount: expense.amount,
      date: expense.date,
      frequency: expense.frequency
    });

    try {
      // Create the recurring expense
      const response = await axios.post('/api/expenses', expense, {
        withCredentials: true,
        timeout: 15000, // 15 second timeout
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.data.success) {
        console.error('Error syncing payroll with upstream:', response.data.message);
        throw new Error(response.data.message || 'Failed to create expense');
      }

      console.log('Successfully created recurring payroll expense');

      // Parse date from string to Date object
      const createdExpense = {
        ...response.data.data,
        date: new Date(response.data.data.date),
        // Add metadata to track source with detailed information
        _source: {
          type: 'xero_payroll_template',
          templateId: templateData.id,
          importedAt: new Date(),
          wages: templateData.wages,
          superannuation: templateData.superannuation,
          tax: templateData.tax,
          employeeCount: templateData.employeeCount,
          source: templateData.source || 'xero'
        }
      };

      return createdExpense;
    } catch (apiError: any) {
      // Handle API errors with more detail
      console.error('API error when creating expense:', apiError);

      if (apiError.response) {
        const statusCode = apiError.response.status;
        const errorData = apiError.response.data;

        throw new Error(
          errorData.message ||
          `Failed to create expense (Status ${statusCode})`
        );
      }

      // Rethrow with a general message if no response details
      throw new Error('Failed to create recurring expense - please try again');
    }
  } catch (error: any) {
    console.error('Error syncing payroll with upstream:', error);

    // Use the most specific error message available
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message);
    } else if (error.message) {
      throw new Error(error.message);
    } else {
      throw new Error('Failed to sync payroll with upstream - unknown error');
    }
  }
};

/**
 * Get expense breakdown from Xero
 * @returns Expense breakdown with separated expense types
 */
export const getXeroExpenseBreakdown = async (): Promise<XeroExpenseBreakdown[]> => {
  try {
    console.log('Fetching Xero expense breakdown');
    const response = await axios.get('/api/xero/expense-breakdown', {
      withCredentials: true
    });

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch expense breakdown');
    }

    return response.data.data;
  } catch (error: any) {
    console.error('Error fetching Xero expense breakdown:', error);

    // Enhanced error handling for different response codes
    if (error.response) {
      // Handle specific status codes with appropriate messages
      const status = error.response.status;
      const errorData = error.response.data;

      // Authentication errors
      if (status === 401) {
        throw new Error(errorData.message || 'Authentication error - please reconnect to Xero');
      }

      // Permission/scope errors
      if (status === 403) {
        throw new Error(errorData.message || 'Missing required permissions for Xero Payroll API');
      }

      // No data found errors
      if (status === 404) {
        throw new Error(errorData.message || 'No payroll data found in Xero');
      }

      // Use the most specific error message available
      throw new Error(
        errorData.details ||
        errorData.message ||
        `Failed to fetch expense breakdown from Xero (Status ${status})`
      );
    }

    throw error;
  }
};

/**
 * Sync net pay expense with upstream (previously called wages)
 * @param expense Expense breakdown data
 * @returns Created recurring expense
 */
export const syncWagesExpense = async (expense: XeroExpenseBreakdown): Promise<CustomExpense> => {
  return syncExpenseType(expense, 'wages', 'Monthly Net Pay');
};

/**
 * Sync tax expense with upstream
 * @param expense Expense breakdown data
 * @returns Created recurring expense
 */
export const syncTaxExpense = async (expense: XeroExpenseBreakdown): Promise<CustomExpense> => {
  return syncExpenseType(expense, 'tax', 'PAYGW Tax');
};

/**
 * Sync superannuation expense with upstream
 * @param expense Expense breakdown data
 * @returns Created recurring expense
 */
export const syncSuperExpense = async (expense: XeroExpenseBreakdown): Promise<CustomExpense> => {
  return syncExpenseType(expense, 'superannuation', 'Monthly Superannuation');
};

/**
 * Helper function to sync different expense types
 * @param expense Expense breakdown data
 * @param type Type of expense (wages, tax, superannuation)
 * @param name Name for the expense
 * @returns Created or updated recurring expense
 */
const syncExpenseType = async (
  expense: XeroExpenseBreakdown,
  type: 'wages' | 'tax' | 'superannuation',
  name: string
): Promise<CustomExpense> => {
  try {
    // Get the specific expense component
    const expenseComponent = expense[type];

    // Validate the expense data
    if (!expense.id || !expenseComponent.amount || !expenseComponent.paymentDate) {
      throw new Error(`Invalid ${type} expense data - missing required fields`);
    }

    // Calculate the next payment date based on the current date
    const today = new Date();

    const paymentDate = new Date(expenseComponent.paymentDate);

    // Check if we have a payment for this expense type in the current month
    // This uses the period dates from the expense to determine if this month's payment has occurred
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    console.log(`Using actual date for ${type} expense:`, {
      today: today.toISOString(),
      currentMonth,
      currentYear
    });

    // Dump the entire expense object to see what data we're working with
    console.log(`Full ${type} expense data:`, JSON.stringify(expense, null, 2));

    const periodStartDate = expense.periodStartDate ? new Date(expense.periodStartDate) : null;
    const periodEndDate = expense.periodEndDate ? new Date(expense.periodEndDate) : null;

    console.log(`${type} period dates after conversion:`, {
      periodStartDate: periodStartDate ? periodStartDate.toISOString() : null,
      periodEndDate: periodEndDate ? periodEndDate.toISOString() : null,
      periodStartMonth: periodStartDate ? periodStartDate.getMonth() : null,
      currentMonth
    });

    const hasCurrentMonthPayment = periodStartDate &&
      periodStartDate.getMonth() === currentMonth &&
      periodStartDate.getFullYear() === currentYear;

    console.log(`${type} payment date calculation info:`, {
      type,
      today: today.toISOString(),
      currentMonth,
      currentYear,
      periodStartDate: periodStartDate ? periodStartDate.toISOString() : null,
      periodStartMonth: periodStartDate ? periodStartDate.getMonth() : null,
      periodStartYear: periodStartDate ? periodStartDate.getFullYear() : null,
      hasCurrentMonthPayment
    });

    // Get the day of month from the payment date
    let nextPaymentDay = paymentDate.getDate();

    // Check if the payment date is in the future
    const paymentDayIsInFuture = nextPaymentDay >= today.getDate();

    console.log(`${type} payment day check:`, {
      nextPaymentDay,
      todayDate: today.getDate(),
      paymentDayIsInFuture
    });

    // If the payment day is in the future, show it in the current month
    // Otherwise, show it in the next month
    let nextPaymentMonth;

    // Always show the next payment in the current month if the payment day hasn't passed yet
    if (paymentDayIsInFuture) {
      nextPaymentMonth = currentMonth; // Current month
      console.log(`${type} payment day is in the future, showing in current month`);
    } else {
      // Payment day has passed, show in next month
      nextPaymentMonth = currentMonth + 1;
      console.log(`${type} payment day has passed, showing in next month`);
    }

    let nextPaymentYear = currentYear;

    // Adjust if we need to roll over to next year
    if (nextPaymentMonth > 11) {
      nextPaymentMonth = 0; // January
      nextPaymentYear += 1;
    }

    // Handle month length differences
    const daysInNextMonth = new Date(nextPaymentYear, nextPaymentMonth + 1, 0).getDate();
    if (nextPaymentDay > daysInNextMonth) {
      nextPaymentDay = daysInNextMonth;
    }

    const nextPaymentDate = new Date(nextPaymentYear, nextPaymentMonth, nextPaymentDay);

    // For tax payments, always set to the 21st of the month
    if (type === 'tax') {
      nextPaymentDate.setDate(21);
    }

    console.log(`Final calculated next payment date for ${type}:`, {
      nextPaymentDate: nextPaymentDate.toISOString(),
      nextPaymentDay,
      nextPaymentMonth,
      nextPaymentYear
    });

    // Format an employee description
    const employeeText = expense.employeeCount === 1
      ? '1 employee'
      : `${expense.employeeCount} employees`;

    // Create the expense data
    // Define the expense type based on the component type
    const expenseType = type === 'wages' ? 'Monthly Payroll' as const :
                        type === 'tax' ? 'Taxes' as const :
                        'Superannuation' as const;

    const expenseData = {
      name: `${name} (${employeeText})`,
      type: expenseType,
      amount: expenseComponent.amount,
      date: nextPaymentDate,
      frequency: 'monthly' as const,
      description: `Recurring ${type === 'wages' ? 'net pay' : type} expense based on Xero payroll data`,
      source: type === 'wages' ? XERO_SOURCES.NET_PAY_EXPENSE :
              type === 'tax' ? XERO_SOURCES.TAX_EXPENSE :
              XERO_SOURCES.SUPERANNUATION_EXPENSE,
      // Additional metadata
      metadata: {
        xeroPayrollId: expense.id,
        expenseType: type,
        employeeCount: expense.employeeCount,
        source: expense.source || 'xero',
        isFromXero: true
      }
    };

    // Check if an expense of this type already exists
    let existingExpenseId = null;
    if (expenseComponent.hasRecurringExpense) {
      try {
        // Get all expenses
        const allExpenses = await getExpenses();

        // Find an expense that matches our criteria
        const existingExpense = allExpenses.find(exp => {
          // First check if it's from Xero
          if (!isXeroExpense(exp)) {
            return false;
          }

          // Check if it's a recurring expense of the right type
          const isRecurring = exp.frequency === 'monthly';
          const isCorrectType = (
            (type === 'wages' && exp.type === 'Monthly Payroll') ||
            (type === 'tax' && exp.type === 'Taxes') ||
            (type === 'superannuation' && exp.type === 'Superannuation')
          );

          // Check if it has the right source
          const hasCorrectSource = type === 'wages'
            ? exp.source?.includes('xero-netpay')
            : exp.source?.includes(`xero-${type}`);

          return isRecurring && isCorrectType && hasCorrectSource;
        });

        if (existingExpense) {
          existingExpenseId = existingExpense.id;
          console.log(`Found existing ${type} expense with ID: ${existingExpenseId}`);
        }
      } catch (error) {
        console.error('Error checking for existing expenses:', error);
        // Continue with creating a new expense if we can't check for existing ones
      }
    }

    // Update existing expense or create a new one
    let createdExpense: CustomExpense;

    if (existingExpenseId) {
      console.log(`Updating existing ${type} expense with ID: ${existingExpenseId}`);
      createdExpense = await updateExpense(existingExpenseId, expenseData);
      console.log(`Successfully updated ${type} expense`);
    } else {
      console.log(`Creating new ${type} expense`);
      // Create the expense through the API
      createdExpense = await createExpense(expenseData);
      console.log(`Successfully created ${type} expense`);
    }

    // Parse date from string to Date object if needed
    const resultExpense: CustomExpense = {
      ...createdExpense,
      date: createdExpense.date instanceof Date ? createdExpense.date : new Date(createdExpense.date as unknown as string),
      // Add metadata to track source
      _source: {
        type: `xero_${type}_expense`,
        templateId: expense.id,
        importedAt: new Date(),
        expenseType: type,
        employeeCount: expense.employeeCount,
        source: expense.source || 'xero'
      }
    };

    return resultExpense;
  } catch (error: any) {
    console.error(`Error syncing ${type} expense:`, error);

    // Log detailed information for debugging
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    }

    // Handle specific error cases
    if (error.response?.status === 400 && error.response?.data?.message?.includes('type')) {
      throw new Error(`Invalid expense type. The '${type}' expense type is not recognized by the system.`);
    }

    // Use the most specific error message available
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message);
    } else if (error.message) {
      throw new Error(error.message);
    } else {
      throw new Error(`Failed to sync ${type} expense - unknown error`);
    }
  }
};

/**
 * Get GST data from Xero
 * @returns GST data with amount and due date
 */
export const getXeroGSTData = async (): Promise<XeroGSTData> => {
  try {
    console.log('Making API request to /api/xero/gst');
    const response = await axios.get('/api/xero/gst', {
      withCredentials: true
    });
    console.log('Received response from /api/xero/gst:', response.data);

    if (!response.data.success) {
      console.error('Error getting GST data from Xero:', response.data.message);
      throw new Error(response.data.message || 'Failed to fetch GST data from Xero API');
    }

    // Parse date from string to Date object
    const result = {
      ...response.data.data,
      dueDate: new Date(response.data.data.dueDate)
    };
    console.log('Parsed GST data:', result);
    return result;
  } catch (error: any) {
    console.error('Error getting GST data from Xero:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch GST data from Xero');
  }
};

/**
 * Convert a Xero superannuation expense to a custom expense
 * @param expense Superannuation expense to convert
 * @returns Created expense
 */
export const convertSuperannuationToExpense = async (expense: XeroSuperannuationExpense): Promise<CustomExpense> => {
  try {
    const response = await axios.post('/api/xero/superannuation/convert', { expense }, {
      withCredentials: true,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.data.success) {
      console.error('Error converting superannuation to expense:', response.data.message);
      throw new Error(response.data.message || 'Failed to create expense');
    }

    // Parse date from string to Date object
    const createdExpense = {
      ...response.data.data,
      date: new Date(response.data.data.date),
      // Add metadata to track source
      _source: {
        type: 'xero_superannuation',
        importedAt: new Date(),
        isFromXero: true
      }
    };

    return createdExpense;
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error converting superannuation to expense:', apiError);
    throw new Error(apiError.response?.data?.message || 'Failed to convert superannuation to expense');
  }
};

/**
 * Sync GST data as an expense
 * @param gstData GST data to sync
 * @returns Created expense
 */
export const syncGSTExpense = async (gstData: XeroGSTData): Promise<CustomExpense> => {
  try {
    const response = await axios.post('/api/xero/gst/convert', { gstData }, {
      withCredentials: true,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.data.success) {
      console.error('Error syncing GST expense:', response.data.message);
      throw new Error(response.data.message || 'Failed to create expense');
    }

    // Parse date from string to Date object
    const createdExpense = {
      ...response.data.data,
      date: new Date(response.data.data.date),
      // Add metadata to track source
      _source: {
        type: 'xero_gst',
        importedAt: new Date(),
        isFromXero: true
      }
    };

    return createdExpense;
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error syncing GST expense:', apiError);
    throw new Error(apiError.response?.data?.message || 'Failed to sync GST expense');
  }
};

/**
 * Get balance sheet report from Xero
 * @param date Date for the balance sheet (defaults to today)
 * @param periods Number of periods to include (optional)
 * @param timeframe Timeframe for periods (optional)
 * @param standardLayout Whether to use standard layout (optional)
 * @returns Balance sheet report data
 */
export const getXeroBalanceSheet = async (
  date?: Date,
  periods?: number,
  timeframe?: string,
  standardLayout?: boolean
): Promise<XeroBalanceSheetResponse> => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Format date as YYYY-MM-DD (required by Xero API)
    if (date) {
      // Format date as YYYY-MM-DD
      const formattedDate = date.toISOString().split('T')[0];
      params.append('date', formattedDate);
      console.log(`Requesting balance sheet for date: ${formattedDate}`);
    } else {
      // If no date is provided, use today's date
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      params.append('date', formattedDate);
      console.log(`No date provided, using today's date: ${formattedDate}`);
    }

    // Add optional parameters
    if (periods !== undefined) {
      params.append('periods', periods.toString());
    }

    if (timeframe) {
      params.append('timeframe', timeframe);
    }

    if (standardLayout !== undefined) {
      params.append('standardLayout', standardLayout.toString());
    }

    // Make the API request
    const queryString = params.toString() ? `?${params.toString()}` : '';
    console.log(`Making request to: /api/xero/reports/balance-sheet${queryString}`);

    const response = await axios.get(`/api/xero/reports/balance-sheet${queryString}`, {
      withCredentials: true
    });

    if (!response.data.success) {
      console.error('Error getting balance sheet from Xero:', response.data.message);
      throw new Error(response.data.message || 'Failed to fetch balance sheet from Xero API');
    }

    return response.data;
  } catch (error: any) {
    console.error('Error getting balance sheet from Xero:', error);

    // Enhanced error handling for different response codes
    if (error.response) {
      // Handle specific status codes with appropriate messages
      const status = error.response.status;
      const errorData = error.response.data;

      // Authentication errors
      if (status === 401) {
        throw new Error(errorData.message || 'Authentication error - please reconnect to Xero');
      }

      // Permission/scope errors
      if (status === 403) {
        throw new Error(errorData.message || 'Missing required permissions for Xero API');
      }

      // No data found errors
      if (status === 404) {
        throw new Error(errorData.message || 'No balance sheet data found in Xero');
      }

      // Use the most specific error message available
      throw new Error(
        errorData.details ||
        errorData.message ||
        `Failed to fetch balance sheet from Xero (Status ${status})`
      );
    }

    throw error;
  }
};


// Add a debounced version to prevent too many events in quick succession
let eventDebounceTimer: NodeJS.Timeout | null = null;
export const debouncedPublishExpenseUpdated = (events: unknown[]) => {
  if (eventDebounceTimer) {
    clearTimeout(eventDebounceTimer);
  }

  eventDebounceTimer = setTimeout(() => {
    events.publish('EXPENSES_UPDATED');
    eventDebounceTimer = null;
  }, 500); // Wait 500ms before triggering event
};