import { fetchFromApi } from "./utils";
import { 
  DraftEstimate, 
  DraftEstimateCreate, 
  DraftEstimateUpdate, 
  DraftEstimateSummary 
} from "../../types/api";

// Re-export the type so it can be imported from this module
export type { DraftEstimateCreate }; 

const BASE_URL = '/api/estimates';

/**
 * Get all draft estimates
 */
export async function getDraftEstimates(): Promise<DraftEstimateSummary[]> {
  return fetchFromApi(`${BASE_URL}/drafts`);
}

/**
 * Get a specific draft estimate by UUID
 */
export async function getDraftEstimate(uuid: string): Promise<DraftEstimate> {
  return fetchFromApi(`${BASE_URL}/drafts/${uuid}`);
}

/**
 * Create a new draft estimate
 */
export async function createDraftEstimate(data: DraftEstimateCreate): Promise<DraftEstimate> {
  return fetchFromApi(`${BASE_URL}/drafts`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
}

/**
 * Update an existing draft estimate
 */
export async function updateDraftEstimate(uuid: string, data: DraftEstimateUpdate): Promise<DraftEstimate> {
  return fetchFromApi(`${BASE_URL}/drafts/${uuid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
}

/**
 * Delete a draft estimate
 */
export async function deleteDraftEstimate(uuid: string): Promise<void> {
  return fetchFromApi(`${BASE_URL}/drafts/${uuid}`, {
    method: 'DELETE'
  });
}

/**
 * Mark a draft estimate as published
 */
export async function publishDraftEstimate(uuid: string, harvestEstimateId: number): Promise<DraftEstimate> {
  return fetchFromApi(`${BASE_URL}/drafts/${uuid}/publish`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ harvestEstimateId })
  });
}
