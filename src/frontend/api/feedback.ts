/**
 * API client functions for submitting feedback (bug reports and feature requests)
 */

export interface FeedbackSubmission {
  type: "bug" | "feature";
  title: string;
  description: string;
  email?: string;
  priority?: "low" | "medium" | "high" | "critical";
}

/**
 * Submit feedback (bug report or feature request)
 * @param feedback The feedback data to submit
 * @returns Promise with the response data
 */
export const submitFeedback = async (feedback: FeedbackSubmission): Promise<{ success: boolean; message?: string; error?: string }> => {
  try {
    const response = await fetch('/api/feedback', {
      method: "POST",
      headers: {
        'Content-Type': "application/json",
      },
      credentials: "include",
      body: JSON.stringify(feedback),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to submit feedback");
    }

    return {
      success: true,
      message: data.message || "Feedback submitted successfully",
    };
  } catch (error: any) {
    console.error('Error submitting feedback:', error);
    return {
      success: false,
      error: error.message || "An unexpected error occurred",
    };
  }
};
