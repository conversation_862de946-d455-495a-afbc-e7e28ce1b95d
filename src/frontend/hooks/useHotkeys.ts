import { useEffect, useCallback } from "react";

type HotkeyCallback = (event: KeyboardEvent) => void;

export const useHotkeys = (keys: string, callback: HotkeyCallback, deps: any[] = []) => {
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    const keyArray = keys.toLowerCase().split('+');
    const hasCtrl = keyArray.includes('ctrl');
    const hasCmd = keyArray.includes('cmd');
    const hasShift = keyArray.includes('shift');
    const hasAlt = keyArray.includes('alt');
    
    const key = keyArray[keyArray.length - 1];
    
    const isCtrlPressed = hasCtrl ? event.ctrlKey : !event.ctrlKey;
    const isCmdPressed = hasCmd ? event.metaKey : !event.metaKey;
    const isShiftPressed = hasShift ? event.shiftKey : !event.shiftKey;
    const isAltPressed = hasAlt ? event.altKey : !event.altKey;
    
    if (
      event.key.toLowerCase() === key &&
      isCtrlPressed &&
      isCmdPressed &&
      isShiftPressed &&
      isAltPressed
    ) {
      event.preventDefault();
      callback(event);
    }
  }, [keys, callback]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown", handleKeyPress);
    };
  }, [handleKeyPress, ...deps]);
};

export default useHotkeys;