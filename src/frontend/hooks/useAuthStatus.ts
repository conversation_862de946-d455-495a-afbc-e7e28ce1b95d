import React, { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { useLoading } from "../contexts/LoadingContext";

/**
 * Custom hook to manage authentication status and related actions.
 */
export const useAuthStatus = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true); // Track initial auth check
  const { setLoading } = useLoading(); // Use global loading context

  // Track if an auth check has been performed
  const authCheckPerformedRef = React.useRef<boolean>(false);

  // Check authentication status on mount and handle URL params/cookies
  useEffect(() => {
    // Return early if we've already performed an auth check
    if (authCheckPerformedRef.current) {
      console.log('Auth check already performed in useAuthStatus, skipping duplicate check');
      return;
    }

    // Mark that we're performing the auth check
    authCheckPerformedRef.current = true;

    // Configure axios to always include credentials
    axios.defaults.withCredentials = true;

    const checkAuth = async () => {
      setIsLoading(true);
      setLoading(true); // Set global loading state
      let authenticated = false;

      // Track start time for performance monitoring
      const startTime = Date.now();

      try {
        // 1. Check for auth_success parameter in URL
        const params = new URLSearchParams(window.location.search);
        if (params.get('auth_success') === 'true') {
          console.log('Auth success parameter detected in URL');
          authenticated = true;
          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);
        }

        // 2. Check for auth_test cookie (only relevant if not authenticated by URL param)
        if (!authenticated) {
          const cookies = document.cookie.split(';').map(cookie => cookie.trim());
          const hasAuthTestCookie = cookies.some(cookie => cookie.startsWith('auth_test='));
          if (hasAuthTestCookie) {
            console.log('Auth test cookie found, session cookies seem to be working');
            // Note: Presence of auth_test cookie doesn't guarantee authentication,
            // but its absence might indicate cookie issues. We still rely on the API check.
          } else {
            console.log('No auth test cookie found, possible cookie issues');
          }
        }

        // 3. Verify authentication status with the backend API
        // Skip API check if already authenticated via URL param to avoid race conditions
        if (!authenticated) {
          try {
            console.log('Checking authentication status via API...');

            // Check authentication status without cache-busting to prevent rate limiting
            const response = await axios.get(`/api/xero/auth-status?source=useAuthStatus`, {
              withCredentials: true,
            });

            console.log('Auth status response:', response.data);
            if (response.data?.authenticated === true) {
              authenticated = true;
            } else {
              console.log('Not authenticated according to API');
            }
          } catch (error) {
            console.error('Error checking auth status via API:', error);
            authenticated = false; // Ensure not authenticated on error
          }
        }

        setIsAuthenticated(authenticated);

        // Log total auth check duration
        const duration = Date.now() - startTime;
        console.log(`Auth check completed in ${duration}ms`);
      } finally {
        setIsLoading(false);
        setLoading(false); // Clear global loading state
      }
    };

    // Run checkAuth only once on component mount
    checkAuth();

    // No dependencies to prevent re-running unless component is remounted
  }, []);

  // Function to handle successful authentication (e.g., after login screen)
  const login = useCallback(() => {
    setIsAuthenticated(true);
  }, []);

  // Function to handle logout
  const logout = useCallback(async () => {
    try {
      setLoading(true); // Set global loading state
      await axios.get('/api/xero/logout', {
        withCredentials: true,
      });
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Error logging out:", error);
      // Optionally handle logout error state if needed
    } finally {
      setLoading(false); // Clear global loading state
    }
  }, [setLoading]);

  return { isAuthenticated, isLoading, login, logout };
};
