/**
 * Custom hook for managing date ranges based on time periods
 */

import { useState, useEffect, useMemo } from "react";
import {
  format,
  addWeeks,
  addMonths,
  addQuarters,
  addYears,
  subWeeks,
  subMonths,
  subQuarters,
  subYears,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfQuarter,
  endOfQuarter,
  startOfYear,
  endOfYear,
} from "date-fns";

// Time period constants
export const TIME_PERIODS = {
  WEEK: "week",
  SEMIMONTH: "semimonth",
  MONTH: "month",
  QUARTER: "quarter",
  FISCAL_YEAR: "fiscal_year",
  YEAR: "year",
  ALL_TIME: "all_time",
  CUSTOM: "custom",
} as const;

export type TimePeriod = (typeof TIME_PERIODS)[keyof typeof TIME_PERIODS];

/**
 * Calculate date range for a given time period
 */
export function calculateDateRange(
  timePeriod: TimePeriod,
  customStartDate?: string,
  customEndDate?: string,
): {
  startDate: Date;
  endDate: Date;
} {
  if (timePeriod === TIME_PERIODS.CUSTOM) {
    if (!customStartDate || !customEndDate) {
      // Return current week as fallback
      return {
        startDate: startOfWeek(new Date(), { weekStartsOn: 1 }),
        endDate: endOfWeek(new Date(), { weekStartsOn: 1 }),
      };
    }
    return {
      startDate: new Date(customStartDate),
      endDate: new Date(customEndDate),
    };
  }

  const today = new Date();

  switch (timePeriod) {
    case TIME_PERIODS.WEEK:
      return {
        startDate: startOfWeek(today, { weekStartsOn: 1 }),
        endDate: endOfWeek(today, { weekStartsOn: 1 }),
      };

    case TIME_PERIODS.SEMIMONTH: {
      const dayOfMonth = today.getDate();
      if (dayOfMonth <= 15) {
        // First half
        return {
          startDate: new Date(today.getFullYear(), today.getMonth(), 1),
          endDate: new Date(today.getFullYear(), today.getMonth(), 15),
        };
      } else {
        // Second half
        return {
          startDate: new Date(today.getFullYear(), today.getMonth(), 16),
          endDate: endOfMonth(today),
        };
      }
    }

    case TIME_PERIODS.MONTH:
      return {
        startDate: startOfMonth(today),
        endDate: endOfMonth(today),
      };

    case TIME_PERIODS.QUARTER:
      return {
        startDate: startOfQuarter(today),
        endDate: endOfQuarter(today),
      };

    case TIME_PERIODS.FISCAL_YEAR: {
      // Assuming fiscal year starts in July
      const currentYear = today.getFullYear();
      const fiscalYearStart =
        today.getMonth() >= 6
          ? new Date(currentYear, 6, 1) // July 1st of current year
          : new Date(currentYear - 1, 6, 1); // July 1st of previous year
      const fiscalYearEnd =
        today.getMonth() >= 6
          ? new Date(currentYear + 1, 5, 30) // June 30th of next year
          : new Date(currentYear, 5, 30); // June 30th of current year
      return {
        startDate: fiscalYearStart,
        endDate: fiscalYearEnd,
      };
    }

    case TIME_PERIODS.YEAR:
      return {
        startDate: startOfYear(today),
        endDate: endOfYear(today),
      };

    case TIME_PERIODS.ALL_TIME:
      // Harvest API has a 1-year limit on time reports
      return {
        startDate: subYears(today, 1),
        endDate: today,
      };

    default:
      return {
        startDate: startOfWeek(today, { weekStartsOn: 1 }),
        endDate: endOfWeek(today, { weekStartsOn: 1 }),
      };
  }
}

/**
 * Navigate to previous period based on time period type
 */
export function navigateToPreviousPeriod(
  timePeriod: TimePeriod,
  currentStartDate: Date,
  currentEndDate: Date,
): { startDate: Date; endDate: Date } {
  switch (timePeriod) {
    case TIME_PERIODS.WEEK:
      return {
        startDate: subWeeks(currentStartDate, 1),
        endDate: subWeeks(currentEndDate, 1),
      };

    case TIME_PERIODS.SEMIMONTH: {
      // If first half of month, go to second half of previous month
      if (currentStartDate.getDate() === 1) {
        const prevMonth = subMonths(currentStartDate, 1);
        return {
          startDate: new Date(
            prevMonth.getFullYear(),
            prevMonth.getMonth(),
            16,
          ),
          endDate: endOfMonth(prevMonth),
        };
      } else {
        // If second half, go to first half of same month
        return {
          startDate: new Date(
            currentStartDate.getFullYear(),
            currentStartDate.getMonth(),
            1,
          ),
          endDate: new Date(
            currentStartDate.getFullYear(),
            currentStartDate.getMonth(),
            15,
          ),
        };
      }
    }

    case TIME_PERIODS.MONTH: {
      const prevMonthStart = subMonths(currentStartDate, 1);
      return {
        startDate: prevMonthStart,
        endDate: endOfMonth(prevMonthStart),
      };
    }

    case TIME_PERIODS.QUARTER: {
      const prevQuarterStart = subQuarters(currentStartDate, 1);
      return {
        startDate: prevQuarterStart,
        endDate: endOfQuarter(prevQuarterStart),
      };
    }

    case TIME_PERIODS.FISCAL_YEAR: {
      const prevFiscalYearStart = subYears(currentStartDate, 1);
      const prevFiscalYearEnd = new Date(
        prevFiscalYearStart.getFullYear() + 1,
        5,
        30,
      ); // June 30th
      return {
        startDate: prevFiscalYearStart,
        endDate: prevFiscalYearEnd,
      };
    }

    case TIME_PERIODS.YEAR: {
      const prevYearStart = subYears(startOfYear(currentStartDate), 1);
      return {
        startDate: prevYearStart,
        endDate: endOfYear(prevYearStart),
      };
    }

    default:
      return { startDate: currentStartDate, endDate: currentEndDate };
  }
}

/**
 * Navigate to next period based on time period type
 */
export function navigateToNextPeriod(
  timePeriod: TimePeriod,
  currentStartDate: Date,
  currentEndDate: Date,
): { startDate: Date; endDate: Date } {
  const today = new Date();

  let nextStart: Date, nextEnd: Date;

  switch (timePeriod) {
    case TIME_PERIODS.WEEK:
      nextStart = addWeeks(currentStartDate, 1);
      nextEnd = addWeeks(currentEndDate, 1);
      break;

    case TIME_PERIODS.SEMIMONTH: {
      // If first half of month, go to second half
      if (currentStartDate.getDate() === 1) {
        nextStart = new Date(
          currentStartDate.getFullYear(),
          currentStartDate.getMonth(),
          16,
        );
        nextEnd = endOfMonth(currentStartDate);
      } else {
        // If second half, go to first half of next month
        const nextMonth = addMonths(currentStartDate, 1);
        nextStart = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 1);
        nextEnd = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 15);
      }
      break;
    }

    case TIME_PERIODS.MONTH: {
      const nextMonthStart = addMonths(currentStartDate, 1);
      nextStart = nextMonthStart;
      nextEnd = endOfMonth(nextMonthStart);
      break;
    }

    case TIME_PERIODS.QUARTER: {
      const nextQuarterStart = addQuarters(currentStartDate, 1);
      nextStart = nextQuarterStart;
      nextEnd = endOfQuarter(nextQuarterStart);
      break;
    }

    case TIME_PERIODS.FISCAL_YEAR: {
      const nextFiscalYearStart = addYears(currentStartDate, 1);
      const nextFiscalYearEnd = new Date(
        nextFiscalYearStart.getFullYear() + 1,
        5,
        30,
      ); // June 30th
      nextStart = nextFiscalYearStart;
      nextEnd = nextFiscalYearEnd;
      break;
    }

    case TIME_PERIODS.YEAR: {
      const nextYearStart = addYears(startOfYear(currentStartDate), 1);
      nextStart = nextYearStart;
      nextEnd = endOfYear(nextYearStart);
      break;
    }

    default:
      return { startDate: currentStartDate, endDate: currentEndDate };
  }

  // Don't allow navigation to future periods
  if (nextStart > today) {
    return { startDate: currentStartDate, endDate: currentEndDate };
  }

  return { startDate: nextStart, endDate: nextEnd };
}

/**
 * Hook for managing date ranges based on time periods
 */
export function useDateRange(
  timePeriod: TimePeriod,
  customStartDate?: string,
  customEndDate?: string,
) {
  const [currentStartDate, setCurrentStartDate] = useState<Date>(
    () =>
      calculateDateRange(timePeriod, customStartDate, customEndDate).startDate,
  );
  const [currentEndDate, setCurrentEndDate] = useState<Date>(
    () =>
      calculateDateRange(timePeriod, customStartDate, customEndDate).endDate,
  );

  // Update date range when time period changes
  useEffect(() => {
    if (timePeriod !== TIME_PERIODS.CUSTOM) {
      const { startDate, endDate } = calculateDateRange(timePeriod);
      setCurrentStartDate(startDate);
      setCurrentEndDate(endDate);
    }
  }, [timePeriod]);

  // Update date range when custom dates change
  useEffect(() => {
    if (
      timePeriod === TIME_PERIODS.CUSTOM &&
      customStartDate &&
      customEndDate
    ) {
      setCurrentStartDate(new Date(customStartDate));
      setCurrentEndDate(new Date(customEndDate));
    }
  }, [timePeriod, customStartDate, customEndDate]);

  const goToPreviousPeriod = () => {
    const { startDate, endDate } = navigateToPreviousPeriod(
      timePeriod,
      currentStartDate,
      currentEndDate,
    );
    setCurrentStartDate(startDate);
    setCurrentEndDate(endDate);
  };

  const goToNextPeriod = () => {
    const { startDate, endDate } = navigateToNextPeriod(
      timePeriod,
      currentStartDate,
      currentEndDate,
    );
    setCurrentStartDate(startDate);
    setCurrentEndDate(endDate);
  };

  // Check if next period navigation is disabled (for future dates)
  const isNextDisabled = useMemo(() => {
    const today = new Date();
    const { startDate: nextStart } = navigateToNextPeriod(
      timePeriod,
      currentStartDate,
      currentEndDate,
    );
    return nextStart > today;
  }, [timePeriod, currentStartDate, currentEndDate]);

  return {
    startDate: currentStartDate,
    endDate: currentEndDate,
    goToPreviousPeriod,
    goToNextPeriod,
    isNextDisabled,
    setCurrentStartDate,
    setCurrentEndDate,
  };
}

/**
 * Hook for managing date range navigation
 */
export function useDateRangeNavigation(timePeriod: TimePeriod) {
  const {
    startDate,
    endDate,
    goToPreviousPeriod,
    goToNextPeriod,
    isNextDisabled,
  } = useDateRange(timePeriod);

  return {
    startDate,
    endDate,
    goToPreviousPeriod,
    goToNextPeriod,
    isNextDisabled,
    // Formatted date strings for display
    dateRangeLabel: `${format(startDate, "MMM dd, yyyy")} - ${format(endDate, "MMM dd, yyyy")}`,
    // API-ready date strings
    apiStartDate: format(startDate, "yyyy-MM-dd"),
    apiEndDate: format(endDate, "yyyy-MM-dd"),
  };
}
