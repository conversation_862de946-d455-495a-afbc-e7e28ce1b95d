/**
 * DEPRECATED - This hook has been split into useEstimateDrafts and useEstimatePublishing
 * for better separation of concerns. Please use those hooks instead.
 * This file is kept for backward compatibility only and will be removed in a future update.
 */

import { useState, useCallback } from "react";
import {
  EstimatePayload,
  EstimateLineItemPayload,
  publishEstimate,
  SaveEstimateResponse,
  CurrentUser, // Needed for draft payload
} from "../api/harvest";
import {
  createDraftEstimate,
  updateDraftEstimate,
  publishDraftEstimate,
} from "../api/estimates";
import { DraftEstimateCreate, DraftEstimateUpdate } from "../../types/api"; // Correct import path for types
import {
  AllocationWithTotals,
  ProjectTotals,
} from "./useEstimateStaffManagement"; // Import calculated types
import { format } from "date-fns";

// Define the shape of the data returned by the hook
export interface EstimateSavingState {
  isSaving: boolean;
  saveError: string | null;
  saveSuccessMessage: string | null;
  savedEstimateId: number | string | null; // Harvest ID or Draft UUID if only draft saved
  isSavedSuccessfully: boolean; // Specifically tracks successful Harvest save
  draftUuid: string | null;
  handleSaveDraft: (
    estimateData: {
      clientId: number;
      clientName: string; // Need client name for draft
      projectName?: string;
      startDate: Date;
      endDate: Date;
      // Corrected: handleSaveDraft uses base staffAllocations, not calculated AllocationWithTotals
      staffAllocations: StaffAllocation[]; // Need base allocations for draft payload
    },
    currentUser: CurrentUser | null, // Pass current user
  ) => Promise<boolean>; // Returns true on success
  handlePublishToHarvest: (
    // Add parameters needed for potential draft save
    estimateData: {
      clientId: number;
      clientName: string; // Needed for draft
      startDate: Date; // Needed for draft
      endDate: Date; // Needed for draft
      staffAllocations: StaffAllocation[]; // Needed for draft
      projectName?: string;
      allocationsWithTotals: AllocationWithTotals[]; // Need calculated totals for Harvest payload
      projectTotals: ProjectTotals;
    },
    currentUser: CurrentUser | null, // Needed for draft
  ) => Promise<boolean>; // Returns true on success
  resetSaveStatus: () => void;
  setDraftUuid: React.Dispatch<React.SetStateAction<string | null>>; // Allow setting UUID when loading
  setSavedEstimateId: React.Dispatch<
    React.SetStateAction<number | string | null>
  >; // Allow setting ID when loading
}

// Import StaffAllocation type separately if not re-exported from useEstimateStaffManagement
import { StaffAllocation } from "../components/EstimatePage";

export const useEstimateSaving = (
  initialDraftUuid: string | null = null,
  initialSavedEstimateId: number | string | null = null,
): EstimateSavingState => {
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccessMessage, setSaveSuccessMessage] = useState<string | null>(
    null,
  );
  const [savedEstimateId, setSavedEstimateId] = useState<
    number | string | null
  >(initialSavedEstimateId);
  const [isSavedSuccessfully, setIsSavedSuccessfully] = useState<boolean>(
    !!initialSavedEstimateId && typeof initialSavedEstimateId === "number",
  ); // True if initial ID is Harvest ID
  const [draftUuid, setDraftUuid] = useState<string | null>(initialDraftUuid);

  const resetSaveStatus = useCallback(() => {
    setIsSaving(false);
    setSaveError(null);
    setSaveSuccessMessage(null);
    // Keep savedEstimateId and draftUuid unless explicitly reset elsewhere
    // setIsSavedSuccessfully(false); // Should this reset? Maybe only on creating new estimate
  }, []);

  const handleSaveDraft = useCallback(
    async (
      estimateData: {
        clientId: number;
        clientName: string;
        projectName?: string;
        startDate: Date;
        endDate: Date;
        staffAllocations: StaffAllocation[]; // Use base allocations
      },
      currentUser: CurrentUser | null,
    ): Promise<boolean> => {
      if (!currentUser) {
        setSaveError("Cannot save draft: User data is not available.");
        return false;
      }
      if (
        !estimateData.clientId ||
        !estimateData.clientName ||
        !estimateData.startDate ||
        !estimateData.endDate
      ) {
        setSaveError("Cannot save draft: Client or dates missing.");
        return false;
      }

      setIsSaving(true);
      setSaveError(null);
      setSaveSuccessMessage(null);

      try {
        // Create the payload
        const payload: DraftEstimateCreate | DraftEstimateUpdate = {
          userId: currentUser.id.toString(),
          clientId: estimateData.clientId,
          clientName: estimateData.clientName,
          projectName: estimateData.projectName,
          startDate: format(estimateData.startDate, "yyyy-MM-dd"),
          endDate: format(estimateData.endDate, "yyyy-MM-dd"),
          allocations: estimateData.staffAllocations.map((alloc) => ({
            internalId: alloc.internalId,
            harvestUserId: alloc.harvestUserId,
            firstName: alloc.firstName,
            lastName: alloc.lastName,
            projectRole: alloc.projectRole,
            level: alloc.level,
            onbordTargetRateDaily: alloc.onbordTargetRateDaily,
            onbordCostRateDaily: alloc.onbordCostRateDaily,
            rateProposedDaily: alloc.rateProposedDaily,
            weeklyAllocation: alloc.weeklyAllocation,
          })),
        };

        // Maximum number of retry attempts
        const MAX_RETRIES = 2;
        let retryCount = 0;
        let result;

        // Retry loop for 401 errors
        while (retryCount <= MAX_RETRIES) {
          try {
            // Attempt to save/update
            if (draftUuid) {
              console.log(
                `Updating draft (attempt ${retryCount + 1}/${MAX_RETRIES + 1}):`,
                draftUuid,
              );
              result = await updateDraftEstimate(
                draftUuid,
                payload as DraftEstimateUpdate,
              );
            } else {
              console.log(
                `Creating draft (attempt ${retryCount + 1}/${MAX_RETRIES + 1}):`,
              );
              result = await createDraftEstimate(
                payload as DraftEstimateCreate,
              );
              setDraftUuid(result.uuid);
            }

            // If we get here, save succeeded - exit the retry loop
            setSaveSuccessMessage("Draft saved successfully");
            setSaveError(null);
            setIsSaving(false);
            return true;
          } catch (error) {
            // Handle authentication errors with retry
            const isAuthError =
              error instanceof Error &&
              (error.message.includes("401") ||
                error.message.includes("Unauthorized"));

            if (isAuthError && retryCount < MAX_RETRIES) {
              console.log(
                `Authentication error on attempt ${retryCount + 1}, retrying in 500ms...`,
              );
              await new Promise((resolve) => setTimeout(resolve, 500));
              retryCount++;
              // Continue to next iteration of while loop
            } else {
              // For other errors or if we've exhausted retries, rethrow
              throw error;
            }
          }
        }

        // We shouldn't reach here, but if we do (exhausted retries):
        setSaveError(
          "Failed to save after multiple attempts. Please try again.",
        );
        setIsSaving(false);
        return false;
      } catch (error) {
        // Final error handler
        console.error("Failed to save draft:", error);
        setSaveError(
          error instanceof Error ? error.message : "Unknown error saving draft",
        );
        setSaveSuccessMessage(null);
        setIsSaving(false);
        return false;
      }
    },
    [draftUuid],
  ); // Dependency on draftUuid

  // Add handleSaveDraft to dependency array if called internally? No, useCallback handles this.
  // Add currentUser to dependency array.
  const handlePublishToHarvest = useCallback(
    async (
      // Add parameters needed for potential draft save
      estimateData: {
        clientId: number;
        clientName: string; // Needed for draft
        startDate: Date; // Needed for draft
        endDate: Date; // Needed for draft
        staffAllocations: StaffAllocation[]; // Needed for draft
        projectName?: string;
        allocationsWithTotals: AllocationWithTotals[];
        projectTotals: ProjectTotals;
      },
      currentUser: CurrentUser | null, // Needed for draft
    ): Promise<boolean> => {
      // Validation
      if (!currentUser) {
        setSaveError("Cannot publish to Harvest: User data not available.");
        return false;
      }
      if (
        !estimateData.clientId ||
        estimateData.allocationsWithTotals.length === 0
      ) {
        setSaveError(
          "Cannot publish estimate to Harvest: Client not selected or no staff allocated.",
        );
        return false;
      }

      setIsSaving(true);
      setSaveError(null);
      setSaveSuccessMessage(null);

      // --- Auto-Save Draft Logic ---
      const currentDraftUuid = draftUuid; // Use const, capture current draftUuid state
      if (!currentDraftUuid) {
        console.log("Auto-saving draft before publishing to Harvest...");
        // Pass the currentUser argument received by handleSaveToHarvest
        const draftSaveSuccess = await handleSaveDraft(
          {
            clientId: estimateData.clientId,
            clientName: estimateData.clientName,
            projectName: estimateData.projectName,
            startDate: estimateData.startDate,
            endDate: estimateData.endDate,
            staffAllocations: estimateData.staffAllocations,
          },
          currentUser,
        );

        if (!draftSaveSuccess) {
          // Error would be set within handleSaveDraft
          console.error("Auto-save draft failed. Aborting Harvest publish.");
          setIsSaving(false); // Ensure saving state is reset
          return false;
        }
        // Need to get the *new* draftUuid set by handleSaveDraft.
        // Since state updates might not be immediate, we rely on the fact
        // that handleSaveDraft calls setDraftUuid internally.
        // Re-accessing draftUuid *might* not work reliably here.
        // A safer approach might be for handleSaveDraft to RETURN the uuid.
        // --- Let's assume for now the state update is fast enough ---
        // Re-check draftUuid state after awaiting handleSaveDraft
        // This is still potentially problematic. Refactor handleSaveDraft later if needed.
        // For now, we proceed assuming draftUuid state is updated.
        // We will use the draftUuid state variable directly later when publishing.
        console.log("Auto-save draft successful before publishing to Harvest.");
      }
      // --- End Auto-Save Draft Logic ---

      try {
        // Format line items from calculated totals
        const lineItems: EstimateLineItemPayload[] =
          estimateData.allocationsWithTotals
            .filter((alloc) => alloc.totalDays > 0) // Only include staff with allocated time
            .map((alloc) => ({
              kind: "Service", // Use a default kind, or derive from role if needed
              description: `${alloc.firstName} ${alloc.lastName} - ${alloc.projectRole}`,
              quantity: alloc.totalDays,
              unit_price: alloc.rateProposedDaily,
              taxed: true, // Set all line items to be taxable with the Australian GST
            }));

        if (lineItems.length === 0) {
          setSaveError(
            "Cannot publish estimate: No time allocated to any staff member.",
          );
          setIsSaving(false);
          return false;
        }

        const payload: EstimatePayload = {
          client_id: estimateData.clientId,
          subject:
            estimateData.projectName ||
            `Estimate for Client ${estimateData.clientId}`,
          issue_date: format(new Date(), "yyyy-MM-dd"), // Use today's date as issue date
          line_items: lineItems,
          tax: 10, // Australian GST rate (10%) - TODO: Make configurable?
          currency: "AUD", // TODO: Make configurable?
          notes:
            "Quote valid for 7 days. All prices exclude GST and additional expenses (e.g., travel, accommodation, materials). Work to be performed as per Onbord Pty Ltd's standard Consultancy Agreement. Payment terms are 15 days from invoice date. Onbord Pty. Ltd. reserves the right to adjust pricing if the scope of work changes. All information in this quote is confidential and proprietary to Onbord Pty Ltd. This quote is subject to availability and final agreement of terms.",
        };

        console.log("Publishing estimate payload to Harvest:", payload);
        const result: SaveEstimateResponse = await publishEstimate(payload);
        console.log("Estimate published successfully to Harvest:", result);

        // Store both the number for display and the ID for links
        const estimateNumber = result.number; // This is what users see
        const estimateId = result.id; // This is what we need for the URL
        setSavedEstimateId(estimateId); // Store the actual ID for linking
        setSaveSuccessMessage(
          `Estimate ${estimateNumber ? `#${estimateNumber}` : ""} published successfully to Harvest.`,
        );
        setIsSavedSuccessfully(true);
        setSaveError(null);

        // If we have a draft, mark it as published
        if (draftUuid) {
          try {
            await publishDraftEstimate(draftUuid, result.id);
            console.log(
              "Draft marked as published with Harvest ID:",
              result.id,
            );
          } catch (publishError) {
            console.error(
              "Error linking draft to Harvest estimate:",
              publishError,
            );
            // Don't overwrite the main success message, but maybe log it?
            // setSaveError(`Estimate saved, but failed to link draft: ${publishError.message}`);
          }
        }
        setIsSaving(false);
        return true;
      } catch (error) {
        console.error("Failed to publish estimate to Harvest:", error);
        setSaveError(
          error instanceof Error
            ? error.message
            : "An unknown error occurred while publishing to Harvest.",
        );
        setSaveSuccessMessage(null);
        setSavedEstimateId(null); // Clear saved ID on error
        setIsSavedSuccessfully(false);
        setIsSaving(false);
        return false;
      }
    },
    [draftUuid, handleSaveDraft],
  ); // Removed currentUser from dependency array

  return {
    isSaving,
    saveError,
    saveSuccessMessage,
    savedEstimateId,
    isSavedSuccessfully,
    draftUuid,
    handleSaveDraft,
    handlePublishToHarvest,
    resetSaveStatus,
    setDraftUuid,
    setSavedEstimateId,
  };
};
