import { useMemo } from "react";
import { startOfWeek, addWeeks, format, isBefore, isEqual, getISOWeek } from "date-fns";
import { WeekInfo } from "../types/estimate-types";

/**
 * Hook to generate week information based on start and end dates
 * @param startDate Project start date
 * @param endDate Project end date
 * @returns Array of week information objects
 */
export function useWeeks(startDate?: Date, endDate?: Date): WeekInfo[] {
  return useMemo(() => {
    if (!startDate || !endDate || isBefore(endDate, startDate)) {
      return []; // Return empty if dates are invalid or missing
    }

    const weeksArray: WeekInfo[] = [];
    // Ensure we start from the beginning of the week containing the startDate
    let currentWeekStart = startOfWeek(startDate, { weekStartsOn: 1 }); // Assuming week starts on Monday
    let weekCounter = 1;

    while (isBefore(currentWeekStart, endDate) || isEqual(currentWeekStart, startOfWeek(endDate, { weekStartsOn: 1 }))) {
      // Use ISO week format YYYY-WW as expected by the database
      const year = currentWeekStart.getFullYear();
      const weekNumber = getISOWeek(currentWeekStart);
      const weekIdentifier = `${year}-${weekNumber.toString().padStart(2, '0')}`;
      const weekLabel = `Week ${weekCounter} (${format(currentWeekStart, 'dd-MMM')})`;
      
      // Create compact label for smaller displays with date parts
      const dateLabel = format(currentWeekStart, 'dd/M'); // More compact date format
      
      // Apply alternating background to every other week for visual distinction
      const hasAlternatingBackground = weekCounter % 2 === 0;
      
      weeksArray.push({ 
        identifier: weekIdentifier, 
        label: weekLabel, 
        shortLabel: `W${weekCounter} ${dateLabel}`,
        hasAlternatingBackground 
      });

      currentWeekStart = addWeeks(currentWeekStart, 1);
      weekCounter++;
    }

    return weeksArray;
  }, [startDate, endDate]);
}

export default useWeeks;