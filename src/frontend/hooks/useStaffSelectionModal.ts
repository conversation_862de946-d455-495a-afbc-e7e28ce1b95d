import { useState, useCallback } from "react";
import { User, getActiveUsers } from "../api/harvest";

// Define the shape of the data returned by the hook
export interface StaffSelectionModalState {
  isStaffModalOpen: boolean;
  availableUsers: User[] | null;
  isLoadingUsers: boolean;
  userFetchError: string | null;
  openStaffSelectionModal: () => void;
  closeStaffSelectionModal: () => void;
}

export const useStaffSelectionModal = (): StaffSelectionModalState => {
  const [isStaffModalOpen, setIsStaffModalOpen] = useState<boolean>(false);
  const [availableUsers, setAvailableUsers] = useState<User[] | null>(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState<boolean>(false);
  const [userFetchError, setUserFetchError] = useState<string | null>(null);

  // Handler to open the modal and fetch users if needed
  const openStaffSelectionModal = useCallback(async () => {
    setIsStaffModalOpen(true);
    setUserFetchError(null); // Clear previous errors

    // Fetch users only if not already fetched or if there was a previous error
    if (!availableUsers || userFetchError) {
      setIsLoadingUsers(true);
      try {
        console.log("Fetching active users for modal...");
        const users = await getActiveUsers();
        console.log("Fetched users for modal:", users);
        setAvailableUsers(users);
        setUserFetchError(null); // Clear error on success
      } catch (error) {
        console.error("Failed to fetch users for modal:", error);
        setUserFetchError(error instanceof Error ? error.message : "Failed to load staff list.");
        setAvailableUsers([]); // Set to empty array on error to avoid re-fetching constantly if modal is reopened
      } finally {
        setIsLoadingUsers(false);
      }
    }
  }, [availableUsers, userFetchError]); // Dependencies

  // Handler to close the modal
  const closeStaffSelectionModal = useCallback(() => {
    setIsStaffModalOpen(false);
    // Optionally clear users/error when closing, or keep them cached
    // setUserFetchError(null);
    // setAvailableUsers(null);
  }, []);

  return {
    isStaffModalOpen,
    availableUsers,
    isLoadingUsers,
    userFetchError,
    openStaffSelectionModal,
    closeStaffSelectionModal,
  };
};
