import { useState, useEffect } from "react";
import { getCurrentUser, CurrentUser } from '../api/harvest'; // Import the API function and type

interface UseCurrentUserPermissionsResult {
  canSaveEstimate: boolean;
  isLoadingPermissions: boolean;
  permissionError: string | null;
  currentUser: CurrentUser | null; // Optionally return the user object
}

/**
 * Hook to fetch the current user's details and determine if they have
 * permissions to save Harvest estimates.
 *
 * Required roles: "estimates_manager" or 'administrator'.
 */
export const useCurrentUserPermissions = (): UseCurrentUserPermissionsResult => {
  // Rely on type inference from initial values
  const [currentUser, setCurrentUser] = useState(null as CurrentUser | null);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(true);
  const [permissionError, setPermissionError] = useState(null as string | null);
  const [canSaveEstimate, setCanSaveEstimate] = useState(false);

  useEffect(() => {
    const fetchUserPermissions = async () => {
      setIsLoadingPermissions(true);
      setPermissionError(null);
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);

        // Check for required roles
        const hasPermission = user.access_roles.includes('estimates_manager') ||
                              user.access_roles.includes('administrator');
        setCanSaveEstimate(hasPermission);

      } catch (error) {
        console.error("Failed to fetch current user permissions:", error);
        setPermissionError(error instanceof Error ? error.message : "Failed to load user permissions.");
        setCanSaveEstimate(false); // Default to no permission on error
      } finally {
        setIsLoadingPermissions(false);
      }
    };

    fetchUserPermissions();
  }, []); // Empty dependency array means this runs once on mount

  return { canSaveEstimate, isLoadingPermissions, permissionError, currentUser };
};
