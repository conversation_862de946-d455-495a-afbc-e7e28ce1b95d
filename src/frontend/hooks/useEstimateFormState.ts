import { useState, useEffect, useMemo, useCallback } from "react";
import { format, addMonths } from "date-fns";
import { Deal, Company } from "../types/crm-types";
import { getCompanies } from "../api/crm";

// Define the shape of the data returned by the hook
export interface EstimateFormState {
  selectedClientId: string | "";
  projectName: string;
  startDateStr: string;
  endDateStr: string;
  invoiceFrequency: string;
  paymentTerms: number | null;
  billingType: "daily" | "hourly";
  hoursPerDay: number;
  clientSearchTerm: string;
  availableClients: Company[];
  filteredClients: Company[];
  clientDropdownOpen: boolean;
  activeDuration: string;
  isLoadingClients: boolean;
  clientFetchError: string | null;
  formError: string | null;
  isDealLinked: boolean; // Track if a deal is linked
  handleClientChange: (clientId: string) => void;
  handleProjectNameChange: (name: string) => void;
  handleDateChange: (date: string, isStart: boolean) => void;
  handleDurationSelect: (preset: string) => void;
  handleInvoiceFrequencyChange: (frequency: string) => void;
  handlePaymentTermsChange: (terms: number | null) => void;
  handleBillingTypeChange: (type: "daily" | "hourly") => void;
  handleHoursPerDayChange: (hours: number) => void;
  setClientSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  setClientDropdownOpen: React.Dispatch<React.SetStateAction<boolean>>;
  validateAndGetFormData: (isDealLinked?: boolean) => { clientId: number; clientName: string; startDateStr: string; endDateStr: string; projectName?: string; invoiceFrequency?: string; paymentTerms?: number; companyId?: string; billingType?: "daily" | "hourly"; hoursPerDay?: number } | null;
  resetForm: (defaults?: { startDate?: string; endDate?: string }) => void; // Added reset function
  setFormData: (data: { clientId: string; clientName: string; projectName: string; startDate: string; endDate: string; invoiceFrequency?: string; paymentTerms?: number; billingType?: "daily" | "hourly"; hoursPerDay?: number }) => void; // Added function to set form data externally (e.g., from loaded draft)
  setFormDataFromDeal: (deal: Deal, clientId?: string) => void; // Added function to populate form from deal data
}

// Define initial values for dates
const today = new Date();
const defaultStartDate = format(today, 'yyyy-MM-dd');
const defaultEndDate = format(addMonths(today, 1), 'yyyy-MM-dd');

export const useEstimateFormState = (
  initialData?: { clientId?: string; clientName?: string; projectName?: string; startDate?: string; endDate?: string; invoiceFrequency?: string; paymentTerms?: number; billingType?: "daily" | "hourly"; hoursPerDay?: number }
): EstimateFormState => {
  // State for client fetching and selection
  const [allCompanies, setAllCompanies] = useState<Company[]>([]); // Store all companies
  const [availableClients, setAvailableClients] = useState<Company[]>([]); // Filtered companies for display
  const [selectedClientId, setSelectedClientId] = useState<string | "">(initialData?.clientId || "");
  const [clientSearchTerm, setClientSearchTerm] = useState<string>(initialData?.clientName || "");
  const [clientDropdownOpen, setClientDropdownOpen] = useState<boolean>(false);
  const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);
  const [clientFetchError, setClientFetchError] = useState<string | null>(null);

  // State for project name
  const [projectName, setProjectName] = useState<string>(initialData?.projectName || "");

  // State for dates and duration
  const [startDateStr, setStartDateStr] = useState<string>(initialData?.startDate || defaultStartDate);
  const [endDateStr, setEndDateStr] = useState<string>(initialData?.endDate || defaultEndDate);
  const [activeDuration, setActiveDuration] = useState<string>('custom'); // Default to custom, let selection update it

  // State for invoice and payment settings
  const [invoiceFrequency, setInvoiceFrequency] = useState<string>(initialData?.invoiceFrequency || "");
  const [paymentTerms, setPaymentTerms] = useState<number | null>(initialData?.paymentTerms || null);

  // State for billing type and hours per day
  const [billingType, setBillingType] = useState<'daily' | "hourly">(initialData?.billingType || "daily");
  const [hoursPerDay, setHoursPerDay] = useState<number>(initialData?.hoursPerDay || 7.5);

  // State for form validation errors
  const [formError, setFormError] = useState<string | null>(null);

  // Always deal-linked now
  const isDealLinked = true;

  // Fetch companies on mount
  useEffect(() => {
    const fetchCompanies = async () => {
      setIsLoadingClients(true);
      setClientFetchError(null);
      try {
        const fetchedCompanies = await getCompanies();

        // Store all companies
        setAllCompanies(fetchedCompanies);

        // Show all companies for client selection (both manual and deal-linked estimates)
        setAvailableClients(fetchedCompanies);

        // If initialData provided a clientId, ensure the clientName is set correctly
        if (initialData?.clientId && !initialData.clientName) {
            const company = fetchedCompanies.find(c => c.id === initialData.clientId);
            if (company) {
                setClientSearchTerm(company.name);
            }
        }
      } catch (err) {
        console.error("Failed to fetch companies:", err);
        setClientFetchError("Failed to load companies from Upstream database.");
      } finally {
        setIsLoadingClients(false);
      }
    };
    fetchCompanies();
  }, [initialData?.clientId, initialData?.clientName]);

  // Update client search term when selected client changes (if not manually searching)
  useEffect(() => {
    if (selectedClientId && !clientDropdownOpen) { // Only update if dropdown is closed (i.e., selection made)
      const selectedClient = availableClients.find((c: Company) => c.id === selectedClientId);
      if (selectedClient && clientSearchTerm !== selectedClient.name) {
        setClientSearchTerm(selectedClient.name);
      }
    }
  }, [selectedClientId, availableClients, clientDropdownOpen, clientSearchTerm]);

  // Filter clients based on search term
  const filteredClients = useMemo(() => {
    // Always show all available clients when dropdown is open, filtered by search term
    if (!clientSearchTerm.trim()) return availableClients;
    return availableClients.filter((client: Company) =>
      client.name.toLowerCase().includes(clientSearchTerm.toLowerCase())
    );
  }, [availableClients, clientSearchTerm]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (clientDropdownOpen && !(e.target as Element).closest('#clientSearch')) { // Assumes input has id= 'clientSearch'
        setClientDropdownOpen(false);
      }
    };
    document.addEventListener('click', handleOutsideClick);
    return () => document.removeEventListener('click', handleOutsideClick);
  }, [clientDropdownOpen]);

  // Handler for client change
  const handleClientChange = useCallback((clientId: string) => {
    setSelectedClientId(clientId);
    const company = availableClients.find((c: Company) => c.id === clientId);
    if (company) {
        setClientSearchTerm(company.name); // Update search term on selection
    }
    setClientDropdownOpen(false); // Close dropdown on selection
    setFormError(null); // Clear errors on change
  }, [availableClients]);

  // Handler for project name change
  const handleProjectNameChange = useCallback((name: string) => {
    setProjectName(name);
  }, []);

  // Handler for date changes
  const handleDateChange = useCallback((date: string, isStart: boolean) => {
    if (isStart) {
      setStartDateStr(date);
    } else {
      setEndDateStr(date);
    }
    setActiveDuration('custom'); // Any manual date change makes duration custom
    setFormError(null); // Clear errors on change
  }, []);

  // Handler for duration preset selection
  const handleDurationSelect = useCallback((preset: string) => {
    const start = new Date(startDateStr); // Use current start date string
    const end = new Date(start); // Clone start date

    switch (preset) {
      case '1week': end.setDate(start.getDate() + 7); break;
      case '2weeks': end.setDate(start.getDate() + 14); break;
      case '3weeks': end.setDate(start.getDate() + 21); break;
      case '6weeks': end.setDate(start.getDate() + 42); break;
      case '1month': end.setMonth(start.getMonth() + 1); break;
      case '2months': end.setMonth(start.getMonth() + 2); break;
      case '3months': end.setMonth(start.getMonth() + 3); break;
      case '6months': end.setMonth(start.getMonth() + 6); break;
      default: setActiveDuration('custom'); return; // Do nothing if preset is invalid
    }

    setActiveDuration(preset);
    setEndDateStr(format(end, 'yyyy-MM-dd'));
    setFormError(null); // Clear errors on change
  }, [startDateStr]); // Depends only on startDateStr

  // Handler for invoice frequency change
  const handleInvoiceFrequencyChange = useCallback((frequency: string) => {
    setInvoiceFrequency(frequency);
    setFormError(null); // Clear errors on change
  }, []);

  // Handler for payment terms change
  const handlePaymentTermsChange = useCallback((terms: number | null) => {
    setPaymentTerms(terms);
    setFormError(null); // Clear errors on change
  }, []);

  // Handler for billing type change
  const handleBillingTypeChange = useCallback((type: "daily" | "hourly") => {
    setBillingType(type);
    setFormError(null); // Clear errors on change
  }, []);

  // Handler for hours per day change
  const handleHoursPerDayChange = useCallback((hours: number) => {
    setHoursPerDay(hours);
    setFormError(null); // Clear errors on change
  }, []);

  // Function to validate form and return data for estimate creation
  const validateAndGetFormData = useCallback(() => {
    setFormError(null); // Clear previous errors

    if (!selectedClientId) {
      setFormError("Please select a deal first.");
      return null;
    }
    if (!startDateStr || !endDateStr) {
      setFormError("Please set start and end dates.");
      return null;
    }
    // Always require invoice frequency and payment terms for estimate creation
    if (!invoiceFrequency) {
      setFormError("Please select an invoice frequency.");
      return null;
    }
    if (!paymentTerms) {
      setFormError("Please select payment terms.");
      return null;
    }

    const start = new Date(startDateStr);
    const end = new Date(endDateStr);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        setFormError("Invalid date format.");
        return null;
    }

    // Set time to avoid timezone issues / ensure full days are compared
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    if (end < start) {
      setFormError("End date cannot be before the start date.");
      return null;
    }

    // Find the selected company in all companies (not just available clients)
    const selectedCompany = allCompanies.find((c: Company) => c.id === selectedClientId);
    if (!selectedCompany) {
      setFormError("Selected client not found.");
      return null;
    }

    // For deal-linked estimates, use the company ID directly
    // The estimate will be linked to the deal, so Harvest ID is not required
    return {
      clientId: selectedCompany.harvestId || 0, // Use 0 as placeholder if no Harvest ID
      clientName: selectedCompany.name,
      companyId: selectedCompany.id, // Include the unified company ID
      startDateStr,
      endDateStr,
      projectName: projectName || undefined,
      invoiceFrequency: invoiceFrequency || undefined,
      paymentTerms: paymentTerms || undefined,
      billingType: billingType,
      hoursPerDay: hoursPerDay,
    };
  }, [selectedClientId, startDateStr, endDateStr, projectName, invoiceFrequency, paymentTerms, billingType, hoursPerDay, allCompanies]);

  // Function to reset the form state
  const resetForm = useCallback((defaults?: { startDate?: string; endDate?: string }) => {
      setSelectedClientId('');
      setProjectName('');
      setClientSearchTerm('');
      setClientDropdownOpen(false);
      setStartDateStr(defaults?.startDate || defaultStartDate);
      setEndDateStr(defaults?.endDate || defaultEndDate);
      setActiveDuration('1month'); // Reset duration too
      setInvoiceFrequency(''); // Reset invoice frequency
      setPaymentTerms(null); // Reset payment terms
      setBillingType('daily'); // Reset billing type to default
      setHoursPerDay(8.0); // Reset hours per day to default
      setFormError(null);
      setClientFetchError(null);

      // Reset available clients to show all companies
      setAvailableClients(allCompanies);
  }, [allCompanies]);

  // Function to set form data externally (e.g., when loading a draft)
  const setFormData = useCallback((data: { clientId: string; clientName: string; projectName: string; startDate: string; endDate: string; invoiceFrequency?: string; paymentTerms?: number; billingType?: "daily" | "hourly"; hoursPerDay?: number }) => {
      // Log the incoming data for debugging
      console.log('useEstimateFormState: Setting form data with invoice and billing settings:', {
        invoiceFrequency: data.invoiceFrequency,
        paymentTerms: data.paymentTerms,
        billingType: data.billingType,
        hoursPerDay: data.hoursPerDay
      });

      setSelectedClientId(data.clientId);
      setClientSearchTerm(data.clientName);
      setProjectName(data.projectName);
      setStartDateStr(data.startDate);
      setEndDateStr(data.endDate);
      setInvoiceFrequency(data.invoiceFrequency || "");
      setPaymentTerms(data.paymentTerms || null);
      setBillingType(data.billingType || "daily");
      setHoursPerDay(data.hoursPerDay || 8.0);

      // Log the state after setting for debugging
      console.log('useEstimateFormState: State after setting:', {
        invoiceFrequency: data.invoiceFrequency || "",
        paymentTerms: data.paymentTerms || null,
        billingType: data.billingType || "daily",
        hoursPerDay: data.hoursPerDay || 8.0
      });

      setActiveDuration('custom'); // Assume loaded data is custom duration
      setFormError(null);
  }, []);

  // Function to populate form data from a deal
  const setFormDataFromDeal = useCallback((deal: Deal, clientId?: string) => {
    console.log('🔍 SETTING FORM DATA FROM DEAL:', deal.name);
    console.log('🔍 Deal company ID:', deal.companyId);
    console.log('🔍 Deal company object:', deal.company);

    // Clear any previous error
    setClientFetchError(null);

    // When a deal is selected, we should use the deal's company directly
    // The deal.companyId should point to a company in our unified database
    if (deal.companyId) {
      // If companies are still loading, store the deal company ID to select later
      if (isLoadingClients || allCompanies.length === 0) {
        console.log('🔍 Companies still loading, deferring company selection');
        // Store the deal company ID for later selection
        setSelectedClientId(deal.companyId);
        // Set the company name from the deal for display
        if (deal.company?.name) {
          setClientSearchTerm(deal.company.name);
        }
      } else {
        // Find the company in our all companies list (not just Harvest-linked ones)
        const dealCompany = allCompanies.find((c: Company) => c.id === deal.companyId);

        if (dealCompany) {
          console.log('🔍 FOUND DEAL COMPANY IN DATABASE:', dealCompany);
          setSelectedClientId(dealCompany.id);
          setClientSearchTerm(dealCompany.name);
        } else {
          console.error('🔍 DEAL COMPANY NOT FOUND IN DATABASE:', deal.companyId);
          // Fallback: try to find by company name if company object exists
          if (deal.company?.name) {
            const companyByName = allCompanies.find((c: Company) =>
              c.name.toLowerCase().trim() === deal.company?.name?.toLowerCase().trim()
            );

            if (companyByName) {
              console.log('🔍 FOUND COMPANY BY NAME:', companyByName);
              setSelectedClientId(companyByName.id);
              setClientSearchTerm(companyByName.name);
            } else {
              console.log('🔍 COMPANY NOT FOUND - setting search term for manual selection');
              setClientSearchTerm(deal.company.name);
            }
          }
        }
      }
    } else if (clientId) {
      // Legacy support for explicit clientId parameter
      setSelectedClientId(clientId);
      const company = allCompanies.find((c: Company) => c.id === clientId);
      if (company) {
        setClientSearchTerm(company.name);
      }
    }

    // Set project name from deal name
    if (deal.name) {
      setProjectName(deal.name);
    }

    // Set dates if available
    if (deal.startDate) {
      setStartDateStr(deal.startDate);
    }
    if (deal.endDate) {
      setEndDateStr(deal.endDate);
    }

    // Set invoice frequency if available
    if (deal.invoiceFrequency) {
      setInvoiceFrequency(deal.invoiceFrequency);
    }

    // Set payment terms if available
    if (deal.paymentTerms) {
      setPaymentTerms(deal.paymentTerms);
    }

    setActiveDuration('custom'); // Assume deal data is custom duration
    setFormError(null);
  }, [allCompanies, isLoadingClients]);

  // Effect to properly set company when companies finish loading and we have a deferred selection
  useEffect(() => {
    if (!isLoadingClients && allCompanies.length > 0 && selectedClientId && isDealLinked) {
      const company = allCompanies.find((c: Company) => c.id === selectedClientId);
      if (company && clientSearchTerm !== company.name) {
        console.log('🔍 Setting company name after companies loaded:', company.name);
        setClientSearchTerm(company.name);
      }
    }
  }, [isLoadingClients, allCompanies, selectedClientId, isDealLinked, clientSearchTerm]);

  // Memoize the return value to prevent unnecessary re-renders in consuming components
  // This is critical for performance as it prevents the cascade of re-renders that leads to 429 errors
  return useMemo(() => ({
    selectedClientId,
    projectName,
    startDateStr,
    endDateStr,
    invoiceFrequency,
    paymentTerms,
    billingType,
    hoursPerDay,
    clientSearchTerm,
    availableClients,
    filteredClients,
    clientDropdownOpen,
    activeDuration,
    isLoadingClients,
    clientFetchError,
    formError,
    isDealLinked, // Expose deal linking state
    handleClientChange,
    handleProjectNameChange,
    handleDateChange,
    handleDurationSelect,
    handleInvoiceFrequencyChange,
    handlePaymentTermsChange,
    handleBillingTypeChange,
    handleHoursPerDayChange,
    setClientSearchTerm,
    setClientDropdownOpen,
    validateAndGetFormData,
    resetForm, // Expose reset function
    setFormData, // Expose set function
    setFormDataFromDeal // Expose deal population function
  }), [
    // Include all values and functions in dependencies to ensure updates propagate correctly
    selectedClientId,
    projectName,
    startDateStr,
    endDateStr,
    invoiceFrequency,
    paymentTerms,
    billingType,
    hoursPerDay,
    clientSearchTerm,
    availableClients,
    filteredClients,
    clientDropdownOpen,
    activeDuration,
    isLoadingClients,
    clientFetchError,
    formError,
    isDealLinked,
    handleClientChange,
    handleProjectNameChange,
    handleDateChange,
    handleDurationSelect,
    handleInvoiceFrequencyChange,
    handlePaymentTermsChange,
    handleBillingTypeChange,
    handleHoursPerDayChange,
    setClientSearchTerm,
    setClientDropdownOpen,
    validateAndGetFormData,
    resetForm,
    setFormData,
    setFormDataFromDeal
  ]);
};
