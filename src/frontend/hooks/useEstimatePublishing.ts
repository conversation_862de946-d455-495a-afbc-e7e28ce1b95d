import { useState, useCallback } from "react";
import { format } from "date-fns";
import {
  EstimatePayload,
  EstimateLineItemPayload,
  publishEstimate,
  SaveEstimateResponse,
  CurrentUser
} from "../api/harvest";
import { publishDraftEstimate } from "../api/estimates";
import { AllocationWithTotals, ProjectTotals } from "./useEstimateStaffManagement";
import { StaffAllocation } from "../types/estimate-types";

/**
 * Interface for the data structure returned by the useEstimatePublishing hook
 */
export interface EstimatePublishingState {
  /** Whether a publish operation is in progress */
  isPublishing: boolean;
  
  /** Error message if publishing failed, null otherwise */
  publishError: string | null;
  
  /** Success message after publishing, null otherwise */
  publishSuccessMessage: string | null;
  
  /** ID of the published Harvest estimate */
  publishedEstimateId: number | null;
  
  /** Whether the estimate has been successfully published */
  isPublishedSuccessfully: boolean;
  
  /** Publish an estimate to Harvest */
  publishToHarvest: (
    estimateData: {
      draftUuid: string; // Required parameter - must have a saved draft first
      clientId: number;
      clientName: string;
      projectName?: string;
      startDate: Date;
      endDate: Date;
      allocationsWithTotals: AllocationWithTotals[];
      projectTotals: ProjectTotals;
    },
    currentUser: CurrentUser | null
  ) => Promise<boolean>;
  
  /** Reset the publish status messages */
  resetPublishStatus: () => void;
}

/**
 * Hook to manage publishing estimates to Harvest
 * 
 * @param initialPublishedId Optional initial published estimate ID
 * @returns State and methods for publishing estimates to Harvest
 */
export const useEstimatePublishing = (
  initialPublishedId: number | null = null
): EstimatePublishingState => {
  const [isPublishing, setIsPublishing] = useState<boolean>(false);
  const [publishError, setPublishError] = useState<string | null>(null);
  const [publishSuccessMessage, setPublishSuccessMessage] = useState<string | null>(null);
  const [publishedEstimateId, setPublishedEstimateId] = useState<number | null>(initialPublishedId);
  const [isPublishedSuccessfully, setIsPublishedSuccessfully] = useState<boolean>(!!initialPublishedId);

  /**
   * Reset the publish status messages
   */
  const resetPublishStatus = useCallback(() => {
    setPublishError(null);
    setPublishSuccessMessage(null);
  }, []);

  /**
   * Publish an estimate to Harvest
   * 
   * @param estimateData The estimate data to publish
   * @param currentUser The current user info
   * @returns Promise resolving to true on success, false on failure
   */
  const publishToHarvest = useCallback(async (
    estimateData: {
      draftUuid: string; // Required parameter - must have a saved draft first
      clientId: number;
      clientName: string;
      projectName?: string;
      startDate: Date;
      endDate: Date;
      allocationsWithTotals: AllocationWithTotals[];
      projectTotals: ProjectTotals;
    },
    currentUser: CurrentUser | null
  ): Promise<boolean> => {
    // Basic validation
    if (!estimateData.draftUuid) {
      setPublishError("Cannot publish to Harvest: No draft has been saved yet.");
      return false;
    }

    if (!currentUser) {
      setPublishError("Cannot publish to Harvest: User data not available.");
      return false;
    }
    
    if (!estimateData.clientId || estimateData.allocationsWithTotals.length === 0) {
      setPublishError("Cannot publish estimate to Harvest: Client not selected or no staff allocated.");
      return false;
    }

    setIsPublishing(true);
    setPublishError(null);
    setPublishSuccessMessage(null);

    try {
      // Format line items from calculated totals
      const lineItems: EstimateLineItemPayload[] = estimateData.allocationsWithTotals
        .filter(alloc => alloc.totalDays > 0) // Only include staff with allocated time
        .map(alloc => ({
          kind: "Service", // Use a default kind, or derive from role if needed
          description: `${alloc.firstName} ${alloc.lastName} - ${alloc.projectRole}`,
          quantity: alloc.totalDays,
          unit_price: alloc.rateProposedDaily,
          taxed: true, // Set all line items to be taxable with the Australian GST
        }));

      if (lineItems.length === 0) {
        setPublishError("Cannot publish estimate: No time allocated to any staff member.");
        setIsPublishing(false);
        return false;
      }

      const payload: EstimatePayload = {
        client_id: estimateData.clientId,
        subject: estimateData.projectName || `Estimate for Client ${estimateData.clientId}`,
        issue_date: format(new Date(), 'yyyy-MM-dd'), // Use today's date as issue date
        line_items: lineItems,
        tax: 10, // Australian GST rate (10%) - TODO: Make configurable?
        currency: "AUD", // TODO: Make configurable?
        notes: "Quote valid for 7 days. All prices exclude GST and additional expenses (e.g., travel, accommodation, materials). Work to be performed as per Onbord Pty Ltd's standard Consultancy Agreement. Payment terms are 15 days from invoice date. Onbord Pty. Ltd. reserves the right to adjust pricing if the scope of work changes. All information in this quote is confidential and proprietary to Onbord Pty Ltd. This quote is subject to availability and final agreement of terms."
      };
      
      // Add discount if applicable
      if (estimateData.projectTotals.discountType === 'percentage' && estimateData.projectTotals.discountValue > 0) {
        // For percentage discounts, we can directly use the value as Harvest expects
        payload.discount = Math.min(estimateData.projectTotals.discountValue, 100);
      } else if (estimateData.projectTotals.discountType === 'amount' && estimateData.projectTotals.discountValue > 0) {
        // For fixed amount discounts, we need to convert to percentage for Harvest API
        // Only if totalRevenue is greater than zero to avoid division by zero
        if (estimateData.projectTotals.totalRevenue > 0) {
          const percentage = (estimateData.projectTotals.discountAmount / estimateData.projectTotals.totalRevenue) * 100;
          payload.discount = Math.min(percentage, 100); // Cap at 100%
        }
      }

      console.log("Publishing estimate payload to Harvest:", payload);
      const result: SaveEstimateResponse = await publishEstimate(payload);
      console.log("Estimate published successfully to Harvest:", result);

      // Store both the number for display and the ID for links
      const estimateNumber = result.number; // This is what users see
      const estimateId = result.id; // This is what we need for the URL
      setPublishedEstimateId(estimateId); // Store the actual ID for linking
      setPublishSuccessMessage(`Estimate ${estimateNumber ? `#${estimateNumber}` : ""} published successfully to Harvest.`);
      setIsPublishedSuccessfully(true);
      setPublishError(null);

      // Mark the draft as published with the Harvest estimate ID
      try {
        await publishDraftEstimate(estimateData.draftUuid, result.id);
        console.log("Draft marked as published with Harvest ID:", result.id);
      } catch (publishError) {
        console.error("Error linking draft to Harvest estimate:", publishError);
        // Don't overwrite the main success message, but log it
      }
      
      setIsPublishing(false);
      return true;

    } catch (error) {
      console.error("Failed to publish estimate to Harvest:", error);
      setPublishError(error instanceof Error ? error.message : "An unknown error occurred while publishing to Harvest.");
      setPublishSuccessMessage(null);
      setPublishedEstimateId(null); // Clear published ID on error
      setIsPublishedSuccessfully(false);
      setIsPublishing(false);
      return false;
    }
  }, []);

  return {
    isPublishing,
    publishError,
    publishSuccessMessage,
    publishedEstimateId,
    isPublishedSuccessfully,
    publishToHarvest,
    resetPublishStatus
  };
};