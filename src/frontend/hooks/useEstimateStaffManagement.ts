import { useState, useMemo, useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import { StaffAllocation } from '../types/estimate-types'; // Updated import path
import {
  calculateTotalDays,
  calculateTotalCost,
  calculateTotalFees,
  calculateDailyGrossMarginPercentage,
  convertRate,
  calculateTotalFeesWithBilling,
  calculateDiscount,
} from "../components/Estimate/utils/index";
import { calculateProjectTotals } from "../../shared/utils/estimate-calculations";

// Define the shape of calculated totals for an allocation
export interface AllocationWithTotals extends StaffAllocation {
  totalDays: number;
  totalCost: number;
  totalFees: number;
  dailyGM: number;
}

// Define the shape of calculated project totals
export interface ProjectTotals {
  totalRevenue: number;
  totalCost: number;
  marginAmount: number;
  marginPercentage: number;
  totalDays: number;
  averageDailyRate: number;
  targetTotalRevenue: number;
  targetRateDifference: number;
  targetRatePercentageDifference: number;

  // Discount fields
  discountType: "percentage" | "amount" | "none";
  discountValue: number; // The input value (percentage or amount)
  discountAmount: number; // Actual calculated amount to deduct
  discountedRevenue: number; // Total after discount, before GST
}

// Define the shape of the data returned by the hook
export interface EstimateStaffManagementState {
  staffAllocations: StaffAllocation[];
  allocationsWithTotals: AllocationWithTotals[];
  projectTotals: ProjectTotals;
  addStaffMember: (staffData: Omit<StaffAllocation, 'internalId" | "weeklyAllocation">) => boolean; // Returns true if added, false if duplicate
  removeStaffMember: (internalId: string) => void;
  updateStaffAllocation: (internalId: string, weekIdentifier: string, days: number) => void;
  updateStaffRate: (internalId: string, newRate: number, rateType?: "daily" | "hourly", rateAsEntered?: number) => void;
  convertAllRatesForBillingType: (fromType: "daily" | "hourly", toType: "daily" | "hourly", hoursPerDay: number) => void;
  setStaffAllocations: (allocations: StaffAllocation[]) => void; // To load from draft
  resetStaffAllocations: () => void; // To clear allocations
  reorderAllocations: (orderedAllocationIds: string[]) => void; // To handle reordering

  // Discount methods
  updateDiscountType: (type: "percentage" | "amount" | "none") => void;
  updateDiscountValue: (value: number) => void;
  setDiscount: (type: "percentage" | "amount" | "none", value: number) => void; // To load from draft
}

// Helper function to calculate discount amount
const calculateDiscountAmount = (
  totalRevenue: number,
  discountType: "percentage" | "amount" | "none",
  discountValue: number
): number => {
  const { discountAmount } = calculateDiscount(totalRevenue, discountType, discountValue);
  return discountAmount;
};

// Helper function to calculate discounted revenue
const calculateDiscountedRevenue = (
  totalRevenue: number,
  discountType: "percentage" | "amount" | "none",
  discountValue: number
): number => {
  const { discountedTotal } = calculateDiscount(totalRevenue, discountType, discountValue);
  return discountedTotal;
};

export const useEstimateStaffManagement = (
  initialAllocations: StaffAllocation[] = [],
  initialDiscountType: "percentage" | "amount" | "none" = 'percentage",
  initialDiscountValue: number = 0,
  billingType: "daily" | "hourly" = 'daily",
  hoursPerDay: number = 7.5
): EstimateStaffManagementState => {
  const [staffAllocations, setStaffAllocations] = useState<StaffAllocation[]>(initialAllocations);
  const [discountType, setDiscountType] = useState<'percentage' | "amount" | "none">(initialDiscountType);
  const [discountValue, setDiscountValue] = useState<number>(initialDiscountValue);

  // Handler to add a new staff member
  const addStaffMember = useCallback((staffData: Omit<StaffAllocation, 'internalId" | "weeklyAllocation">): boolean => {
    // Prevent adding the same user twice
    if (staffAllocations.some(alloc => alloc.harvestUserId === staffData.harvestUserId)) {
      console.warn(`User ${staffData.firstName} ${staffData.lastName} is already in the estimate.`);
      // Optionally provide user feedback here via a returned status or error state
      return false; // Indicate user was not added
    }

    const newAllocation: StaffAllocation = {
      ...staffData,
      internalId: uuidv4(), // Generate unique ID
      weeklyAllocation: {}, // Initialize empty weekly allocation
    };

    setStaffAllocations(prev => [...prev, newAllocation]);
    return true; // Indicate user was added
  }, [staffAllocations]); // Dependency on current allocations

  // Handler to remove a staff member
  const removeStaffMember = useCallback((internalId: string) => {
    setStaffAllocations(prev => prev.filter(alloc => alloc.internalId !== internalId));
  }, []);

  // Handler for changing weekly allocation days
  const updateStaffAllocation = useCallback((internalId: string, weekIdentifier: string, days: number) => {
    setStaffAllocations(prev =>
      prev.map(alloc => {
        if (alloc.internalId === internalId) {
          const newWeeklyAllocation = { ...alloc.weeklyAllocation };
          // Store values with 3 decimal place precision
          if (days > 0) {
            // Ensure the value is stored with exactly 3 decimal place precision
            const roundedDays = Math.round(days * 1000) / 1000;
            newWeeklyAllocation[weekIdentifier] = roundedDays;
          } else {
            delete newWeeklyAllocation[weekIdentifier]; // Remove if days are 0 or less
          }
          return { ...alloc, weeklyAllocation: newWeeklyAllocation };
        }
        return alloc;
      })
    );
  }, []);

  // Handler for changing the proposed rate
  const updateStaffRate = useCallback((internalId: string, newRate: number, rateType?: "daily" | "hourly", rateAsEntered?: number) => {
    setStaffAllocations(prev =>
      prev.map(alloc =>
        alloc.internalId === internalId 
          ? { 
              ...alloc, 
              rateProposedDaily: newRate,
              rateType: rateType || alloc.rateType,
              rateAsEntered: rateAsEntered !== undefined ? rateAsEntered : alloc.rateAsEntered
            } 
          : alloc
      )
    );
  }, []);

  // Convert all staff rates when billing type changes
  const convertAllRatesForBillingType = useCallback((fromType: "daily" | "hourly", toType: "daily" | "hourly", hoursPerDay: number) => {
    // In the current implementation, we always store daily rates in the database
    // So we don't need to convert the stored values
    // The UI handles display conversion in TeamMembersTable
    // This is different from the design doc which suggests storing in entered format
    return;
  }, []);

  // Function to reset allocations
  const resetStaffAllocations = useCallback(() => {
      setStaffAllocations([]);
  }, []);

  // Function to reorder allocations based on ordered IDs
  const reorderAllocations = useCallback((orderedAllocationIds: string[]) => {
    setStaffAllocations(prev => {
      // Create a map of allocations by internalId for quick lookup
      const allocationMap = new Map(prev.map(alloc => [alloc.internalId, alloc]));
      
      // Create new array following the order of IDs
      const reordered: StaffAllocation[] = [];
      orderedAllocationIds.forEach((id, index) => {
        const allocation = allocationMap.get(id);
        if (allocation) {
          // Update sortIndex for each allocation
          reordered.push({
            ...allocation,
            sortIndex: index
          });
        }
      });
      
      // If any allocations weren't in the ordered list (shouldn't happen but just in case)
      // add them at the end
      prev.forEach(alloc => {
        if (!orderedAllocationIds.includes(alloc.internalId)) {
          reordered.push({
            ...alloc,
            sortIndex: reordered.length
          });
        }
      });
      
      return reordered;
    });
  }, []);

  // Calculate derived data for the table and summary - Memoize this
  const allocationsWithTotals = useMemo((): AllocationWithTotals[] => {
    // Sort allocations by sortIndex (if available) or by name
    const sortedAllocations = [...staffAllocations].sort((a, b) => {
      // First sort by sortIndex if both have it
      if (a.sortIndex !== undefined && b.sortIndex !== undefined) {
        return a.sortIndex - b.sortIndex;
      }
      // If only one has sortIndex, prioritize it
      if (a.sortIndex !== undefined) return -1;
      if (b.sortIndex !== undefined) return 1;
      // Otherwise sort by name
      const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
      const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
      return nameA.localeCompare(nameB);
    });

    return sortedAllocations.map(alloc => {
      const totalDays = calculateTotalDays(alloc.weeklyAllocation);
      const totalCost = calculateTotalCost(alloc.onbordCostRateDaily, totalDays);
      // Use billing-aware calculation for fees with rate type information
      const totalFees = calculateTotalFeesWithBilling(
        alloc.rateProposedDaily, 
        totalDays, 
        billingType, 
        hoursPerDay,
        alloc.rateType,
        alloc.rateAsEntered
      );
      const dailyGM = calculateDailyGrossMarginPercentage(alloc.rateProposedDaily, alloc.onbordCostRateDaily);
      return {
        ...alloc,
        totalDays,
        totalCost,
        totalFees,
        dailyGM,
      };
    });
  }, [staffAllocations, billingType, hoursPerDay]);

  // Discount handlers
  const updateDiscountType = useCallback((type: "percentage" | "amount" | "none") => {
    // Only reset value when switching to 'none'
    if (type === 'none") {
      setDiscountValue(0);
    }
    // When switching between percentage and amount, we keep the value
    // This allows users to toggle between discount types without losing their input
    setDiscountType(type);
  }, []);

  const updateDiscountValue = useCallback((value: number) => {
    // Handle NaN values and ensure non-negative
    const validValue = isNaN(value) ? 0 : Math.max(0, value);
    setDiscountValue(validValue);
  }, []);

  const setDiscount = useCallback((type: "percentage" | "amount" | "none", value: number) => {
    setDiscountType(type);
    // Handle NaN values and ensure non-negative
    const validValue = isNaN(value) ? 0 : Math.max(0, value);
    setDiscountValue(validValue);
  }, []);

  // Calculate overall project totals - Memoize this
  const projectTotals = useMemo((): ProjectTotals => {
    // Call the shared utility function
    const sharedTotals = calculateProjectTotals(allocationsWithTotals, discountType, discountValue);

    // Map the property names correctly (totalCosts -> totalCost)
    return {
      totalRevenue: sharedTotals.totalRevenue,
      totalCost: sharedTotals.totalCosts, // Fix the property name mismatch
      marginAmount: sharedTotals.marginAmount,
      marginPercentage: sharedTotals.marginPercentage,
      totalDays: sharedTotals.totalDays,
      averageDailyRate: sharedTotals.averageDailyRate,
      targetTotalRevenue: sharedTotals.targetTotalRevenue,
      targetRateDifference: sharedTotals.targetRateDifference,
      targetRatePercentageDifference: sharedTotals.targetRatePercentageDifference,

      // Add discount fields that aren't in the shared utility
      discountType: discountType,
      discountValue: discountValue,
      // Calculate discount amount based on the discount type and value
      discountAmount: calculateDiscountAmount(sharedTotals.totalRevenue, discountType, discountValue),
      // Calculate discounted revenue (total revenue minus discount)
      discountedRevenue: calculateDiscountedRevenue(sharedTotals.totalRevenue, discountType, discountValue),
    };
  }, [allocationsWithTotals, discountType, discountValue]);

  return {
    staffAllocations,
    allocationsWithTotals,
    projectTotals,
    addStaffMember,
    removeStaffMember,
    updateStaffAllocation,
    updateStaffRate,
    convertAllRatesForBillingType,
    setStaffAllocations, // Expose setter for loading drafts
    resetStaffAllocations, // Expose reset function
    reorderAllocations, // Expose reorder function
    updateDiscountType,
    updateDiscountValue,
    setDiscount
  };
};
