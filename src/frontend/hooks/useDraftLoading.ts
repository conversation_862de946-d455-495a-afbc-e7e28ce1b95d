import { useState, useEffect } from "react";
import { getDraftEstimate } from "../api/estimates";
// Updated import path for StaffAllocation
import { StaffAllocation } from "../types/estimate-types";

// Define the shape of the loaded data structure expected by the page
export interface LoadedEstimateData {
    companyId: string;
    clientId: string; // Keep for backward compatibility
    clientName: string;
    projectName?: string;
    startDate: Date;
    endDate: Date;
    staffAllocations: StaffAllocation[];
    draftUuid: string;
    harvestEstimateId: number | null;
    discountType?: 'percentage' | 'amount' | 'none';
    discountValue?: number;
    invoiceFrequency?: string;
    paymentTerms?: number;
    billingType?: 'daily' | 'hourly';
    hoursPerDay?: number;
}

// Define the shape of the data returned by the hook
export interface DraftLoadingState {
  isLoading: boolean;
  error: string | null;
  loadedData: LoadedEstimateData | null;
}

export const useDraftLoading = (estimateId?: number | string | null): DraftLoadingState => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [loadedData, setLoadedData] = useState<LoadedEstimateData | null>(null);

  useEffect(() => {
    // Reset state if estimateId is cleared or not a loadable type
    if (!estimateId || !(typeof estimateId === 'string' && estimateId.includes('-'))) {
        setIsLoading(false);
        setError(null);
        setLoadedData(null);
        return;
    }

    // Only proceed if it's a string UUID (draft estimate)
    // TODO: Add logic for numeric Harvest estimate IDs if needed later
    if (typeof estimateId === 'string' && estimateId.includes('-')) {
      console.log(`useDraftLoading: Loading draft estimate ID: ${estimateId}`);
      setIsLoading(true);
      setError(null);
      setLoadedData(null);

      getDraftEstimate(estimateId)
        .then((draft) => {
          console.log(`useDraftLoading: Draft loaded successfully`, draft);
          // Transform draft data into the structure needed by the page/other hooks
          const startDate = new Date(draft.startDate);
          const endDate = new Date(draft.endDate);
          // Adjust end date to ensure it covers the full day for comparisons/calculations
          endDate.setHours(23, 59, 59, 999);

          const staffAllocations = draft.allocations.map(alloc => {
            // Convert timeAllocations array back to weeklyAllocation object
            const weeklyAllocation: Record<string, number> = {};
            alloc.timeAllocations.forEach(ta => {
              weeklyAllocation[ta.weekIdentifier] = ta.days;
            });

            return {
              internalId: alloc.internalId,
              harvestUserId: alloc.harvestUserId,
              firstName: alloc.firstName,
              lastName: alloc.lastName || '',
              projectRole: alloc.projectRole || '',
              level: alloc.level || '',
              onbordTargetRateDaily: alloc.onbordTargetRateDaily,
              onbordCostRateDaily: alloc.onbordCostRateDaily,
              rateProposedDaily: alloc.rateProposedDaily,
              weeklyAllocation
            };
          });

          // Log the draft data for debugging
          console.log('useDraftLoading: Draft data with invoice and billing settings:', {
            invoiceFrequency: draft.invoiceFrequency,
            paymentTerms: draft.paymentTerms,
            billingType: draft.billingType,
            hoursPerDay: draft.hoursPerDay
          });

          setLoadedData({
            companyId: draft.companyId,
            clientId: draft.companyId, // Keep clientId for backward compatibility
            clientName: draft.clientName,
            projectName: draft.projectName || undefined,
            startDate,
            endDate,
            staffAllocations,
            draftUuid: draft.uuid,
            harvestEstimateId: draft.harvestEstimateId,
            discountType: draft.discountType || 'none',
            discountValue: draft.discountValue || 0,
            invoiceFrequency: draft.invoiceFrequency,
            paymentTerms: draft.paymentTerms,
            billingType: draft.billingType,
            hoursPerDay: draft.hoursPerDay
          });
          setError(null);
        })
        .catch(loadError => {
          console.error("useDraftLoading: Failed to load draft estimate:", loadError);
          setError(`Failed to load draft: ${loadError instanceof Error ? loadError.message : String(loadError)}`);
          setLoadedData(null);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
    // Intentionally depend only on estimateId to trigger reload if it changes
  }, [estimateId]);

  return {
    isLoading,
    error,
    loadedData,
  };
};
