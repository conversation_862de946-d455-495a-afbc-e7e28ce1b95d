import { useState, useEffect } from "react";

/**
 * Custom hook to track media query changes
 * @param query - Media query string (e.g., '(min-width: 768px)')
 * @returns boolean indicating if the media query matches
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const mediaQuery = window.matchMedia(query);
    
    // Update state if the initial value was incorrect
    setMatches(mediaQuery.matches);

    // Define the handler
    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Add the listener
    try {
      // Modern browsers
      mediaQuery.addEventListener('change', handler);
    } catch (e) {
      // Fallback for older browsers
      mediaQuery.addListener(handler);
    }

    // Clean up
    return () => {
      try {
        mediaQuery.removeEventListener('change", handler);
      } catch (e) {
        mediaQuery.removeListener(handler);
      }
    };
  }, [query]);

  return matches;
}

// Preset media queries matching Tailwind breakpoints
export const useIsDesktop = () => useMediaQuery('(min-width: 768px)');
export const useIsMobile = () => !useMediaQuery('(min-width: 768px)');