import { useState, useCallback } from "react";
import { format } from "date-fns";
import { CurrentUser } from "../api/harvest";
import {
  createDraftEstimate,
  updateDraftEstimate,
} from "../api/estimates";
import { DraftEstimateCreate, DraftEstimateUpdate } from "../../types/api";
import { StaffAllocation } from "../types/estimate-types";

/**
 * Interface for the data structure returned by the useEstimateDrafts hook
 */
export interface EstimateDraftsState {
  /** Whether a draft save operation is in progress */
  isSaving: boolean;

  /** Error message if saving failed, null otherwise */
  saveError: string | null;

  /** Success message after save, null otherwise */
  saveSuccessMessage: string | null;

  /** UUID of the current draft estimate */
  draftUuid: string | null;

  /** Save a draft estimate */
  saveDraft: (
    estimateData: {
      companyId: string;
      clientName: string;
      projectName?: string;
      startDate: Date;
      endDate: Date;
      invoiceFrequency?: string;
      paymentTerms?: number;
      billingType?: 'daily' | 'hourly';
      hoursPerDay?: number;
      staffAllocations: StaffAllocation[];
    },
    currentUser: CurrentUser | null
  ) => Promise<string | null>; // Returns the UUID on success, null on failure

  /** Reset the save status messages */
  resetSaveStatus: () => void;

  /** Set the draft UUID (for loading existing drafts) */
  setDraftUuid: React.Dispatch<React.SetStateAction<string | null>>;
}

/**
 * Hook to manage draft estimate saving functionality
 *
 * @param initialDraftUuid Optional initial draft UUID
 * @returns State and methods for managing draft estimates
 */
export const useEstimateDrafts = (
  initialDraftUuid: string | null = null
): EstimateDraftsState => {
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccessMessage, setSaveSuccessMessage] = useState<string | null>(null);
  const [draftUuid, setDraftUuid] = useState<string | null>(initialDraftUuid);

  /**
   * Reset the save status messages
   */
  const resetSaveStatus = useCallback(() => {
    setSaveError(null);
    setSaveSuccessMessage(null);
  }, []);

  /**
   * Save a draft estimate
   *
   * @param estimateData The estimate data to save
   * @param currentUser The current user info
   * @returns Promise resolving to the draft UUID on success, or null on failure
   */
  const saveDraft = useCallback(async (
    estimateData: {
      companyId: string;
      clientName: string;
      projectName?: string;
      startDate: Date;
      endDate: Date;
      invoiceFrequency?: string;
      paymentTerms?: number;
      billingType?: 'daily' | 'hourly';
      hoursPerDay?: number;
      staffAllocations: StaffAllocation[];
      projectTotals?: {
        discountType: 'percentage' | 'amount' | 'none';
        discountValue: number;
      };
    },
    currentUser: CurrentUser | null
  ): Promise<string | null> => {
    // Validation
    if (!currentUser) {
      setSaveError("Cannot save draft: User data is not available.");
      return null;
    }

    if (!estimateData.companyId || !estimateData.clientName || !estimateData.startDate || !estimateData.endDate) {
      setSaveError("Cannot save draft: Company or dates missing.");
      return null;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccessMessage(null);

    try {
      // Log the incoming data for debugging
      console.log('EstimateDrafts - Incoming data:', {
        invoiceFrequency: estimateData.invoiceFrequency,
        paymentTerms: estimateData.paymentTerms,
        billingType: estimateData.billingType,
        hoursPerDay: estimateData.hoursPerDay
      });

      // Create the payload
      const payload: DraftEstimateCreate | DraftEstimateUpdate = {
        userId: currentUser.id.toString(),
        companyId: estimateData.companyId,
        clientName: estimateData.clientName,
        projectName: estimateData.projectName,
        startDate: format(estimateData.startDate, 'yyyy-MM-dd'),
        endDate: format(estimateData.endDate, 'yyyy-MM-dd'),
        // Add invoice frequency and payment terms if available
        invoiceFrequency: estimateData.invoiceFrequency,
        paymentTerms: estimateData.paymentTerms,
        // Add billing type and hours per day
        billingType: estimateData.billingType,
        hoursPerDay: estimateData.hoursPerDay,
        // Add discount information if available
        discountType: estimateData.projectTotals?.discountType || 'none',
        discountValue: estimateData.projectTotals?.discountValue || 0,
        allocations: estimateData.staffAllocations.map(alloc => ({
          internalId: alloc.internalId,
          harvestUserId: alloc.harvestUserId,
          firstName: alloc.firstName,
          lastName: alloc.lastName,
          projectRole: alloc.projectRole,
          level: alloc.level,
          onbordTargetRateDaily: alloc.onbordTargetRateDaily,
          onbordCostRateDaily: alloc.onbordCostRateDaily,
          rateProposedDaily: alloc.rateProposedDaily,
          weeklyAllocation: alloc.weeklyAllocation
        }))
      };

      // Maximum number of retry attempts
      const MAX_RETRIES = 2;
      let retryCount = 0;
      let result;
      let currentDraftUuid = draftUuid;

      // Log the final payload for debugging
      console.log('EstimateDrafts - Final payload:', {
        invoiceFrequency: payload.invoiceFrequency,
        paymentTerms: payload.paymentTerms,
        fullPayload: payload
      });

      // Retry loop for 401 errors
      while (retryCount <= MAX_RETRIES) {
        try {
          // Attempt to save/update
          if (currentDraftUuid) {
            console.log(`Updating draft (attempt ${retryCount + 1}/${MAX_RETRIES + 1}):`, currentDraftUuid);
            result = await updateDraftEstimate(currentDraftUuid, payload as DraftEstimateUpdate);
          } else {
            console.log(`Creating draft (attempt ${retryCount + 1}/${MAX_RETRIES + 1}):`);
            result = await createDraftEstimate(payload as DraftEstimateCreate);
            currentDraftUuid = result.uuid;
            setDraftUuid(currentDraftUuid);
          }

          // If we get here, save succeeded - exit the retry loop
          setSaveSuccessMessage('Draft saved successfully');
          setSaveError(null);
          setIsSaving(false);
          return currentDraftUuid;

        } catch (error) {
          // Handle authentication errors with retry
          const isAuthError = error instanceof Error &&
            (error.message.includes('401') || error.message.includes('Unauthorized'));

          if (isAuthError && retryCount < MAX_RETRIES) {
            console.log(`Authentication error on attempt ${retryCount + 1}, retrying in 500ms...`);
            await new Promise(resolve => setTimeout(resolve, 500));
            retryCount++;
            // Continue to next iteration of while loop
          } else {
            // For other errors or if we've exhausted retries, rethrow
            throw error;
          }
        }
      }

      // We shouldn't reach here, but if we do (exhausted retries):
      setSaveError("Failed to save after multiple attempts. Please try again.");
      setIsSaving(false);
      return null;

    } catch (error) {
      // Final error handler
      console.error('Failed to save draft:', error);
      setSaveError(error instanceof Error ? error.message : "Unknown error saving draft");
      setSaveSuccessMessage(null);
      setIsSaving(false);
      return null;
    }
  }, [draftUuid]);

  return {
    isSaving,
    saveError,
    saveSuccessMessage,
    draftUuid,
    saveDraft,
    resetSaveStatus,
    setDraftUuid
  };
};