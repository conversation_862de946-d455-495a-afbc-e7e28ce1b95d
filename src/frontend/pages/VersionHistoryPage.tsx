import React, { useState, useEffect } from "react";
import { format } from "date-fns";
import { versionHistoryService, type MajorRelease, type MinorRelease, type PatchRelease } from "../../services/version-history-service";
import { Card } from "../components/ui";

// Icons for expand/collapse
import { Button } from "@/frontend/components/ui/Button";const ChevronDown = () =>
<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
  </svg>;


const ChevronRight = () =>
<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
  </svg>;


interface PatchReleaseCardProps {
  patch: PatchRelease;
}

const PatchReleaseCard: React.FC<PatchReleaseCardProps> = ({ patch }) => {
  if (!patch || !patch.version || !patch.date) {
    return null;
  }

  return (
    <div className="ml-6 mt-3 border-l-2 border-default pl-4 py-2">
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-sm font-medium text-primary">
            {patch.version}
          </h3>
          <p className="text-xs text-muted">
            {format(new Date(patch.date),"dd MMMM yyyy")}
          </p>
        </div>
        <div className="">
          {patch.title}
        </div>
      </div>
      
      <p className="text-sm text-secondary mt-2">
        {patch.description}
      </p>
      
      <div className="mt-2">
        <ul className="list-disc pl-5 space-y-1 text-sm text-secondary">
          {patch.changes.map((change, changeIndex) =>
          <li key={changeIndex}>{change}</li>
          )}
        </ul>
      </div>
    </div>);

};

interface MinorReleaseCardProps {
  minor: MinorRelease;
}

const MinorReleaseCard: React.FC<MinorReleaseCardProps> = ({ minor }) => {
  const [expanded, setExpanded] = useState(false);

  if (!minor || !minor.version || !minor.date) {
    return null;
  }

  return (
    <div className="ml-6 mt-4 border-l-2 border-primary pl-4 py-2">
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-md font-semibold text-primary">
            Version {minor.version}
          </h3>
          <p className="text-sm text-muted">
            {format(new Date(minor.date),"dd MMMM yyyy")}
          </p>
        </div>
        <div className="bg-primary-light/30 text-primary-color text-xs px-2 py-1 rounded-full">
          {minor.title}
        </div>
      </div>
      
      <p className="text-primary mt-2 text-sm">
        {minor.description}
      </p>
      
      <div className="mt-3">
        <h4 className="text-sm font-medium text-primary mb-2">Changes:</h4>
        <ul className="list-disc pl-5 space-y-1 text-sm text-secondary">
          {minor.changes.map((change, changeIndex) =>
          <li key={changeIndex}>{change}</li>
          )}
        </ul>
      </div>

      {minor.patches.length > 0 &&
      <div className="mt-4">
          <Button variant="secondary"
        onClick={() => setExpanded(!expanded)}
        className="flex items-center text-sm text-primary-color hover:text-primary-color dark:hover:text-primary">

            {expanded ? <ChevronDown /> : <ChevronRight />}
            <span className="ml-1">{expanded ? "Hide" : "Show"} {minor.patches.length} bug fixes & improvements</span>
          </Button>
          
          {expanded &&
        <div className="mt-2 space-y-3">
              {minor.patches.map((patch, patchIndex) =>
          <PatchReleaseCard key={patchIndex} patch={patch} />
          )}
            </div>
        }
        </div>
      }
    </div>);

};

/**
 * Page that displays the version history of the application
 */
const VersionHistoryPage: React.FC = () => {
  const [versionHistory, setVersionHistory] = useState<MajorRelease[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadVersionHistory = async () => {
      try {
        const history = await versionHistoryService.getVersionHistory();
        setVersionHistory(history);
      } catch (err) {
        setError('Failed to load version history');
        console.error('Failed to load version history:", err);
      } finally {
        setLoading(false);
      }
    };
    loadVersionHistory();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-primary mb-6">Version History</h1>
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <p className="mt-2 text-secondary">Loading version history...</p>
        </div>
      </div>);

  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-primary mb-6">Version History</h1>
        <div className="text-center py-8">
          <p className="text-error">{error}</p>
        </div>
      </div>);

  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-primary mb-6">Version History</h1>
      
      <div className="space-y-6">
        {versionHistory && versionHistory.length > 0 ? versionHistory.map((majorRelease, index) =>
        <Card key={index} className="border-l-4 border-l-blue-500">
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-xl font-bold text-primary">
                  Version {majorRelease.version}
                </h2>
                <p className="text-sm text-muted">
                  Released on {format(new Date(majorRelease.date),"dd MMMM yyyy")}
                </p>
              </div>
              <div className="bg-primary-light/30 text-primary-color text-xs px-2 py-1 rounded-full">
                {majorRelease.title}
              </div>
            </div>
            
            <p className="text-primary mt-3">
              {majorRelease.description}
            </p>
            
            <div className="mt-4">
              <h3 className="text-sm font-medium text-primary mb-2">Major Changes:</h3>
              <ul className="list-disc pl-5 space-y-1 text-primary">
                {majorRelease.changes.map((change, changeIndex) =>
              <li key={changeIndex}>{change}</li>
              )}
              </ul>
            </div>

            <div className="mt-6">
              <h3 className="text-md font-semibold text-primary mb-3">Minor Releases & Feature Updates</h3>
              <div className="space-y-2">
                {majorRelease.minorReleases && majorRelease.minorReleases.length > 0 ? majorRelease.minorReleases.map((minorRelease, minorIndex) =>
              <MinorReleaseCard key={minorIndex} minor={minorRelease} />
              ) : null}
              </div>
            </div>
          </Card>
        ) :
        <div className="text-center py-8">
            <p className="text-secondary">No version history available.</p>
          </div>
        }
      </div>
    </div>);

};

export default VersionHistoryPage;