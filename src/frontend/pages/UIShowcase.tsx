import React, { useState } from 'react';
import {
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PlusIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  UserIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  BuildingOffice2Icon,
  ArrowUpIcon,
  ArrowDownIcon,
  StarIcon,
  ClockIcon,
  BellIcon,
  Cog6ToothIcon,
  CommandLineIcon,
  Bars3Icon } from '@heroicons/react/24/outline';
import { Modal } from '../components/shared/ui/Modal';
import SimpleTooltip, { TooltipData } from '../components/common/SimpleTooltip';
import { Button, Badge, Card, Alert } from '../components/ui';
import { Input } from "@/frontend/components/ui/Input";
import { Select } from "@/frontend/components/ui/Select";
import { Textarea } from "@/frontend/components/ui/Textarea";
import { Checkbox } from "@/frontend/components/ui/Checkbox";
import { Radio } from "@/frontend/components/ui/Radio";
import { FileInput } from "@/frontend/components/ui/FileInput";

// Additional components used in the app
import SegmentedControl from '../components/shared/SegmentedControl';
import Table, { TableBody, TableCell, TableHeader, TableRow } from '../components/shared/lists/Table';
import LoadingIndicator from '../components/ForwardProjection/LoadingIndicator';
import List from '../components/shared/lists/List';
import ListItem from '../components/shared/lists/ListItem';

// Navigation components
import { UnifiedNavigation } from '../components/Navigation/UnifiedNavigation';

// Progress components
import DealStageProgress from '../components/CRM/DealEdit/DealStageProgress';

// Data display components
import EstimateInfoAccordion from '../components/Estimate/EstimateInfoAccordion';

const UIShowcase: React.FC = () => {
  const [toggleActive, setToggleActive] = useState('option1');
  const [tabActive, setTabActive] = useState('tab1');
  const [accordionOpen, setAccordionOpen] = useState<string[]>(['section1']);
  const [modalOpen, setModalOpen] = useState(false);
  const [switchOn, setSwitchOn] = useState(false);
  const [selectedRow, setSelectedRow] = useState<number | null>(null);
  const [tooltip, setTooltip] = useState<TooltipData>({ visible: false, content: '', x: 0, y: 0 });

  const toggleAccordion = (section: string) => {
    setAccordionOpen((prev) =>
    prev.includes(section) ?
    prev.filter((s) => s !== section) :
    [...prev, section]
    );
  };

  const showTooltip = (e: React.MouseEvent, content: string) => {
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    setTooltip({
      visible: true,
      content,
      x: rect.left + rect.width / 2,
      y: rect.bottom + 8
    });
  };

  const hideTooltip = () => {
    setTooltip({ visible: false, content: '', x: 0, y: 0 });
  };

  return (
    <div className="min-h-screen page-background p-8">
      <SimpleTooltip tooltip={tooltip} />
      
      <div className="max-w-7xl mx-auto space-y-12">
        <div>
          <h1 className="text-3xl font-bold text-primary mb-2">
            UI Component Showcase
          </h1>
          <p className="text-muted">
            All available UI components and their variants in our design system
          </p>
        </div>

        {/* Buttons Section */}
        <section className="card rounded-lg p-6" data-testid="button-showcase">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Buttons (btn-modern system)
          </h2>
          
          <div className="space-y-6">
            {/* Primary Buttons */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Primary Actions
              </h3>
              <div className="flex flex-wrap gap-3 mb-3">
                <Button variant="primary">
                  Primary Button
                </Button>
                <Button variant="primary" disabled>
                  Primary Disabled
                </Button>
                <Button variant="primary" size="sm">
                  Small Primary
                </Button>
                <Button variant="primary">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  With Icon
                </Button>
              </div>
              <div className="info-box">
                <strong>Used in 95 files:</strong> Submit buttons, save actions, create new items, main CTAs
                <br />
                <strong>Key components:</strong> EstimateConfigurationForm, DealEditPage, ContactForm, CompanyDetail, 
                HubSpotImport, StaffSelectionModal, AssociationModal, TransactionsList, ExpenseList
              </div>
            </div>

            {/* Secondary Buttons */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Secondary Actions
              </h3>
              <div className="flex flex-wrap gap-3 mb-3">
                <Button variant="secondary">
                  Secondary Button
                </Button>
                <Button variant="secondary" disabled>
                  Secondary Disabled
                </Button>
                <Button variant="secondary" size="sm">
                  Small Secondary
                </Button>
              </div>
              <div className="info-box">
                <strong>Used in 59 files:</strong> Cancel buttons, alternative options, secondary paths
                <br />
                <strong>Key components:</strong> Modal, DealEditPage, EstimateActions, ContactDetail, 
                CompanyDetail, DealCalendarView, TeamCoverageMatrix, PeriodSelector
              </div>
            </div>

            {/* Status Buttons */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Status Actions
              </h3>
              <div className="flex flex-wrap gap-3 mb-3">
                <Button variant="success">
                  <CheckIcon className="w-4 h-4 mr-2" />
                  Success
                </Button>
                <Button variant="danger">
                  <XMarkIcon className="w-4 h-4 mr-2" />
                  Danger
                </Button>
                <Button variant="warning">
                  <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
                  Warning
                </Button>
              </div>
              <div className="info-box">
                <strong>Success (14 files):</strong> Confirm actions, positive states, save confirmations<br />
                <strong>Danger (22 files):</strong> Delete, disconnect, remove actions - ContactDetail, HubSpotSettings, XeroSettings<br />
                <strong>Warning (4 files):</strong> Attention states, warnings - XeroBillsSection, XeroPayrollSection
              </div>
            </div>

            {/* Outline & Ghost Buttons */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Outline & Ghost Variants
              </h3>
              <div className="flex flex-wrap gap-3 mb-3">
                <Button variant="outline">
                  Outline Button
                </Button>
                <Button variant="ghost">
                  Ghost Button
                </Button>
                <Button variant="ghost" size="sm">
                  Small Ghost
                </Button>
              </div>
              <div className="info-box">
                <strong>Outline (10 files):</strong> Tertiary actions, less emphasis - CRMDashboard, StatCard<br />
                <strong>Ghost (59 files):</strong> Icon buttons, inline actions, subtle interactions - CompanyList, Radar, EstimateTable
              </div>
            </div>

            {/* Tab Buttons */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Tab Navigation
              </h3>
              <div className="flex gap-1 bg-surface-alt dark:bg-surface-alt p-1 rounded-lg w-fit mb-3">
                <Button
                  variant="tab"
                  isActive={tabActive === 'tab1'}
                  onClick={() => setTabActive('tab1')}>

                  Tab 1
                </Button>
                <Button
                  variant="tab"
                  isActive={tabActive === 'tab2'}
                  onClick={() => setTabActive('tab2')}>

                  Tab 2
                </Button>
                <Button
                  variant="tab"
                  isActive={tabActive === 'tab3'}
                  onClick={() => setTabActive('tab3')}>

                  Tab 3
                </Button>
              </div>
              <div className="info-box">
                <strong>Used in 6 files:</strong> Tab navigation, view switching<br />
                <strong>Key components:</strong> DealCalendarView, EstimateTimeAllocation, CRMDirectory
              </div>
            </div>

            {/* Toggle Buttons */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Toggle Options
              </h3>
              <div className="flex gap-2 mb-3">
                <Button
                  variant="toggle"
                  isActive={toggleActive === 'option1'}
                  onClick={() => setToggleActive('option1')}>

                  Option 1
                </Button>
                <Button
                  variant="toggle"
                  isActive={toggleActive === 'option2'}
                  onClick={() => setToggleActive('option2')}>

                  Option 2
                </Button>
                <Button
                  variant="toggle"
                  isActive={toggleActive === 'option3'}
                  onClick={() => setToggleActive('option3')}>

                  Option 3
                </Button>
              </div>
              <div className="info-box">
                <strong>Used in 3 files:</strong> Multi-state toggles, option switching<br />
                <strong>Key components:</strong> EstimateConfiguration, EstimateTimeAllocation, EstimateConfigurationForm<br />
                <strong>Special: Xero variant (1 file):</strong> XeroBillsSection for Xero-specific branding
              </div>
            </div>
          </div>
        </section>

        {/* Tables Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Tables
          </h2>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="info-box">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted dark:text-subtle uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted dark:text-subtle uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted dark:text-subtle uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-muted dark:text-subtle uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-muted dark:text-subtle uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="card divide-y divide-gray-200 dark:divide-gray-700">
                {[1, 2, 3].map((row) =>
                <tr
                  key={row}
                  className={`hover-surface cursor-pointer transition-colors ${
                  selectedRow === row ? 'bg-primary-light dark:bg-primary-dark/20' : ''}`
                  }
                  onClick={() => setSelectedRow(row)}>

                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary">
                      John Doe {row}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted dark:text-subtle">
                      Developer
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="success">Active</Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-primary text-right">
                      $1,500
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </section>

        {/* Cards Section */}
        <section className="card rounded-lg p-6" data-testid="card-showcase">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Cards
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <h3 className="font-medium text-primary mb-2">Basic Card</h3>
              <p className="text-subtle">Standard card with default styling</p>
            </Card>

            <Card variant="hover">
              <h3 className="font-medium text-primary mb-2">Hoverable Card</h3>
              <p className="text-subtle">Card with hover effects</p>
            </Card>

            <Card variant="info">
              <div className="flex items-start">
                <InformationCircleIcon className="w-5 h-5 text-primary-color dark:text-primary-light mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-primary mb-2">Info Card</h3>
                  <p className="text-subtle">Informational card variant</p>
                </div>
              </div>
            </Card>

            <Card variant="success">
              <div className="flex items-start">
                <CheckIcon className="w-5 h-5 text-success dark:text-success-light mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-primary mb-2">Success Card</h3>
                  <p className="text-subtle">Positive state card</p>
                </div>
              </div>
            </Card>

            <Card variant="warning">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="w-5 h-5 text-warning dark:text-warning-light mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-primary mb-2">Warning Card</h3>
                  <p className="text-subtle">Warning state card</p>
                </div>
              </div>
            </Card>

            <Card variant="error">
              <div className="flex items-start">
                <XMarkIcon className="w-5 h-5 text-error dark:text-error-light mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-primary mb-2">Error Card</h3>
                  <p className="text-subtle">Error state card</p>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Accordions Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Accordions
          </h2>
          
          <div className="space-y-2">
            {/* Accordion Item 1 */}
            <div className="border border-default rounded-lg">
              <Button variant="secondary"
              onClick={() => toggleAccordion('section1')}
              className="w-full px-4 py-3 flex items-center justify-between text-left hover-surface transition-colors">

                <span className="font-medium text-primary">Section 1: Basic Information</span>
                <ChevronDownIcon
                  className={`w-5 h-5 text-muted transition-transform ${
                  accordionOpen.includes('section1') ? 'rotate-180' : ''}`
                  } />

              </Button>
              {accordionOpen.includes('section1') &&
              <div className="px-4 py-3 border-t border-default">
                  <p className="text-subtle">
                    This is the content of section 1. It can contain any content including forms, lists, or other components.
                  </p>
                </div>
              }
            </div>

            {/* Accordion Item 2 */}
            <div className="border border-default rounded-lg">
              <Button variant="secondary"
              onClick={() => toggleAccordion('section2')}
              className="w-full px-4 py-3 flex items-center justify-between text-left hover-surface transition-colors">

                <span className="font-medium text-primary">Section 2: Advanced Settings</span>
                <ChevronDownIcon
                  className={`w-5 h-5 text-muted transition-transform ${
                  accordionOpen.includes('section2') ? 'rotate-180' : ''}`
                  } />

              </Button>
              {accordionOpen.includes('section2') &&
              <div className="px-4 py-3 border-t border-default">
                  <p className="text-subtle">
                    Advanced settings and configurations go here.
                  </p>
                </div>
              }
            </div>
          </div>
        </section>

        {/* Modals Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Modals & Dialogs
          </h2>
          
          <div className="space-y-4">
            <Button
              onClick={() => setModalOpen(true)}
              variant="primary">

              Open Modal
            </Button>

            <Modal
              isOpen={modalOpen}
              onClose={() => setModalOpen(false)}
              title="Example Modal"
              size="default">

              <div className="p-6">
                <p className="text-subtle mb-4">
                  This is a modal dialog. It can contain forms, confirmations, or any other content.
                </p>
                <div className="flex gap-3 justify-end">
                  <Button
                    onClick={() => setModalOpen(false)}
                    variant="secondary">

                    Cancel
                  </Button>
                  <Button
                    onClick={() => setModalOpen(false)}
                    variant="primary">

                    Confirm
                  </Button>
                </div>
              </div>
            </Modal>
          </div>
        </section>

        {/* Form Elements Section */}
        <section className="card rounded-lg p-6" data-testid="form-showcase">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Form Elements
          </h2>
          
          <div className="space-y-8">
            {/* Text Inputs */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-4">
                Text Inputs
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Default Input"
                  placeholder="Enter text..."
                  helperText="This is helper text"
                />
                
                <Input
                  label="Required Input"
                  placeholder="Required field"
                  required
                />
                
                <Input
                  label="Disabled Input"
                  placeholder="Cannot edit"
                  disabled
                  value="Disabled value"
                />
                
                <Input
                  label="Error Input"
                  placeholder="Enter valid email"
                  error="Please enter a valid email address"
                  type="email"
                />
                
                <Input
                  label="Success Input"
                  placeholder="Valid input"
                  variant="success"
                  value="<EMAIL>"
                />
                
                <Input
                  label="Password Input"
                  type="password"
                  placeholder="Enter password"
                />
              </div>

            </div>

            {/* Select Dropdowns */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-4">
                Select Dropdowns
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select label="Default Select">
                  <option value="">Choose an option</option>
                  <option value="1">Option 1</option>
                  <option value="2">Option 2</option>
                  <option value="3">Option 3</option>
                </Select>
                
                <Select label="Required Select" required>
                  <option value="">Choose an option</option>
                  <option value="1">Critical</option>
                  <option value="2">High</option>
                  <option value="3">Medium</option>
                  <option value="4">Low</option>
                </Select>
                
                <Select label="Disabled Select" disabled value="2">
                  <option value="1">Option 1</option>
                  <option value="2">Option 2</option>
                  <option value="3">Option 3</option>
                </Select>
                
                <Select label="Select with Groups">
                  <optgroup label="Fruits">
                    <option value="apple">Apple</option>
                    <option value="banana">Banana</option>
                    <option value="orange">Orange</option>
                  </optgroup>
                  <optgroup label="Vegetables">
                    <option value="carrot">Carrot</option>
                    <option value="lettuce">Lettuce</option>
                    <option value="tomato">Tomato</option>
                  </optgroup>
                </Select>
              </div>
            </div>

            {/* Textareas */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-4">
                Textareas
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Textarea
                  label="Default Textarea"
                  placeholder="Enter description..."
                  rows={3}
                />
                
                <Textarea
                  label="Textarea with Error"
                  placeholder="Enter at least 10 characters"
                  error="Description is too short"
                  rows={3}
                />
              </div>
            </div>

            {/* Checkboxes and Radios */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-4">
                Checkboxes & Radio Buttons
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-muted mb-2">Checkboxes</h4>
                  <Checkbox
                    id="checkbox-1"
                    label="Default checkbox"
                  />
                  <Checkbox
                    id="checkbox-2"
                    label="Checked checkbox"
                    defaultChecked
                  />
                  <Checkbox
                    id="checkbox-3"
                    label="Disabled checkbox"
                    disabled
                  />
                  <Checkbox
                    id="checkbox-4"
                    label="Disabled checked"
                    disabled
                    defaultChecked
                  />
                </div>
                
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-muted mb-2">Radio Buttons</h4>
                  <Radio
                    id="radio-1"
                    name="radio-group"
                    label="Option 1"
                    defaultChecked
                  />
                  <Radio
                    id="radio-2"
                    name="radio-group"
                    label="Option 2"
                  />
                  <Radio
                    id="radio-3"
                    name="radio-group"
                    label="Option 3"
                  />
                  <Radio
                    id="radio-4"
                    name="radio-group-disabled"
                    label="Disabled option"
                    disabled
                  />
                </div>
              </div>
            </div>

            {/* File Input */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-4">
                File Upload
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FileInput
                  label="Upload Image"
                  accept="image/*"
                  helperText="PNG, JPG, GIF up to 10MB"
                />
                
                <FileInput
                  label="Upload Documents"
                  accept=".pdf,.doc,.docx"
                  helperText="PDF or Word documents only"
                  multiple
                />
              </div>
            </div>

            {/* Switch/Toggle */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-4">
                Switch Toggle
              </h3>
              <div className="flex items-center justify-between max-w-xs">
                <span className="text-sm font-medium text-muted">
                  Enable notifications
                </span>
                <button
                  onClick={() => setSwitchOn(!switchOn)}
                  className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
                    switchOn ? 'bg-primary' : 'bg-surface-alt dark:bg-gray-600'
                  }`}
                  role="switch"
                  aria-checked={switchOn}
                >
                  <span className="sr-only">Enable notifications</span>
                  <span
                    className={`inline-block w-4 h-4 transform bg-surface-card rounded-full transition-transform ${
                      switchOn ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Tooltips Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Tooltips
          </h2>
          
          <div className="flex gap-4">
            <Button
              variant="secondary"
              onMouseEnter={(e) => showTooltip(e, 'This is a helpful tooltip')}
              onMouseLeave={hideTooltip}>

              Hover for tooltip
            </Button>
            
            <span
              className="inline-flex items-center text-muted cursor-help"
              onMouseEnter={(e) => showTooltip(e, 'Additional information about this item')}
              onMouseLeave={hideTooltip}>

              <InformationCircleIcon className="w-5 h-5" />
            </span>
          </div>
        </section>

        {/* Loading States Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Loading States
          </h2>
          
          <div className="space-y-6">
            {/* Spinner */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Loading Spinner
              </h3>
              <div className="flex items-center gap-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              </div>
            </div>

            {/* Skeleton */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Skeleton Loader
              </h3>
              <div className="space-y-3">
                <div className="h-4 bg-surface-alt dark:bg-surface-alt rounded animate-pulse"></div>
                <div className="h-4 bg-surface-alt dark:bg-surface-alt rounded animate-pulse w-3/4"></div>
                <div className="h-4 bg-surface-alt dark:bg-surface-alt rounded animate-pulse w-1/2"></div>
              </div>
            </div>

            {/* Progress Bar */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Progress Bar
              </h3>
              <div className="w-full bg-surface-alt dark:bg-surface-alt rounded-full h-2">
                <div className="bg-primary h-2 rounded-full" style={{ width: '60%' }}></div>
              </div>
            </div>
          </div>
        </section>

        {/* Badges & Pills Section */}
        <section className="card rounded-lg p-6" data-testid="badge-showcase">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Badges & Pills
          </h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Status Badges
              </h3>
              <div className="flex flex-wrap gap-3">
                <Badge variant="primary">Primary</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="success">Success</Badge>
                <Badge variant="warning">Warning</Badge>
                <Badge variant="error">Error</Badge>
                <Badge variant="info">Info</Badge>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Count Badges
              </h3>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span>Notifications</span>
                  <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-primary bg-error rounded-full">
                    5
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span>Messages</span>
                  <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-primary bg-primary-color rounded-full">
                    12
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Tags/Chips
              </h3>
              <div className="flex flex-wrap gap-2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-surface-alt dark:bg-surface-alt text-muted">
                  JavaScript
                  <Button variant="secondary" className="ml-2 hover:text-primary dark:hover:text-primary">
                    <XMarkIcon className="w-3 h-3" />
                  </Button>
                </span>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-surface-alt dark:bg-surface-alt text-muted">
                  React
                  <Button variant="secondary" className="ml-2 hover:text-primary dark:hover:text-primary">
                    <XMarkIcon className="w-3 h-3" />
                  </Button>
                </span>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-surface-alt dark:bg-surface-alt text-muted">
                  TypeScript
                  <Button variant="secondary" className="ml-2 hover:text-primary dark:hover:text-primary">
                    <XMarkIcon className="w-3 h-3" />
                  </Button>
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Alerts Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Alerts & Notifications
          </h2>
          
          <div className="space-y-4">
            {/* Info Alert */}
            <Alert
              variant="info"
              icon={<InformationCircleIcon />}
              title="Information">

              This is an informational alert to provide context or guidance.
            </Alert>

            {/* Success Alert */}
            <Alert
              variant="success"
              icon={<CheckIcon />}
              title="Success">

              Your changes have been saved successfully.
            </Alert>

            {/* Warning Alert */}
            <Alert
              variant="warning"
              icon={<ExclamationTriangleIcon />}
              title="Warning">

              Please review this important information before proceeding.
            </Alert>

            {/* Error Alert */}
            <Alert
              variant="error"
              icon={<XMarkIcon />}
              title="Error">

              There was an error processing your request. Please try again.
            </Alert>
          </div>
        </section>

        {/* Lists Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Lists
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Simple List */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Simple List
              </h3>
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                <li className="py-3">
                  <div className="flex items-center">
                    <DocumentTextIcon className="w-5 h-5 text-subtle mr-3" />
                    <span className="text-primary">Document item 1</span>
                  </div>
                </li>
                <li className="py-3">
                  <div className="flex items-center">
                    <DocumentTextIcon className="w-5 h-5 text-subtle mr-3" />
                    <span className="text-primary">Document item 2</span>
                  </div>
                </li>
                <li className="py-3">
                  <div className="flex items-center">
                    <DocumentTextIcon className="w-5 h-5 text-subtle mr-3" />
                    <span className="text-primary">Document item 3</span>
                  </div>
                </li>
              </ul>
            </div>

            {/* Interactive List */}
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Interactive List
              </h3>
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                <li className="py-3 hover-surface px-3 -mx-3 rounded cursor-pointer transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <UserIcon className="w-5 h-5 text-subtle mr-3" />
                      <span className="text-primary">User Profile</span>
                    </div>
                    <ChevronRightIcon className="w-5 h-5 text-subtle" />
                  </div>
                </li>
                <li className="py-3 hover-surface px-3 -mx-3 rounded cursor-pointer transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Cog6ToothIcon className="w-5 h-5 text-subtle mr-3" />
                      <span className="text-primary">Settings</span>
                    </div>
                    <ChevronRightIcon className="w-5 h-5 text-subtle" />
                  </div>
                </li>
                <li className="py-3 hover-surface px-3 -mx-3 rounded cursor-pointer transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <BellIcon className="w-5 h-5 text-subtle mr-3" />
                      <span className="text-primary">Notifications</span>
                    </div>
                    <Badge variant="error">3</Badge>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Empty States Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Empty States
          </h2>
          
          <div className="border-2 border-dashed border-strong dark:border-strong rounded-lg p-12">
            <div className="text-center">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-subtle" />
              <h3 className="mt-2 text-sm font-medium text-primary">
                No documents
              </h3>
              <p className="mt-1 text-sm text-muted dark:text-subtle">
                Get started by creating a new document.
              </p>
              <div className="mt-6">
                <Button variant="primary">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  New Document
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Avatars Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Avatars
          </h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Avatar Sizes
              </h3>
              <div className="flex items-center gap-4">
                {/* Small */}
                <div className="w-8 h-8 rounded-full bg-surface-alt dark:bg-gray-600 flex items-center justify-center text-xs font-medium text-secondary dark:text-subtle">
                  JD
                </div>
                {/* Medium */}
                <div className="w-10 h-10 rounded-full bg-surface-alt dark:bg-gray-600 flex items-center justify-center text-sm font-medium text-secondary dark:text-subtle">
                  JD
                </div>
                {/* Large */}
                <div className="w-12 h-12 rounded-full bg-surface-alt dark:bg-gray-600 flex items-center justify-center text-base font-medium text-secondary dark:text-subtle">
                  JD
                </div>
                {/* XL */}
                <div className="w-16 h-16 rounded-full bg-surface-alt dark:bg-gray-600 flex items-center justify-center text-lg font-medium text-secondary dark:text-subtle">
                  JD
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Avatar Group
              </h3>
              <div className="flex -space-x-2">
                <div className="w-10 h-10 rounded-full bg-primary-light0 flex items-center justify-center text-sm font-medium text-primary ring-2 ring-white dark:ring-gray-800">
                  AB
                </div>
                <div className="w-10 h-10 rounded-full bg-success-light0 flex items-center justify-center text-sm font-medium text-primary ring-2 ring-white dark:ring-gray-800">
                  CD
                </div>
                <div className="w-10 h-10 rounded-full bg-accent-light0 flex items-center justify-center text-sm font-medium text-primary ring-2 ring-white dark:ring-gray-800">
                  EF
                </div>
                <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-sm font-medium text-secondary dark:text-subtle ring-2 ring-white dark:ring-gray-800">
                  +3
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Status Indicators Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Status Indicators
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-success-light0 rounded-full"></div>
                <span className="text-sm text-muted">Online</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-warning-light0 rounded-full"></div>
                <span className="text-sm text-muted">Away</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-error-light0 rounded-full"></div>
                <span className="text-sm text-muted">Busy</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm text-muted">Offline</span>
              </div>
            </div>

            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-success-light0 rounded-full animate-pulse"></div>
                <span className="text-sm text-muted">Active</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-primary-light0 rounded-full animate-ping"></div>
                <span className="text-sm text-muted">Syncing</span>
              </div>
            </div>
          </div>
        </section>

        {/* Dividers Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Dividers & Separators
          </h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Horizontal Divider
              </h3>
              <hr className="border-default" />
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Text Divider
              </h3>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-default"></div>
                </div>
                <div className="relative flex justify-center">
                  <span className="px-4 card text-sm text-muted">
                    Or continue with
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Vertical Divider
              </h3>
              <div className="flex items-center gap-4">
                <span>Option 1</span>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <span>Option 2</span>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <span>Option 3</span>
              </div>
            </div>
          </div>
        </section>

        {/* Typography Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Typography
          </h2>
          
          <div className="space-y-4">
            <h1 className="text-4xl font-bold text-primary">Heading 1</h1>
            <h2 className="text-3xl font-semibold text-primary">Heading 2</h2>
            <h3 className="text-2xl font-medium text-primary">Heading 3</h3>
            <h4 className="text-xl font-medium text-primary">Heading 4</h4>
            <p className="text-base text-muted">
              Body text - Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </p>
            <p className="text-sm text-subtle">
              Small text - Used for descriptions and secondary information
            </p>
            <p className="text-xs text-muted dark:text-muted">
              Extra small text - Used for metadata and timestamps
            </p>
          </div>
        </section>

        {/* Timeline Component */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Timeline Component
          </h2>
          
          <div className="max-w-3xl">
            <h3 className="text-lg font-medium text-muted mb-3">
              Activity Timeline (Static Example)
            </h3>
            <div className="border border-default rounded-lg p-4">
              {/* Static timeline representation */}
              <div className="space-y-4">
                <div className="sticky top-0 card py-2 border-b border-default">
                  <h4 className="text-sm font-medium text-primary">Today</h4>
                </div>
                <div className="flex gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary-light dark:bg-primary-dark flex items-center justify-center">
                    <UserIcon className="w-4 h-4 text-primary-color dark:text-primary-light" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-primary">
                      <strong>John Doe</strong> created deal "Enterprise Software"
                    </p>
                    <p className="text-xs text-muted dark:text-subtle">2 hours ago</p>
                  </div>
                </div>
                <div className="flex gap-3">
                  <div className="w-8 h-8 rounded-full bg-success-light dark:bg-success-dark flex items-center justify-center">
                    <CheckIcon className="w-4 h-4 text-success dark:text-success-light" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-primary">
                      <strong>System</strong> synced with Xero successfully
                    </p>
                    <p className="text-xs text-muted dark:text-subtle">4 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="info-box mt-3">
              <strong>Component:</strong> ActivityTimeline.tsx<br />
              <strong>Used in:</strong> ActivityFeedPage, activity tracking throughout the app<br />
              <strong>Features:</strong> Date grouping, infinite scroll, loading states, chronological display
            </div>
          </div>
        </section>

        {/* Command Palette */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Command Palette
          </h2>
          
          <div className="space-y-4">
            <div className="border border-default rounded-lg overflow-hidden max-w-2xl">
              <div className="border-b border-default px-4 py-3 info-box">
                <div className="flex items-center gap-2">
                  <MagnifyingGlassIcon className="w-5 h-5 text-subtle" />
                  <span className="text-muted">Search for anything...</span>
                </div>
              </div>
              <div className="p-2">
                <div className="text-xs text-muted dark:text-subtle px-3 py-1">Pages</div>
                <div className="space-y-1">
                  <div className="flex items-center gap-3 px-4 py-2 rounded bg-primary-light dark:bg-primary-dark/20">
                    <span className="text-xs px-1.5 py-0.5 rounded bg-surface-alt dark:bg-gray-600">Deal</span>
                    <span className="text-sm text-primary">Enterprise Software Deal</span>
                    <kbd className="ml-auto text-xs bg-surface-alt dark:bg-surface-alt px-2 py-1 rounded">↵</kbd>
                  </div>
                  <div className="flex items-center gap-3 px-4 py-2 rounded hover:bg-surface-alt dark:hover:bg-surface-elevated">
                    <span className="text-xs px-1.5 py-0.5 rounded bg-surface-alt dark:bg-gray-600">Contact</span>
                    <span className="text-sm text-primary">John Doe</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="info-box">
              <strong>Component:</strong> CommandPalette.tsx<br />
              <strong>Used in:</strong> Global navigation via CRM layout<br />
              <strong>Features:</strong> Global search, keyboard navigation, real-time filtering, quick actions<br />
              <strong>Keyboard shortcut:</strong> Cmd/Ctrl + K
            </div>
          </div>
        </section>

        {/* Select Dropdown */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Enhanced Select Dropdown
          </h2>
          
          <div className="max-w-md space-y-4">
            {/* Normal Select */}
            <div>
              <label className="block text-sm font-medium text-muted mb-1">
                Example Select
              </label>
              <Select className="w-full px-3 py-2 border border-strong dark:border-strong rounded-md 
                       bg-surface-card dark:bg-surface-alt text-primary
                       focus:ring-2 focus:ring-primary focus:border-primary">








                <option>Select an option</option>
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
              </Select>
            </div>
            
            {/* Loading State */}
            <div>
              <label className="block text-sm font-medium text-muted mb-1">
                Loading State
              </label>
              <div className="relative">
                <Select className="w-full px-3 py-2 border border-strong dark:border-strong rounded-md 
                         bg-surface-card dark:bg-surface-alt text-primary opacity-50"




                disabled>
                  <option>Loading...</option>
                </Select>
                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                  <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
              </div>
            </div>
            
            {/* Error State */}
            <div>
              <label className="block text-sm font-medium text-muted mb-1">
                Error State
              </label>
              <Select className="w-full px-3 py-2 border border-error rounded-md 
                       bg-surface-card dark:bg-surface-alt text-primary
                       focus:ring-2 focus:ring-error focus:border-error">








                <option>Select an option</option>
              </Select>
              <p className="mt-1 text-sm text-error dark:text-error-light">This field is required</p>
            </div>
            
            <div className="info-box">
              <strong>Used in:</strong> Forms throughout the application<br />
              <strong>Features:</strong> Dark mode support, loading state, error state, consistent styling
            </div>
          </div>
        </section>

        {/* Mobile FAB */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Mobile Floating Action Button (FAB)
          </h2>
          
          <div className="space-y-4">
            <div className="border-2 border-dashed border-strong dark:border-strong rounded-lg p-8 text-center">
              <Bars3Icon className="w-12 h-12 mx-auto text-subtle mb-4" />
              <p className="text-subtle">
                Mobile FAB is only visible on mobile devices (viewport width &lt; 768px)
              </p>
              <p className="text-sm text-muted dark:text-muted mt-2">
                Resize your browser window to see it in the bottom right corner
              </p>
            </div>
            
            <div className="info-box">
              <strong>Component:</strong> MobileFAB.tsx<br />
              <strong>Features:</strong> Expandable menu, scroll-aware visibility, feedback/updates/AI assistant actions<br />
              <strong>Mobile only:</strong> Hidden on desktop via md:hidden class
            </div>
          </div>
        </section>

        {/* Step Indicators / Progress Tracker */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Step Indicators / Progress Tracker
          </h2>
          
          <div className="space-y-4">
            {/* Static Progress Tracker */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-success-light0 text-primary">
                  <CheckIcon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-primary">Companies</p>
                  <p className="text-xs text-muted">Completed</p>
                </div>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-success-light0"></div>
              
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary-light0 text-primary">
                  <span className="text-sm font-medium">2</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-primary">Deals</p>
                  <p className="text-xs text-primary-color">In Progress</p>
                </div>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-gray-300 dark:bg-gray-600"></div>
              
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 rounded-full border-2 border-strong dark:border-strong">
                  <span className="text-sm text-muted">3</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-muted dark:text-subtle">Contacts</p>
                  <p className="text-xs text-subtle">Upcoming</p>
                </div>
              </div>
            </div>
            
            <div className="info-box">
              <strong>Used in:</strong> HubSpot import process, multi-step forms<br />
              <strong>Features:</strong> Step status indicators, progress visualization, current step highlighting
            </div>
          </div>
        </section>

        {/* Date Picker */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Date Picker
          </h2>
          
          <div className="max-w-md space-y-4">
            <div>
              <label className="block text-sm font-medium text-muted mb-1">
                Select Date
              </label>
              <Input
                type="date"
                className="w-full px-3 py-2 border border-strong dark:border-strong rounded-md 
                         bg-surface-card dark:bg-surface-alt text-primary
                         focus:ring-2 focus:ring-primary focus:border-primary" />









            </div>
            
            <div>
              <label className="block text-sm font-medium text-muted mb-1">
                Date Range
              </label>
              <div className="flex gap-2 items-center">
                <Input
                  type="date"
                  className="flex-1 px-3 py-2 border border-strong dark:border-strong rounded-md 
                           bg-surface-card dark:bg-surface-alt text-primary
                           focus:ring-2 focus:ring-primary focus:border-primary" />









                <span className="text-muted">to</span>
                <Input
                  type="date"
                  className="flex-1 px-3 py-2 border border-strong dark:border-strong rounded-md 
                           bg-surface-card dark:bg-surface-alt text-primary
                           focus:ring-2 focus:ring-primary focus:border-primary" />









              </div>
            </div>
            
            <div className="info-box">
              <strong>Used in:</strong> Estimate forms, report filters, period selectors<br />
              <strong>Note:</strong> Currently using native HTML date inputs. Custom date picker can be added if needed.
            </div>
          </div>
        </section>

        {/* Kanban Card */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Kanban Board Card
          </h2>
          
          <div className="max-w-sm">
            {/* Static Kanban Card */}
            <div className="border border-default rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-primary">Enterprise Software Deal</h4>
                <span className="text-xs px-2 py-1 rounded-full bg-primary-light dark:bg-primary-dark text-primary-color dark:text-blue-300">
                  Negotiation
                </span>
              </div>
              <p className="text-sm text-subtle mb-3">Acme Corp</p>
              <div className="flex items-center justify-between">
                <span className="text-lg font-semibold text-primary">$75,000</span>
                <div className="flex items-center gap-1 text-xs text-muted">
                  <CalendarIcon className="w-3 h-3" />
                  <span>Mar 15</span>
                </div>
              </div>
              <div className="mt-3 pt-3 border-t border-default">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted">Probability</span>
                  <span className="font-medium">70%</span>
                </div>
                <div className="mt-1 w-full bg-surface-alt dark:bg-surface-alt rounded-full h-1.5">
                  <div className="bg-primary-color h-1.5 rounded-full" style={{ width: '70%' }}></div>
                </div>
              </div>
            </div>
            
            <div className="info-box mt-4">
              <strong>Used in:</strong> CRM pipeline board, deal management<br />
              <strong>Features:</strong> Drag-and-drop support, hover actions, progress indicators, status badges
            </div>
          </div>
        </section>

        {/* Usage Summary */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Component Usage Summary
          </h2>
          
          <div className="prose dark:prose-invert max-w-none">
            <h3 className="text-lg font-medium text-muted mb-4">
              Button Migration Guide
            </h3>
            <div className="bg-warning-light dark:bg-yellow-900/20 border border-warning dark:border-yellow-800 rounded-md p-4 mb-4">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Important:</strong> When migrating buttons, map based on their purpose and context, not just color:
              </p>
              <ul className="mt-2 space-y-1 text-sm text-warning dark:text-yellow-300">
                <li>• Blue buttons → btn-modern--primary (main actions)</li>
                <li>• Gray buttons → btn-modern--secondary (cancel, secondary)</li>
                <li>• Green buttons → btn-modern--success (save, confirm)</li>
                <li>• Red buttons → btn-modern--danger (delete, destructive)</li>
                <li>• Orange/Yellow → btn-modern--warning (attention needed)</li>
                <li>• Text-only → btn-modern--ghost (subtle actions)</li>
                <li>• Border-only → btn-modern--outline (tertiary actions)</li>
              </ul>
            </div>
            
            <h3 className="text-lg font-medium text-muted mb-4">
              Most Used Components
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="info-box rounded p-4">
                <h4 className="font-medium text-primary mb-2">High Usage (50+ files)</h4>
                <ul className="text-sm text-subtle space-y-1">
                  <li>• btn-modern--primary (95 files)</li>
                  <li>• btn-modern--secondary (59 files)</li>
                  <li>• btn-modern--ghost (59 files)</li>
                  <li>• Tables with hover states</li>
                  <li>• Modal dialogs</li>
                  <li>• Form inputs and selects</li>
                </ul>
              </div>
              
              <div className="info-box rounded p-4">
                <h4 className="font-medium text-primary mb-2">Specialized Components</h4>
                <ul className="text-sm text-subtle space-y-1">
                  <li>• Timeline (Activity feed)</li>
                  <li>• Command palette (Global search)</li>
                  <li>• Kanban cards (Deal pipeline)</li>
                  <li>• Progress trackers (Multi-step processes)</li>
                  <li>• Mobile FAB (Mobile-only actions)</li>
                  <li>• Time allocation grid (Resource planning)</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Segmented Control Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Segmented Control
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Basic Segmented Control
              </h3>
              <SegmentedControl
                value={toggleActive}
                onChange={setToggleActive}
                options={[
                  { value: 'option1', label: 'Option 1' },
                  { value: 'option2', label: 'Option 2' },
                  { value: 'option3', label: 'Option 3' }
                ]}
              />
            </div>
          </div>
        </section>

        {/* Loading Indicators Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Loading Indicators
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Spinner Sizes
              </h3>
              <div className="flex items-center gap-6">
                <LoadingIndicator size="sm" />
                <LoadingIndicator size="md" />
                <LoadingIndicator size="lg" />
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                With Text
              </h3>
              <LoadingIndicator size="md" text="Loading data..." />
            </div>
          </div>
        </section>

        {/* Table Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Tables
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Basic Table
              </h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>John Doe</TableCell>
                    <TableCell>Developer</TableCell>
                    <TableCell><Badge variant="success">Active</Badge></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Jane Smith</TableCell>
                    <TableCell>Designer</TableCell>
                    <TableCell><Badge variant="success">Active</Badge></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Bob Johnson</TableCell>
                    <TableCell>Manager</TableCell>
                    <TableCell><Badge variant="secondary">Away</Badge></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Striped Table
              </h3>
              <Table variant="striped">
                <TableHeader>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell>Price</TableCell>
                    <TableCell>Stock</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>Widget A</TableCell>
                    <TableCell>$19.99</TableCell>
                    <TableCell>152</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Widget B</TableCell>
                    <TableCell>$29.99</TableCell>
                    <TableCell>87</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Widget C</TableCell>
                    <TableCell>$39.99</TableCell>
                    <TableCell>0</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </section>

        {/* List Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Lists
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Basic List
              </h3>
              <List>
                <ListItem
                  icon={<UserIcon className="w-5 h-5" />}
                  onClick={() => console.log('Clicked profile')}
                >
                  <div>
                    <div className="font-medium">Your Profile</div>
                    <div className="text-sm text-muted">View and edit your profile information</div>
                  </div>
                </ListItem>
                <ListItem
                  icon={<Cog6ToothIcon className="w-5 h-5" />}
                  onClick={() => console.log('Clicked settings')}
                >
                  <div>
                    <div className="font-medium">Settings</div>
                    <div className="text-sm text-muted">Manage your account settings</div>
                  </div>
                </ListItem>
                <ListItem
                  icon={<BellIcon className="w-5 h-5" />}
                  onClick={() => console.log('Clicked notifications')}
                >
                  <div>
                    <div className="font-medium">Notifications</div>
                    <div className="text-sm text-muted">Configure notification preferences</div>
                  </div>
                </ListItem>
              </List>
            </div>
          </div>
        </section>

        {/* Navigation Components Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Navigation Components
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Unified Navigation (Static Example)
              </h3>
              <div className="border border-default rounded-lg overflow-hidden">
                <div className="bg-surface-alt p-4">
                  <p className="text-sm text-muted mb-3">Desktop Navigation</p>
                  <UnifiedNavigation variant="desktop" />
                </div>
                <div className="border-t border-default p-4">
                  <p className="text-sm text-muted mb-3">Mobile Navigation</p>
                  <UnifiedNavigation variant="mobile" />
                </div>
              </div>
              <div className="info-box mt-3">
                <strong>Component:</strong> UnifiedNavigation.tsx<br />
                <strong>Used in:</strong> Main app layout<br />
                <strong>Features:</strong> Responsive design, active state tracking, icon support, NEW badges
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Tab Navigation Pattern
              </h3>
              <div className="border-b border-default">
                <nav className="flex space-x-8">
                  <button
                    className={`btn-modern btn-modern--tab ${tabActive === 'tab1' ? 'btn-modern--tab-active' : ''}`}
                    onClick={() => setTabActive('tab1')}
                  >
                    Overview
                  </button>
                  <button
                    className={`btn-modern btn-modern--tab ${tabActive === 'tab2' ? 'btn-modern--tab-active' : ''}`}
                    onClick={() => setTabActive('tab2')}
                  >
                    Details
                  </button>
                  <button
                    className={`btn-modern btn-modern--tab ${tabActive === 'tab3' ? 'btn-modern--tab-active' : ''}`}
                    onClick={() => setTabActive('tab3')}
                  >
                    History
                  </button>
                  <button
                    className={`btn-modern btn-modern--tab ${tabActive === 'tab4' ? 'btn-modern--tab-active' : ''}`}
                    onClick={() => setTabActive('tab4')}
                  >
                    Settings
                  </button>
                </nav>
              </div>
              <div className="info-box mt-3">
                <strong>Pattern:</strong> btn-modern--tab and btn-modern--tab-active<br />
                <strong>Used in:</strong> LeadsTabs, section navigation, multi-tab interfaces<br />
                <strong>Features:</strong> Underline indicator, hover states, responsive design
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Profile Dropdown Pattern
              </h3>
              <div className="relative">
                <button className="flex items-center gap-2 px-4 py-2 rounded-lg bg-surface-alt hover:bg-surface-elevated transition-colors">
                  <div className="w-8 h-8 rounded-full bg-primary-light dark:bg-primary-dark flex items-center justify-center">
                    <UserIcon className="w-5 h-5 text-primary-color dark:text-primary-light" />
                  </div>
                  <span className="text-sm font-medium text-primary">John Doe</span>
                  <ChevronDownIcon className="w-4 h-4 text-subtle" />
                </button>
                <div className="absolute top-full mt-2 right-0 w-56 border border-default rounded-lg shadow-lg bg-surface-card p-2">
                  <div className="py-2 px-3 border-b border-default">
                    <p className="text-sm font-medium text-primary">John Doe</p>
                    <p className="text-xs text-muted"><EMAIL></p>
                  </div>
                  <button className="w-full text-left px-3 py-2 text-sm text-primary hover:bg-surface-alt rounded">
                    Account Settings
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm text-primary hover:bg-surface-alt rounded">
                    Sign Out
                  </button>
                </div>
              </div>
              <div className="info-box mt-3">
                <strong>Component:</strong> ProfileBadge.tsx<br />
                <strong>Used in:</strong> Main navigation header<br />
                <strong>Features:</strong> User avatar, dropdown menu, hover states
              </div>
            </div>
          </div>
        </section>

        {/* Progress & Status Components Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Progress & Status Components
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Deal Stage Progress
              </h3>
              <div className="max-w-3xl">
                <DealStageProgress
                  currentStage="proposal"
                  probability={70}
                />
              </div>
              <div className="info-box mt-3">
                <strong>Component:</strong> DealStageProgress.tsx<br />
                <strong>Used in:</strong> Deal edit pages, CRM pipeline<br />
                <strong>Features:</strong> Visual progress tracking, probability indicator, stage transitions
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Status Badges Collection
              </h3>
              <div className="flex flex-wrap gap-3">
                <Badge variant="primary">Xero Connected</Badge>
                <Badge variant="success">Harvest Synced</Badge>
                <Badge variant="warning">HubSpot Pending</Badge>
                <Badge variant="secondary">Estimate Draft</Badge>
                <Badge variant="error">Sync Failed</Badge>
                <Badge variant="info">Update Available</Badge>
              </div>
              <div className="info-box mt-3">
                <strong>Pattern:</strong> Badge component with semantic variants<br />
                <strong>Used in:</strong> Integration status displays, sync indicators, entity states<br />
                <strong>Note:</strong> Use semantic colors (success, warning, error) for status, not arbitrary colors
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Global Loading Overlay (Demonstration)
              </h3>
              <div className="relative h-32 border-2 border-dashed border-strong rounded-lg overflow-hidden">
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                  <div className="bg-surface-card rounded-lg p-6 shadow-xl">
                    <LoadingIndicator size="lg" text="Loading application..." />
                  </div>
                </div>
              </div>
              <div className="info-box mt-3">
                <strong>Component:</strong> GlobalLoadingIndicator<br />
                <strong>Used in:</strong> App initialization, major data fetches<br />
                <strong>Features:</strong> Full-screen overlay, prevents interaction, loading text
              </div>
            </div>
          </div>
        </section>

        {/* Data Display Components Section */}
        <section className="card rounded-lg p-6">
          <h2 className="text-2xl font-semibold text-primary mb-6">
            Data Display Components
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Accordion/Collapsible Pattern
              </h3>
              <div className="max-w-2xl">
                <EstimateInfoAccordion
                  estimate={{
                    id: 'demo-1',
                    project_name: 'Demo Project',
                    client_name: 'Acme Corp',
                    total_amount: 75000,
                    status: 'sent',
                    estimate_date: new Date().toISOString(),
                    valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                  }}
                  isOpen={accordionOpen.includes('demo-1')}
                  onToggle={() => toggleAccordion('demo-1')}
                />
                <EstimateInfoAccordion
                  estimate={{
                    id: 'demo-2',
                    project_name: 'Another Project',
                    client_name: 'Tech Corp',
                    total_amount: 125000,
                    status: 'draft',
                    estimate_date: new Date().toISOString(),
                    valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                  }}
                  isOpen={accordionOpen.includes('demo-2')}
                  onToggle={() => toggleAccordion('demo-2')}
                />
              </div>
              <div className="info-box mt-3">
                <strong>Component:</strong> EstimateInfoAccordion.tsx<br />
                <strong>Used in:</strong> Estimate lists, collapsible sections throughout app<br />
                <strong>Features:</strong> Smooth animations, chevron indicators, expandable content
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-muted mb-3">
                Simple Accordion Pattern
              </h3>
              <div className="space-y-2 max-w-2xl">
                <div className="border border-default rounded-lg">
                  <button
                    className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-surface-alt transition-colors"
                    onClick={() => toggleAccordion('section1')}
                  >
                    <span className="font-medium text-primary">What is this application?</span>
                    <ChevronRightIcon 
                      className={`w-5 h-5 text-subtle transition-transform ${
                        accordionOpen.includes('section1') ? 'rotate-90' : ''
                      }`}
                    />
                  </button>
                  {accordionOpen.includes('section1') && (
                    <div className="px-4 py-3 border-t border-default">
                      <p className="text-sm text-muted">
                        This is a financial dashboard application that integrates with Xero, Harvest, and HubSpot.
                      </p>
                    </div>
                  )}
                </div>
                <div className="border border-default rounded-lg">
                  <button
                    className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-surface-alt transition-colors"
                    onClick={() => toggleAccordion('section2')}
                  >
                    <span className="font-medium text-primary">How do I get started?</span>
                    <ChevronRightIcon 
                      className={`w-5 h-5 text-subtle transition-transform ${
                        accordionOpen.includes('section2') ? 'rotate-90' : ''
                      }`}
                    />
                  </button>
                  {accordionOpen.includes('section2') && (
                    <div className="px-4 py-3 border-t border-default">
                      <p className="text-sm text-muted">
                        Connect your accounts in the Data Management section and start importing your data.
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <div className="info-box mt-3">
                <strong>Pattern:</strong> Simple collapsible sections<br />
                <strong>Used in:</strong> FAQs, expandable content areas, settings panels<br />
                <strong>Features:</strong> Click to expand/collapse, rotation animation on chevron
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>);

};

export default UIShowcase;