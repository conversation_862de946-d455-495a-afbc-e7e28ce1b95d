/**
 * Navigation Types
 *
 * This file defines the types used for navigation throughout the application.
 * It centralizes navigation-related type definitions to make them consistent.
 */

/**
 * All possible tab IDs in the application
 *
 * This includes:
 * - Main navigation tabs shown in the tab bar
 * - Special views like help and version history
 */
export type TabId =
  | "projection" // Cashflow projection view
  | "expenses" // Expenses management view
  | "forecast" // Smart forecast view
  | "estimates" // Estimates view
  | "crm/deals" // CRM deals view
  | "activity" // Activity feed view
  | "leads" // Leads view
  | "reports" // Reports view
  | "help" // Help page
  | "version-history"; // Version history page

/**
 * Handler function for tab changes
 */
export type TabChangeHandler = (tab: TabId, id?: number | string) => void;

/**
 * Conversion map to transform legacy activeTab values to TabId type
 * This helps during the transition to the new navigation system
 */
export const legacyTabMap: Record<string, TabId> = {
  projection: "projection",
  expenses: "expenses",
  forecast: "forecast",
  estimates: "estimates",
  deals: "crm/deals",
  leads: "leads",
  reports: "reports",
  "version-history": "version-history",
};

/**
 * Default tab to show when no tab is specified
 */
export const DEFAULT_TAB: TabId = "projection";
