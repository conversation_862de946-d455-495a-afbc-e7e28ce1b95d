/**
 * Activity Types
 *
 * This file defines the types used for the activity feed system.
 */

/**
 * Activity entity types
 */
export type ActivityEntityType = 'deal' | "company" | "estimate" | "expense" | "contact" | "tender";

/**
 * Activity sources
 */
export type ActivitySource = 'user' | "hubspot" | "xero" | "harvest" | "system" | "tender_ingestion";

/**
 * Activity status
 */
export type ActivityStatus = 'planned' | "completed" | "canceled" | "in_progress";

/**
 * Activity importance levels
 */
export type ActivityImportance = 'low' | "normal" | "high";

/**
 * Activity types for different operations
 */
export type ActivityType =
  // User Activities
  | "deal_created" | "deal_updated" | "deal_deleted" | "deal_stage_changed"
  | "estimate_created" | "estimate_updated" | "estimate_published" | "estimate_deleted"
  | "company_created" | "company_updated" | "company_linked" | "company_unlinked" | "company_radar_updated"
  | "contact_created" | "contact_updated" | "contact_deleted"
  | "note_added" | "note_updated" | "note_deleted"
  | "expense_created" | "expense_updated" | "expense_deleted"
  | "tender_received" | "tender_qualified" | "tender_disqualified" | "tender_interested" | "tender_converted"
  // System Activities
  | "hubspot_sync_started" | "hubspot_sync_completed" | "hubspot_sync_failed"
  | "xero_sync_started" | "xero_sync_completed" | "xero_sync_failed"
  | "harvest_sync_started" | "harvest_sync_completed" | "harvest_sync_failed"
  | "cashflow_projection_generated"
  | "estimate_deal_linked" | "estimate_deal_unlinked"
  // Integration Activities
  | "auth_connected" | "auth_disconnected" | "auth_refreshed"
  | "data_import_started" | "data_import_completed" | "data_import_failed"
  | "bulk_operation_started" | "bulk_operation_completed";

/**
 * Base activity interface
 */
export interface Activity {
  id: string;
  type: ActivityType;
  subject: string;
  description?: string;
  status?: ActivityStatus;
  
  // Entity relationships
  entityType?: ActivityEntityType;
  entityId?: string;
  
  // Timing information
  dueDate?: string;
  completedDate?: string;
  
  // Legacy relationships (for backward compatibility)
  companyId?: string;
  contactId?: string;
  dealId?: string;
  
  // Metadata
  metadata?: Record<string, any>;
  isRead: boolean;
  importance: ActivityImportance;
  
  // User and system information
  createdBy: string;
  source: ActivitySource;
  
  // Audit information
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

/**
 * Activity creation interface
 */
export interface ActivityCreate {
  type: ActivityType;
  subject: string;
  description?: string;
  status?: ActivityStatus;
  
  // Entity relationships
  entityType?: ActivityEntityType;
  entityId?: string;
  
  // Timing information
  dueDate?: string;
  completedDate?: string;
  
  // Legacy relationships
  companyId?: string;
  contactId?: string;
  dealId?: string;
  
  // Metadata
  metadata?: Record<string, any>;
  importance?: ActivityImportance;
  
  // User and system information
  createdBy: string;
  source: ActivitySource;
}

/**
 * Activity update interface
 */
export interface ActivityUpdate {
  subject?: string;
  description?: string;
  status?: ActivityStatus;
  dueDate?: string;
  completedDate?: string;
  metadata?: Record<string, any>;
  isRead?: boolean;
  importance?: ActivityImportance;
}

/**
 * Activity filters for querying
 */
export interface ActivityFilters {
  type?: ActivityType | ActivityType[];
  source?: ActivitySource | ActivitySource[];
  entityType?: ActivityEntityType;
  entityId?: string;
  createdBy?: string;
  isRead?: boolean;
  importance?: ActivityImportance;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  offset?: number;
  search?: string;
}

/**
 * Activity feed response
 */
export interface ActivityFeedResponse {
  activities: Activity[];
  total: number;
  hasMore?: boolean;
}

/**
 * Activity statistics
 */
export interface ActivityStats {
  total: number;
  unread: number;
  byType: Record<ActivityType, number>;
  bySource: Record<ActivitySource, number>;
  recent: number; // Activities in last 24 hours
}

/**
 * Activity logger interface for creating activities
 */
export interface ActivityLogger {
  logUserAction(type: ActivityType, subject: string, details: Partial<ActivityCreate>): Promise<Activity>;
  logSystemEvent(type: ActivityType, subject: string, details: Partial<ActivityCreate>): Promise<Activity>;
  logIntegrationEvent(source: ActivitySource, type: ActivityType, details: Partial<ActivityCreate>): Promise<Activity>;
  logEntityChange(entityType: ActivityEntityType, entityId: string, changes: Record<string, any>): Promise<Activity>;
}

/**
 * Activity display configuration
 */
export interface ActivityDisplayConfig {
  showUserAvatar: boolean;
  showTimestamp: boolean;
  showDescription: boolean;
  showMetadata: boolean;
  groupByDate: boolean;
  maxDescriptionLength: number;
}

/**
 * Activity notification settings
 */
export interface ActivityNotificationSettings {
  enabled: boolean;
  types: ActivityType[];
  sources: ActivitySource[];
  importance: ActivityImportance[];
  realTime: boolean;
  email: boolean;
}
