/**
 * Type definitions for the estimate components
 */

/**
 * Represents a staff allocation for a project estimate
 */
export interface StaffAllocation {
  /**
   * Unique identifier for the allocation within the app
   */
  internalId: string;

  /**
   * ID of the selected Harvest user
   */
  harvestUserId: number;

  /**
   * First name from Harvest
   */
  firstName: string;

  /**
   * Last name from Harvest
   */
  lastName: string;

  /**
   * Role assigned for this project
   */
  projectRole: string;

  /**
   * Level derived from Harvest roles
   */
  level: string;

  /**
   * Calculated from Harvest default hourly rate
   */
  onbordTargetRateDaily: number;

  /**
   * Calculated from Harvest cost rate
   */
  onbordCostRateDaily: number;

  /**
   * User-inputted daily sell rate for this project
   * Note: This always contains the daily rate equivalent for backward compatibility
   */
  rateProposedDaily: number;

  /**
   * How the rate was originally entered ('daily' or 'hourly')
   */
  rateType?: "daily" | "hourly";

  /**
   * The actual rate value as entered by the user
   * (e.g., if hourly, this is the hourly rate, not the daily equivalent)
   */
  rateAsEntered?: number;

  /**
   * Object mapping week identifiers to allocated days
   */
  weeklyAllocation: { [weekIdentifier: string]: number };

  /**
   * Optional flag to identify placeholder staff
   */
  isPlaceholder?: boolean;

  /**
   * Soft delete timestamp
   */
  deletedAt?: string;

  /**
   * Profile photo URL from Harvest
   */
  avatarUrl?: string;

  /**
   * Sort index for drag-and-drop reordering
   */
  sortIndex?: number;
}

export interface WeekInfo {
  /**
   * Unique identifier for the week (ISO date)
   */
  identifier: string;

  /**
   * Display label for the week
   */
  label: string;

  /**
   * Short display label for the week (e.g. "W1 01/1")
   */
  shortLabel: string;

  /**
   * Whether this week has alternating background styling
   * Used to create zebra-striping effect for better row distinction
   */
  hasAlternatingBackground: boolean;
}
