/**
 * CRM Types
 *
 * This file defines the types used for the CRM feature.
 */

import { 
  BaseCompany, 
  BaseContact, 
  BaseDeal, 
  DealPriority, 
  DealStage, 
  DataSource,
  ContactRole,
  ContactSource,
  DealSource,
  CompanyPriority,
  RadarState,
  CompanySource 
} from "../../types/shared-types";

// Import company types from the unified company types file
import { CompanyCreate, CompanyUpdate } from "../../types/company-types";

// Re-export shared types
export { 
  DealPriority, 
  DealStage, 
  DataSource,
  ContactRole,
  ContactSource,
  DealSource,
  CompanyPriority,
  RadarState,
  CompanySource,
  CompanyCreate,
  CompanyUpdate 
};

/**
 * Company interface extends BaseCompany
 */
export interface Company extends BaseCompany {
  // Relationships
  childCompanies?: CompanyRelationship[];
  parentCompanies?: CompanyRelationship[];
  contacts?: Contact[];
}

/**
 * Deal interface that extends BaseDeal
 */
export interface Deal extends BaseDeal {
  // These fields reference other entities but use the BaseTypes
  // to prevent circular dependencies
  contacts?: Contact[];
  contactRoles?: { contact: Contact; role?: string }[];
  company?: Partial<BaseCompany>; // Primary company
  companyName?: string; // Backward compatibility field for display
  relatedCompanies?: CompanyRelationship[]; // Additional related companies
  notes?: Note[];
  customFields?: Record<string, any>;
  estimates?: DealEstimate[];
}

/**
 * Deal estimate relationship interface
 */
export interface DealEstimate {
  id: string;
  estimateId: string;
  estimateType: string;
  type: "internal" | "harvest";
  linkedAt: string;
  linkedBy?: string;
  // Additional fields populated by the API
  name?: string;
  clientName?: string;
  amount?: number;
  status?: string;
  deletedAt?: string;
}

/**
 * Company relationship type
 */
export type CompanyRelationshipType = 'parent' | "subsidiary" | "partner" | "acquisition";

/**
 * Company relationship interface
 */
export interface CompanyRelationship {
  company: Partial<BaseCompany>;
  relationshipType: CompanyRelationshipType;
}

/**
 * Contact-company relationship interface
 */
export interface ContactCompanyRelationship {
  company: Partial<BaseCompany>;
  role?: ContactRole;
  isPrimary: boolean;
}

/**
 * Contact interface that extends BaseContact
 */
export interface Contact extends BaseContact {
  // These fields reference other entities but use the BaseTypes
  // to prevent circular dependencies
  company?: Partial<BaseCompany>; // For backward compatibility
  companies?: ContactCompanyRelationship[]; // New relationship model
  deals?: Partial<BaseDeal>[];
}

/**
 * Note interface
 */
export interface Note {
  id: string;
  dealId: string;
  content: string;
  createdAt: string;
  createdBy?: string;
  deletedAt?: string;
}

/**
 * HubSpot import history interface
 */
export interface HubSpotImport {
  id: string;
  importDate: string;
  status: "pending" | "completed" | "completed_with_warnings" | "failed";
  importType?: "quick" | "full";
  dealsCount?: number;
  contactsCount?: number;
  companiesCount?: number;
  notesCount?: number;
  associationsCount?: number;
  errorMessage?: string;
}

/**
 * Deal create interface
 */
export interface DealCreate {
  name: string;
  stage: DealStage;
  value?: number;
  currency?: string;
  probability?: number;
  expectedCloseDate?: string;
  startDate?: string;
  endDate?: string;
  invoiceFrequency?: string;
  paymentTerms?: number;
  description?: string;
  source?: string;
  priority?: DealPriority;
  owner?: string;
  hubspotId?: string;
  harvestProjectId?: string;
  customFields?: Record<string, any>;
  // New fields for data model cleanup
  includeInProjections?: boolean;
  // Contact IDs to associate with the deal
  contacts?: string[];
  companyId?: string;
  status?: "Active" | "Inactive";
  createdBy?: string;
  updatedBy?: string;
  // Optional field to preserve original creation date from external systems
  createdAt?: string;
  // HubSpot preserved values
  hubspot_name?: string;
  hubspot_value?: number;
}

/**
 * Deal update interface
 */
export interface DealUpdate {
  name?: string;
  stage?: DealStage;
  value?: number;
  currency?: string;
  probability?: number;
  expectedCloseDate?: string;
  startDate?: string;
  endDate?: string;
  invoiceFrequency?: string;
  paymentTerms?: number;
  description?: string;
  source?: string;
  priority?: DealPriority;
  owner?: string;
  customFields?: Record<string, any>;
  // New fields for data model cleanup
  includeInProjections?: boolean;
  updatedBy?: string;
  // HubSpot preserved values
  hubspot_name?: string;
  hubspot_value?: number;
}

/**
 * Contact create interface
 */
export interface ContactCreate {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  companyId?: string; // For backward compatibility
  notes?: string;
  hubspotId?: string;
  harvestUserId?: string;
  source?: ContactSource;
  // New relationship model
  companies?: Array<{
    companyId: string;
    role?: ContactRole;
    isPrimary?: boolean;
  }>;
  createdBy?: string;
  updatedBy?: string;
  companyAssociations?: Array<{
    companyId: string;
    role?: ContactRole;
    isPrimary?: boolean;
  }>;
}

/**
 * Contact update interface
 */
export interface ContactUpdate {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  companyId?: string; // For backward compatibility
  notes?: string;
  hubspotId?: string;
  harvestUserId?: string;
  source?: ContactSource;
  updatedBy?: string;
  companyAssociations?: Array<{
    companyId: string;
    role?: ContactRole;
    isPrimary?: boolean;
  }>;
}

/**
 * Contact company association interface
 */
export interface ContactCompanyAssociation {
  contactId: string;
  companyId: string;
  role?: ContactRole;
  isPrimary?: boolean;
}

/**
 * Note create interface
 */
export interface NoteCreate {
  dealId: string;
  content: string;
  createdBy?: string;
}

/**
 * Field ownership interface
 */
export interface FieldOwnership {
  dealId: string;
  fieldName: string;
  ownerSource: DataSource;
  lastUpdated: string;
}

/**
 * Change log entry interface
 */
export interface ChangeLogEntry {
  id: string;
  dealId: string;
  fieldName: string;
  oldValue: string | null;
  newValue: string | null;
  changeSource: DataSource;
  changedAt: string;
  changedBy: string | null;
}

/**
 * Company relationship creation interface
 */
export interface CompanyRelationshipCreate {
  parentCompanyId: string;
  childCompanyId: string;
  relationshipType: CompanyRelationshipType;
}

/**
 * Deal contact role interface
 */
export interface DealContactRole {
  dealId: string;
  contactId: string;
  role: string;
}

/**
 * Deal primary company interface
 */
export interface DealPrimaryCompany {
  dealId: string;
  companyId: string;
}