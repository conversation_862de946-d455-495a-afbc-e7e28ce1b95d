import { HubSpotSyncJob } from "../../jobs/hubspot-sync-job";

// Singleton instance of the HubSpot sync job
let hubspotSyncJobInstance: HubSpotSyncJob | null = null;

/**
 * Initialize the HubSpot sync job
 * This function ensures the job is only initialized once
 */
export function initializeHubSpotSyncJob(): void {
  if (!hubspotSyncJobInstance) {
    console.log('Initializing HubSpot sync job...');
    hubspotSyncJobInstance = new HubSpotSyncJob();
    hubspotSyncJobInstance.start();
    console.log('HubSpot sync job initialized and started');
  } else {
    console.log('HubSpot sync job already initialized');
  }
}

/**
 * Get the HubSpot sync job instance
 * @returns The sync job instance, or null if not initialized
 */
export function getHubSpotSyncJob(): HubSpotSyncJob | null {
  return hubspotSyncJobInstance;
}

/**
 * Stop the HubSpot sync job
 * Useful for graceful shutdown or testing
 */
export function stopHubSpotSyncJob(): void {
  if (hubspotSyncJobInstance) {
    hubspotSyncJobInstance.stop();
    console.log('HubSpot sync job stopped');
  }
}