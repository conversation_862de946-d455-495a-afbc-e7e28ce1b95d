/**
 * Re-export error handling utilities from utils/error
 * This file serves as a bridge to maintain compatibility with imports
 */

import {
  withErrorHandling,
  withTimeout,
  isRetryableError,
  AppError,
  ApiError,
  ValidationError,
  TimeoutError,
  ErrorHandlingOptions as ErrorHandlingOptionsType
} from "../utils/error";

// Re-export everything
export {
  withErrorHandling,
  withTimeout,
  isRetryableError,
  AppError,
  ApiError,
  ValidationError,
  TimeoutError
};

// Explicitly re-export the type
export type ErrorHandlingOptions<T> = ErrorHandlingOptionsType<T>;
