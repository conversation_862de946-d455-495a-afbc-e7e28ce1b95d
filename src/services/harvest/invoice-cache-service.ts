/**
 * Harvest Invoice Cache Service
 * 
 * Manages caching of Harvest invoice totals for performance optimization.
 * Fetches invoice data from Harvest API and stores aggregated totals in local cache.
 */

import { HarvestClient } from "../../api/integrations/harvest";
import db from "../../database";

export interface HarvestInvoiceCache {
  harvestClientId: number;
  totalInvoiced: number;
  invoiceCount: number;
  lastUpdated: string;
  createdAt: string;
}

export interface HarvestInvoiceSummary {
  clientId: number;
  clientName: string;
  totalAmount: number;
  invoiceCount: number;
}

export class HarvestInvoiceCacheService {
  private client: HarvestClient;
  private cacheExpiryHours: number;

  constructor(client: HarvestClient, cacheExpiryHours: number = 6) {
    this.client = client;
    this.cacheExpiryHours = cacheExpiryHours;
  }

  /**
   * Get cached invoice total for a specific Harvest client
   * @param harvestClientId Harvest client ID
   * @returns Cached invoice data or null if not found/expired
   */
  getCachedInvoiceTotal(harvestClientId: number): HarvestInvoiceCache | null {
    try {
      const cached = db.prepare(`
        SELECT 
          harvest_client_id as harvestClientId,
          total_invoiced as totalInvoiced,
          invoice_count as invoiceCount,
          last_updated as lastUpdated,
          created_at as createdAt
        FROM harvest_invoice_cache 
        WHERE harvest_client_id = ?
          AND datetime(last_updated) > datetime('now', '-${this.cacheExpiryHours} hours')
      `).get(harvestClientId) as HarvestInvoiceCache | undefined;

      return cached || null;
    } catch (error) {
      console.error('Error getting cached invoice total:', error);
      return null;
    }
  }

  /**
   * Get all cached invoice totals (non-expired)
   * @returns Array of cached invoice data
   */
  getAllCachedInvoiceTotals(): HarvestInvoiceCache[] {
    try {
      const cached = db.prepare(`
        SELECT 
          harvest_client_id as harvestClientId,
          total_invoiced as totalInvoiced,
          invoice_count as invoiceCount,
          last_updated as lastUpdated,
          created_at as createdAt
        FROM harvest_invoice_cache 
        WHERE datetime(last_updated) > datetime('now', '-${this.cacheExpiryHours} hours')
        ORDER BY harvest_client_id
      `).all() as HarvestInvoiceCache[];

      return cached;
    } catch (error) {
      console.error('Error getting all cached invoice totals:', error);
      return [];
    }
  }

  /**
   * Fetch invoice totals from Harvest API and update cache
   * @param harvestClientId Optional specific client ID to refresh
   * @returns Updated cache data
   */
  async refreshInvoiceCache(harvestClientId?: number): Promise<HarvestInvoiceCache[]> {
    try {
      console.log(`Refreshing Harvest invoice cache${harvestClientId ? ` for client ${harvestClientId}` : ""}`);

      // Get invoices from Harvest API
      const invoices = await this.client.getInvoices();
      
      if (!invoices || invoices.length === 0) {
        console.log('No invoices found from Harvest API');
        return [];
      }

      // Group invoices by client and calculate totals
      const clientTotals = new Map<number, HarvestInvoiceSummary>();

      for (const invoice of invoices) {
        const clientId = invoice.client?.id;
        const clientName = invoice.client?.name;
        if (!clientId || !clientName) continue;

        // Only process specific client if requested
        if (harvestClientId && clientId !== harvestClientId) continue;

        // Only count paid/sent/open invoices (not drafts)
        if (invoice.state !== 'paid' && invoice.state !== 'sent' && invoice.state !== 'open') continue;

        const existing = clientTotals.get(clientId) || {
          clientId,
          clientName,
          totalAmount: 0,
          invoiceCount: 0
        };

        existing.totalAmount += invoice.amount || 0;
        existing.invoiceCount += 1;
        clientTotals.set(clientId, existing);
      }

      // Update cache for each client
      const updatedCache: HarvestInvoiceCache[] = [];
      const now = new Date().toISOString();

      for (const [clientId, totals] of clientTotals) {
        try {
          // Upsert cache entry
          db.prepare(`
            INSERT OR REPLACE INTO harvest_invoice_cache (
              harvest_client_id,
              client_name,
              total_invoiced,
              invoice_count,
              last_updated,
              updated_at,
              created_at
            ) VALUES (?, ?, ?, ?, ?, ?, COALESCE(
              (SELECT created_at FROM harvest_invoice_cache WHERE harvest_client_id = ?),
              ?
            ))
          `).run(
            clientId,
            totals.clientName,
            totals.totalAmount,
            totals.invoiceCount,
            now,
            now,
            clientId, // For the COALESCE subquery
            now       // Default created_at if new record
          );

          updatedCache.push({
            harvestClientId: clientId,
            totalInvoiced: totals.totalAmount,
            invoiceCount: totals.invoiceCount,
            lastUpdated: now,
            createdAt: now // This will be the actual created_at from DB
          });

          console.log(`Updated cache for client ${clientId}: ${totals.invoiceCount} invoices, $${totals.totalAmount.toFixed(2)}`);
        } catch (error) {
          console.error(`Error updating cache for client ${clientId}:`, error);
        }
      }

      console.log(`Harvest invoice cache refresh complete. Updated ${updatedCache.length} clients.`);
      return updatedCache;

    } catch (error) {
      console.error('Error refreshing Harvest invoice cache:', error);
      return [];
    }
  }

  /**
   * Get invoice total for a client (cached or fresh)
   * @param harvestClientId Harvest client ID
   * @param forceRefresh Force refresh from API
   * @returns Invoice total data
   */
  async getInvoiceTotal(harvestClientId: number, forceRefresh: boolean = false): Promise<HarvestInvoiceCache | null> {
    // Try cache first unless force refresh
    if (!forceRefresh) {
      const cached = this.getCachedInvoiceTotal(harvestClientId);
      if (cached) {
        return cached;
      }
    }

    // Refresh cache for this specific client
    const refreshed = await this.refreshInvoiceCache(harvestClientId);
    return refreshed.find(cache => cache.harvestClientId === harvestClientId) || null;
  }

  /**
   * Clear expired cache entries
   * @returns Number of entries cleared
   */
  clearExpiredCache(): number {
    try {
      const result = db.prepare(`
        DELETE FROM harvest_invoice_cache 
        WHERE datetime(last_updated) <= datetime('now', '-${this.cacheExpiryHours} hours')
      `).run();

      console.log(`Cleared ${result.changes} expired Harvest invoice cache entries`);
      return result.changes;
    } catch (error) {
      console.error('Error clearing expired cache:', error);
      return 0;
    }
  }

  /**
   * Get cache statistics
   * @returns Cache statistics
   */
  getCacheStats(): { totalEntries: number; expiredEntries: number; validEntries: number } {
    try {
      const total = db.prepare(`SELECT COUNT(*) as count FROM harvest_invoice_cache`).get() as { count: number };
      
      const expired = db.prepare(`
        SELECT COUNT(*) as count FROM harvest_invoice_cache 
        WHERE datetime(last_updated) <= datetime('now', '-${this.cacheExpiryHours} hours')
      `).get() as { count: number };

      return {
        totalEntries: total.count,
        expiredEntries: expired.count,
        validEntries: total.count - expired.count
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return { totalEntries: 0, expiredEntries: 0, validEntries: 0 };
    }
  }
}
