import { HarvestClient } from "../../api/integrations/harvest";
import { withErrorHandling, type ErrorHandlingOptions } from "../error-handling";

export interface TaskBreakdown {
  taskId: number;
  taskName: string;
  hours: number;
  billableHours: number;
  color?: string; // For visualization
  isBillable: boolean;
}

export interface FixedPriceProject {
  projectId: number;
  projectName: string;
  clientId: number;
  clientName: string;
  billableAmount: number;
  currency: string;
}

export interface StaffUtilization {
  userId: number;
  userName: string;
  billableHours: number;
  totalHours: number;
  leaveHours: number; // Hours spent on leave tasks
  weeklyCapacity: number; // in hours, not seconds
  utilization: number; // percentage (0-100) with leave included in capacity
  adjustedUtilization: number; // percentage (0-100) with leave excluded from capacity
  harvestUtilization: number; // Harvest's calculation (billable/total)
  billableAmount?: number; // Billable amount in currency (from Harvest)
  currency?: string; // Currency code (from Harvest)
  costRate?: number; // Cost rate in currency (from Harvest)
  avatarUrl?: string;
  isContractor: boolean;
  taskBreakdown: TaskBreakdown[]; // Task breakdown for visualization
}

export class HarvestTimeReportService {
  constructor(private client: HarvestClient) {}

  /**
   * Calculate period capacity based on workdays
   * @param startDate Start date
   * @param endDate End date
   * @param weeklyCapacity Weekly capacity in hours
   * @returns Capacity for the period in hours
   */
  private calculatePeriodCapacity(startDate: Date, endDate: Date, weeklyCapacity: number): number {
    // Calculate daily capacity (weekly capacity divided by 5 workdays)
    const dailyCapacity = weeklyCapacity / 5;

    // If the end date is in the future, use today's date as the effective end date
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

    // Use the earlier of endDate or today for capacity calculation
    const effectiveEndDate = endDate > today ? today : endDate;

    // If start date is after today (future period), return 0 capacity
    if (startDate > today) {
      return 0;
    }

    // Calculate total days in the period
    const daysDifference = Math.ceil((effectiveEndDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Count workdays (excluding weekends)
    let workdays = 0;
    const currentDate = new Date(startDate);

    // Iterate through each day in the range
    for (let i = 0; i < daysDifference; i++) {
      const dayOfWeek = currentDate.getDay();
      // Only count weekdays (0 = Sunday, 6 = Saturday)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        workdays++;
      }
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calculate capacity based on workdays
    return dailyCapacity * workdays;
  }

  /**
   * Get fixed price project income for a given time period
   * @param fromDate Start date in YYYY-MM-DD format
   * @param toDate End date in YYYY-MM-DD format
   * @returns Fixed price project income data
   */
  async getFixedPriceProjectIncome(fromDate: string, toDate: string): Promise<FixedPriceProject[]> {
    return withErrorHandling(
      async () => {
        console.log('Fetching fixed price project income from Harvest API');

        // Get projects time report with include_fixed_fee=true
        const projectsReport = await this.client.getTimeReport('projects', {
          from: fromDate,
          to: toDate,
          include_fixed_fee: true
        });

        // Get all projects to identify which ones are fixed fee
        const projects = await this.client.getProjects();

        // Filter for fixed fee projects
        const fixedPriceProjects: FixedPriceProject[] = [];

        if (projectsReport.results) {
          for (const result of projectsReport.results) {
            // Find the corresponding project to check if it's fixed fee
            const project = projects.find(p => p.id === result.project_id);

            if (project && project.is_fixed_fee && result.billable_amount > 0) {
              fixedPriceProjects.push({
                projectId: result.project_id,
                projectName: result.project_name,
                clientId: result.client_id,
                clientName: result.client_name,
                billableAmount: result.billable_amount,
                currency: result.currency
              });
            }
          }
        }

        console.log(`Found ${fixedPriceProjects.length} fixed price projects with income`);
        return fixedPriceProjects;
      },
      {
        operationName: "getFixedPriceProjectIncome",
        fallbackValue: [],
        onError: (error) => {
          console.error('Error getting fixed price project income:', error);
        }
      }
    );
  }

  /**
   * Get staff utilization data
   * @param fromDate Start date in YYYY-MM-DD format
   * @param toDate End date in YYYY-MM-DD format
   * @returns Staff utilization data
   */
  async getStaffUtilization(fromDate: string, toDate: string): Promise<StaffUtilization[]> {
    return withErrorHandling(
      async () => {
        // Get team time report from Harvest
        const teamReport = await this.client.getTimeReport('team', {
          from: fromDate,
          to: toDate,
          include_fixed_fee: true
        });

        // Get tasks time report from Harvest
        const tasksReport = await this.client.getTimeReport('tasks', {
          from: fromDate,
          to: toDate
        });

        // Get detailed time entries to map tasks to users
        const timeEntries = await this.client.getTimeEntries({
          from: fromDate,
          to: toDate
        });

        // Get all users to ensure we include those with no hours
        const users = await this.client.getUsers();

        // Map to utilization data
        const utilizationData: StaffUtilization[] = [];

        // We no longer assign colors here - this is now handled by the frontend
        // using the colorUtils module for consistent colors across components

        // Process active users
        users.filter(user => user.is_active).forEach(user => {
          // Find user in team report
          const reportEntry = teamReport.results?.find(entry => entry.user_id === user.id);

          // Get weekly capacity in hours (convert from seconds)
          // The weekly_capacity from Harvest API is in seconds
          // Default to 37.5 hours (135000 seconds) if not specified
          const weeklyCapacityHours = (user.weekly_capacity || 135000) / 3600;

          // Calculate billable and total hours
          const billableHours = reportEntry?.billable_hours || 0;
          const totalHours = reportEntry?.total_hours || 0;

          // Get billable amount and currency if available
          const billableAmount = reportEntry?.billable_amount;
          const currency = reportEntry?.currency;

          // Calculate utilization (billable hours / weekly capacity)
          // For the selected time period, we need to adjust the weekly capacity
          // based on the number of workdays in the period
          const startDate = new Date(fromDate);
          const endDate = new Date(toDate);

          // Calculate period capacity using workdays (excluding weekends)
          const periodCapacity = this.calculatePeriodCapacity(startDate, endDate, weeklyCapacityHours);

          // Calculate utilization
          const utilization = periodCapacity > 0 ? (billableHours / periodCapacity) * 100 : 0;

          // Calculate Harvest's utilization (for comparison)
          const harvestUtilization = totalHours > 0 ? (billableHours / totalHours) * 100 : 0;

          // Get task breakdown for this user
          const userTaskBreakdown: TaskBreakdown[] = [];

          // Filter time entries for this user and group by task
          const userTimeEntries = timeEntries.filter(entry => entry.user?.id === user.id);

          // Group time entries by task
          const taskGroups = new Map<number, {
            taskName: string,
            hours: number,
            billableHours: number,
            isBillable: boolean
          }>();

          userTimeEntries.forEach(entry => {
            if (!entry.task?.id) return;

            const taskId = entry.task.id;
            const hours = entry.hours || 0;
            const isBillable = entry.billable || false;
            const billableHours = isBillable ? hours : 0;

            if (taskGroups.has(taskId)) {
              const task = taskGroups.get(taskId)!;
              task.hours += hours;
              task.billableHours += billableHours;
            } else {
              taskGroups.set(taskId, {
                taskName: entry.task.name || "Unknown Task",
                hours,
                billableHours,
                isBillable
              });
            }
          });

          // Convert task groups to task breakdown array
          taskGroups.forEach((task, taskId) => {
            userTaskBreakdown.push({
              taskId,
              taskName: task.taskName,
              hours: task.hours,
              billableHours: task.billableHours,
              isBillable: task.isBillable,
              // Color will be assigned in the frontend
            });
          });

          // Sort tasks by hours (descending)
          userTaskBreakdown.sort((a, b) => b.hours - a.hours);

          // Define leave task names (case-insensitive)
          const LEAVE_TASK_NAMES = [
            'public holiday',
            'annual leave',
            'other unpaid leave',
            "carer's leave',
            'community service leave',
            'compassionate leave',
            'parental leave',
            'personal leave',
            'sick leave'
          ];

          // Calculate leave hours
          const leaveHours = userTaskBreakdown
            .filter(task => LEAVE_TASK_NAMES.some(leaveName =>
              task.taskName.toLowerCase().includes(leaveName.toLowerCase())))
            .reduce((sum, task) => sum + task.hours, 0);

          // Calculate adjusted utilisation (excluding leave from capacity)
          const adjustedCapacity = Math.max(0.1, periodCapacity - leaveHours);
          const adjustedUtilization = (billableHours / adjustedCapacity) * 100;

          // Debug logging
          const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
          let workdays = 0;
          const tempDate = new Date(startDate);
          for (let i = 0; i < daysDifference; i++) {
            const dayOfWeek = tempDate.getDay();
            if (dayOfWeek !== 0 && dayOfWeek !== 6) workdays++;
            tempDate.setDate(tempDate.getDate() + 1);
          }

          // Check if the period extends into the future
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const isProrated = endDate > today;

          console.log(`Utilization for ${user.first_name} ${user.last_name}:`, {
            billableHours,
            totalHours,
            leaveHours,
            weeklyCapacityHours,
            dailyCapacity: weeklyCapacityHours / 5,
            workdays,
            periodCapacity,
            adjustedCapacity,
            utilization: `${Math.round(utilization)}%`,
            adjustedUtilization: `${Math.round(adjustedUtilization)}%`,
            harvestUtilization: `${Math.round(harvestUtilization)}%`,
            dateRange: `${fromDate} to ${toDate}${isProrated ? ' (prorated to today)" : "}`,
            taskCount: userTaskBreakdown.length
          });

          utilizationData.push({
            userId: user.id,
            userName: `${user.first_name} ${user.last_name}`,
            billableHours,
            totalHours,
            leaveHours,
            weeklyCapacity: weeklyCapacityHours,
            utilization,
            adjustedUtilization,
            harvestUtilization,
            billableAmount,
            currency,
            costRate: user.cost_rate || undefined, // Include cost rate if available
            avatarUrl: user.avatar_url,
            isContractor: user.is_contractor || false,
            taskBreakdown: userTaskBreakdown
          });
        });

        // Sort by utilization (highest first)
        return utilizationData.sort((a, b) => b.utilization - a.utilization);
      },
      {
        operationName: "getStaffUtilization",
        fallbackValue: [],
        onError: (error) => {
          console.error('Error getting staff utilization:', error);
        }
      }
    );
  }
}
