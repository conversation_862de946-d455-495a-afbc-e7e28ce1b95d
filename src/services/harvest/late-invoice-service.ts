import { HarvestClient, HarvestInvoice } from "../../api/integrations/harvest";
import { withErrorHandling } from "../../utils/error";

export interface LateInvoice {
  id: string;
  invoiceNumber: string;
  reference: string;
  dueDate: Date;
  amount: number;
  client: string;
  status: string;
  daysPastDue: number;
}

/**
 * Service for handling late (overdue) Harvest invoices
 */
export class LateInvoiceService {
  constructor(private readonly client: HarvestClient) {}

  /**
   * Get late invoices from Harvest
   * @returns Array of late invoices
   */
  async getLateInvoices(): Promise<LateInvoice[]> {
    return withErrorHandling(
      async () => {
        // Get current date at start of day
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Get all invoices without any filtering
        console.log('Fetching all invoices from Harvest for late invoice check');
        const allInvoices = await this.client.getInvoices({});
        console.log(`Found ${allInvoices.length} total invoices in Harvest`);

        // Filter to include only open invoices (sent but not paid)
        // that are past their due date
        const lateInvoices = allInvoices.filter(invoice => 
          invoice.state === 'open" && 
          new Date(invoice.due_date) < today
        );

        console.log(`Found ${lateInvoices.length} late invoices from Harvest`);

        // Transform to LateInvoice format
        return this.transformToLateInvoices(lateInvoices, today);
      },
      {
        operationName: "getLateInvoices",
        fallbackValue: [], // Return empty array on error for resilience
        onError: (error) => {
          console.error('Error getting late invoices from Harvest:", error);
        }
      }
    );
  }

  /**
   * Transform Harvest invoices to LateInvoice format
   * @param invoices Harvest invoices
   * @param today Today"s date for calculating days past due
   * @returns Transformed late invoices
   */
  private transformToLateInvoices(invoices: HarvestInvoice[], today: Date): LateInvoice[] {
    return invoices.map(invoice => {
      const dueDate = new Date(invoice.due_date);

      // Calculate days past due
      const diffTime = Math.abs(today.getTime() - dueDate.getTime());
      const daysPastDue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return {
        id: invoice.id.toString(),
        invoiceNumber: invoice.number || "",
        reference: invoice.subject || `Invoice #${invoice.number}`,
        dueDate,
        amount: invoice.amount,
        client: invoice.client?.name || "Unknown Client",
        status: invoice.state || "unknown",
        daysPastDue
      };
    });
  }
}