import { Transaction } from "../../types/financial";
import { HarvestClient, HarvestInvoice } from "../../api/integrations/harvest";
import { withErrorHandling } from "../../utils/error";

/**
 * Service for handling Harvest invoice operations
 * Focused on retrieving invoices and transforming them to Transaction format
 */
export class HarvestInvoiceService {
  constructor(private readonly client: HarvestClient) {}
  
  /**
   * Get invoices from Harvest and transform to Transaction format
   * @param startDate Start date for invoices
   * @param endDate End date for invoices
   * @param _unused Unused parameter (kept for API compatibility)
   * @returns Transformed invoice transactions
   */
  /**
   * Get invoices from Harvest and transform to Transaction format
   * 
   * @param startDate Start date for invoices
   * @param endDate End date for invoices
   * @returns Transformed invoice transactions
   */
  async getInvoices(startDate: Date, endDate: Date): Promise<Transaction[]> {
    // Format dates for logging
    const fromDate = startDate.toISOString().split('T')[0];
    const toDate = endDate.toISOString().split('T')[0];
    console.log(`Fetching invoices from Harvest API (${fromDate} to ${toDate})`);
    
    return withErrorHandling(
      async () => {
        // Get all invoices without any filtering
        console.log('Fetching all invoices from Harvest without filtering');
        const allInvoices = await this.client.getInvoices({});
        console.log(`Found ${allInvoices.length} total invoices in Harvest`);
        
        // Filter to include only open invoices (sent but not paid)
        const invoices = allInvoices.filter(invoice => invoice.state === 'open');
        
        console.log(`After filtering, found ${invoices.length} open invoices from Harvest`);
        
        if (invoices.length > 0) {
          console.log('Sample invoice:');
          console.log(`- ID: ${invoices[0].id}`);
          console.log(`- Number: ${invoices[0].number}`);
          // Access client name via nested object
          console.log(`- Client: ${invoices[0].client?.name}`); 
          console.log(`- Amount: ${invoices[0].amount}`);
          console.log(`- State: ${invoices[0].state}`);
          console.log(`- Issue Date: ${invoices[0].issue_date}`);
          console.log(`- Due Date: ${invoices[0].due_date}`);
        }
        
        // Transform invoices to Transaction format
        return this.transformInvoicesToTransactions(invoices);
      },
      {
        operationName: 'getHarvestInvoices',
        fallbackValue: [], // Return empty array on error for resilience
        onError: (error) => {
          console.error('Error getting invoices from Harvest:', error);
        }
      }
    );
  }
  
  /**
   * Transform Harvest invoices to Transaction format
   * @param invoices Harvest invoices
   * @returns Transformed transactions
   */
  private transformInvoicesToTransactions(invoices: HarvestInvoice[]): Transaction[] {
    return invoices.map(invoice => {
      // Generate a unique ID based on invoice state
      // For open invoices, use 'outstanding-invoice-' prefix to match what the cashflow service expects
      // This ensures the cashflow service recognizes them properly
      const id = invoice.state === 'open' 
        ? `outstanding-invoice-${invoice.id}`
        : `harvest-invoice-${invoice.id}`;
      
      // Use due_date as the transaction date
      // This is when we expect to receive the money
      const dueDate = new Date(invoice.due_date);
      
      // Use subject or invoice number as description
      const description = invoice.subject || `Invoice #${invoice.number}`;
      
      // Create Harvest invoice URL using the numeric invoice ID
      const harvestUrl = `https://onbord.harvestapp.com/invoices/${invoice.id}`;
      
      // Extract project info from the first line item (assuming single-project invoices)
      const firstLineItemProject = invoice.line_items?.[0]?.project;
      const projectName = firstLineItemProject?.name || 'Unknown Project';
      const projectId = firstLineItemProject?.id?.toString() || '';

      // Create the transaction object with metadata
      return {
        id,
        type: 'invoice',
        description: description,
        date: dueDate,
        source: 'harvest', // Use lowercase to match type union
        amount: invoice.amount, // Positive for money in
        // Add metadata to help with identification
        metadata: {
          projectId: projectId,
          projectName: projectName,
          // Access client name via nested object
          clientName: invoice.client?.name || 'Unknown Client', 
          invoiceState: invoice.state || 'unknown',
          issue_date: invoice.issue_date, // Add issue date for "recent invoice" detection
          harvestUrl: harvestUrl, // Add URL to view invoice in Harvest
          invoiceNumber: invoice.number // Add invoice number for display
        }
      };
    });
  }
}
