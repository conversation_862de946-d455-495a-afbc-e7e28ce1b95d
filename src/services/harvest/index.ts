import {
  HarvestClient,
  createHarvestClient,
} from "../../api/integrations/harvest";
import { HarvestInvoiceService } from "./invoice-service";
import { HarvestProjectBudgetService } from "./project-budget-service";
import { HarvestTimeReportService } from "./time-report-service";
import { LateInvoiceService } from "./late-invoice-service";
import { HarvestInvoiceCacheService } from "./invoice-cache-service";

/**
 * Harvest service class
 * Main service that orchestrates all Harvest endpoint-specific services
 */
class HarvestService {
  private static instance: HarvestService;
  private client: HarvestClient;

  // Endpoint-specific services
  public invoices: HarvestInvoiceService;
  public projectBudgets: HarvestProjectBudgetService;
  public timeReports: HarvestTimeReportService;
  public lateInvoices: LateInvoiceService;
  public invoiceCache: HarvestInvoiceCacheService;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor(client?: HarvestClient) {
    if (client) {
      this.client = client;
    } else {
      this.client = createHarvestClient();
    }

    // Initialize endpoint-specific services
    this.invoices = new HarvestInvoiceService(this.client);
    this.projectBudgets = new HarvestProjectBudgetService(this.client);
    this.timeReports = new HarvestTimeReportService(this.client);
    this.lateInvoices = new LateInvoiceService(this.client);
    this.invoiceCache = new HarvestInvoiceCacheService(this.client);
  }

  /**
   * Get singleton instance
   * @returns HarvestService instance
   */
  public static getInstance(): HarvestService {
    if (!HarvestService.instance) {
      HarvestService.instance = new HarvestService();
    }
    return HarvestService.instance;
  }

  /**
   * Set Harvest client
   * @param client Harvest client
   */
  public setClient(client: HarvestClient): void {
    this.client = client;

    // Reinitialize services with new client
    this.invoices = new HarvestInvoiceService(this.client);
    this.projectBudgets = new HarvestProjectBudgetService(this.client);
    this.timeReports = new HarvestTimeReportService(this.client);
    this.lateInvoices = new LateInvoiceService(this.client);
  }

  /**
   * Get Harvest client
   * @returns Harvest client
   */
  public getClient(): HarvestClient {
    return this.client;
  }
}

/**
 * Get the HarvestService instance
 */
export const getHarvestService = (): HarvestService => {
  return HarvestService.getInstance();
};

export {
  HarvestService,
  HarvestInvoiceService,
  HarvestProjectBudgetService,
  HarvestTimeReportService,
  LateInvoiceService,
};
