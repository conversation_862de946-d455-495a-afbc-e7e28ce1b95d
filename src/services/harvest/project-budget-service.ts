import { HarvestClient } from "../../api/integrations/harvest";
import { Transaction } from "../../types/financial";
import { addDays } from "date-fns";

/**
 * Project settings interface
 */
export interface ProjectSetting {
  projectId: string;
  invoiceFrequency: 'weekly' | 'biweekly' | 'monthly' | 'custom';
  invoiceIntervalDays: number;
  paymentTerms: number; // days
}

/**
 * Project budget information
 */
export interface ProjectBudget {
  id: string;
  name: string;
  clientId: string;
  clientName: string;
  budgetRemaining: number;
  uninvoicedAmount: number;
  startDate: Date | null;
  endDate: Date | null;
  isActive: boolean;
}

/**
 * Outstanding invoice
 */
export interface OutstandingInvoice {
  id: string;
  projectId: string;
  projectName: string;
  clientName: string;
  amount: number;
  dueDate: Date;
}

/**
 * Service for handling Harvest project budget operations
 * Focused on retrieving project budget data and generating projected invoices
 */
export class HarvestProjectBudgetService {
  constructor(private readonly client: HarvestClient) {}
  
  /**
   * Get project budget information
   * @returns Array of project budget information
   */
  async getProjectBudgets(): Promise<ProjectBudget[]> {
    try {
      console.log('Fetching project budgets from Harvest API');
      
      // Get project budget report
      const projectBudgetReport = await this.client.getProjectBudgetReport();
      
      // Get projects for additional data
      const projects = await this.client.getProjects();
      
      // Get uninvoiced report with required from and to parameters
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const fromDate = `${sixMonthsAgo.getFullYear()}-${String(sixMonthsAgo.getMonth() + 1).padStart(2, '0')}-${String(sixMonthsAgo.getDate()).padStart(2, '0')}`;
      
      const today = new Date();
      const toDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      
      const uninvoicedReport = await this.client.getUninvoicedReport({
        from: fromDate,
        to: toDate
      });
      
      // Check if we have any results from the project budget report
      if (!projectBudgetReport.results || projectBudgetReport.results.length === 0) {
        console.log('No project budget results found, returning empty array');
        return [];
      }
      
      // Transform to ProjectBudget objects
      return projectBudgetReport.results.map(budgetItem => {
        // Find matching project for end date
        const project = projects.find(p => p.id.toString() === budgetItem.project_id.toString());
        
        // Find uninvoiced amount for this project
        const uninvoiced = uninvoicedReport.results?.find(u => 
          u.project_id.toString() === budgetItem.project_id.toString()
        );
        
        return {
          id: budgetItem.project_id.toString(),
          name: budgetItem.project_name,
          clientId: budgetItem.client_id.toString(),
          clientName: budgetItem.client_name,
          budgetRemaining: budgetItem.budget_remaining || 0,
          uninvoicedAmount: uninvoiced?.uninvoiced_amount || 0,
          startDate: project?.starts_on ? new Date(project.starts_on) : null,
          endDate: project?.ends_on ? new Date(project.ends_on) : null,
          isActive: budgetItem.is_active || false
        };
      }).filter(project => project.budgetRemaining > 0 || project.uninvoicedAmount > 0);
    } catch (error: any) {
      console.error('Error getting project budgets from Harvest:', error);
      return [];
    }
  }
  
  /**
   * Get outstanding invoices (sent but not paid)
   * @returns Array of outstanding invoices
   */
  async getOutstandingInvoices(): Promise<OutstandingInvoice[]> {
    try {
      console.log('Fetching outstanding invoices from Harvest API');
      
      // Get ALL invoices without filtering first to see what's available
      const allInvoices = await this.client.getInvoices();
      console.log(`Total invoices from Harvest: ${allInvoices.length}`);
      
      // Log all available states for debugging
      const states = new Set<string>();
      allInvoices.forEach(inv => states.add(inv.state || ''));
      console.log('Available invoice states:', Array.from(states).join(', '));
      
      // Filter to get only outstanding (open) invoices
      const openInvoices = allInvoices.filter(inv => inv.state === 'open');
      console.log(`Found ${openInvoices.length} open (outstanding) invoices`);
      
      // Transform to OutstandingInvoice objects
      return openInvoices.map(invoice => {
        // Log information about each invoice for debugging
        console.log(`Invoice: ID=${invoice.id}, State=${invoice.state}, ` +
                    `Client=${invoice.client?.name}, Amount=${invoice.amount}`);
        
        // Extract project info from the first line item
        const firstLineItemProject = invoice.line_items?.[0]?.project;
        const projectId = firstLineItemProject?.id?.toString() || '';
        const projectName = firstLineItemProject?.name || 'Unknown Project';
        
        return {
          id: invoice.id.toString(),
          projectId: projectId,
          projectName: projectName,
          clientName: invoice.client?.name || 'Unknown Client',
          amount: invoice.amount || 0,
          dueDate: invoice.due_date ? new Date(invoice.due_date) : new Date()
        };
      });
    } catch (error: any) {
      console.error('Error getting outstanding invoices from Harvest:', error);
      return [];
    }
  }
  
  /**
   * Generate projected income transactions based on project budgets and settings
   * @param startDate Start date for projection
   * @param endDate End date for projection
   * @param projectSettings Settings for each project
   * @returns Array of projected income transactions
   */
  async generateProjectedIncome(
    startDate: Date,
    endDate: Date,
    projectSettings: ProjectSetting[]
  ): Promise<Transaction[]> {
    try {
      console.log('Generating projected income from Harvest project data');
      
      const projectedTransactions: Transaction[] = [];
      
      // Get data needed for projections
      const projectBudgets = await this.getProjectBudgets();
      
      // Note: We no longer fetch or include outstanding invoices as they will be
      // calculated on the actual cashflow projection page
      
      // Process each project with budget remaining
      for (const project of projectBudgets) {
        // Skip inactive/archived projects
        if (!project.isActive) {
          console.log(`Skipping project ${project.name} (ID: ${project.id}) - project is not active`);
          continue;
        }
        
        // Get settings for this project
        let settings = projectSettings.find(s => s.projectId === project.id);
        
        // If no settings found, use defaults instead of skipping
        if (!settings) {
          console.log(`Using default settings for project ${project.name} - no saved settings found`);
          settings = {
            projectId: project.id,
            invoiceFrequency: 'biweekly',
            invoiceIntervalDays: 14,
            paymentTerms: 14  // Default to Net 14
          };
        }
        
        // Add uninvoiced work first, but only if it's actually pending
        if (project.uninvoicedAmount > 0) {
          // Skip projects without start and end dates
          if (!project.startDate || !project.endDate) {
            console.log(`Skipping uninvoiced work for project ${project.name} (ID: ${project.id}) - missing start or end date`);
            continue;
          }
          
          // Get today's date for calculations
          const today = new Date();
          
          // For uninvoiced work, we'll invoice at the next logical invoice date
          // Starting from the project start, find the next invoice date after today
          let nextInvoiceDate = new Date(project.startDate);
          nextInvoiceDate.setDate(nextInvoiceDate.getDate() + settings.invoiceIntervalDays);
          
          // Keep advancing until we find an invoice date in the future
          while (nextInvoiceDate <= today) {
            nextInvoiceDate.setDate(nextInvoiceDate.getDate() + settings.invoiceIntervalDays);
          }
          
          // Ensure we don't go beyond project end date
          if (nextInvoiceDate > project.endDate) {
            nextInvoiceDate = new Date(project.endDate);
          }
          
          // Calculate payment date based on payment terms
          const paymentDate = addDays(nextInvoiceDate, settings.paymentTerms);
          
          console.log(`Project ${project.name}: Uninvoiced work will be invoiced on ${nextInvoiceDate.toLocaleDateString()}, paid on ${paymentDate.toLocaleDateString()}`);
          
          // Always include uninvoiced work as long as invoice date is in the future
          if (nextInvoiceDate > today) {
            projectedTransactions.push({
              id: `uninvoiced-${project.id}`,
              type: 'invoice', // Use standard 'invoice' type but with clear ID prefix
              description: `Uninvoiced Work - ${project.name}`,
              date: paymentDate,
              source: 'harvest', // Use lowercase to match type union
              amount: project.uninvoicedAmount,
              // Add metadata to help with identification
              metadata: {
                projectId: project.id,
                projectName: project.name,
                clientName: project.clientName,
                paymentTerms: settings.paymentTerms,
                invoiceFrequency: settings.invoiceFrequency,
                invoiceIntervalDays: settings.invoiceIntervalDays,
                invoiceDate: nextInvoiceDate
              }
            });
          } else {
            console.log(`Skipping uninvoiced work for project ${project.name} - invoice date ${nextInvoiceDate.toISOString()} is not in the future`);
          }
        }
        
        // Handle future work based on remaining budget (excluding uninvoiced amount)
        const remainingBudget = project.budgetRemaining - project.uninvoicedAmount;
        
        if (remainingBudget > 0) {
          // Skip projects without start and end dates
          if (!project.startDate || !project.endDate) {
            console.log(`Skipping project ${project.name} (ID: ${project.id}) - missing start or end date`);
            continue;
          }
          
          // Use the project's actual start and end dates
          const projectStartDate = project.startDate;
          const projectEndDate = project.endDate;
          
          // Always use the project's own timeline for calculations, regardless of today's date
          // This ensures future projects are handled the same as current projects
          
          // Calculate total project duration in days
          const projectDurationDays = Math.max(
            1, 
            Math.round((projectEndDate.getTime() - projectStartDate.getTime()) / (1000 * 60 * 60 * 24))
          );
          
          // Get today's date for filtering
          const today = new Date();
          
          console.log(`Project ${project.name}: Duration ${projectDurationDays} days, Interval ${settings.invoiceIntervalDays} days`);
          
          // Calculate exact number of invoice periods based on project duration
          // We use Math.ceil to ensure we account for partial periods at the end
          const totalInvoiceCount = Math.ceil(projectDurationDays / settings.invoiceIntervalDays);
          console.log(`Project ${project.name}: Will generate ${totalInvoiceCount} total invoices`);
          
          // Calculate amount per invoice - distribute budget evenly across all invoices
          const amountPerInvoice = remainingBudget / totalInvoiceCount;
          
          // Generate all invoices for the project duration
          // For a systematic approach, we'll create all invoices and then filter later
          const invoiceDates = [];
          
          // First invoice is always after first work period (start date + invoice interval)
          let currentInvoiceDate = new Date(projectStartDate);
          currentInvoiceDate.setDate(currentInvoiceDate.getDate() + settings.invoiceIntervalDays);
          
          // Generate all invoice dates through project completion
          for (let i = 0; i < totalInvoiceCount; i++) {
            if (i > 0) {
              // Add invoice interval to get next invoice date
              currentInvoiceDate = new Date(currentInvoiceDate);
              currentInvoiceDate.setDate(currentInvoiceDate.getDate() + settings.invoiceIntervalDays);
            }
            
            // Special handling for the final invoice - align with project end date
            if (i === totalInvoiceCount - 1) {
              // Final invoice should be on project end date
              currentInvoiceDate = new Date(projectEndDate);
            }
            
            // Store invoice date
            invoiceDates.push(new Date(currentInvoiceDate));
          }
          
          // Filter to only include future invoices
          const futureInvoiceDates = invoiceDates.filter(date => date > today);
          
          console.log(`Project ${project.name}: ${futureInvoiceDates.length} future invoices out of ${invoiceDates.length} total`);
          
          // If we have future invoices, adjust the amount to distribute remaining budget
          // over just the future invoices
          let adjustedAmount = amountPerInvoice;
          if (futureInvoiceDates.length > 0 && futureInvoiceDates.length < totalInvoiceCount) {
            adjustedAmount = remainingBudget / futureInvoiceDates.length;
          }
          
          // Create transactions for each future invoice
          futureInvoiceDates.forEach((invoiceDate, index) => {
            const paymentDate = addDays(invoiceDate, settings.paymentTerms);
            
            const isFinalInvoice = index === futureInvoiceDates.length - 1;
            const invoiceType = isFinalInvoice ? "Final Invoice" : "Projected Income";
            
            projectedTransactions.push({
              id: `future-work-${project.id}-${index}`,
              type: 'invoice', // Use standard 'invoice' type but with clear ID prefix
              description: `${invoiceType} - ${project.name}`,
              date: paymentDate,  // Payment date
              source: 'harvest', // Use lowercase to match type union
              amount: adjustedAmount,
              // Add metadata to help with identification
              metadata: {
                projectId: project.id,
                projectName: project.name,
                clientName: project.clientName,
                paymentTerms: settings.paymentTerms,
                invoiceFrequency: settings.invoiceFrequency,
                invoiceIntervalDays: settings.invoiceIntervalDays,
                invoiceDate: invoiceDate
              }
            });
            
            console.log(`Project ${project.name}: Added invoice ${index+1}/${futureInvoiceDates.length} - Invoice: ${invoiceDate.toLocaleDateString()}, Payment: ${paymentDate.toLocaleDateString()}`);
          });
          
          // We no longer need to update currentDate since each invoice date 
          // is calculated independently based on its position in the sequence
        }
      }
      
      return projectedTransactions;
    } catch (error: any) {
      console.error('Error generating projected income:', error);
      return [];
    }
  }
  
  // We no longer need the calculateNextInvoiceDate method as invoice dates
  // are now calculated directly in the generateProjectedIncome method
  
  // Method removed as we now require all projects to have proper start and end dates
}
