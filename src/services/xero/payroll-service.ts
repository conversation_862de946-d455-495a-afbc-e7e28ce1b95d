import { XeroClient } from "../../api/integrations/xero";
import { XeroExpenseBreakdown, XeroPayrollExpenseDisplay } from "../../api/types/xero";

/**
 * Service for handling Xero payroll operations
 */
export class PayrollService {
  constructor(private readonly client: XeroClient) {}

  /**
   * Get payroll expenses from Xero for display
   * @param tenantId Xero tenant ID
   * @param fromDate Optional date to filter from
   * @returns Processed payroll expenses for display
   */
  async getPayrollExpensesForDisplay(tenantId: string, fromDate?: Date): Promise<XeroPayrollExpenseDisplay[]> {
    try {
      // Check for necessary scopes with detailed logging
      const tokenSet = this.client.readTokenSet();
      const scopes = tokenSet?.scope?.split(' ') || [];

      console.log('Available Xero scopes:', scopes);

      const hasPayrollScope = scopes.some(scope =>
        scope.includes('payroll.') ||
        scope.includes('payroll/')
      );

      if (!hasPayrollScope) {
        console.error('Missing payroll scopes in token. Available scopes:', scopes);
        throw new Error('Missing Xero Payroll API scopes. Please ensure proper Xero authorization with payroll.payruns scope.');
      }

      // Fetch pay runs from Xero with detailed logging
      console.log('Fetching payroll data from Xero API...');

      try {
        // Add a timeout to the API call to prevent hanging
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Xero API request timed out after 30 seconds')), 30000);
        });

        // Race the API call against the timeout
        const response = await Promise.race([
          this.client.getPayrollPayRuns(tenantId),
          timeoutPromise
        ]) as any;

        // Log response structure for debugging
        console.log('Xero API response structure:', {
          hasBody: !!response?.body,
          responseKeys: response?.body ? Object.keys(response.body) : [],
          hasPayRuns: !!response?.body?.PayRuns,
          payRunsCount: response?.body?.PayRuns?.length || 0
        });

        // Check if we got valid data back with proper field names
        if (!response || !response.body) {
          console.error('No response body returned from Xero API');
          throw new Error('Empty response from Xero API. Please check your Xero account configuration.');
        }

        // Check for PayRuns vs payRuns (casing can vary in API response)
        // Note: We're explicitly checking for both cases
        const payRuns = response.body.PayRuns || response.body.payRuns;

        if (!payRuns || !Array.isArray(payRuns)) {
          console.error('No payroll data returned from Xero API. Response body structure:',
            Object.keys(response.body));

          // Check if we have any array fields that might contain payroll data
          const arrayFields = Object.entries(response.body)
            .filter(([_, value]) => Array.isArray(value))
            .map(([key, value]) => ({ key, length: (value as any[]).length }));

          if (arrayFields.length > 0) {
            console.log('Found array fields in response that might contain payroll data:', arrayFields);

            // Try the first array field with data
            const firstArrayField = arrayFields.find(field => field.length > 0);
            if (firstArrayField) {
              console.log(`Trying to use ${firstArrayField.key} as payroll data source`);
              const possiblePayRuns = response.body[firstArrayField.key];

              // Check if this array has the expected fields
              if (possiblePayRuns.length > 0 &&
                  (possiblePayRuns[0].PayRunID ||
                   possiblePayRuns[0].Wages !== undefined ||
                   possiblePayRuns[0].Super !== undefined)) {
                console.log(`Found valid payroll data in ${firstArrayField.key}`);
                const result = this.mapPayrollToDisplayFormat(possiblePayRuns);
                console.log(`Mapped ${result.length} pay runs from alternative field`);
                return result;
              }
            }
          }

          throw new Error('No payroll data available from Xero API. Please check your Xero account configuration.');
        }

        if (payRuns.length === 0) {
          console.warn('Xero API returned empty payroll data array');
          return []; // Return empty array instead of throwing an error
        } else {
          console.log(`Found ${payRuns.length} pay runs from Xero API`);
          // Log first item structure for debugging
          console.log('First pay run structure:', Object.keys(payRuns[0]));

          // Check if the first item has the expected fields
          const firstItem = payRuns[0];
          const hasExpectedFields = firstItem.PayRunID ||
                                   firstItem.Wages !== undefined ||
                                   firstItem.Super !== undefined;

          if (!hasExpectedFields) {
            console.warn('Payroll data may not be in the expected format:', firstItem);
          }
        }

        // Transform payroll data for display
        const result = this.mapPayrollToDisplayFormat(payRuns);
        console.log(`Mapped ${result.length} pay runs to display format`);

        // Check if we have valid data (non-zero amounts and valid IDs)
        const hasValidData = result.length > 0 && result.some(item =>
          item.id && item.amount > 0 && item.payRunId
        );

        if (!hasValidData && result.length > 0) {
          console.warn('All mapped data appears to be invalid - returning empty array instead');
          return [];
        }

        return result;
      } catch (apiError) {
        console.error('Error from Xero API:', apiError);

        // Enhanced error message with API details
        const errorMsg = apiError instanceof Error
          ? `Xero API error: ${apiError.message}`
          : "Unknown error from Xero API";

        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('Error getting payroll data from Xero:', error);
      throw error; // Re-throw to properly propagate the error
    }
  }

  /**
   * Map Xero payroll data to display format
   * @param payRuns Raw payroll data from Xero API
   * @returns Transformed payroll expenses for display
   */
  private mapPayrollToDisplayFormat(payRuns: any[]): XeroPayrollExpenseDisplay[] {
    // Log the raw input to understand format
    console.log('Raw payroll data format:', {
      payRunsCount: payRuns.length,
      sampleFields: payRuns.length > 0 ? Object.keys(payRuns[0]) : [],
      firstItem: payRuns.length > 0 ? {
        id: payRuns[0].PayRunID || payRuns[0].payRunID,
        status: payRuns[0].PayRunStatus || payRuns[0].payRunStatus,
        hasWages: "Wages" in payRuns[0] || "wages" in payRuns[0],
        hasSuper: "Super" in payRuns[0] || "_super" in payRuns[0],
        hasTax: "Tax" in payRuns[0] || "tax" in payRuns[0],
        paymentDate: payRuns[0].PaymentDate || payRuns[0].paymentDate,
        periodStartDate: payRuns[0].PayRunPeriodStartDate || payRuns[0].payRunPeriodStartDate,
        periodEndDate: payRuns[0].PayRunPeriodEndDate || payRuns[0].payRunPeriodEndDate
      } : null
    });

    // If we have no pay runs, return an empty array instead of generating mock data
    if (!payRuns || payRuns.length === 0) {
      console.warn('No payroll data to map - returning empty array');
      return [];
    }

    return payRuns.map(payRun => {
      // Handle field name variations (PascalCase vs camelCase)
      // Get the ID field - could be PayRunID or payRunID
      const payRunId = payRun.PayRunID || payRun.payRunID || "";

      // Get the status field - could be PayRunStatus or payRunStatus
      const payRunStatus = payRun.PayRunStatus || payRun.payRunStatus || "";

      // Get date fields - handle both naming conventions
      const paymentDateField = payRun.PaymentDate || payRun.paymentDate;
      const periodStartDateField = payRun.PayRunPeriodStartDate || payRun.payRunPeriodStartDate;
      const periodEndDateField = payRun.PayRunPeriodEndDate || payRun.payRunPeriodEndDate;

      // Extract dates from Xero's date format
      // Xero uses "/Date(timestamp)/" format, need to extract the timestamp
      const extractDate = (dateString: string | Date): Date => {
        // If it's already a Date object, return it
        if (dateString instanceof Date) {
          return dateString;
        }

        if (!dateString) {
          console.warn('Missing date string, using current date');
          return new Date();
        }

        try {
          // Check if it's in Xero's /Date(timestamp)/ format
          if (typeof dateString === 'string' && dateString.includes('/Date(')) {
            // Extract the numeric timestamp (milliseconds since epoch)
            const matches = dateString.match(/\/Date\(([+-]?\d+)([+-]\d{4})?\)\//);

            if (!matches || matches.length < 2) {
              console.warn(`Invalid Xero date format: ${dateString}, using current date instead`);
              return new Date();
            }

            const timestamp = parseInt(matches[1]);

            if (isNaN(timestamp)) {
              console.warn(`Invalid timestamp in date string: ${dateString}, using current date instead`);
              return new Date();
            }

            return new Date(timestamp);
          } else {
            // Try to parse as a regular date string or timestamp
            const date = new Date(dateString);

            if (isNaN(date.getTime())) {
              console.warn(`Invalid date string format: ${dateString}, using current date instead`);
              return new Date();
            }

            return date;
          }
        } catch (error) {
          console.error(`Error parsing date ${dateString}:`, error);
          return new Date();
        }
      };

      // Extract all relevant dates with robust error handling
      const paymentDate = paymentDateField ? extractDate(paymentDateField) : new Date();
      const periodStartDate = periodStartDateField ? extractDate(periodStartDateField) : new Date();
      const periodEndDate = periodEndDateField ? extractDate(periodEndDateField) : new Date();

      // Log the extracted dates for debugging
      console.log('Extracted dates for payroll:', {
        id: payRunId,
        rawPaymentDate: paymentDateField,
        parsedPaymentDate: paymentDate.toISOString(),
        rawStartDate: periodStartDateField,
        parsedStartDate: periodStartDate.toISOString(),
        rawEndDate: periodEndDateField,
        parsedEndDate: periodEndDate.toISOString()
      });

      // Calculate total payroll amount including all costs to business
      // Handle missing fields with defaults and different field names
      // Use NetPay instead of Wages to avoid double counting with Tax
      const netPay = typeof payRun.NetPay === 'number' ? payRun.NetPay :
                   (typeof payRun.netPay === 'number' ? payRun.netPay : 0);

      const superAmount = typeof payRun.Super === 'number' ? payRun.Super :
                         (typeof payRun._super === 'number' ? payRun._super : 0);

      const tax = typeof payRun.Tax === 'number' ? payRun.Tax :
                (typeof payRun.tax === 'number' ? payRun.tax : 0);

      const reimbursement = typeof payRun.Reimbursement === 'number' ? payRun.Reimbursement :
                           (typeof payRun.reimbursement === 'number' ? payRun.reimbursement : 0);

      // Total cost to business is all components
      const totalCost = netPay + superAmount + tax + reimbursement;

      // Log financial calculations
      console.log('Payroll financial details:', {
        id: payRunId,
        netPay,
        superAmount,
        tax,
        reimbursement,
        totalCost
      });

      // For employee count, use a better estimation method
      // Based on typical monthly net pay per employee if not provided
      const avgNetPayPerEmployee = 5500; // Approximate monthly net pay per employee
      const estimatedEmployeeCount = Math.max(1, Math.round(netPay / avgNetPayPerEmployee));

      // Format description to include total cost details
      const formattedAmount = new Intl.NumberFormat('en-AU', {
        style: "currency",
        currency: "AUD"
      }).format(totalCost);

      // Format a detailed description showing the components
      const formattedNetPay = new Intl.NumberFormat('en-AU', { style: "currency", currency: "AUD" }).format(netPay);
      const formattedSuper = new Intl.NumberFormat('en-AU', { style: "currency", currency: "AUD" }).format(superAmount);
      const formattedTax = new Intl.NumberFormat('en-AU', { style: "currency", currency: "AUD" }).format(tax);

      // Create a more detailed description for the frontend
      const description = `Monthly Payroll: ${formattedAmount}`;
      const detailedDescription = `Net Pay: ${formattedNetPay}, Super: ${formattedSuper}, Tax: ${formattedTax}`;

      // Determine the frequency from period dates
      const frequency = this.determinePayrollFrequency(periodStartDate, periodEndDate);

      // Check if we have a valid PayRunID
      if (!payRunId) {
        console.warn('Missing PayRunID in payroll data - this may indicate an API format issue');
      }

      // Return a properly mapped object with real data
      return {
        id: payRunId, // Use the real ID from Xero, NO random fallbacks
        payRunId: payRunId,
        paymentDate,
        periodStartDate,
        periodEndDate,
        amount: totalCost,
        wages: netPay, // Use netPay instead of wages to avoid double counting with tax
        superannuation: superAmount,
        tax: tax,
        reimbursement: reimbursement,
        employeeCount: estimatedEmployeeCount,
        status: this.mapPayrollStatus(payRunStatus),
        description,
        detailedDescription,
        frequency: frequency || "monthly", // Use calculated frequency, fallback to monthly
        isAlreadyAdded: false,
        source: "xero"
      };
    });
  }

  /**
   * Determine payroll frequency based on period length
   * @param startDate Period start date
   * @param endDate Period end date
   * @returns Frequency string (weekly, fortnightly, monthly)
   */
  private determinePayrollFrequency(startDate: Date, endDate: Date): string {
    // Add validation to ensure dates are valid
    if (!startDate || !endDate || isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.warn('Invalid dates for frequency determination, defaulting to monthly');
      return 'monthly';
    }

    const daysDifference = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    console.log(`Determining frequency: ${daysDifference} days between ${startDate.toISOString()} and ${endDate.toISOString()}`);

    if (daysDifference <= 7) return 'weekly';
    if (daysDifference <= 14) return 'fortnightly';
    if (daysDifference <= 31) return 'monthly';
    return 'custom';
  }

  /**
   * Map Xero payroll status to user-friendly status
   * @param status Status from Xero API
   * @returns User-friendly status
   */
  private mapPayrollStatus(status?: string): string {
    if (!status) return 'Unknown';

    // Log the status for debugging
    console.log(`Mapping payroll status: ${status}`);

    // Normalize the status by converting to uppercase and trimming
    const normalizedStatus = status.toUpperCase().trim();

    switch (normalizedStatus) {
      case 'DRAFT': return 'Draft';
      case 'SCHEDULED': return 'Scheduled';
      case 'COMPLETED': return 'Completed';
      case 'POSTED': return 'Completed'; // POSTED means completed in Xero Payroll AU API
      case 'PAID': return 'Completed';
      case 'APPROVED': return 'Approved';
      case 'PENDING': return 'Pending';
      case 'REJECTED': return 'Rejected';
      case 'DELETED': return 'Deleted';
      case 'VOIDED': return 'Voided';
      case 'PROCESSING': return 'Processing';
      case 'AUTHORISED': return 'Authorized';
      case 'UNKNOWN': return 'Unknown';
      default: {
        // Try to extract a meaningful status from the string
        // This handles cases where the status might be in a different format
        const statusWords = ['draft', 'scheduled', 'completed', 'posted', 'paid', 'approved',
                            'pending', 'rejected', 'deleted', 'voided', 'processing', 'authorised'];

        const matchedStatus = statusWords.find(word => normalizedStatus.includes(word.toUpperCase()));

        if (matchedStatus) {
          // Capitalize first letter
          return matchedStatus.charAt(0).toUpperCase() + matchedStatus.slice(1);
        }

        // If we get an unrecognized status, log it and return a generic value
        console.warn(`Unrecognized payroll status: ${status}`);
        return 'Unknown';
      }
    }
  }

  /**
   * Get payroll expense breakdown from Xero for display
   * @param tenantId Xero tenant ID
   * @param fromDate Optional date to filter from
   * @returns Processed payroll expense breakdown for display
   */
  async getPayrollExpenseBreakdown(tenantId: string, fromDate?: Date): Promise<XeroExpenseBreakdown[]> {
    try {
      // Reuse the existing method to get payroll expenses
      const payrollExpenses = await this.getPayrollExpensesForDisplay(tenantId, fromDate);

      // Transform the payroll expenses into the expense breakdown format
      return this.mapPayrollToExpenseBreakdown(payrollExpenses);
    } catch (error) {
      console.error('Error getting payroll expense breakdown from Xero:', error);
      throw error;
    }
  }

  /**
   * Map payroll expenses to expense breakdown format
   * @param payrollExpenses Payroll expenses from getPayrollExpensesForDisplay
   * @returns Transformed expense breakdown
   */
  private mapPayrollToExpenseBreakdown(payrollExpenses: XeroPayrollExpenseDisplay[]): XeroExpenseBreakdown[] {
    if (!payrollExpenses || payrollExpenses.length === 0) {
      console.warn('No payroll expenses to map to breakdown - returning empty array');
      return [];
    }

    return payrollExpenses.map(expense => {
      // Calculate tax payment date (21st of the following month)
      const taxPaymentDate = new Date(expense.paymentDate);
      taxPaymentDate.setMonth(taxPaymentDate.getMonth() + 1);
      taxPaymentDate.setDate(21);

      // Create the expense breakdown object
      return {
        id: expense.id,
        payRunId: expense.payRunId,
        paymentDate: expense.paymentDate,
        periodStartDate: expense.periodStartDate,
        periodEndDate: expense.periodEndDate,
        employeeCount: expense.employeeCount,
        status: expense.status,
        frequency: expense.frequency,
        source: expense.source || "xero",
        description: expense.description,

        // Net Pay component (renamed from wages to avoid confusion)
        wages: {
          amount: expense.wages || 0, // This is actually netPay now, but we keep the field name for compatibility
          paymentDate: new Date(expense.paymentDate),
          isAdded: false,
          hasRecurringExpense: false
        },

        // Tax component with adjusted payment date
        tax: {
          amount: expense.tax || 0,
          paymentDate: taxPaymentDate,
          isAdded: false,
          hasRecurringExpense: false
        },

        // Superannuation component
        superannuation: {
          amount: expense.superannuation || 0,
          paymentDate: new Date(expense.paymentDate),
          isAdded: false,
          hasRecurringExpense: false
        }
      };
    });
  }
}