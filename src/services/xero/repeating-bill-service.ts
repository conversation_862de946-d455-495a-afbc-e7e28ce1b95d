import { XeroClient } from "../../api/integrations/xero";

/**
 * Interface for repeating bill
 */
export interface RepeatingBill {
  id: string;
  type: string;
  reference: string;
  date: Date;
  amount: number;
  status: string;
}

/**
 * Service for handling Xero repeating bills operations
 * Maps to the Accounting API's repeating invoices endpoint
 */
export class RepeatingBillService {
  constructor(private readonly client: XeroClient) {}
  
  /**
   * Get repeating bills
   * Uses Xero's getRepeatingInvoices endpoint and filters for ACCPAY type
   * @param tenantId Xero tenant ID
   * @param startDate Start date for bills
   * @param endDate End date for bills
   * @returns Repeating bills forecasts
   */
  async getRepeatingBills(tenantId: string, startDate: Date, endDate: Date): Promise<RepeatingBill[]> {
    try {
      // Check if we have the necessary scopes
      const tokenSet = this.client.readTokenSet();
      const scopes = tokenSet?.scope?.split(' ') || [];
      
      const hasAccountingScope = scopes.some(scope => 
        scope.includes('accounting.') || 
        scope.includes('accounting/')
      );
      
      if (!hasAccountingScope) {
        console.log('Missing accounting scopes in token, using empty set');
        return [];
      }
      
      // Get repeating invoices from Xero
      console.log('Fetching repeating invoices from Xero API');
      const response = await this.client.getRepeatingInvoices(tenantId);
      
      // Check if we got valid data back
      if (!response || !response.body || !response.body.repeatingInvoices) {
        console.log('No valid repeating invoices returned from Xero');
        return [];
      }
      
      // Filter for bills (ACCPAY type)
      const repeatingBills = (response.body.repeatingInvoices || [])
        .filter((invoice: { type?: string }) => invoice.type === 'ACCPAY');
      
      console.log(`Found ${repeatingBills.length} repeating bills`);
      
      // Log the first bill structure for debugging (with sensitive data removed)
      if (repeatingBills.length > 0) {
        const sampleBill = { ...repeatingBills[0] };
        // Remove potentially sensitive fields
        delete sampleBill.contact;
        delete sampleBill.lineItems;
        console.log('Sample bill structure:', JSON.stringify(sampleBill, null, 2));
      }
      
      // Calculate future occurrences within the date range
      return this.calculateFutureOccurrences(repeatingBills, startDate, endDate);
    } catch (error: any) {
      console.error('Error getting repeating bills:', error);
      
      // Return empty array on error for resilience
      return [];
    }
  }
  
  /**
   * Calculate future occurrences of repeating bills
   * @param repeatingBills Repeating bills from Xero
   * @param startDate Start date for range
   * @param endDate End date for range
   * @returns Calculated future bill occurrences
   */
  private calculateFutureOccurrences(repeatingBills: any[], startDate: Date, endDate: Date): RepeatingBill[] {
    const futureOccurrences: RepeatingBill[] = [];
    
    for (const bill of repeatingBills) {
      try {
        // Skip bills with no schedule
        if (!bill.schedule) {
          console.log(`Skipping bill without schedule: ${bill.repeatingInvoiceID || "unknown ID"}`);
          continue;
        }
        
        // Get the next scheduled date with fallback to start date
        let nextDate: Date;
        try {
          nextDate = new Date(bill.schedule?.nextScheduledDate || startDate);
          // Validate date is valid
          if (isNaN(nextDate.getTime())) {
            console.log(`Invalid next scheduled date for bill ${bill.repeatingInvoiceID || "unknown"}, using start date`);
            nextDate = new Date(startDate);
          }
        } catch (e) {
          console.log(`Error parsing next scheduled date, using start date`);
          nextDate = new Date(startDate);
        }
        
        // Skip if the next date is beyond our end date
        if (nextDate > endDate) {
          continue;
        }
        
        // Get the frequency from the schedule
        const frequency = this.getFrequency(bill.schedule);
      
        // Calculate occurrences
        let currentDate = new Date(nextDate);
        while (currentDate <= endDate) {
          if (currentDate >= startDate) {
            const billId = bill.repeatingInvoiceID || `bill-${Date.now()}-${Math.random()}`;
            const billReference = bill.reference || bill.name || "Repeating Bill";
            
            // Parse total amount safely
            let amount = 0;
            if (typeof bill.total === 'number') {
              amount = -bill.total; // Negative for outflow
            } else if (typeof bill.total === 'string') {
              try {
                amount = -parseFloat(bill.total);
              } catch (e) {
                console.log(`Error parsing bill amount: ${e}`);
              }
            }
            
            futureOccurrences.push({
              id: `${billId}-${currentDate.toISOString()}`,
              type: "repeating_bill",
              reference: billReference,
              date: new Date(currentDate),
              amount: amount,
              status: "PLANNED"
            });
          }
          
          // Move to next occurrence
          currentDate = this.getNextOccurrence(currentDate, frequency);
        }
      } catch (error) {
        console.error(`Error processing repeating bill: ${error}`);
        // Continue to next bill on error
        continue;
      }
    }
    
    console.log(`Generated ${futureOccurrences.length} future bill occurrences`);
    return futureOccurrences;
  }
  
  /**
   * Parse the frequency from a Xero schedule
   * @param schedule Xero schedule object
   * @returns Frequency object with unit and value
   */
  private getFrequency(schedule: any): { unit: string, value: number } {
    // Default to monthly if no schedule provided
    if (!schedule) {
      return { unit: "month", value: 1 };
    }
    
    // Check if the period is a number or string and handle accordingly
    const periodValue = schedule.period;
    
    // If it's a number, map it directly
    if (typeof periodValue === 'number') {
      switch (periodValue) {
        case 1:
          return { unit: "month", value: 1 }; // Monthly
        case 2:
          return { unit: "month", value: 2 }; // Bi-monthly
        case 3:
          return { unit: "month", value: 3 }; // Quarterly
        case 6:
          return { unit: "month", value: 6 }; // Semi-annually
        case 12:
          return { unit: "year", value: 1 }; // Annually
        case 4:
          return { unit: "week", value: 1 }; // Weekly
        case 5:
          return { unit: "week", value: 2 }; // Fortnightly
        default:
          console.log(`Numeric frequency value: ${periodValue}, defaulting to monthly`);
          return { unit: "month", value: 1 };
      }
    }
    
    // Otherwise, treat it as a string
    const frequency = String(periodValue || "MONTHLY");
    
    switch (frequency.toUpperCase()) {
      case 'DAILY':
        return { unit: "day", value: 1 };
      case 'WEEKLY':
        return { unit: "week", value: 1 };
      case 'FORTNIGHTLY':
      case 'TWO_WEEKLY':
        return { unit: "week", value: 2 };
      case 'FOUR_WEEKLY':
        return { unit: "week", value: 4 };
      case 'MONTHLY':
        return { unit: "month", value: 1 };
      case 'TWO_MONTHLY':
        return { unit: "month", value: 2 };
      case 'QUARTERLY':
      case 'THREE_MONTHLY':
        return { unit: "month", value: 3 };
      case 'SIX_MONTHLY':
        return { unit: "month", value: 6 };
      case 'YEARLY':
      case 'ANNUALLY':
        return { unit: "year", value: 1 };
      default:
        console.log(`Unknown frequency: ${frequency}, defaulting to monthly`);
        return { unit: "month", value: 1 };
    }
  }
  
  /**
   * Calculate the next occurrence date based on frequency
   * @param currentDate Current date
   * @param frequency Frequency object
   * @returns Next occurrence date
   */
  private getNextOccurrence(currentDate: Date, frequency: { unit: string, value: number }): Date {
    const nextDate = new Date(currentDate);
    
    switch (frequency.unit) {
      case 'day':
        nextDate.setDate(nextDate.getDate() + frequency.value);
        break;
      case 'week':
        nextDate.setDate(nextDate.getDate() + 7 * frequency.value);
        break;
      case 'month':
        nextDate.setMonth(nextDate.getMonth() + frequency.value);
        break;
      case 'year':
        nextDate.setFullYear(nextDate.getFullYear() + frequency.value);
        break;
    }
    
    return nextDate;
  }
}
