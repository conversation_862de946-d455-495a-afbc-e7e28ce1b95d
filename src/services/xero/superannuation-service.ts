import { XeroClient } from "../../api/integrations/xero";
import { XeroSuperannuationExpenseDisplay } from "../../api/types/xero";

/**
 * Service for handling Xero superannuation operations
 */
export class SuperannuationService {
  constructor(private readonly client: XeroClient) {}
  
  /**
   * Get superannuation expenses from Xero for display
   * @param tenantId Xero tenant ID
   * @param fromDate Optional date to filter from
   * @returns Processed superannuation expenses for display
   */
  async getSuperannuationExpensesForDisplay(tenantId: string, fromDate?: Date): Promise<XeroSuperannuationExpenseDisplay[]> {
    try {
      // Check for necessary scopes
      const tokenSet = this.client.readTokenSet();
      const scopes = tokenSet?.scope?.split(' ') || [];
      
      const hasPayrollScope = scopes.some(scope => 
        scope.includes('payroll.') || 
        scope.includes('payroll/')
      );
      
      if (!hasPayrollScope) {
        console.log('Missing payroll scopes in token, using empty set');
        return [];
      }
      
      // Fetch superannuation data from Xero
      console.log('Fetching superannuation data from Xero API...');
      const response = await this.client.getSuperannuationPayments(tenantId, fromDate);
      
      // Check if we got valid data back
      if (!response || !response.body) {
        console.log('No superannuation data returned from Xero');
        return [];
      }
      
      // API format can vary - check for both possible response formats
      const payments = response.body.superannuationPayments || [];
      
      if (payments.length > 0) {
        console.log(`Found ${payments.length} superannuation payment records`);
        return this.mapSuperannuationToDisplayFormat(payments);
      } else {
        console.log('No superannuation payment records found');
        return [];
      }
    } catch (error) {
      console.error('Error getting superannuation data from Xero:', error);
      // Return empty array for resilience
      return [];
    }
  }
  
  /**
   * Map Xero superannuation data to display format
   * @param payments Raw superannuation data from Xero API
   * @returns Transformed superannuation expenses for display
   */
  private mapSuperannuationToDisplayFormat(payments: any[]): XeroSuperannuationExpenseDisplay[] {
    return payments.map(payment => {
      // Extract date with fallback
      const paymentDate = payment.paymentDate ? new Date(payment.paymentDate) : new Date();
      
      // Format provider name
      const provider = payment.superFund?.name || "Unknown Provider";
      
      // Format description
      const description = `Superannuation: ${provider}`;
      
      return {
        id: payment.superannuationPaymentID || `xero-super-${Date.now()}-${Math.random()}`,
        provider,
        paymentDate,
        amount: payment.amount || 0,
        status: this.mapPaymentStatus(payment.status),
        description,
        employeeCount: payment.employeeCount || 1,
        frequency: "monthly", // Default frequency, may need to be determined dynamically
        isAlreadyAdded: false
      };
    });
  }
  
  /**
   * Map Xero payment status to user-friendly status
   * @param status Status from Xero API
   * @returns User-friendly status
   */
  private mapPaymentStatus(status?: string): string {
    switch (status?.toUpperCase()) {
      case 'DRAFT': return 'Draft';
      case 'SCHEDULED': return 'Scheduled';
      case 'PAID': return 'Paid';
      default: return 'Unknown';
    }
  }
}