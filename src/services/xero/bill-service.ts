import { XeroClient } from "../../api/integrations/xero";
import { XeroBill } from "../../api/types/xero";

/**
 * Interface for bill display data
 */
export interface XeroBillDisplay {
  id: string;
  invoiceNumber: string;
  reference: string;
  date: Date;
  dueDate: Date;
  amount: number;
  vendor: string;
  status: string;
  lineItems: any[];
  description?: string; // Add description as a fallback for reference
}

/**
 * Service for handling Xero bills operations
 * Maps to the Accounting API's invoices endpoint for ACCPAY types
 */
export class BillService {
  constructor(private readonly client: XeroClient) {}
  
  /**
   * Get bills from Xero for display
   * @param tenantId Xero tenant ID
   * @param since Optional date to filter bills from
   * @returns Processed bills for display
   */
  async getBillsForDisplay(tenantId: string, since?: Date): Promise<XeroBillDisplay[]> {
    try {
      // Check if we have the necessary scopes
      const tokenSet = this.client.readTokenSet();
      const scopes = tokenSet?.scope?.split(' ') || [];
      
      const hasAccountingScope = scopes.some(scope => 
        scope.includes('accounting.') || 
        scope.includes('accounting/')
      );
      
      if (!hasAccountingScope) {
        console.log('Missing accounting scopes in token, using empty set');
        return [];
      }
      
      // Fetch bills from Xero using the XeroClient
      console.log('Fetching bills from Xero API...');
      const response = await this.client.getBills(tenantId, since);
      
      // Check if we got valid data back
      if (!response || !response.body || !response.body.invoices) {
        console.log('No bills returned from Xero');
        return [];
      }
      
      console.log(`Found ${response.body.invoices.length} bills`);
      
      // Filter out irrelevant bills before transformation
      const relevantBills = this.filterRelevantBills(response.body.invoices);
      console.log(`${relevantBills.length} bills are relevant (not paid, deleted, or voided)`);
      
      // Transform bills for display
      return this.mapBillsToDisplayFormat(relevantBills);
    } catch (error) {
      console.error('Error getting bills from Xero:', error);
      // Return empty array for resilience
      return [];
    }
  }
  
  /**
   * Map Xero bills to display format
   * @param bills Raw bills from Xero API
   * @returns Transformed bills for display
   */
  private mapBillsToDisplayFormat(bills: any[]): XeroBillDisplay[] {
    return bills.map(bill => {
      // Extract date (fallback to current date if invalid)
      const date = bill.date ? new Date(bill.date) : new Date();
      const dueDate = bill.dueDate ? new Date(bill.dueDate) : new Date();
      
      // Ensure amount is positive for display
      const amount = bill.total ? Math.abs(Number(bill.total)) : 0;
      
      // Extract contact name if available with fallback
      const vendor = bill.contact?.name || 'Unknown Vendor';
      
      // Use multiple fallbacks for reference
      const reference = bill.reference || bill.invoiceNumber || bill.description || `Bill from ${vendor}`;
      
      return {
        id: bill.invoiceID || `xero-bill-${Date.now()}-${Math.random()}`,
        invoiceNumber: bill.invoiceNumber || '',
        reference,
        date,
        dueDate,
        amount,
        vendor,
        status: this.mapBillStatus(bill.status),
        lineItems: bill.lineItems || [],
        description: bill.description
      };
    });
  }
  
  /**
   * Map Xero bill status to user-friendly status
   * @param xeroStatus Status from Xero API
   * @returns User-friendly status
   */
  private mapBillStatus(xeroStatus?: string): string {
    switch (xeroStatus?.toUpperCase()) {
      case 'DRAFT': return 'Draft';
      case 'SUBMITTED': return 'Submitted';
      case 'AUTHORISED': return 'Approved';
      case 'PAID': return 'Paid';
      case 'VOIDED': return 'Void';
      case 'DELETED': return 'Deleted';
      case 'AWAITING_APPROVAL': return 'Awaiting Approval';
      case 'AWAITING_PAYMENT': return 'Awaiting Payment';
      default: return 'Unknown';
    }
  }
  
  /**
   * Filter out irrelevant bills (paid, deleted, etc.)
   * @param bills Raw bills from Xero
   * @returns Filtered bills
   */
  private filterRelevantBills(bills: any[]): any[] {
    // Only show bills that are relevant for expenses
    return bills.filter(bill => {
      const status = bill.status?.toUpperCase();
      // Exclude bills that are already paid or deleted
      return status !== 'PAID' && status !== 'DELETED' && status !== 'VOIDED';
    });
  }
}