import { XeroClient, createXeroClient } from "../../api/integrations/xero";
import { XeroConfig } from "../../api/types/xero";
import { BankAccountService } from "./bank-account-service";
import { RepeatingBillService } from "./repeating-bill-service";
import { BillService } from "./bill-service";

/**
 * Interface for bank account data
 */
interface BankAccount {
  id: string;
  name: string;
  balance: number;
  type: string;
  code?: string;
}

/**
 * Interface for bank balances data
 */
interface BankBalances {
  totalOpeningBalance: number;
  totalClosingBalance: number;
  accounts: BankAccount[];
}

/**
 * Interface for expense data
 */
interface ExpenseData {
  id: string;
  name: string;
  amount: number;
  frequency: string;
  category?: string;
  date?: string;
}

/**
 * Interface for repeating bill data
 */
interface RepeatingBillData {
  id: string;
  reference: string;
  contact: string;
  lineAmount: number;
  totalAmount: number;
  period: string;
  unit: number;
  dueDate: string;
  nextScheduledDate?: string;
}

/**
 * Interface for Xero tenant data
 */
interface XeroTenant {
  tenantId: string;
  tenantName: string;
  tenantType: string;
  createdDateUtc: string;
  updatedDateUtc: string;
}

/**
 * Interface for Xero token set
 */
interface XeroTokenSet {
  access_token: string;
  token_type: string;
  expires_at?: number;
  refresh_token?: string;
  scope?: string;
  id_token?: string;
}

/**
 * Interface for cashflow data
 */
interface CashflowData {
  bankBalances: BankBalances;
  fixedExpenses: ExpenseData[];
  repeatingBills: RepeatingBillData[];
}

/**
 * Main Xero service that orchestrates all endpoint-specific services
 */
export class XeroService {
  private static instance: XeroService;
  private client: XeroClient;
  
  // Endpoint-specific services
  public bankAccounts: BankAccountService;
  public repeatingBills: RepeatingBillService;
  public bills: BillService;
  
  // Active tenant ID and tenants list
  private activeTenantId: string | null = null;
  private tenants: XeroTenant[] = [];

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor(client?: XeroClient) {
    if (client) {
      this.client = client;
    } else {
      // Create a client with config from environment variables
      const config: XeroConfig = {
        clientId: process.env.XERO_CLIENT_ID || '',
        clientSecret: process.env.XERO_CLIENT_SECRET || '',
        redirectUri: process.env.XERO_REDIRECT_URI || '',
        scopes: (process.env.XERO_SCOPES || '').split(' ')
      };

      // Check if environment variables are properly loaded
      if (!config.clientId) {
        console.error('XERO_CLIENT_ID environment variable is missing or empty');
      }
      if (!config.clientSecret) {
        console.error('XERO_CLIENT_SECRET environment variable is missing or empty');
      }
      if (!config.redirectUri) {
        console.error('XERO_REDIRECT_URI environment variable is missing or empty');
      }
      if (config.scopes.length === 1 && config.scopes[0] === '') {
        console.error('XERO_SCOPES environment variable is missing or empty');
      }
      
      // Always use the full URL without port number for the callback
      if (process.env.NODE_ENV === 'production') {
        // Make sure we're using the correct redirect URI from environment variables
        // Do not hardcode the production URL to allow for preview deployments
        if (!process.env.XERO_REDIRECT_URI) {
          console.error('XERO_REDIRECT_URI environment variable is required in production');
        }
        console.log('Production environment detected, using environment variable for callback URL');
      }
      
      // Log the callback URL that's being used for easier verification
      console.log('Xero callback URL configured as:', config.redirectUri);
      
      // Log the configuration for debugging
      console.log('Initializing XeroClient with config:', {
        clientId: config.clientId ? `${config.clientId.substring(0, 5)}...` : 'missing',
        redirectUri: config.redirectUri,
        scopes: config.scopes
      });
      
      this.client = createXeroClient(config);
    }
    
    // Initialize endpoint-specific services
    this.bankAccounts = new BankAccountService(this.client);
    this.repeatingBills = new RepeatingBillService(this.client);
    this.bills = new BillService(this.client);
  }
  
  /**
   * Get singleton instance
   * @returns XeroService instance
   */
  public static getInstance(): XeroService {
    if (!XeroService.instance) {
      XeroService.instance = new XeroService();
    }
    return XeroService.instance;
  }
  
  /**
   * Set Xero client
   * @param client Xero client
   */
  public setClient(client: XeroClient): void {
    this.client = client;
    
    // Reinitialize services with new client
    this.bankAccounts = new BankAccountService(this.client);
    this.repeatingBills = new RepeatingBillService(this.client);
    this.bills = new BillService(this.client);
  }
  
  /**
   * Get Xero client
   * @returns Xero client
   */
  public getClient(): XeroClient {
    return this.client;
  }

  /**
   * Set active tenant ID
   * @param tenantId Xero tenant ID
   */
  public setActiveTenantId(tenantId: string): void {
    this.activeTenantId = tenantId;
  }

  /**
   * Get active tenant ID
   * @returns Active tenant ID
   */
  public getActiveTenantId(): string | null {
    return this.activeTenantId;
  }
  
  /**
   * Build consent URL for OAuth flow
   * @returns Consent URL
   */
  public async buildConsentUrl(): Promise<string> {
    try {
      // Ensure we have the latest configuration before building the consent URL
      console.log('Building consent URL with redirectUri:', this.client.config.redirectUris[0]);
      
      // No need to override the redirect URI as we're now using the environment variable properly
      // The client config already has the correct redirect URI from initialization
      
      // Pass a state parameter for security
      const url = await this.client.buildConsentUrl();
      console.log('Generated consent URL:', url);
      return url;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error building consent URL:', errorMessage);
      console.error('Client config:', this.client.config);
      throw new Error('Failed to build consent URL');
    }
  }
  
  /**
   * Handle OAuth callback
   * @param callbackUrl Callback URL with authorization code
   * @returns Token set
   */
  public async handleCallback(callbackUrl: string): Promise<XeroTokenSet> {
    try {
      console.log('Handling Xero callback with URL:', callbackUrl);
      
      // The SDK requires a complete URL including protocol, host, and query parameters
      // Verify all parts are correctly formatted
      if (!callbackUrl.includes('code=')) {
        throw new Error('Invalid callback URL: No authorization code found');
      }
      
      // Save all relevant parts for debugging
      const callbackParts = new URL(callbackUrl);
      console.log('Callback URL parts:', {
        protocol: callbackParts.protocol,
        host: callbackParts.host,
        pathname: callbackParts.pathname,
        search: callbackParts.search.substring(0, 20) + '...' // Only log part of the query for security
      });
      
      // Try the SDK apiCallback method - it's the proper way to handle the OAuth flow
      console.log('Attempting token exchange with apiCallback...');
      
      // Log client configuration before making the request
      console.log('Client configuration:', {
        clientIdPrefix: this.client.config.clientId?.substring(0, 5) || 'missing',
        redirectUri: this.client.config.redirectUris?.[0] || 'missing',
        scopesCount: this.client.config.scopes?.length || 0
      });
      
      // Process the callback and get the token
      const tokenSet = await this.client.apiCallback(callbackUrl);
      console.log('Successfully received token set from Xero');
      
      // Update tenants after getting tokens
      await this.client.updateTenants();
      this.tenants = this.client.tenants || [];
      
      console.log(`Retrieved ${this.tenants.length} tenants from Xero`);
      
      if (this.tenants.length > 0) {
        this.activeTenantId = this.tenants[0].tenantId;
        console.log(`Set active tenant ID to: ${this.activeTenantId}`);
      }
      
      return tokenSet;
    } catch (error: unknown) {
      // More detailed error logging
      const errorObj = error as { response?: { status: number; data: unknown }; message?: string };
      console.error('Error handling Xero callback:', errorObj);
      
      if (errorObj.response) {
        console.error('Response status:', errorObj.response.status);
        console.error('Response data:', errorObj.response.data);
      }
      
      // Add diagnostic information
      console.error('Client configuration:', {
        redirectUris: this.client.config.redirectUris,
        clientIdLength: this.client.config.clientId?.length || 0,
        clientSecretLength: this.client.config.clientSecret?.length || 0
      });
      
      // Preserve the original error for better debugging
      throw error;
    }
  }
  
  /**
   * Check if authenticated with Xero
   * @returns True if authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    // Mock mode - always authenticated
    if (process.env.USE_MOCK_AUTH === 'true' && process.env.NODE_ENV !== 'production') {
      console.log('[MOCK AUTH] XeroService.isAuthenticated returning true');
      // Set mock tenant data
      this.tenants = [{
        tenantId: 'mock-tenant-123',
        tenantName: 'Mock Test Company',
        tenantType: 'ORGANISATION',
        createdDateUtc: new Date().toISOString(),
        updatedDateUtc: new Date().toISOString(),
        tenantConnections: []
      }];
      this.activeTenantId = 'mock-tenant-123';
      return true;
    }

    try {
      // Check if we have a token set
      const tokenSet = this.client.readTokenSet();
      
      // If no token set, not authenticated
      if (!tokenSet) {
        return false;
      }
      
      // Check if token is expired and refresh if needed
      if (tokenSet.expires_at) {
        const expiresAt = tokenSet.expires_at * 1000; // Convert to milliseconds
        const now = Date.now();
        
        // If token is expired or will expire soon (within 5 minutes), try to refresh it
        if (now >= expiresAt - 5 * 60 * 1000) {
          try {
            // Check if a refresh token exists - we can't refresh without it
            if (!tokenSet.refresh_token) {
              console.warn('No refresh token available, skipping refresh');
              return false; // Without a refresh token, we need to re-authenticate
            }
            
            // Attempt to refresh the token
            console.log('Token expired or expiring soon. Attempting to refresh...');
            await this.client.refreshToken();
            console.log('Successfully refreshed expired token');
            
            // Get updated tenants after token refresh
            await this.client.updateTenants();
            this.tenants = this.client.tenants || [];
            
            if (this.tenants.length > 0) {
              this.activeTenantId = this.tenants[0].tenantId;
            }
          } catch (refreshError) {
            console.error('Failed to refresh token:', refreshError);
            return false;
          }
        }
      }
      
      // If we have tenants, we're authenticated
      return this.tenants.length > 0;
    } catch (error: unknown) {
      console.error('Error checking authentication:', error instanceof Error ? error.message : error);
      return false;
    }
  }
  
  /**
   * Get Xero tenants
   * @returns Tenants
   */
  public getTenants(): XeroTenant[] {
    return this.tenants;
  }
  
  /**
   * Get all data needed for cashflow analysis
   * @param tenantId Xero tenant ID
   * @param startDate Start date for cashflow
   * @param endDate End date for cashflow
   * @returns Cashflow data
   */
  public async getCashflowData(tenantId: string, startDate: Date, endDate: Date): Promise<CashflowData> {
    console.log(`Getting cashflow data from ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Check if token is expired and refresh if needed
    const tokenSet = this.client.readTokenSet();
    if (tokenSet && tokenSet.expires_at) {
      const expiresAt = tokenSet.expires_at * 1000; // Convert to milliseconds
      const now = Date.now();
      
      // If token is expired or will expire soon (within 5 minutes), try to refresh it
      if (now >= expiresAt - 5 * 60 * 1000) {
        try {
          console.log('Token expired or expiring soon before API call. Attempting to refresh...');
          
          // Check if a refresh token exists - we can't refresh without it
          if (!tokenSet.refresh_token) {
            console.warn('No refresh token available, skipping refresh');
            // Continue with existing token even if it's expired - the API call will fail
            // and the user will need to re-authenticate, but we're not forcing a logout here
          } else {
            // Refresh the token
            await this.client.refreshToken();
            console.log('Successfully refreshed token before API call');
            
            // Get updated tenants after token refresh
            await this.client.updateTenants();
            this.tenants = this.client.tenants || [];
          }
        } catch (refreshError) {
          console.error('Failed to refresh token before API call:', refreshError);
          console.log('Continuing with existing token if possible...');
          // We'll try with the existing token and let the API call fail naturally
          // rather than forcing a logout by throwing an error here
        }
      }
    }
    
    // Initialize with default values to ensure we always return something
    const result: CashflowData = {
      bankBalances: { totalOpeningBalance: 0, totalClosingBalance: 0, accounts: [] },
      fixedExpenses: [],
      repeatingBills: []
    };
    
    // Maximum retry attempts
    const maxRetries = 4;
    
    // Sequential API calls with retry logic to avoid rate limiting
    try {
      // 1. Get bank balances
      try {
        console.log('Step 1: Requesting bank balances...');
        result.bankBalances = await this.retryWithBackoff(
          () => this.bankAccounts.getBankBalancesFromBalanceSheet(tenantId),
          maxRetries,
          'Bank balances'
        );
        console.log('Successfully retrieved bank balances');
      } catch (error: unknown) {
        console.error('Failed to get bank balances after retries:', error instanceof Error ? error.message : error);
        // Keep default value
      }
      
      // Add a significant delay between API calls to avoid rate limiting
      console.log('Waiting 5 seconds before next API call to avoid rate limits...');
      await this.delay(5000); // 5 second delay
      
      // We no longer need to fetch payroll - using fixed monthly expense
      
      // 3. Get repeating bills
      try {
        console.log('Step 3: Requesting repeating bills data...');
        result.repeatingBills = await this.retryWithBackoff(
          () => this.repeatingBills.getRepeatingBills(tenantId, startDate, endDate),
          maxRetries,
          'Repeating Bills'
        );
        console.log(`Successfully retrieved ${result.repeatingBills.length} repeating bill entries`);
      } catch (error: unknown) {
        console.error('Failed to get repeating bills after retries:', error instanceof Error ? error.message : error);
        // Keep default value
      }
      
      console.log('All API calls completed, returning data');
      return result;
    } catch (error: unknown) {
      console.error('Error getting cashflow data from Xero:', error instanceof Error ? error.message : error);
      // Return whatever data we managed to collect
      return result;
    }
  }
  
  /**
   * Retry a function with exponential backoff
   * @param fn Function to retry
   * @param maxRetries Maximum number of retries
   * @param operationName Name of the operation for logging
   * @returns Result of the function
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number,
    operationName: string
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        // Add a significant delay before each attempt after the first one
        if (attempt > 0) {
          // More aggressive backoff with longer base delay
          const baseDelay = 10000; // 10 seconds base delay
          const backoffTime = baseDelay + Math.pow(2, attempt + 2) * 1000 + Math.random() * 3000;
          console.log(`Adding delay before retry for ${operationName}. Waiting ${Math.round(backoffTime/1000)} seconds (attempt ${attempt + 1}/${maxRetries})`);
          await this.delay(backoffTime);
        }
        
        // Before making the request, output that we're trying
        console.log(`Attempting ${operationName} (attempt ${attempt + 1}/${maxRetries})...`);
        const result = await fn();
        console.log(`${operationName} request succeeded`);
        return result;
      } catch (error: unknown) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Check if it's a rate limit error (HTTP 429)
        const errorObj = error as { response?: { statusCode?: number; headers?: Record<string, string> }; message?: string };
        const isRateLimit = errorObj.response?.statusCode === 429;
        
        if (isRateLimit) {
          // Get the Retry-After header if available and other rate limit headers for debugging
          const retryAfter = errorObj.response?.headers?.['retry-after'];
          const dayLimitRemaining = errorObj.response?.headers?.['x-daylimit-remaining'];
          const minLimitRemaining = errorObj.response?.headers?.['x-minlimit-remaining'];
          
          console.log(`Rate limit info - Day limit remaining: ${dayLimitRemaining}, Minute limit remaining: ${minLimitRemaining}`);
          
          // Calculate backoff time based on Retry-After header or use exponential backoff
          let backoffTime = 0;
          
          if (retryAfter && !isNaN(parseInt(retryAfter))) {
            // Retry-After header value is in seconds, convert to milliseconds
            // Add 5 seconds extra to be safe
            backoffTime = (parseInt(retryAfter) + 5) * 1000;
            console.log(`Rate limit hit for ${operationName}. Server requested wait of ${retryAfter} seconds.`);
          } else {
            // Use exponential backoff with a larger base value and random jitter
            backoffTime = Math.pow(2, attempt + 3) * 1000 + Math.random() * 5000; // More aggressive backoff
          }
          
          // Log the retry attempt
          console.log(`Rate limit hit for ${operationName}. Retrying in ${Math.round(backoffTime/1000)} seconds (attempt ${attempt + 1}/${maxRetries})`);
          
          // Wait for backoff time
          await this.delay(backoffTime);
        } else if (attempt < maxRetries - 1) {
          // For non-rate limit errors, retry with a delay that increases with each attempt
          const delay = 5000 * (attempt + 1); // More significant delays for non-rate limit errors
          console.log(`Error in ${operationName}. Retrying in ${delay/1000} seconds (attempt ${attempt + 1}/${maxRetries}): ${errorObj.message || 'Unknown error'}`);
          await this.delay(delay);
        } else {
          // Last attempt failed
          console.error(`Failed to ${operationName} after ${maxRetries} attempts:`, error);
          throw error;
        }
      }
    }
    
    throw lastError;
  }
  
  /**
   * Delay execution for a specified time
   * @param ms Milliseconds to delay
   * @returns Promise that resolves after the delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
