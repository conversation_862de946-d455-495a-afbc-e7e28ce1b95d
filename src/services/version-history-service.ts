/**
 * Version History Service
 *
 * Manages loading and accessing version history data from JSON file
 */

export interface PatchRelease {
  version: string;
  date: string; // ISO format date string
  title: string;
  description: string;
  changes: string[];
}

export interface MinorRelease {
  version: string;
  date: string; // ISO format date string
  title: string;
  description: string;
  changes: string[];
  patches: PatchRelease[];
}

export interface MajorRelease {
  version: string;
  date: string; // ISO format date string
  title: string;
  description: string;
  changes: string[];
  minorReleases: MinorRelease[];
}

export type VersionUpdate = MajorRelease | MinorRelease | PatchRelease;

/**
 * Version History Service Class
 */
class VersionHistoryService {
  private versionData: MajorRelease[] | null = null;
  private isLoading = false;

  /**
   * Get minimal version history for fallback
   */
  private getMinimalVersionHistory(): MajorRelease[] {
    return [
      {
        version: "0.26.2",
        date: "2025-01-28",
        title: "Client Radar UI Enhancement & Modern Design System",
        description:
          "Comprehensive redesign of the Client Radar interface with modern UI components, enhanced drag-and-drop functionality, and improved user experience",
        changes: [
          "Complete visual redesign of Client Radar with modern card-based layout",
          "Enhanced company selection modal with improved search and visual feedback",
          "Redesigned company cards with better information hierarchy and visual indicators",
          "Improved quadrant layout with visual dividers and enhanced drop zones",
          "Added comprehensive statistics dashboard with real-time metrics",
          "Implemented modern CSS architecture with dedicated radar.css component styles",
        ],
        minorReleases: [],
      },
    ];
  }

  /**
   * Load version history data
   */
  private async loadVersionHistory(): Promise<MajorRelease[]> {
    if (this.versionData) {
      return this.versionData;
    }

    if (this.isLoading) {
      // Wait for existing load to complete
      while (this.isLoading) {
        await new Promise((resolve) => setTimeout(resolve, 10));
      }
      return this.versionData || [];
    }

    this.isLoading = true;

    try {
      // For now, just use minimal built-in data
      // TODO: In the future, could load from external JSON files
      this.versionData = this.getMinimalVersionHistory();
    } catch (error) {
      // Fallback to minimal data
      console.error("Failed to load version history:", error);
      this.versionData = this.getMinimalVersionHistory();
    } finally {
      this.isLoading = false;
    }

    return this.versionData;
  }

  /**
   * Get all version history
   */
  async getVersionHistory(): Promise<MajorRelease[]> {
    return this.loadVersionHistory();
  }

  /**
   * Get latest version
   */
  async getLatestVersion(): Promise<MajorRelease | null> {
    const history = await this.loadVersionHistory();
    return history.length > 0 ? history[0] : null;
  }

  /**
   * Get version by version number
   */
  async getVersion(
    versionNumber: string,
  ): Promise<MajorRelease | MinorRelease | PatchRelease | null> {
    const history = await this.loadVersionHistory();

    // Search in major releases
    for (const major of history) {
      if (major.version === versionNumber) {
        return major;
      }

      // Search in minor releases
      for (const minor of major.minorReleases) {
        if (minor.version === versionNumber) {
          return minor;
        }

        // Search in patches
        for (const patch of minor.patches) {
          if (patch.version === versionNumber) {
            return patch;
          }
        }
      }
    }

    return null;
  }

  /**
   * Get all versions flattened
   */
  async getAllVersions(): Promise<VersionUpdate[]> {
    const history = await this.loadVersionHistory();
    const allVersions: VersionUpdate[] = [];

    for (const major of history) {
      allVersions.push(major);

      for (const minor of major.minorReleases) {
        allVersions.push(minor);

        for (const patch of minor.patches) {
          allVersions.push(patch);
        }
      }
    }

    return allVersions;
  }

  /**
   * Search version history by text
   */
  async searchVersions(searchTerm: string): Promise<VersionUpdate[]> {
    const allVersions = await this.getAllVersions();
    const searchLower = searchTerm.toLowerCase();

    return allVersions.filter(
      (version) =>
        version.title.toLowerCase().includes(searchLower) ||
        version.description.toLowerCase().includes(searchLower) ||
        version.changes.some((change) =>
          change.toLowerCase().includes(searchLower),
        ),
    );
  }

  /**
   * Get versions by date range
   */
  async getVersionsByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<VersionUpdate[]> {
    const allVersions = await this.getAllVersions();
    const start = new Date(startDate);
    const end = new Date(endDate);

    return allVersions.filter((version) => {
      const versionDate = new Date(version.date);
      return versionDate >= start && versionDate <= end;
    });
  }

  /**
   * Clear cached data (useful for testing or force refresh)
   */
  clearCache(): void {
    this.versionData = null;
    this.isLoading = false;
  }
}

// Export singleton instance
export const versionHistoryService = new VersionHistoryService();

// Export types and service
export { VersionHistoryService };
