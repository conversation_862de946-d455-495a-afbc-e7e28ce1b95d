/**
 * Feature Flags Service
 * Manages feature flags using the settings repository
 */

import { SettingsRepository } from "../api/repositories/settings-repository";
import { FeatureFlag, FeatureFlagConfig } from "../types/feature-flags";

/**
 * Feature flag configurations
 */
const FEATURE_FLAGS: Record<FeatureFlag, FeatureFlagConfig> = {
  [FeatureFlag.KNOWLEDGE_GRAPH]: {
    name: 'Knowledge Graph',
    description: 'Interactive network visualization of business relationships',
    defaultValue: false,
    category: 'stable'
  },
  [FeatureFlag.OPPORTUNITY_INTELLIGENCE]: {
    name: 'Opportunity Intelligence',
    description: 'AI-powered business opportunity detection and analysis',
    defaultValue: true,
    category: 'beta'
  },
  [FeatureFlag.DATA_ENRICHMENT]: {
    name: 'Data Enrichment',
    description: 'External data augmentation for companies and contacts',
    defaultValue: true,
    category: 'stable'
  },
  [FeatureFlag.ACTIVITY_FEED]: {
    name: 'Activity Feed',
    description: 'Real-time activity tracking and timeline',
    defaultValue: true,
    category: 'stable'
  },
  [FeatureFlag.TENDER_MANAGEMENT]: {
    name: 'Tender Management',
    description: 'Government tender tracking and qualification',
    defaultValue: true,
    category: 'beta'
  },
  [FeatureFlag.XERO_CHAT]: {
    name: 'Xero Chat',
    description: 'Natural language interface for Xero data',
    defaultValue: true,
    category: 'experimental'
  },
  [FeatureFlag.HUBSPOT_MCP]: {
    name: 'HubSpot MCP',
    description: 'Model Context Protocol for HubSpot integration',
    defaultValue: false,
    category: 'experimental'
  }
};

/**
 * Feature Flags Service
 */
export class FeatureFlagsService {
  private settingsRepository: SettingsRepository;
  private cache: Map<string, boolean> = new Map();
  private cacheExpiry: number = 60000; // 1 minute cache
  private lastCacheUpdate: number = 0;

  constructor() {
    this.settingsRepository = new SettingsRepository();
  }

  /**
   * Check if a feature flag is enabled
   * @param flag Feature flag to check
   * @returns Boolean indicating if feature is enabled
   */
  isEnabled(flag: FeatureFlag): boolean {
    try {
      // Check cache first
      if (this.isCacheValid() && this.cache.has(flag)) {
        return this.cache.get(flag)!;
      }

      // Get from settings
      const value = this.settingsRepository.getSettingValue(flag);
      
      // If no value exists, use default
      if (value === null) {
        const config = FEATURE_FLAGS[flag];
        const enabled = config ? config.defaultValue : false;
        
        // Cache the default value
        this.cache.set(flag, enabled);
        
        return enabled;
      }

      // Parse boolean value
      const enabled = value === 'true' || value === '1';
      
      // Update cache
      this.cache.set(flag, enabled);
      this.lastCacheUpdate = Date.now();
      
      return enabled;
    } catch (error) {
      console.error(`Error checking feature flag ${flag}:`, error);
      
      // Return default value on error
      const config = FEATURE_FLAGS[flag];
      return config ? config.defaultValue : false;
    }
  }

  /**
   * Enable a feature flag
   * @param flag Feature flag to enable
   * @param updatedBy User making the change
   */
  enable(flag: FeatureFlag, updatedBy?: string): void {
    try {
      this.settingsRepository.setSetting(flag, 'true', updatedBy);
      this.cache.set(flag, true);
      this.lastCacheUpdate = Date.now();
    } catch (error) {
      console.error(`Error enabling feature flag ${flag}:`, error);
      throw error;
    }
  }

  /**
   * Disable a feature flag
   * @param flag Feature flag to disable
   * @param updatedBy User making the change
   */
  disable(flag: FeatureFlag, updatedBy?: string): void {
    try {
      this.settingsRepository.setSetting(flag, 'false', updatedBy);
      this.cache.set(flag, false);
      this.lastCacheUpdate = Date.now();
    } catch (error) {
      console.error(`Error disabling feature flag ${flag}:`, error);
      throw error;
    }
  }

  /**
   * Toggle a feature flag
   * @param flag Feature flag to toggle
   * @param updatedBy User making the change
   */
  toggle(flag: FeatureFlag, updatedBy?: string): boolean {
    try {
      const currentValue = this.isEnabled(flag);
      if (currentValue) {
        this.disable(flag, updatedBy);
      } else {
        this.enable(flag, updatedBy);
      }
      return !currentValue;
    } catch (error) {
      console.error(`Error toggling feature flag ${flag}:`, error);
      throw error;
    }
  }

  /**
   * Get all feature flags and their current status
   * @returns Object with all feature flags and their status
   */
  getAllFlags(): Record<FeatureFlag, { enabled: boolean; config: FeatureFlagConfig }> {
    try {
      const result: Record<string, { enabled: boolean; config: FeatureFlagConfig }> = {};
      
      for (const [flag, config] of Object.entries(FEATURE_FLAGS)) {
        result[flag] = {
          enabled: this.isEnabled(flag as FeatureFlag),
          config
        };
      }
      
      return result as Record<FeatureFlag, { enabled: boolean; config: FeatureFlagConfig }>;
    } catch (error) {
      console.error('Error getting all feature flags:', error);
      throw error;
    }
  }

  /**
   * Get feature flags by category
   * @param category Category to filter by
   * @returns Array of feature flags in the category
   */
  getFlagsByCategory(category: 'experimental' | 'beta' | 'stable'): Array<{
    flag: FeatureFlag;
    enabled: boolean;
    config: FeatureFlagConfig;
  }> {
    try {
      const result = [];
      
      for (const [flag, config] of Object.entries(FEATURE_FLAGS)) {
        if (config.category === category) {
          result.push({
            flag: flag as FeatureFlag,
            enabled: this.isEnabled(flag as FeatureFlag),
            config
          });
        }
      }
      
      return result;
    } catch (error) {
      console.error(`Error getting feature flags by category ${category}:`, error);
      return [];
    }
  }

  /**
   * Reset a feature flag to its default value
   * @param flag Feature flag to reset
   * @param updatedBy User making the change
   */
  reset(flag: FeatureFlag, updatedBy?: string): void {
    try {
      const config = FEATURE_FLAGS[flag];
      if (config) {
        this.settingsRepository.setSetting(
          flag,
          config.defaultValue ? 'true' : 'false',
          updatedBy
        );
        this.cache.set(flag, config.defaultValue);
        this.lastCacheUpdate = Date.now();
      }
    } catch (error) {
      console.error(`Error resetting feature flag ${flag}:`, error);
      throw error;
    }
  }

  /**
   * Reset all feature flags to their default values
   * @param updatedBy User making the change
   */
  resetAll(updatedBy?: string): void {
    try {
      for (const flag of Object.keys(FEATURE_FLAGS)) {
        this.reset(flag as FeatureFlag, updatedBy);
      }
    } catch (error) {
      console.error('Error resetting all feature flags:', error);
      throw error;
    }
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear();
    this.lastCacheUpdate = 0;
  }

  /**
   * Check if cache is valid
   * @returns Boolean indicating if cache is valid
   */
  private isCacheValid(): boolean {
    return Date.now() - this.lastCacheUpdate < this.cacheExpiry;
  }

  /**
   * Get feature flag configuration
   * @param flag Feature flag
   * @returns Feature flag configuration or null
   */
  getConfig(flag: FeatureFlag): FeatureFlagConfig | null {
    return FEATURE_FLAGS[flag] || null;
  }

  /**
   * Enable all experimental features
   * @param updatedBy User making the change
   */
  enableExperimental(updatedBy?: string): void {
    try {
      const experimentalFlags = this.getFlagsByCategory('experimental');
      for (const { flag } of experimentalFlags) {
        this.enable(flag, updatedBy);
      }
    } catch (error) {
      console.error('Error enabling experimental features:', error);
      throw error;
    }
  }

  /**
   * Disable all experimental features
   * @param updatedBy User making the change
   */
  disableExperimental(updatedBy?: string): void {
    try {
      const experimentalFlags = this.getFlagsByCategory('experimental');
      for (const { flag } of experimentalFlags) {
        this.disable(flag, updatedBy);
      }
    } catch (error) {
      console.error('Error disabling experimental features:', error);
      throw error;
    }
  }

  /**
   * Export feature flags configuration
   * @returns JSON string of feature flags
   */
  exportFlags(): string {
    try {
      const flags = this.getAllFlags();
      const exportData = {
        exportedAt: new Date().toISOString(),
        flags: Object.entries(flags).reduce((acc, [flag, data]) => {
          acc[flag] = {
            enabled: data.enabled,
            ...data.config
          };
          return acc;
        }, {} as Record<string, any>)
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Error exporting feature flags:', error);
      throw error;
    }
  }

  /**
   * Import feature flags configuration
   * @param jsonData JSON string containing feature flags
   * @param updatedBy User performing the import
   * @returns Number of imported flags
   */
  importFlags(jsonData: string, updatedBy?: string): number {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.flags || typeof importData.flags !== 'object') {
        throw new Error('Invalid feature flags import format');
      }

      let imported = 0;
      
      for (const [flag, data] of Object.entries(importData.flags)) {
        if (flag in FeatureFlag) {
          const value = (data as any).enabled ? 'true' : 'false';
          this.settingsRepository.setSetting(flag, value, updatedBy);
          imported++;
        }
      }
      
      // Clear cache after import
      this.clearCache();
      
      return imported;
    } catch (error) {
      console.error('Error importing feature flags:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const featureFlagsService = new FeatureFlagsService();