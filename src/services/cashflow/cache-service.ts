import { CashflowForecast } from "../../types/financial";

/**
 * Interface for cache entry
 */
interface CacheEntry<T> {
  timestamp: number;
  data: T;
}

/**
 * Service for caching cashflow data to improve performance
 */
export class CacheService {
  private static readonly DEFAULT_TTL = 300000; // 5 minutes default (5 * 60 * 1000 ms)
  private cache = new Map<string, CacheEntry<any>>();
  private inProgress = new Map<string, Promise<any>>();

  /**
   * Get an item from the cache
   * @param key Cache key
   * @param ttl Time-to-live in milliseconds
   * @returns Cached data or undefined if expired/missing
   */
  get<T>(key: string, ttl = CacheService.DEFAULT_TTL): T | undefined {
    const entry = this.cache.get(key);
    if (entry && Date.now() - entry.timestamp < ttl) {
      return entry.data as T;
    }
    return undefined;
  }

  /**
   * Set an item in the cache
   * @param key Cache key
   * @param data Data to cache
   * @returns The cached data
   */
  set<T>(key: string, data: T): T {
    this.cache.set(key, {
      timestamp: Date.now(),
      data,
    });
    return data;
  }

  /**
   * Check if there's an in-progress request for this key
   * @param key Cache key
   * @returns True if there's an in-progress request
   */
  hasInProgress(key: string): boolean {
    return this.inProgress.has(key);
  }

  /**
   * Get an in-progress request
   * @param key Cache key
   * @returns In-progress promise or undefined
   */
  getInProgress<T>(key: string): Promise<T> | undefined {
    return this.inProgress.get(key) as Promise<T> | undefined;
  }

  /**
   * Set an in-progress request
   * @param key Cache key
   * @param promise Promise to store
   * @returns The stored promise
   */
  setInProgress<T>(key: string, promise: Promise<T>): Promise<T> {
    this.inProgress.set(key, promise);
    return promise;
  }

  /**
   * Remove an in-progress request
   * @param key Cache key
   */
  removeInProgress(key: string): void {
    this.inProgress.delete(key);
  }

  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
    this.inProgress.clear();
  }
}
