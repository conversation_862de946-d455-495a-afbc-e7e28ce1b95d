import { CashflowForecast } from "../../types/financial";
import db from "../../api/services/db-service";
import { v4 as uuidv4 } from "uuid";

/**
 * Service for managing cashflow projection snapshots
 * <PERSON>les creating, retrieving, and listing historical snapshots
 */
export class CashflowSnapshotService {
  /**
   * Create a new snapshot of cashflow projection
   *
   * @param tenantId Xero tenant ID
   * @param forecast Cashflow forecast data to save
   * @param daysAhead Projection timeframe (default: 90 days)
   * @returns Date of the created snapshot (YYYY-MM-DD)
   */
  async createSnapshot(
    tenantId: string,
    forecast: CashflowForecast,
    daysAhead: number = 90
  ): Promise<string> {
    try {
      const date = new Date().toISOString().split('T')[0];
      const snapshotData = JSON.stringify(forecast);
      const now = new Date().toISOString();
      const id = uuidv4();

      db.prepare(`
        INSERT OR REPLACE INTO cashflow_snapshot (
          id, date, tenant_id, days_ahead, snapshot_data, created_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        date,
        tenantId,
        daysAhead,
        snapshotData,
        now,
        'system'
      );

      console.log(`Created cashflow snapshot for ${date}, tenant ${tenantId}, timeframe ${daysAhead} days`);
      return date;
    } catch (error) {
      console.error('Error creating snapshot:', error);
      throw new Error('Failed to create snapshot');
    }
  }

  /**
   * Get a snapshot for a specific date
   *
   * @param tenantId Xero tenant ID
   * @param date Snapshot date in YYYY-MM-DD format
   * @param daysAhead Projection timeframe (default: 90 days)
   * @returns Cashflow forecast from the snapshot, or null if not found
   */
  async getSnapshot(
    tenantId: string,
    date: string,
    daysAhead: number = 90
  ): Promise<CashflowForecast | null> {
    try {
      const row = db.prepare(`
        SELECT snapshot_data FROM cashflow_snapshot
        WHERE tenant_id = ? AND date = ? AND days_ahead = ?
      `).get(tenantId, date, daysAhead);

      if (!row) {
        console.log(`No snapshot found for date ${date}, tenant ${tenantId}, timeframe ${daysAhead} days`);
        return null;
      }

      // Parse the snapshot data
      const snapshot = JSON.parse(row.snapshot_data);

      // Convert date strings back to Date objects
      if (snapshot.startDate) snapshot.startDate = new Date(snapshot.startDate);
      if (snapshot.endDate) snapshot.endDate = new Date(snapshot.endDate);

      // Convert dates in daily cashflow
      if (snapshot.dailyCashflow && Array.isArray(snapshot.dailyCashflow)) {
        snapshot.dailyCashflow = snapshot.dailyCashflow.map((day: any) => ({
          ...day,
          date: new Date(day.date)
        }));
      }

      // Convert dates in projected transactions
      if (snapshot.projectedTransactions && Array.isArray(snapshot.projectedTransactions)) {
        snapshot.projectedTransactions = snapshot.projectedTransactions.map((transaction: any) => ({
          ...transaction,
          date: new Date(transaction.date)
        }));
      }

      // Convert dates in custom expenses
      if (snapshot.customExpenses && Array.isArray(snapshot.customExpenses)) {
        snapshot.customExpenses = snapshot.customExpenses.map((expense: any) => ({
          ...expense,
          date: new Date(expense.date)
        }));
      }

      return snapshot;
    } catch (error) {
      console.error('Error retrieving snapshot:', error);
      return null;
    }
  }

  /**
   * Get available snapshot dates
   *
   * @param tenantId Xero tenant ID
   * @param daysAhead Projection timeframe (default: 90 days)
   * @returns Array of available dates in YYYY-MM-DD format, sorted newest to oldest
   */
  async getAvailableDates(
    tenantId: string,
    daysAhead: number = 90
  ): Promise<string[]> {
    try {
      const rows = db.prepare(`
        SELECT date FROM cashflow_snapshot
        WHERE tenant_id = ? AND days_ahead = ?
        ORDER BY date DESC
      `).all(tenantId, daysAhead);

      return rows.map(row => row.date);
    } catch (error) {
      console.error('Error retrieving snapshot dates:", error);
      return [];
    }
  }
}
