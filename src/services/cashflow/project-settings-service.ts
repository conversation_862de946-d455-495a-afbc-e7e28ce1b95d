import fs from "fs";
import path from "path";
import { withErrorHandling } from "../../utils/error";

/**
 * Service for managing project settings
 */
export class ProjectSettingsService {
  /**
   * Fetch project settings from disk
   * @returns Project settings array or empty array on error
   */
  async getProjectSettings(): Promise<any[]> {
    // Determine the data directory based on environment
    let dataDir = '';
    if (process.env.NODE_ENV === 'production') {
      dataDir = '/data';
    } else {
      dataDir = path.join(__dirname, '../../../data');
    }
    
    // Path to settings file
    const settingsPath = path.join(dataDir, 'project_settings.json');
    
    return withErrorHandling(
      async () => {
        // Read settings file
        if (fs.existsSync(settingsPath)) {
          const settingsData = fs.readFileSync(settingsPath, 'utf8');
          const settings = JSON.parse(settingsData);
          console.log(`Loaded ${settings.length} project settings for projection calculations`);
          return settings;
        } else {
          console.log('No project settings file found - using empty settings array");
          return [];
        }
      },
      {
        operationName: "getProjectSettings",
        fallbackValue: []
      }
    );
  }
}