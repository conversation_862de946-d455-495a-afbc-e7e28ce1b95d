// Types for projection filter service
interface ProjectionInvoice {
  id: string;
  what: string;
  date: string | Date;
  amount: number;
  metadata?: {
    projectId?: string;
    projectName?: string;
    paymentTerms?: number;
    [key: string]: any;
  };
}

interface OpenInvoice {
  id: string;
  what: string;
  date: string | Date;
  metadata?: {
    projectId?: string;
    id?: string;
    [key: string]: any;
  };
}

/**
 * Service for filtering projected invoices and emitting filter events
 */
export class ProjectionFilterService {
  /**
   * Filter out projected invoices that would have already been invoiced
   * 
   * This function implements filtering rules that consider:
   * 1. Existing invoices - exclude projected invoices when real invoices from the same project exist within 5 days
   * 2. Payment terms - never show projected payments due within the payment terms period
   * 3. Uninvoiced work - exclude uninvoiced work when projected income for the same project exists within 3 days
   * 
   * @param projectedInvoices Array of projected invoices to filter
   * @param openInvoices Array of existing open invoices for comparison
   * @returns Filtered array without already-invoiced payments
   */
  filterAlreadyInvoicedProjections(projectedInvoices: ProjectionInvoice[], openInvoices: OpenInvoice[] = []): ProjectionInvoice[] {
    const today = new Date();
    
    console.log(`====== FILTER DEBUG INFO ======`);
    console.log(`Today's date: ${today.toLocaleDateString()}`);
    console.log(`Analyzing ${projectedInvoices.length} projected invoices...`);
    console.log(`Comparing against ${openInvoices.length} existing open invoices`);
    
    // First, separate projected income from uninvoiced work
    const projectedIncome = projectedInvoices.filter(invoice => invoice.id.startsWith('future-work-'));
    const uninvoicedWork = projectedInvoices.filter(invoice => invoice.id.startsWith('uninvoiced-'));
    
    console.log(`Split invoices: ${projectedIncome.length} projected income, ${uninvoicedWork.length} uninvoiced work`);
    
    // Apply filtering logic to projected income
    const filteredProjectedIncome = this.filterProjectedIncome(projectedIncome, openInvoices, today);
    
    // Apply filtering logic to uninvoiced work
    const filteredUninvoicedWork = this.filterUninvoicedWork(uninvoicedWork, filteredProjectedIncome);
    
    // Combine filtered results
    const filtered = [...filteredProjectedIncome, ...filteredUninvoicedWork];
    
    console.log(`Filtered from ${projectedInvoices.length} to ${filtered.length} invoices`);
    console.log(`Final result: ${filteredProjectedIncome.length} projected income, ${filteredUninvoicedWork.length} uninvoiced work`);
    console.log(`====== END FILTER DEBUG INFO ======`);
    
    return filtered;
  }
  
  /**
   * Apply filtering logic to projected income
   * @param projectedIncome Array of projected income invoices
   * @param openInvoices Array of open invoices
   * @param today Today"s date
   * @returns Filtered array of projected income invoices
   * @private
   */
  private filterProjectedIncome(projectedIncome: ProjectionInvoice[], openInvoices: OpenInvoice[], today: Date): ProjectionInvoice[] {
    return projectedIncome.filter(invoice => {
      // Extract necessary information
      const paymentDate = new Date(invoice.date);
      const projectId = invoice.metadata?.projectId;
      const projectName = invoice.metadata?.projectName || "Unknown Project";
      
      // Get payment terms from metadata with proper fallback 
      const paymentTerms = typeof invoice.metadata?.paymentTerms === 'number' ? 
                         invoice.metadata.paymentTerms : 30;
      
      // Log invoice details for debugging
      console.log(`\nDETAILED CHECK: "${invoice.what}" for ${projectName}`);
      console.log(`  Type: Projected Income`);
      console.log(`  Payment date: ${paymentDate.toLocaleDateString()} (${paymentDate.toISOString()})`);
      console.log(`  Payment terms: ${paymentTerms} days`);
      
      // Get invoice date by subtracting payment terms from payment date
      const invoiceDate = new Date(paymentDate);
      invoiceDate.setDate(invoiceDate.getDate() - paymentTerms);
      console.log(`  Invoice date: ${invoiceDate.toLocaleDateString()} (${invoiceDate.toISOString()})`);
      
      // Check if this projected invoice already has a real invoice from the same project
      // that's close in time (within 5 days)
      if (openInvoices.length > 0 && projectId) {
        // Find ALL potential matching invoices (not just the first one)
        const matchingRealInvoices = openInvoices.filter(realInvoice => {
          // Check if from the same project
          if (realInvoice.metadata?.projectId !== projectId) {
            return false;
          }
          
          // Check if dates are within 5 days of each other
          const realInvoiceDate = new Date(realInvoice.date);
          const diffTime = Math.abs(paymentDate.getTime() - realInvoiceDate.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          return diffDays <= 5;
        });
        
        // Use the first match if any were found
        const matchingRealInvoice = matchingRealInvoices.length > 0 ? matchingRealInvoices[0] : null;
        
        if (matchingRealInvoice) {
          const reason = `Real invoice from same project exists within 5 days`;
          console.log(`  EXCLUDING: ${reason} (${matchingRealInvoice.what})`);
          
          // Add more detailed reason with invoice details
          const detailedReason = `Real invoice "${matchingRealInvoice.what}" from same project exists within 5 days`;
          
          // Emit filter event for audit tracking with enhanced reason
          this.emitFilterEvent(invoice, 'excluded', detailedReason, matchingRealInvoice);
          
          return false;
        }
      }
      
      // Payment terms filter as fallback (only applied if no real invoice was found)
      const cutoffDate = new Date(today);
      cutoffDate.setDate(cutoffDate.getDate() + paymentTerms);
      console.log(`  Payment cutoff: ${cutoffDate.toLocaleDateString()}`);
      
      if (paymentDate < cutoffDate) {
        // More detailed reason including the dates
        const reason = `Payment date ${paymentDate.toLocaleDateString()} is before cutoff ${cutoffDate.toLocaleDateString()} (within payment terms of ${paymentTerms} days)`;
        console.log(`  EXCLUDING: ${reason}`);
        
        // Emit filter event for audit tracking with date details
        this.emitFilterEvent(invoice, 'excluded', reason);
        
        return false;
      }
      
      // We successfully passed all checks
      console.log(`  NOTE: Invoice follows proper scheduling from generation logic`);
      console.log(`  KEEPING: Payment date after cutoff and no matching real invoice`);
      
      // Emit filter event for audit tracking - IMPORTANT: Always emit for both kept and excluded
      this.emitFilterEvent(invoice, 'kept", 'Meets projection criteria");
      
      return true;
    });
  }
  
  /**
   * Apply filtering logic to uninvoiced work
   * @param uninvoicedWork Array of uninvoiced work invoices
   * @param filteredProjectedIncome Array of filtered projected income invoices
   * @returns Filtered array of uninvoiced work invoices
   * @private
   */
  private filterUninvoicedWork(uninvoicedWork: ProjectionInvoice[], filteredProjectedIncome: ProjectionInvoice[]): ProjectionInvoice[] {
    return uninvoicedWork.filter(uninvoiced => {
      // Extract necessary information
      const paymentDate = new Date(uninvoiced.date);
      const projectId = uninvoiced.metadata?.projectId;
      const projectName = uninvoiced.metadata?.projectName || "Unknown Project";
      
      // Log uninvoiced details for debugging
      console.log(`\nDETAILED CHECK: "${uninvoiced.what}" for ${projectName}`);
      console.log(`  Type: Uninvoiced Work`);
      console.log(`  Payment date: ${paymentDate.toLocaleDateString()} (${paymentDate.toISOString()})`);
      
      // Check if there's projected income for the same project around the same date (±3 days)
      // Find ALL potential matching projected income
      const matchingProjectedIncomes = filteredProjectedIncome.filter(projected => {
        // Check if from the same project
        if (projected.metadata?.projectId !== projectId) {
          return false;
        }
        
        // Check if dates are within 3 days of each other
        const projectedDate = new Date(projected.date);
        const diffTime = Math.abs(paymentDate.getTime() - projectedDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return diffDays <= 3;
      });
      
      // Use the first match if any were found
      const matchingProjectedIncome = matchingProjectedIncomes.length > 0 ? matchingProjectedIncomes[0] : null;
      
      if (matchingProjectedIncome) {
        // More detailed reason with specific projected income info
        const reason = `Projected income "${matchingProjectedIncome.what}" from same project exists within 3 days`;
        console.log(`  EXCLUDING: ${reason}`);
        
        // Emit filter event for audit tracking with enhanced reason
        this.emitFilterEvent(uninvoiced, 'excluded', reason, matchingProjectedIncome);
        
        return false;
      }
      
      // We successfully passed all checks
      console.log(`  KEEPING: No matching projected income within 3 days`);
      
      // Emit filter event for audit tracking - IMPORTANT: Always emit for both kept and excluded
      this.emitFilterEvent(uninvoiced, 'kept", 'Meets projection criteria");
      
      return true;
    });
  }
  
  /**
   * Emit filter event for audit tracking
   * @param invoice Invoice being filtered
   * @param action Action taken (kept or excluded)
   * @param reason Reason for the action
   * @param relatedInvoice Related invoice if any
   * @private
   */
  private emitFilterEvent(invoice: ProjectionInvoice, action: "kept" | "excluded", reason: string, relatedInvoice?: OpenInvoice): void {
    try {
      // Extract and normalize client name to ensure consistency
      const clientName = invoice.clientName || invoice.metadata?.clientName || null;
      
      // Create event data object
      const eventData = {
        invoice: {
          id: invoice.id,
          projectId: invoice.metadata?.projectId || "unknown",
          projectName: invoice.metadata?.projectName || invoice.what || "Unknown Project",
          clientName: clientName, // Use normalized client name
          type: "invoice", // Use consistent type with ID prefix for identification
          what: invoice.what,
          description: invoice.what, // Add description field to match Transaction interface
          date: invoice.date,
          invoiceDate: invoice.metadata?.invoiceDate,
          amount: invoice.amount,
          metadata: invoice.metadata // Include full metadata for better client name resolution
        },
        action,
        reason,
        relatedInvoice: relatedInvoice ? {
          what: relatedInvoice.what,
          description: relatedInvoice.what, // Add description field to match Transaction interface
          date: relatedInvoice.date,
          id: relatedInvoice.id,  // Important: Include the ID for proper link generation
          clientName: relatedInvoice.clientName || relatedInvoice.metadata?.clientName, // Pull from either source
          // Include metadata fields needed for proper display
          metadata: {
            ...(relatedInvoice.metadata || {}),
            // Explicitly add invoiceNumber for display purposes if it exists
            invoiceNumber: relatedInvoice.metadata?.invoiceNumber || relatedInvoice.number,
            // Explicitly add due_date for proper date display
            due_date: relatedInvoice.metadata?.due_date || relatedInvoice.due_date
          }
        } : undefined
      };
      
      // For backend contexts, directly output the event object for log capture
      console.log('PROJECTION_FILTERED', eventData);
      
      // The eventBus has been refactored to use React Context
      // Store events in an array instead of overwriting the last event
      if (typeof window !== 'undefined') {
        // Initialize the array if it doesn't exist
        // @ts-ignore
        if (!window.__projectionEvents) {
          // @ts-ignore
          window.__projectionEvents = [];
        }
        
        // Add the new event to the array
        // @ts-ignore
        window.__projectionEvents.push(eventData);
        
        // Also maintain backwards compatibility with the old approach
        // @ts-ignore
        window.__lastProjectionEvent = eventData;
        
        // Try to use the event context system if it's available
        // @ts-ignore
        if (window.__dispatchProjectionEvent) {
          try {
            // @ts-ignore
            window.__dispatchProjectionEvent(eventData);
          } catch (error) {
            console.warn('Failed to dispatch through event context, falling back to global storage', error);
          }
        }
      }
    } catch (error) {
      // If there's an error with the eventBus, we should still continue with filtering
      console.warn('Failed to emit filter event, but continuing with filtering", error);
    }
  }
}