import { Transaction } from "../../types/financial";

/**
 * Service for transforming various data types to Transaction format
 */
export class TransactionService {
  /**
   * Transform expenses to Transaction format
   * @param expenseRaw Raw expense data
   * @returns Transformed transactions
   */
  transformExpensesToTransactions(expenseRaw: any[]): Transaction[] {
    return (expenseRaw || []).map(p => ({
      id: p.id || `expense-${Date.now()}-${Math.random()}`,
      type: p.type || "expense",
      description: p.reference || p.name || "Expense",
      date: p.date instanceof Date ? p.date : new Date(),
      source: p.source || "Expense",
      amount: typeof p.amount === 'number' ? p.amount : 0,
      metadata: p.metadata || {} // Preserve metadata for filtering
    }));
  }

  /**
   * Transform repeating bills to Transaction format
   * @param billsRaw Raw bill data
   * @returns Transformed transactions
   */
  transformBillsToTransactions(billsRaw: any[]): Transaction[] {
    return (billsRaw || []).map(b => ({
      id: b.id || `bill-${Date.now()}-${Math.random()}`,
      type: "bill", // Use standard 'bill' type
      description: b.reference || "Repeating Bill",
      date: b.date instanceof Date ? b.date : new Date(),
      source: "xero", // Repeating bills come from Xero
      amount: typeof b.amount === 'number' ? b.amount : 0,
      metadata: { // Store original type for potential frontend use
        originalType: b.type || "repeating_bill"
      }
    }));
  }

  /**
   * Transform invoices to Transaction format
   * @param invoicesRaw Raw invoice data
   * @returns Transformed transactions
   */
  transformInvoicesToTransactions(invoicesRaw: any[]): Transaction[] {
    return (invoicesRaw || []).map(invoice => ({
      id: invoice.id || `invoice-${Date.now()}-${Math.random()}`,
      type: "invoice",
      description: invoice.what || "Invoice", // Map 'what' from raw data to 'description'
      date: invoice.date instanceof Date ? invoice.date : new Date(),
      source: "harvest", // Use lowercase 'harvest' to match the type union
      amount: typeof invoice.amount === 'number' ? invoice.amount : 0,
      metadata: invoice.metadata || {} // Preserve metadata for filtering
    }));
  }
}