import { CashflowSnapshotJob } from "../../jobs/cashflow-snapshot-job";

// Singleton instance of the snapshot job
let snapshotJobInstance: CashflowSnapshotJob | null = null;

/**
 * Initialize the cashflow snapshot job
 * This function ensures the job is only initialized once
 */
export function initializeCashflowSnapshotJob(): void {
  if (!snapshotJobInstance) {
    console.log('Initializing cashflow snapshot job...');
    snapshotJobInstance = new CashflowSnapshotJob();
    snapshotJobInstance.start();
    console.log('Cashflow snapshot job initialized and started');
  } else {
    console.log('Cashflow snapshot job already initialized');
  }
}

/**
 * Get the snapshot job instance
 * @returns The snapshot job instance, or null if not initialized
 */
export function getSnapshotJob(): CashflowSnapshotJob | null {
  return snapshotJobInstance;
}

/**
 * Stop the snapshot job
 * Useful for graceful shutdown or testing
 */
export function stopSnapshotJob(): void {
  if (snapshotJobInstance) {
    snapshotJobInstance.stop();
    console.log('Cashflow snapshot job stopped');
  }
}
