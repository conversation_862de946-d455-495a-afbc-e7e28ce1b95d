import { CustomExpense } from "../../types/financial";
import { addMonths, addWeeks, addDays } from "date-fns";

/**
 * Service for calculating expense occurrences and transactions
 */
export class ExpenseService {
  /**
   * Generate transactions from custom expenses
   *
   * @param startDate Start date for forecast period
   * @param endDate End date for forecast period
   * @param customExpenses Array of custom expense definitions
   * @returns Array of transactions representing all expense occurrences
   */
  generateCustomExpenseTransactions(
    startDate: Date,
    endDate: Date,
    customExpenses: CustomExpense[]
  ): any[] {
    const transactions: any[] = [];

    // Process each custom expense
    for (const expense of customExpenses) {
      // Calculate expense occurrences within projection timeframe
      const occurrences = this.calculateExpenseOccurrences(expense, startDate, endDate);

      // Create a transaction for each occurrence
      for (const occurrenceDate of occurrences) {
        transactions.push({
          id: `custom-expense-${expense.id}-${occurrenceDate.toISOString()}`,
          type: `custom_expense_${expense.type}`,
          reference: expense.name,
          name: expense.name,
          description: expense.description || expense.name,
          date: new Date(occurrenceDate),
          // Preserve the original source if it exists, otherwise use a generic source
          source: expense.source || `Custom Expense (${expense.type})`,
          // Include metadata if it exists
          metadata: expense.metadata || {},
          amount: -Math.abs(expense.amount) // Ensure negative for expense
        });
      }
    }

    return transactions;
  }

  /**
   * Calculate all occurrence dates for an expense within a timeframe
   *
   * @param expense The custom expense definition
   * @param startDate Start date of the forecast period
   * @param endDate End date of the forecast period
   * @returns Array of dates when the expense will occur
   */
  calculateExpenseOccurrences(
    expense: CustomExpense,
    startDate: Date,
    endDate: Date
  ): Date[] {
    const occurrences: Date[] = [];
    const firstOccurrence = new Date(expense.date);

    // Handle one-off expenses
    if (expense.frequency === 'one-off') {
      // For one-off expenses, only include the expense date if it's within the range
      if (firstOccurrence >= startDate && firstOccurrence <= endDate) {
        occurrences.push(new Date(firstOccurrence));
      }
      return occurrences;
    }

    // For recurring expenses

    // Find the first occurrence that matches our expense date pattern
    let baseDate = new Date(firstOccurrence);

    // If the expense date is before the start date, calculate the next occurrence
    // after the start date by adding frequency intervals until we reach the range
    if (baseDate < startDate) {
      // Adjust baseDate to find the first occurrence within or after the range
      while (baseDate < startDate) {
        switch (expense.frequency) {
          case 'weekly':
            baseDate = addWeeks(baseDate, 1);
            break;
          case 'fortnightly':
            baseDate = addDays(baseDate, 14); // Two weeks
            break;
          case 'monthly':
            baseDate = addMonths(baseDate, 1);
            break;
          case 'quarterly':
            baseDate = addMonths(baseDate, 3);
            break;
        }
      }
    }

    // Now generate all occurrences from this base date until end date
    let currentDate = new Date(baseDate);

    // Start with the first occurrence that matches our pattern
    if (currentDate >= startDate && currentDate <= endDate) {
      occurrences.push(new Date(currentDate));
    }

    // Generate subsequent occurrences
    while (currentDate <= endDate) {
      // Calculate next occurrence
      switch (expense.frequency) {
        case 'weekly':
          currentDate = addWeeks(currentDate, 1);
          break;
        case 'fortnightly':
          currentDate = addDays(currentDate, 14); // Two weeks
          break;
        case 'monthly':
          currentDate = addMonths(currentDate, 1);
          break;
        case 'quarterly':
          currentDate = addMonths(currentDate, 3);
          break;
      }

      // Stop if we've gone past the end date
      if (currentDate > endDate) {
        break;
      }

      // Add this occurrence if it's within our timeframe
      if (currentDate >= startDate && currentDate <= endDate) {
        occurrences.push(new Date(currentDate));
      }

      // If repeatCount is specified and we've reached that number of occurrences, stop
      if (expense.repeatCount && occurrences.length >= expense.repeatCount) {
        break;
      }
    }

    // If repeatCount is specified, limit the occurrences to that number
    if (expense.repeatCount && occurrences.length > expense.repeatCount) {
      return occurrences.slice(0, expense.repeatCount);
    }

    return occurrences;
  }
}