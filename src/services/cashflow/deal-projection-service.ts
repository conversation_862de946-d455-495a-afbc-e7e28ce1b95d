import { Deal } from "../../frontend/types/crm-types";
import { PotentialTransaction, ScenarioType } from "../../types/financial";
import { isDealValidForProjections } from "../../utils/deal-validation";

/**
 * Service to convert CRM deals into potential transactions
 */
export class DealProjectionService {
  /**
   * Transform deals into potential transactions
   *
   * @param deals Array of CRM deals
   * @param startDate Beginning of forecast period
   * @param endDate End of forecast period
   * @returns Array of potential transactions
   */
  transformDealsToTransactions(
    deals: Deal[],
    startDate: Date,
    endDate: Date
  ): PotentialTransaction[] {
    const potentialTransactions: PotentialTransaction[] = [];

    // Only process deals that have all required fields for projections
    // and are explicitly included in projections (if that flag exists)
    const validDeals = deals.filter(deal => {
      // Skip deals that are explicitly excluded from projections
      if ((deal as any).includeInProjections === 0) return false;

      // Use the validation utility to check if the deal has all required fields
      return isDealValidForProjections(deal);
    });

    for (const deal of validDeals) {
      // Skip deals already in Closed Won or Closed Lost or Abandoned stages
      if (deal.stage === 'Closed won' || deal.stage === 'Closed lost' || deal.stage === 'Abandoned') {
        continue;
      }

      const dealStartDate = new Date(deal.startDate!);
      const dealEndDate = new Date(deal.endDate!);

      // Skip deals that end before our forecast period starts
      if (dealEndDate < startDate) {
        continue;
      }

      // Determine which scenarios this transaction belongs to based on probability
      const scenarioType = this.determineScenarioType(deal.probability!);

      // Generate transactions by dividing deal value across project duration
      this.generateTransactionsForDeal(
        deal,
        dealStartDate,
        dealEndDate,
        startDate,
        endDate,
        scenarioType,
        potentialTransactions
      );
    }

    return potentialTransactions;
  }

  /**
   * Generate transactions by dividing deal value evenly across project duration
   * This is a simplified approach compared to the complex "Smart forecast" logic
   *
   * @param deal The deal to generate transactions for
   * @param dealStartDate Deal start date
   * @param dealEndDate Deal end date
   * @param forecastStartDate Forecast period start date
   * @param forecastEndDate Forecast period end date
   * @param scenarioType The scenario type for this deal
   * @param potentialTransactions Array to add transactions to
   */
  private generateTransactionsForDeal(
    deal: Deal,
    dealStartDate: Date,
    dealEndDate: Date,
    forecastStartDate: Date,
    forecastEndDate: Date,
    scenarioType: ScenarioType,
    potentialTransactions: PotentialTransaction[]
  ): void {
    // Frequency determines how many invoices to create
    // Default to monthly if not specified
    const frequency = deal.invoiceFrequency || "monthly";

    let numberOfInvoices = 1; // Default to one invoice

    // Calculate project duration in months
    const projectDurationMs = dealEndDate.getTime() - dealStartDate.getTime();
    const projectDurationMonths = Math.max(Math.ceil(projectDurationMs / (30 * 24 * 60 * 60 * 1000)), 1);

    // Determine number of invoices based on frequency
    switch (frequency.toLowerCase()) {
      case 'weekly':
        numberOfInvoices = Math.ceil(projectDurationMonths * 4); // ~4 weeks per month
        break;
      case 'fortnightly':
      case 'biweekly':
        numberOfInvoices = Math.ceil(projectDurationMonths * 2); // ~2 fortnights per month
        break;
      case 'monthly':
        numberOfInvoices = projectDurationMonths;
        break;
      case 'quarterly':
        numberOfInvoices = Math.max(Math.ceil(projectDurationMonths / 3), 1);
        break;
      case 'upfront':
        numberOfInvoices = 1;
        break;
      case 'completion':
        numberOfInvoices = 1;
        break;
      default:
        numberOfInvoices = projectDurationMonths; // Default to monthly
    }

    // Calculate invoice amount
    const invoiceAmount = (deal.value || 0) / numberOfInvoices;

    // Generate invoice dates
    const invoiceDates: Date[] = [];

    if (frequency.toLowerCase() === 'upfront') {
      // Upfront - single invoice at start
      invoiceDates.push(new Date(dealStartDate));
    } else if (frequency.toLowerCase() === 'completion") {
      // Completion - single invoice at end
      invoiceDates.push(new Date(dealEndDate));
    } else {
      // Distributed invoices
      const intervalMs = projectDurationMs / numberOfInvoices;

      for (let i = 0; i < numberOfInvoices; i++) {
        const invoiceDate = new Date(dealStartDate.getTime() + (intervalMs * i));
        invoiceDates.push(invoiceDate);
      }
    }

    // Apply payment terms (days until payment)
    const paymentTerms = deal.paymentTerms || 14; // Default to 14 days if not specified

    // Create a transaction for each invoice date
    for (let i = 0; i < invoiceDates.length; i++) {
      const invoiceDate = invoiceDates[i];

      // Apply payment terms
      const paymentDate = new Date(invoiceDate);
      paymentDate.setDate(paymentDate.getDate() + paymentTerms);

      // Skip if payment date is outside our forecast period
      if (paymentDate < forecastStartDate || paymentDate > forecastEndDate) {
        continue;
      }

      // Create the transaction
      const potentialTransaction: PotentialTransaction = {
        id: `deal-${deal.id}-invoice-${i+1}`,
        type: "invoice",
        description: `Deal: ${deal.name} (Invoice ${i+1}/${numberOfInvoices})`,
        date: paymentDate,
        source: "system",
        amount: invoiceAmount,
        dealId: deal.id,
        probability: deal.probability!,
        scenarioType: scenarioType,
        metadata: {
          dealName: deal.name,
          dealStage: deal.stage,
          invoiceNumber: i + 1,
          totalInvoices: numberOfInvoices,
          originalAmount: deal.value,
          company: deal.company?.name
        }
      };

      potentialTransactions.push(potentialTransaction);
    }
  }

  /**
   * Determine which scenario types this deal belongs to based on probability
   *
   * @param probability Deal probability as decimal (0-1)
   * @returns The scenario type
   */
  private determineScenarioType(probability: number): ScenarioType {
    // Best case includes all deals
    // Expected case includes probability-weighted deals
    // Worst case includes no deals (base projection)

    // Ensure probability is treated as a decimal (0-1)
    // This is a safety check in case any probability values are stored as percentages (0-100)
    const normalizedProbability = probability > 1 ? probability / 100 : probability;

    if (normalizedProbability >= 0.9) {
      // High probability deals included in expected case at full value
      return 'expected-case';
    } else {
      // Lower probability deals only included in best case
      return 'best-case';
    }
  }
}
