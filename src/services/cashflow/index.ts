import { CashflowForecast, CustomExpense } from "../../types/financial";
import { XeroService, getXeroService } from "../xero";
import { HarvestService, getHarvestService } from "../harvest";
import { ProjectSettingsService } from "./project-settings-service";
import { CacheService } from "./cache-service";
import { ExpenseService } from "./expense-service";
import { ProjectionFilterService } from "./projection-filter-service";
import { TransactionService } from "./transaction-service";
import { DailyCashflowService } from "./daily-cashflow-service";
import { CashflowSnapshotService } from "./snapshot-service";
import { withErrorHandling, withTimeout, AppError } from "../../utils/error";

// Feature flags to control projection system behavior
const INCLUDE_REPEATING_BILLS = false;

/**
 * Service for generating cashflow forecasts
 *
 * This service orchestrates data collection from Xero and Harvest APIs
 * to generate accurate cashflow projections.
 */
export class CashflowService {
  private xeroService: XeroService;
  private harvestService: HarvestService;
  private projectSettingsService: ProjectSettingsService;
  private cacheService: CacheService;
  private expenseService: ExpenseService;
  private projectionFilterService: ProjectionFilterService;
  private transactionService: TransactionService;
  private dailyCashflowService: DailyCashflowService;

  /**
   * Constructor
   */
  constructor() {
    this.xeroService = getXeroService();
    this.harvestService = getHarvestService();
    this.projectSettingsService = new ProjectSettingsService();
    this.cacheService = new CacheService();
    this.expenseService = new ExpenseService();
    this.projectionFilterService = new ProjectionFilterService();
    this.transactionService = new TransactionService();
    this.dailyCashflowService = new DailyCashflowService();
  }

  /**
   * Generate a forward-looking cashflow projection from today
   *
   * This is the main entry point for creating cashflow projections. It handles:
   * - Validating input parameters
   * - Setting appropriate date ranges
   * - Caching results to avoid redundant API calls
   * - Implementing timeouts to prevent hanging requests
   * - Graceful fallback to default values if APIs fail
   *
   * @param tenantId Xero tenant ID
   * @param daysAhead Number of days to project forward (30, 60, 90, 120, 150, 180, or 210)
   * @param customExpenses Array of custom expenses
   * @returns Cashflow forecast with daily projections and transaction details
   */
  async generateForwardProjection(
    tenantId: string,
    daysAhead: number,
    customExpenses: CustomExpense[] = [],
  ): Promise<CashflowForecast> {
    // Validate daysAhead parameter
    if (![30, 60, 90, 120, 150, 180, 210].includes(daysAhead)) {
      console.warn(
        `Invalid daysAhead value: ${daysAhead}. Using 90 days as default.`,
      );
      daysAhead = 90;
    }

    console.log(`Generating forward projection for ${daysAhead} days`);

    // Calculate start and end dates
    const startDate = new Date(); // Today
    startDate.setHours(0, 0, 0, 0); // Normalize to start of day

    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + daysAhead - 1); // Subtract 1 to make it inclusive
    endDate.setHours(23, 59, 59, 999); // End of day

    // Create cache key that includes expenses
    // Hash the custom expenses for the cache key
    const expensesHash = JSON.stringify(
      customExpenses.map((e) => ({
        id: e.id,
        amount: e.amount,
        date: e.date.toISOString(),
        frequency: e.frequency,
      })),
    );
    const cacheKey = `forward_${tenantId}:${daysAhead}:custom:${expensesHash}`;

    // Check if we have a cached result
    const cachedResult = this.cacheService.get<CashflowForecast>(cacheKey);
    if (cachedResult) {
      console.log("Using cached forward projection");
      return cachedResult;
    }

    // Check if there's already a request in progress for this key
    if (this.cacheService.hasInProgress(cacheKey)) {
      console.log("Reusing in-progress forward projection request");
      return this.cacheService.getInProgress<CashflowForecast>(cacheKey)!;
    }

    // Initialize with default values
    const defaultForecast: CashflowForecast = {
      startDate,
      endDate,
      startingBalance: 0,
      endingBalance: 0,
      accounts: [],
      projectedTransactions: [],
      dailyCashflow: [],
      customExpenses: customExpenses || [], // Ensure it's always an array
    };

    // Create a promise that will handle caching and in-progress tracking
    const resultPromise = withTimeout(
      async () => {
        try {
          // Perform the actual forecast generation with standardized error handling
          const result = await this.generateProjection(
            tenantId,
            startDate,
            endDate,
            defaultForecast,
            customExpenses,
          );

          // Cache successful result
          this.cacheService.set(cacheKey, result);

          return result;
        } finally {
          // Always remove from in-progress map when done
          this.cacheService.removeInProgress(cacheKey);
        }
      },
      20000, // 20 second timeout
      "generateForwardProjection",
    ).catch((error) => {
      // Remove from in-progress map
      this.cacheService.removeInProgress(cacheKey);

      console.error("Error generating forward projection:", error);
      return defaultForecast;
    });

    // Store the promise in the inProgress map
    this.cacheService.setInProgress(cacheKey, resultPromise);

    // Return the promise
    return resultPromise;
  }

  /**
   * Generate a complete cashflow projection
   *
   * Core implementation of the cashflow projection algorithm:
   * 1. Fetch current bank balances from Xero (starting point)
   * 2. Generate expense transactions from custom expenses
   * 3. Retrieve invoice data from Harvest (expected income)
   * 4. Retrieve repeating bills from Xero (if enabled)
   * 5. Combine all transactions and sort chronologically
   * 6. Generate daily cashflow projections
   *
   * @param tenantId Xero tenant ID
   * @param startDate Start date for projection
   * @param endDate End date for projection
   * @param defaultForecast Default forecast to return on error
   * @param customExpenses Custom expense definitions
   * @returns Complete cashflow forecast
   * @private
   */
  private async generateProjection(
    tenantId: string,
    startDate: Date,
    endDate: Date,
    defaultForecast: CashflowForecast,
    customExpenses: CustomExpense[] = [],
  ): Promise<CashflowForecast> {
    return withErrorHandling(
      async () => {
        // Step 1: Get bank account balances (crucial for starting point)
        console.log("Fetching bank account balances...");
        const bankBalances = await this.fetchBankBalances(tenantId);

        // Ensure we have a valid starting balance
        if (
          !bankBalances ||
          typeof bankBalances.totalClosingBalance !== "number"
        ) {
          throw new AppError("Failed to get valid bank balances", { tenantId });
        }

        // Use the bank balance from API
        const startingBalance = bankBalances.totalClosingBalance;
        console.log(`Starting balance: ${startingBalance}`);

        // Generate expense transactions using custom expenses
        let expenseRaw;

        if (customExpenses.length > 0) {
          console.log(`Using ${customExpenses.length} custom expenses`);
          expenseRaw = this.expenseService.generateCustomExpenseTransactions(
            startDate,
            endDate,
            customExpenses,
          );
        } else {
          console.log(`No custom expenses found. Using empty expense list.`);
          expenseRaw = [];
        }

        // Step 3: Get all needed data in parallel
        console.log("Fetching forecasting data in parallel...");

        const [openInvoicesRaw, projectedInvoicesRaw, repeatingBillsRaw] =
          await Promise.all([
            // Get open invoices (sent but not paid)
            this.fetchOpenInvoices(startDate, endDate),

            // Get projected income
            this.fetchProjectSettings().then((settings) =>
              this.fetchProjectedIncome(startDate, endDate, settings),
            ),

            // Get repeating bills
            this.fetchRepeatingBills(tenantId, startDate, endDate),
          ]);

        // Filter projected invoices to exclude those that would have already been invoiced
        console.log(
          "Filtering projected invoices to prevent double-counting...",
        );
        const filteredProjectedInvoices =
          this.projectionFilterService.filterAlreadyInvoicedProjections(
            projectedInvoicesRaw,
            openInvoicesRaw,
          );

        // Combine open invoices with filtered projected invoices
        const harvestInvoicesRaw = [
          ...openInvoicesRaw,
          ...filteredProjectedInvoices,
        ];
        console.log(
          `Combined ${openInvoicesRaw.length} open invoices with ${filteredProjectedInvoices.length} projected invoices (filtered from ${projectedInvoicesRaw.length})`,
        );

        // Transform all data to Transaction format
        const expenseTransactions =
          this.transactionService.transformExpensesToTransactions(expenseRaw);
        const billTransactions =
          this.transactionService.transformBillsToTransactions(
            repeatingBillsRaw,
          );
        const invoiceTransactions =
          this.transactionService.transformInvoicesToTransactions(
            harvestInvoicesRaw,
          );

        // Combine all transactions, excluding repeating bills if feature flag is off
        const allTransactions = [
          ...expenseTransactions,
          ...(INCLUDE_REPEATING_BILLS ? billTransactions : []), // Only include repeating bills if flag is true
          ...invoiceTransactions,
        ].sort(
          (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
        );

        console.log(
          `Found ${allTransactions.length} transactions for cashflow projection`,
        );

        // Generate daily cash flow projection
        console.log("Generating daily cashflow projection...");
        const dailyCashflow = this.dailyCashflowService.generateDailyCashflow(
          allTransactions,
          startingBalance,
          startDate,
          endDate,
        );

        console.log("Forward projection generation successful");

        // Filter transactions to match the requested date range
        const filteredTransactions = allTransactions.filter(
          (transaction) =>
            transaction.date >= startDate && transaction.date <= endDate,
        );

        console.log(
          `Filtered transactions from ${allTransactions.length} to ${filteredTransactions.length} based on timeframe`,
        );

        return {
          startDate,
          endDate,
          startingBalance,
          endingBalance:
            dailyCashflow.length > 0
              ? dailyCashflow[dailyCashflow.length - 1].balance
              : startingBalance,
          accounts: bankBalances.accounts || [],
          projectedTransactions: filteredTransactions,
          dailyCashflow,
          customExpenses: customExpenses ? [...customExpenses] : undefined,
        };
      },
      {
        operationName: "generateProjection",
        maxRetries: 2,
        fallbackValue: defaultForecast,
        onError: (error) => {
          console.error("Error generating forecast:", error);
        },
      },
    );
  }

  /**
   * Fetch bank balances with standardized error handling
   * @param tenantId Xero tenant ID
   * @returns Bank balances or undefined on error
   * @private
   */
  private async fetchBankBalances(tenantId: string) {
    return withErrorHandling(
      () =>
        this.xeroService.bankAccounts.getBankBalancesFromBalanceSheet(tenantId),
      {
        operationName: "fetchBankBalances",
        fallbackValue: { totalClosingBalance: 0, accounts: [] },
      },
    );
  }

  /**
   * Fetch open invoices with standardized error handling
   * @param startDate Start date for invoices
   * @param endDate End date for invoices
   * @returns Open invoices or empty array on error
   * @private
   */
  private async fetchOpenInvoices(startDate: Date, endDate: Date) {
    console.log("Fetching open invoices from Harvest...");
    return withErrorHandling(
      () => this.harvestService.invoices.getInvoices(startDate, endDate),
      {
        operationName: "fetchOpenInvoices",
        fallbackValue: [],
      },
    );
  }

  /**
   * Fetch project settings with standardized error handling
   * @returns Project settings or empty array on error
   * @private
   */
  private async fetchProjectSettings() {
    return this.projectSettingsService.getProjectSettings();
  }

  /**
   * Fetch projected income with standardized error handling
   * @param startDate Start date for projection
   * @param endDate End date for projection
   * @param projectSettings Project settings
   * @returns Projected income or empty array on error
   * @private
   */
  private async fetchProjectedIncome(
    startDate: Date,
    endDate: Date,
    projectSettings: any[],
  ) {
    console.log("Generating projected income...");
    return withErrorHandling(
      () =>
        this.harvestService.projectBudgets.generateProjectedIncome(
          startDate,
          endDate,
          projectSettings,
        ),
      {
        operationName: "fetchProjectedIncome",
        fallbackValue: [],
      },
    );
  }

  /**
   * Fetch repeating bills with standardized error handling
   * @param tenantId Xero tenant ID
   * @param startDate Start date for bills
   * @param endDate End date for bills
   * @returns Repeating bills or empty array on error
   * @private
   */
  private async fetchRepeatingBills(
    tenantId: string,
    startDate: Date,
    endDate: Date,
  ) {
    console.log("Fetching repeating bills...");
    return withErrorHandling(
      () =>
        this.xeroService.repeatingBills.getRepeatingBills(
          tenantId,
          startDate,
          endDate,
        ),
      {
        operationName: "fetchRepeatingBills",
        fallbackValue: [],
      },
    );
  }
}

/**
 * Factory function to get the cashflow service
 * @returns CashflowService instance
 */
export function getCashflowService(): CashflowService {
  return new CashflowService();
}

/**
 * Factory function to get the cashflow snapshot service
 * @returns CashflowSnapshotService instance
 */
export function getCashflowSnapshotService(): CashflowSnapshotService {
  return new CashflowSnapshotService();
}

// Export job initializer functions
export {
  initializeCashflowSnapshotJob,
  getSnapshotJob,
  stopSnapshotJob,
} from "./job-initializer";
