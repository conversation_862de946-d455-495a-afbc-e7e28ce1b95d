import {
  DailyCashflow,
  PotentialTransaction,
  Transaction,
} from "../../types/financial";

/**
 * Service for generating daily cashflow projections
 */
export class DailyCashflowService {
  /**
   * Generate daily cashflow projection from transactions
   *
   * This function performs the core financial calculation for the forecast:
   * 1. Groups transactions by date
   * 2. Calculates daily inflows and outflows
   * 3. Computes running balance for each day
   * 4. Ensures all days in the period have an entry, even with no transactions
   *
   * @param transactions All financial transactions in the period
   * @param startBalance Starting bank balance
   * @param startDate Beginning of the forecast period
   * @param endDate End of the forecast period
   * @returns Array of daily cashflow entries with balances and transactions
   */
  generateDailyCashflow(
    transactions: Transaction[],
    startBalance: number,
    startDate: Date,
    endDate: Date,
  ): DailyCashflow[] {
    const dailyCashflow: DailyCashflow[] = [];
    let currentBalance = startBalance;

    // Create a map of daily net changes
    const dailyChanges = new Map<
      string,
      { inflows: number; outflows: number; transactions: Transaction[] }
    >();

    // Add all transactions to daily changes
    for (const transaction of transactions) {
      // Safeguard against invalid transaction dates
      if (!transaction.date || !(transaction.date instanceof Date)) {
        console.warn("Skipping transaction with invalid date:", transaction);
        continue;
      }

      // Skip transactions outside our date range
      if (transaction.date < startDate || transaction.date > endDate) {
        continue;
      }

      const dateKey = transaction.date.toISOString().split("T")[0];

      if (!dailyChanges.has(dateKey)) {
        dailyChanges.set(dateKey, {
          inflows: 0,
          outflows: 0,
          transactions: [],
        });
      }

      const dayData = dailyChanges.get(dateKey)!;

      // Safeguard against invalid transaction amounts
      const amount =
        typeof transaction.amount === "number" ? transaction.amount : 0;

      if (amount > 0) {
        dayData.inflows += amount;
      } else {
        dayData.outflows += Math.abs(amount);
      }

      dayData.transactions.push(transaction);
    }

    // Generate daily data points
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateKey = currentDate.toISOString().split("T")[0];

      // Get transactions for this date
      const dayData = dailyChanges.get(dateKey) || {
        inflows: 0,
        outflows: 0,
        transactions: [],
      };

      // Calculate net flow
      const netFlow = dayData.inflows - dayData.outflows;
      currentBalance += netFlow;

      // Add to projection
      dailyCashflow.push({
        date: new Date(currentDate),
        inflows: dayData.inflows,
        outflows: dayData.outflows,
        netFlow,
        balance: currentBalance,
        transactions: dayData.transactions,
      });

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dailyCashflow;
  }

  /**
   * Generate multiple scenario projections based on deals
   *
   * @param transactions Confirmed transactions
   * @param potentialTransactions Deal-based potential transactions
   * @param startBalance Starting bank balance
   * @param startDate Beginning of forecast period
   * @param endDate End of forecast period
   * @returns Object containing multiple scenario projections
   */
  generateScenarioProjections(
    transactions: Transaction[],
    potentialTransactions: PotentialTransaction[],
    startBalance: number,
    startDate: Date,
    endDate: Date,
  ): {
    worstCase: DailyCashflow[];
    expectedCase: DailyCashflow[];
    bestCase: DailyCashflow[];
  } {
    // Base projection (worst case) - just the confirmed transactions
    const worstCase = this.generateDailyCashflow(
      transactions,
      startBalance,
      startDate,
      endDate,
    );

    // Expected case - include expected-case transactions and probability-weighted best-case transactions
    const expectedCaseTransactions = [
      ...transactions,
      ...potentialTransactions.filter(
        (t) => t.scenarioType === "expected-case",
      ),
      // Include probability-weighted best-case transactions
      ...potentialTransactions
        .filter((t) => t.scenarioType === "best-case")
        .map((t) => ({
          ...t,
          amount: t.amount * (t.probability / 100), // Weight by probability
        })),
    ];

    const expectedCase = this.generateDailyCashflow(
      expectedCaseTransactions,
      startBalance,
      startDate,
      endDate,
    );

    // Best case - include all potential transactions at full value
    const bestCaseTransactions = [...transactions, ...potentialTransactions];

    const bestCase = this.generateDailyCashflow(
      bestCaseTransactions,
      startBalance,
      startDate,
      endDate,
    );

    return {
      worstCase,
      expectedCase,
      bestCase,
    };
  }
}
