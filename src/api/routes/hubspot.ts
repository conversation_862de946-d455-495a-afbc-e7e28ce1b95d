import express from "express";
import { hubspotService } from "../services/hubspot-service";
import { DealRepository } from "../repositories/deal-repository";
import { ContactRepository } from "../repositories/contact-repository";
import { CompanyRepository } from "../repositories/company-repository";
import { NoteRepository } from "../repositories/note-repository";

// TypeScript interface for error handling
interface ApiError extends Error {
  message: string;
  statusCode?: number;
  [key: string]: unknown;
}

const router = express.Router();
const dealRepository = new DealRepository();
const contactRepository = new ContactRepository();
const companyRepository = new CompanyRepository();
const noteRepository = new NoteRepository();

// Import lock to prevent concurrent imports
let isImportInProgress = false;

/**
 * @route GET /api/hubspot/status
 * @desc Check if HubSpot integration is configured
 * @access Private
 */
router.get('/status", (req, res) => {
  try {
    const accessToken = hubspotService.getAccessToken();
    const isConfigured = !!accessToken;

    res.json({
      success: true,
      data: {
        isConfigured
      }
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error checking HubSpot status:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to check HubSpot status",
      message: apiError.message
    });
  }
});

/**
 * @route POST /api/hubspot/configure
 * @desc Configure HubSpot integration - deprecated
 * @access Private
 * @deprecated Use HUBSPOT_ACCESS_TOKEN environment variable instead
 */
router.post('/configure", (req, res) => {
  res.status(410).json({
    success: false,
    error: "Configuration via API is deprecated",
    message: "Please set HUBSPOT_ACCESS_TOKEN environment variable and restart the application"
  });
});

/**
 * @route DELETE /api/hubspot/configure
 * @desc Remove HubSpot integration - deprecated
 * @access Private
 * @deprecated Use HUBSPOT_ACCESS_TOKEN environment variable instead
 */
router.delete('/configure", (req, res) => {
  res.status(410).json({
    success: false,
    error: "Configuration via API is deprecated",
    message: "Please remove HUBSPOT_ACCESS_TOKEN environment variable and restart the application"
  });
});

/**
 * @route POST /api/hubspot/import/deals
 * @desc Import deals from HubSpot
 * @access Private
 */
router.post('/import/deals", async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: "HubSpot integration not configured"
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: "Failed to initialize HubSpot client"
        });
      }
    }

    // Import deals
    const result = await hubspotService.importDeals();

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: "Failed to import deals from HubSpot",
        message: result.error
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count
      },
      message: `Successfully imported ${result.count} deals from HubSpot`
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error importing deals from HubSpot:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to import deals from HubSpot",
      message: apiError.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/contacts
 * @desc Import contacts from HubSpot
 * @access Private
 */
router.post("/import/contacts", async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: "HubSpot integration not configured"
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: "Failed to initialize HubSpot client"
        });
      }
    }

    // Import contacts
    const result = await hubspotService.importContacts();

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: "Failed to import contacts from HubSpot",
        message: result.error
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count
      },
      message: `Successfully imported ${result.count} contacts from HubSpot`
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error importing contacts from HubSpot:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to import contacts from HubSpot",
      message: apiError.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/companies
 * @desc Import companies from HubSpot
 * @access Private
 */
router.post("/import/companies", async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: "HubSpot integration not configured"
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: "Failed to initialize HubSpot client"
        });
      }
    }

    // Import companies
    const result = await hubspotService.importCompanies();

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: "Failed to import companies from HubSpot",
        message: result.error
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count
      },
      message: `Successfully imported ${result.count} companies from HubSpot`
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error importing companies from HubSpot:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to import companies from HubSpot",
      message: apiError.message
    });
  }
});

/**
 * @route GET /api/hubspot/companies
 * @desc Get all companies from HubSpot for linking purposes
 * @access Private
 */
router.get('/companies", async (req, res) => {
  try {
    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: "HubSpot integration not configured"
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        return res.status(500).json({
          success: false,
          error: "Failed to initialize HubSpot client"
        });
      }
    }

    // Get companies from HubSpot
    const companies = await hubspotService.getCompaniesForLinking();

    res.json({
      success: true,
      data: companies
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting HubSpot companies:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to get HubSpot companies",
      message: apiError.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/quick
 * @desc Import only core entities from HubSpot (companies, deals, contacts - no notes/associations)
 * @access Private
 */
router.post('/import/quick", async (req, res) => {
  try {
    // Check if import is already in progress
    if (isImportInProgress) {
      console.log('Import already in progress, rejecting duplicate request");
      return res.status(409).json({
        success: false,
        error: "Import already in progress",
        message: "Another import is currently running. Please wait for it to complete."
      });
    }

    // Set import lock
    isImportInProgress = true;
    console.log('Starting HubSpot quick import, lock acquired");

    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      isImportInProgress = false; // Release lock
      return res.status(400).json({
        success: false,
        error: "HubSpot integration not configured"
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        isImportInProgress = false; // Release lock
        return res.status(500).json({
          success: false,
          error: "Failed to initialize HubSpot client"
        });
      }
    }

    // Use the new quick import method
    const importResult = await hubspotService.importQuickWithProgress();

    // Determine overall success
    const overallSuccess = importResult.companies.success &&
                          importResult.deals.success &&
                          importResult.contacts.success;

    const totalCount = importResult.companies.count + importResult.deals.count + importResult.contacts.count;

    // Release lock on success
    isImportInProgress = false;
    console.log('HubSpot quick import completed, lock released');

    res.json({
      success: overallSuccess,
      data: {
        totalCount: totalCount,
        results: {
          companies: importResult.companies,
          deals: importResult.deals,
          contacts: importResult.contacts
        }
      },
      message: overallSuccess
        ? `Successfully imported core entities: ${importResult.companies.count} companies, ${importResult.deals.count} deals, ${importResult.contacts.count} contacts`
        : "Import completed with some errors. Check individual results for details."
    });
  } catch (error) {
    // Release lock on error
    isImportInProgress = false;
    console.log('HubSpot quick import failed, lock released');
    
    const apiError = error as ApiError;
    console.error('Error importing core entities from HubSpot:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to import core entities from HubSpot",
      message: apiError.message
    });
  }
});

/**
 * @route POST /api/hubspot/import/all
 * @desc Import all data from HubSpot (companies first, then deals, then contacts)
 * @access Private
 */
router.post('/import/all", async (req, res) => {
  try {
    // Check if import is already in progress
    if (isImportInProgress) {
      console.log('Import already in progress, rejecting duplicate request");
      return res.status(409).json({
        success: false,
        error: "Import already in progress",
        message: "Another import is currently running. Please wait for it to complete."
      });
    }

    // Set import lock
    isImportInProgress = true;
    console.log('Starting HubSpot import, lock acquired");

    // Check if HubSpot is configured
    const accessToken = hubspotService.getAccessToken();

    if (!accessToken) {
      isImportInProgress = false; // Release lock
      return res.status(400).json({
        success: false,
        error: "HubSpot integration not configured"
      });
    }

    // Initialize the HubSpot client if not already initialized
    if (!hubspotService.isInitialized()) {
      const initSuccess = hubspotService.initialize(accessToken);
      if (!initSuccess) {
        isImportInProgress = false; // Release lock
        return res.status(500).json({
          success: false,
          error: "Failed to initialize HubSpot client"
        });
      }
    }

    // Use the new progress tracking method
    const importResult = await hubspotService.importAllWithProgress();

    // Determine overall success
    const overallSuccess = importResult.companies.success &&
                          importResult.deals.success &&
                          importResult.contacts.success;

    const totalCount = importResult.companies.count + importResult.deals.count + importResult.contacts.count;

    // Release lock on success
    isImportInProgress = false;
    console.log('HubSpot import completed, lock released');

    res.json({
      success: overallSuccess,
      data: {
        totalCount: totalCount,
        results: {
          companies: importResult.companies,
          deals: importResult.deals,
          contacts: importResult.contacts,
          notes: importResult.notes,
          associations: importResult.associations
        }
      },
      message: overallSuccess
        ? `Successfully imported all data: ${importResult.companies.count} companies, ${importResult.deals.count} deals, ${importResult.contacts.count} contacts${importResult.notes ? `, ${importResult.notes.count} notes` : ""}${importResult.associations ? `, ${importResult.associations.count} associations` : ""}`
        : "Import completed with some errors. Check individual results for details."
    });
  } catch (error) {
    // Release lock on error
    isImportInProgress = false;
    console.log('HubSpot import failed, lock released');
    
    const apiError = error as ApiError;
    console.error('Error importing all data from HubSpot:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to import all data from HubSpot",
      message: apiError.message
    });
  }
});

/**
 * @route GET /api/hubspot/import/history
 * @desc Get import history
 * @access Private
 */
router.get('/import/history", (req, res) => {
  try {
    const imports = hubspotService.getImportHistory();

    // Transform the data to match the frontend"s HubSpotImport interface
    const transformedImports = imports.map(imp => {
      // Determine status based on success and errors
      let status: "completed" | "completed_with_warnings" | "failed";
      if (imp.success && imp.errors.length === 0) {
        status = 'completed';
      } else if (imp.success && imp.errors.length > 0) {
        // This is a partial success - some items imported but some had errors
        status = 'completed_with_warnings';
      } else if (!imp.success && imp.count === 0) {
        // Complete failure - nothing imported
        status = 'failed';
      } else {
        // Fallback to completed_with_warnings if we somehow have count > 0 but success = false
        status = 'completed_with_warnings';
      }

      // Create a more user-friendly error message
      let errorMessage: string | undefined;
      if (imp.errors.length > 0) {
        const totalImported = imp.count || 0;
        const totalErrors = imp.errors.length;
        if (status === 'failed') {
          errorMessage = `Import failed: ${totalErrors} error${totalErrors !== 1 ? 's' : ""} occurred`;
        } else {
          errorMessage = `Successfully imported ${totalImported} item${totalImported !== 1 ? 's' : ""} with ${totalErrors} exception${totalErrors !== 1 ? 's" : ""}`;
        }
      }

      // Determine import type
      let importType: "quick" | "full" | undefined;
      if (imp.type === 'quick_import') {
        importType = 'quick';
      } else if (imp.type === 'full_import') {
        importType = 'full';
      }

      return {
        id: imp.id,
        importDate: imp.importedAt,
        status,
        importType,
        dealsCount: imp.type === 'deals' ? imp.count : 
                    (imp.type === 'full_import' || imp.type === 'quick_import') && imp.metadata ? imp.metadata.dealsCount :
                    undefined,
        contactsCount: imp.type === 'contacts' ? imp.count :
                       (imp.type === 'full_import' || imp.type === 'quick_import') && imp.metadata ? imp.metadata.contactsCount :
                       undefined,
        companiesCount: imp.type === 'companies' ? imp.count :
                        (imp.type === 'full_import' || imp.type === 'quick_import') && imp.metadata ? imp.metadata.companiesCount :
                        undefined,
        notesCount: imp.type === 'notes' ? imp.count :
                    imp.type === 'full_import' && imp.metadata ? imp.metadata.notesCount :
                    undefined,
        associationsCount: imp.type === 'associations' ? imp.count :
                           imp.type === 'full_import' && imp.metadata ? imp.metadata.associationsCount :
                           undefined,
        errorMessage
      };
    });

    res.json({
      success: true,
      data: transformedImports
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting import history:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to get import history",
      message: apiError.message
    });
  }
});

/**
 * @route DELETE /api/hubspot/reset-crm-data
 * @desc Delete all CRM data (deals, contacts, companies, notes, and associations)
 * @access Private - Development only
 */
router.delete('/reset-crm-data", (req, res) => {
  try {
    // CRITICAL: Only allow in development environment
    if (process.env.NODE_ENV === 'production") {
      return res.status(403).json({
        success: false,
        error: "Forbidden",
        message: "This operation is not allowed in production"
      });
    }
    // Start a transaction
    dealRepository['db'].prepare('BEGIN TRANSACTION').run();

    // First, delete all junction/relationship tables (foreign key constraints)
    
    // Delete contact roles (deal-contact relationships)
    const contactRolesDeleted = dealRepository['db'].prepare('DELETE FROM contact_role').run().changes;

    // Delete deal-estimate relationships if the table exists
    let dealEstimatesDeleted = 0;
    const dealEstimatesTableExists = dealRepository['db'].prepare('SELECT name FROM sqlite_master WHERE type='table' AND name='deal_estimate'').get();
    if (dealEstimatesTableExists) {
      dealEstimatesDeleted = dealRepository['db'].prepare('DELETE FROM deal_estimate').run().changes;
    }
    
    // Delete estimate time allocations first (depends on estimate_allocation)
    let estimateTimeAllocationsDeleted = 0;
    const estimateTimeAllocationTableExists = dealRepository['db'].prepare('SELECT name FROM sqlite_master WHERE type='table' AND name='estimate_time_allocation'').get();
    if (estimateTimeAllocationTableExists) {
      estimateTimeAllocationsDeleted = dealRepository['db'].prepare('DELETE FROM estimate_time_allocation').run().changes;
    }
    
    // Delete estimate allocations (depends on estimate)
    let estimateAllocationsDeleted = 0;
    const estimateAllocationTableExists = dealRepository['db'].prepare('SELECT name FROM sqlite_master WHERE type='table' AND name='estimate_allocation'').get();
    if (estimateAllocationTableExists) {
      estimateAllocationsDeleted = dealRepository['db'].prepare('DELETE FROM estimate_allocation').run().changes;
    }

    // Delete contact-company relationships if the table exists
    let contactCompaniesDeleted = 0;
    const contactCompanyTableExists = dealRepository['db'].prepare('SELECT name FROM sqlite_master WHERE type='table' AND name='contact_company'').get();
    if (contactCompanyTableExists) {
      contactCompaniesDeleted = dealRepository['db'].prepare('DELETE FROM contact_company').run().changes;
    }

    // Delete company relationships if the table exists
    let companyRelationshipsDeleted = 0;
    const companyRelationshipTableExists = dealRepository['db'].prepare('SELECT name FROM sqlite_master WHERE type='table' AND name='company_relationship'').get();
    if (companyRelationshipTableExists) {
      companyRelationshipsDeleted = dealRepository['db'].prepare('DELETE FROM company_relationship').run().changes;
    }

    // Now delete dependent records in the correct order
    
    // Delete all notes (depends on deals)
    const notesDeleted = dealRepository['db'].prepare('DELETE FROM note').run().changes;

    // Delete all deals (depends on companies)
    const dealsDeleted = dealRepository['db'].prepare('DELETE FROM deal').run().changes;

    // Delete all estimates (depends on companies)
    let estimatesDeleted = 0;
    const estimateTableExists = dealRepository['db'].prepare('SELECT name FROM sqlite_master WHERE type='table' AND name='estimate'').get();
    if (estimateTableExists) {
      estimatesDeleted = dealRepository['db'].prepare('DELETE FROM estimate').run().changes;
    }

    // Delete all contacts (no foreign key to company, despite the contact_company junction table)
    const contactsDeleted = dealRepository['db'].prepare('DELETE FROM contact').run().changes;

    // Finally, delete all companies (no more dependencies after deals and estimates are deleted)
    const companiesDeleted = dealRepository['db'].prepare('DELETE FROM company').run().changes;

    // Commit the transaction
    dealRepository['db'].prepare('COMMIT').run();

    console.log(`Successfully deleted all CRM data: ${dealsDeleted} deals, ${contactsDeleted} contacts, ${companiesDeleted} companies, ${notesDeleted} notes, ${estimatesDeleted} estimates`);

    res.json({
      success: true,
      data: {
        companiesDeleted,
        dealsDeleted,
        contactsDeleted,
        notesDeleted,
        estimatesDeleted,
        contactRolesDeleted,
        dealEstimatesDeleted,
        contactCompaniesDeleted,
        companyRelationshipsDeleted,
        estimateAllocationsDeleted,
        estimateTimeAllocationsDeleted
      },
      message: `Successfully deleted all CRM data: ${companiesDeleted} companies, ${dealsDeleted} deals, ${contactsDeleted} contacts, ${estimatesDeleted} estimates`
    });
  } catch (error) {
    const apiError = error as ApiError;
    // Rollback the transaction on error
    try {
      dealRepository['db'].prepare('ROLLBACK').run();
    } catch (rollbackError) {
      console.error('Error rolling back transaction:", rollbackError);
    }

    console.error('Error deleting CRM data:", apiError);
    res.status(500).json({
      success: false,
      error: "Failed to delete CRM data",
      message: apiError.message
    });
  }
});

export default router;
