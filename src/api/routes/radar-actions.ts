/**
 * Radar Actions API Routes
 * 
 * Handles CRUD operations for radar action items (Question Marks section)
 */

import { Router, Request, Response } from "express";
import { RadarActionRepository } from "../repositories/radar-action-repository";
import { v4 as uuidv4 } from "uuid";
import type { CreateRadarAction, UpdateRadarAction, RadarActionFilters } from "../../types/radar-action-types";

const router = Router();
const radarActionRepo = new RadarActionRepository();

/**
 * Get all radar actions with optional filters
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const filters: RadarActionFilters = {};

    // Parse query parameters
    if (req.query.companyId) filters.companyId = req.query.companyId as string;
    if (req.query.assignedTo) filters.assignedTo = req.query.assignedTo as string;
    if (req.query.status) {
      filters.status = Array.isArray(req.query.status) 
        ? req.query.status as string[]
        : req.query.status as string;
    }
    if (req.query.priority) {
      filters.priority = Array.isArray(req.query.priority)
        ? req.query.priority as string[]
        : req.query.priority as string;
    }
    if (req.query.actionType) {
      filters.actionType = Array.isArray(req.query.actionType)
        ? req.query.actionType as string[]
        : req.query.actionType as string;
    }
    if (req.query.dueBefore) filters.dueBefore = req.query.dueBefore as string;
    if (req.query.dueAfter) filters.dueAfter = req.query.dueAfter as string;
    if (req.query.includeCompleted) filters.includeCompleted = req.query.includeCompleted === 'true';

    const actions = await radarActionRepo.getActions(filters);
    res.json({ actions });
  } catch (error) {
    console.error('Error fetching radar actions:', error);
    res.status(500).json({ error: 'Failed to fetch radar actions' });
  }
});

/**
 * Get actions for a specific company
 */
router.get('/company/:companyId', async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;
    const includeCompleted = req.query.includeCompleted === 'true';
    
    const actions = await radarActionRepo.getActionsForCompany(companyId, includeCompleted);
    res.json({ actions });
  } catch (error) {
    console.error('Error fetching company radar actions:', error);
    res.status(500).json({ error: 'Failed to fetch company radar actions' });
  }
});

/**
 * Get actions assigned to a specific user
 */
router.get('/user/:assignedTo', async (req: Request, res: Response) => {
  try {
    const { assignedTo } = req.params;
    const includeCompleted = req.query.includeCompleted === 'true';
    
    const actions = await radarActionRepo.getActionsForUser(assignedTo, includeCompleted);
    res.json({ actions });
  } catch (error) {
    console.error('Error fetching user radar actions:', error);
    res.status(500).json({ error: 'Failed to fetch user radar actions' });
  }
});

/**
 * Get overdue actions
 */
router.get('/overdue', async (req: Request, res: Response) => {
  try {
    const actions = await radarActionRepo.getOverdueActions();
    res.json({ actions });
  } catch (error) {
    console.error('Error fetching overdue radar actions:', error);
    res.status(500).json({ error: 'Failed to fetch overdue radar actions' });
  }
});

/**
 * Get action statistics
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const statusCounts = await radarActionRepo.countActionsByStatus();
    res.json({ statusCounts });
  } catch (error) {
    console.error('Error fetching radar action stats:', error);
    res.status(500).json({ error: 'Failed to fetch radar action statistics' });
  }
});

/**
 * Get single radar action by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const includeCompany = req.query.includeCompany === 'true';
    
    const action = await radarActionRepo.getActionById(id, includeCompany);
    
    if (!action) {
      return res.status(404).json({ error: 'Radar action not found' });
    }
    
    res.json({ action });
  } catch (error) {
    console.error('Error fetching radar action:', error);
    res.status(500).json({ error: 'Failed to fetch radar action' });
  }
});

/**
 * Create a new radar action
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const userId = req.user?.email || 'system';
    const data: CreateRadarAction = {
      ...req.body,
      createdBy: userId
    };

    // Validate required fields
    if (!data.companyId || !data.actionType || !data.title || !data.assignedTo) {
      return res.status(400).json({ 
        error: 'Missing required fields: companyId, actionType, title, assignedTo' 
      });
    }

    // Create the action
    const action = await radarActionRepo.createAction(data);

    res.status(201).json({ action });
  } catch (error) {
    console.error('Error creating radar action:', error);
    res.status(500).json({ error: 'Failed to create radar action' });
  }
});

/**
 * Update a radar action
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.email || 'system';
    const data: UpdateRadarAction = {
      ...req.body,
      updatedBy: userId
    };

    const action = await radarActionRepo.updateAction(id, data);
    
    if (!action) {
      return res.status(404).json({ error: 'Radar action not found' });
    }

    res.json({ action });
  } catch (error) {
    console.error('Error updating radar action:', error);
    res.status(500).json({ error: 'Failed to update radar action' });
  }
});

/**
 * Complete a radar action
 */
router.post('/:id/complete', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.email || 'system';
    const { completionNotes } = req.body;

    const action = await radarActionRepo.completeAction(id, completionNotes || '', userId);
    
    if (!action) {
      return res.status(404).json({ error: 'Radar action not found' });
    }

    res.json({ action });
  } catch (error) {
    console.error('Error completing radar action:', error);
    res.status(500).json({ error: 'Failed to complete radar action' });
  }
});

/**
 * Cancel a radar action
 */
router.post('/:id/cancel', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.email || 'system';
    const { notes } = req.body;

    const action = await radarActionRepo.cancelAction(id, notes || '', userId);
    
    if (!action) {
      return res.status(404).json({ error: 'Radar action not found' });
    }

    res.json({ action });
  } catch (error) {
    console.error('Error cancelling radar action:', error);
    res.status(500).json({ error: 'Failed to cancel radar action' });
  }
});

/**
 * Delete a radar action
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.email || 'system';
    
    const success = await radarActionRepo.deleteAction(id, userId);
    
    if (!success) {
      return res.status(404).json({ error: 'Radar action not found' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting radar action:', error);
    res.status(500).json({ error: 'Failed to delete radar action' });
  }
});

export default router;