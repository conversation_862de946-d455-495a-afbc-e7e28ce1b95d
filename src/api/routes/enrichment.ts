/**
 * Enrichment API Routes
 *
 * Endpoints for triggering and managing data enrichment
 */

import express from "express";
import { enrichmentService } from "../services/enrichment/enrichment-service";
import { CompanyRepository } from "../repositories/company-repository";
import { ContactRepository } from "../repositories/contact-repository";
import { EnrichmentRepository } from "../repositories/enrichment-repository";

const router = express.Router();
const companyRepository = new CompanyRepository();
const contactRepository = new ContactRepository();
const enrichmentRepository = new EnrichmentRepository();

/**
 * Enrich a company
 * POST /api/enrichment/company/:id
 */
router.post("/company/:id", async (req, res, next) => {
  try {
    const { id } = req.params;
    const { sources } = req.body; // Optional array of sources to use

    // Get the company
    const company = companyRepository.getCompanyById(id);
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }

    // Log the enrichment attempt
    const startTime = Date.now();

    // Trigger enrichment
    const results = await enrichmentService.enrichCompany(company, sources);

    // Log enrichment results
    for (const result of results) {
      await enrichmentRepository.logEnrichmentAttempt({
        entityType: "company",
        entityId: id,
        source: result.source,
        status: result.success ? "success" : "failed",
        errorMessage: result.error,
        responseTimeMs: Date.now() - startTime,
        apiCreditsUsed: 0, // Update when we have paid APIs
      });
    }

    // Get updated company with enrichment data
    const enrichedCompany = companyRepository.getCompanyById(id);
    const enrichmentData = await enrichmentService.getCompanyEnrichment(id);

    res.json({
      company: enrichedCompany,
      enrichment: enrichmentData,
      results,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get enrichment data for a company
 * GET /api/enrichment/company/:id
 */
router.get("/company/:id", async (req, res, next) => {
  try {
    const { id } = req.params;
    const { source } = req.query;

    const enrichmentData = await enrichmentService.getCompanyEnrichment(
      id,
      source as any,
    );

    res.json({ enrichment: enrichmentData });
  } catch (error) {
    next(error);
  }
});

/**
 * Check if a company needs enrichment
 * GET /api/enrichment/company/:id/check
 */
router.get("/company/:id/check", async (req, res, next) => {
  try {
    const { id } = req.params;

    const company = companyRepository.getCompanyById(id);
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }

    const needsEnrichment =
      await enrichmentService.companyNeedsEnrichment(company);

    res.json({
      needsEnrichment,
      lastEnrichedAt: company.lastEnrichedAt,
      enrichmentStatus: company.enrichmentStatus,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get companies that need enrichment
 * GET /api/enrichment/companies/pending
 */
router.get("/companies/pending", async (req, res, next) => {
  try {
    const { limit = 100 } = req.query;

    const companies = await enrichmentRepository.getCompaniesNeedingEnrichment(
      parseInt(limit as string),
    );

    res.json({ companies });
  } catch (error) {
    next(error);
  }
});

/**
 * Enrich a contact (placeholder for future implementation)
 * POST /api/enrichment/contact/:id
 */
router.post("/contact/:id", async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get the contact
    const contact = contactRepository.getContactById(id);
    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // TODO: Implement contact enrichment
    res.status(501).json({
      error: "Contact enrichment not yet implemented",
      message: "This feature is coming soon",
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Get enrichment statistics
 * GET /api/enrichment/stats
 */
router.get("/stats", async (req, res, next) => {
  try {
    // Get enrichment statistics from the database
    const stats = (await enrichmentRepository.db
      .prepare(
        `
      SELECT 
        COUNT(DISTINCT company_id) as enrichedCompanies,
        COUNT(*) as totalEnrichments,
        AVG(confidence_score) as avgConfidence,
        MAX(enriched_at) as lastEnrichmentAt
      FROM company_enrichment
    `,
      )
      .get()) as any;

    const sourceStats = (await enrichmentRepository.db
      .prepare(
        `
      SELECT 
        source,
        COUNT(*) as count,
        AVG(confidence_score) as avgConfidence
      FROM company_enrichment
      GROUP BY source
    `,
      )
      .all()) as any[];

    res.json({
      overview: stats,
      bySource: sourceStats,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Clean up expired enrichment data
 * POST /api/enrichment/cleanup
 */
router.post("/cleanup", async (req, res, next) => {
  try {
    const deletedCount = await enrichmentRepository.deleteExpiredEnrichments();

    res.json({
      success: true,
      deletedCount,
      message: `Cleaned up ${deletedCount} expired enrichment records`,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
