/**
 * Conversation threading routes
 */

import express from "express";
import { NoteRepository } from "../../repositories/note-repository";

// TypeScript interfaces
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

const router = express.Router();
const noteRepo = new NoteRepository();

/**
 * @route GET /api/crm/notes/threads
 * @desc Get conversation threads for an entity
 * @access Private
 */
router.get('/threads', async (req, res) => {
  try {
    const { entityType, entityId } = req.query;

    if (!entityType || !entityId) {
      return res.status(400).json({
        success: false,
        error: "Missing required parameters: entityType and entityId"
      });
    }

    const threads = noteRepo.getConversationThreads(
      entityType as string,
      entityId as string
    );

    res.json({
      success: true,
      data: threads
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching conversation threads:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch conversation threads",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm/notes/thread/:threadId
 * @desc Get notes in a specific thread
 * @access Private
 */
router.get('/thread/:threadId', async (req, res) => {
  try {
    const { threadId } = req.params;

    const notes = noteRepo.getThreadNotes(threadId);

    res.json({
      success: true,
      data: notes
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching thread notes:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch thread notes",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm/notes
 * @desc Create a new note (with threading support)
 * @access Private
 */
router.post('/', async (req, res) => {
  try {
    const {
      dealId,
      content,
      parentNoteId,
      threadId,
      participants,
      conversationType,
      status,
      createdBy
    } = req.body;

    if (!dealId || !content) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields: dealId and content"
      });
    }

    const note = noteRepo.createThreadedNote({
      dealId,
      content,
      parentNoteId,
      threadId,
      participants,
      conversationType,
      status,
      createdBy: createdBy || req.user?.email || "system"
    });

    res.status(201).json({
      success: true,
      data: note
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating threaded note:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create note",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PATCH /api/crm/notes/:id/status
 * @desc Update note status
 * @access Private
 */
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: "Missing required field: status"
      });
    }

    const validStatuses = ['open', 'resolved', 'parked', 'archived'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      });
    }

    const note = noteRepo.updateNoteStatus(id, status);

    if (!note) {
      return res.status(404).json({
        success: false,
        error: "Note not found"
      });
    }

    res.json({
      success: true,
      data: note
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating note status:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update note status",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

export default router;