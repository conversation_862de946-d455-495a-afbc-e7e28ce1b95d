import express from "express";
import { ContactRepository } from "../../repositories/contact-repository";
import {
  ContactCreate,
  ContactUpdate,
} from "../../../frontend/types/crm-types";
import activityLogger from "../../../utils/activity-logger";

// TypeScript interfaces for this file
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

const router = express.Router();
const contactRepository = new ContactRepository();

/**
 * @route GET /api/crm/contacts
 * @desc Get all contacts
 * @access Private
 */
router.get("/", async (req, res) => {
  try {
    const contacts = contactRepository.getAllContacts();
    res.json({
      success: true,
      data: contacts,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting contacts:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contacts",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route GET /api/crm/contacts/:id
 * @desc Get a contact by ID
 * @access Private
 */
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const contact = contactRepository.getContactById(id);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found",
      });
    }

    res.json({
      success: true,
      data: contact,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting contact:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contact",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/contacts
 * @desc Create a new contact
 * @access Private
 */
router.post("/", async (req, res) => {
  try {
    const contactData: ContactCreate = req.body;

    // Validate required fields
    if (!contactData.firstName || !contactData.lastName) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        requiredFields: ["firstName", "lastName"],
      });
    }

    const contact = contactRepository.createContact(contactData);

    if (!contact) {
      return res.status(500).json({
        success: false,
        error: "Failed to create contact",
      });
    }

    // Log contact creation activity
    try {
      const contactName = `${contact.firstName} ${contact.lastName}`;
      await activityLogger.logContactCreated(
        contact.id,
        contactName,
        req.session?.userInfo?.sub || "unknown-user",
      );
    } catch (activityError) {
      console.error("Error logging contact creation activity:", activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: contact,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error creating contact:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create contact",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route PUT /api/crm/contacts/:id
 * @desc Update a contact
 * @access Private
 */
router.put("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const contactData: ContactUpdate = req.body;

    const contact = contactRepository.updateContact(id, contactData);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found",
      });
    }

    res.json({
      success: true,
      data: contact,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error updating contact:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update contact",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route DELETE /api/crm/contacts/:id
 * @desc Delete a contact
 * @access Private
 */
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const success = contactRepository.deleteContact(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact not found",
      });
    }

    res.json({
      success: true,
      message: "Contact deleted successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error deleting contact:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to delete contact",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/contacts/:contactId/companies
 * @desc Link a contact to a company
 * @access Private
 */
router.post("/:contactId/companies", async (req, res) => {
  try {
    const { contactId } = req.params;
    const { companyId, role, isPrimary } = req.body;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: "Company ID is required",
      });
    }

    // Check if contact exists
    const contact = contactRepository.getContactById(contactId);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found",
      });
    }

    // Add the relationship
    const now = new Date().toISOString();
    const db = contactRepository.db;

    try {
      // If isPrimary is true, unset any existing primary company
      if (isPrimary) {
        db.prepare(
          `
          UPDATE contact_company 
          SET is_primary = 0 
          WHERE contact_id = ?
        `,
        ).run(contactId);
      }

      // Check if relationship already exists
      const existing = db
        .prepare(
          `
        SELECT id FROM contact_company 
        WHERE contact_id = ? AND company_id = ?
      `,
        )
        .get(contactId, companyId);

      if (existing) {
        // Update existing relationship
        db.prepare(
          `
          UPDATE contact_company 
          SET role = ?, is_primary = ?, updated_at = ?, updated_by = ?
          WHERE contact_id = ? AND company_id = ?
        `,
        ).run(
          role || "user",
          isPrimary ? 1 : 0,
          now,
          req.session?.userInfo?.sub || "system",
          contactId,
          companyId,
        );
      } else {
        // Insert new relationship
        db.prepare(
          `
          INSERT INTO contact_company (contact_id, company_id, role, is_primary, created_at, created_by)
          VALUES (?, ?, ?, ?, ?, ?)
        `,
        ).run(
          contactId,
          companyId,
          role || "user",
          isPrimary ? 1 : 0,
          now,
          req.session?.userInfo?.sub || "system",
        );
      }

      res.json({
        success: true,
        message: "Contact linked to company successfully",
      });
    } catch (dbError) {
      console.error("Database error linking contact to company:", dbError);
      res.status(500).json({
        success: false,
        error: "Failed to link contact to company",
        message: dbError.message,
      });
    }
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error linking contact to company:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to link contact to company",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route DELETE /api/crm/contacts/:contactId/companies/:companyId
 * @desc Unlink a contact from a company
 * @access Private
 */
router.delete("/:contactId/companies/:companyId", async (req, res) => {
  try {
    const { contactId, companyId } = req.params;

    // Check if contact exists
    const contact = contactRepository.getContactById(contactId);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found",
      });
    }

    // Remove the relationship
    const db = contactRepository.db;
    const result = db
      .prepare(
        `
      DELETE FROM contact_company 
      WHERE contact_id = ? AND company_id = ?
    `,
      )
      .run(contactId, companyId);

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        error: "Relationship not found",
      });
    }

    res.json({
      success: true,
      message: "Contact unlinked from company successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error unlinking contact from company:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to unlink contact from company",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route GET /api/crm/contacts/:contactId/relationships
 * @desc Get contact relationships
 * @access Private
 */
router.get("/:contactId/relationships", async (req, res) => {
  try {
    const { contactId } = req.params;

    // Check if contact exists
    const contact = contactRepository.getContactById(contactId);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found",
      });
    }

    // Get relationships from contact_relationships table
    const db = contactRepository.db;
    const relationships = db
      .prepare(
        `
      SELECT 
        cr.id,
        cr.source_contact_id as sourceContactId,
        cr.target_contact_id as targetContactId,
        cr.relationship_type as relationshipType,
        cr.strength,
        cr.context,
        -- Source contact details
        sc.id as sourceId,
        sc.first_name as sourceFirstName,
        sc.last_name as sourceLastName,
        sc.email as sourceEmail,
        -- Target contact details
        tc.id as targetId,
        tc.first_name as targetFirstName,
        tc.last_name as targetLastName,
        tc.email as targetEmail
      FROM contact_relationships cr
      LEFT JOIN contact sc ON cr.source_contact_id = sc.id
      LEFT JOIN contact tc ON cr.target_contact_id = tc.id
      WHERE cr.source_contact_id = ? OR cr.target_contact_id = ?
    `,
      )
      .all(contactId, contactId);

    // Format the relationships
    const formattedRelationships = relationships.map((rel) => ({
      id: rel.id,
      sourceContactId: rel.sourceContactId,
      targetContactId: rel.targetContactId,
      relationshipType: rel.relationshipType,
      strength: rel.strength,
      context: rel.context,
      sourceContact: {
        id: rel.sourceId,
        firstName: rel.sourceFirstName,
        lastName: rel.sourceLastName,
        email: rel.sourceEmail,
      },
      targetContact: {
        id: rel.targetId,
        firstName: rel.targetFirstName,
        lastName: rel.targetLastName,
        email: rel.targetEmail,
      },
    }));

    res.json({
      success: true,
      data: formattedRelationships,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting contact relationships:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contact relationships",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/contacts/relationships
 * @desc Create a contact relationship
 * @access Private
 */
router.post("/relationships", async (req, res) => {
  try {
    const {
      sourceContactId,
      targetContactId,
      relationshipType,
      strength,
      context,
    } = req.body;

    if (!sourceContactId || !targetContactId || !relationshipType) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        requiredFields: [
          "sourceContactId",
          "targetContactId",
          "relationshipType",
        ],
      });
    }

    // Check if both contacts exist
    const sourceContact = contactRepository.getContactById(sourceContactId);
    const targetContact = contactRepository.getContactById(targetContactId);

    if (!sourceContact || !targetContact) {
      return res.status(404).json({
        success: false,
        error: "One or both contacts not found",
      });
    }

    const db = contactRepository.db;
    const now = new Date().toISOString();
    const id = require("uuid").v4();

    // Insert the relationship
    db.prepare(
      `
      INSERT INTO contact_relationships (
        id,
        source_contact_id,
        target_contact_id,
        relationship_type,
        strength,
        context,
        created_at,
        created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `,
    ).run(
      id,
      sourceContactId,
      targetContactId,
      relationshipType,
      strength || 3,
      context || null,
      now,
      req.session?.userInfo?.sub || "system",
    );

    res.status(201).json({
      success: true,
      data: { id },
      message: "Contact relationship created successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error creating contact relationship:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create contact relationship",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route DELETE /api/crm/contacts/relationships/:id
 * @desc Delete a contact relationship
 * @access Private
 */
router.delete("/relationships/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const db = contactRepository.db;
    const result = db
      .prepare(
        `
      DELETE FROM contact_relationships 
      WHERE id = ?
    `,
      )
      .run(id);

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        error: "Relationship not found",
      });
    }

    res.json({
      success: true,
      message: "Contact relationship deleted successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error deleting contact relationship:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to delete contact relationship",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

export default router;
