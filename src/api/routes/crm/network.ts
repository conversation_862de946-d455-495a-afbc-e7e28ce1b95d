/**
 * Network and relationship visualization routes
 */

import express from "express";
import { ContactRelationshipsRepository } from "../../repositories/contact-relationships-repository";
import { TeamCoverageRepository } from "../../repositories/team-coverage-repository";
import { ContactRepository } from "../../repositories/contact-repository";
import { CompanyRepository } from "../../repositories/company-repository";
import { DealRepository } from "../../repositories/deal-repository";
import { getHarvestService } from "../../../services/harvest";

// TypeScript interfaces
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

const router = express.Router();
const contactRelationshipsRepo = new ContactRelationshipsRepository();
const teamCoverageRepo = new TeamCoverageRepository();
const contactRepo = new ContactRepository();
const companyRepo = new CompanyRepository();
const dealRepo = new DealRepository();

/**
 * @route GET /api/crm/network/relationships/:entityId
 * @desc Get relationship network for a contact or company
 * @access Private
 */
router.get('/relationships/:entityId', async (req, res) => {
  try {
    const { entityId } = req.params;
    const { type = 'contact', depth = '1' } = req.query;

    // Validate entity type
    if (type !== 'contact' && type !== 'company') {
      return res.status(400).json({
        success: false,
        error: "Invalid entity type. Must be "contact" or "company''
      });
    }

    // Validate depth
    const depthNum = parseInt(depth as string, 10);
    if (isNaN(depthNum) || depthNum < 1 || depthNum > 3) {
      return res.status(400).json({
        success: false,
        error: "Invalid depth. Must be between 1 and 3"
      });
    }

    // Verify entity exists
    if (type === 'contact') {
      const contact = contactRepo.getContactById(entityId);
      if (!contact) {
        return res.status(404).json({
          success: false,
          error: "Contact not found"
        });
      }
    } else {
      const company = companyRepo.getCompanyById(entityId);
      if (!company) {
        return res.status(404).json({
          success: false,
          error: "Company not found"
        });
      }
    }

    // Get relationship network
    const network = contactRelationshipsRepo.getRelationshipNetwork(
      entityId,
      type as 'contact' | "company",
      depthNum
    );

    // Transform nodes to have label property for visualization
    const transformedNetwork = {
      nodes: network.nodes.map(node => ({
        id: node.id,
        label: node.name, // Map name to label
        type: node.type,
        level: 0, // Will be calculated by the frontend
        email: node.email,
        company: node.company,
        group: node.group
      })),
      links: network.links.map(link => ({
        source: link.source,
        target: link.target,
        type: link.type,
        strength: link.strength,
        label: link.type.replace('_', ' '), // Make type more readable
        context: link.context
      }))
    };

    res.json({
      success: true,
      data: transformedNetwork
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching relationship network:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch relationship network",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm/network/contacts/:id/relationships
 * @desc Get all relationships for a specific contact
 * @access Private
 */
router.get('/contacts/:id/relationships', async (req, res) => {
  try {
    const { id } = req.params;

    // Verify contact exists
    const contact = contactRepo.getContactById(id);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found"
      });
    }

    const relationships = contactRelationshipsRepo.getContactRelationships(id);

    res.json({
      success: true,
      data: relationships
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching contact relationships:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch contact relationships",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm/network/contacts/relationships
 * @desc Create a new contact relationship
 * @access Private
 */
router.post('/contacts/relationships', async (req, res) => {
  try {
    const { 
      sourceContactId, 
      targetContactId, 
      relationshipType, 
      strength, 
      context 
    } = req.body;

    // Validate required fields
    if (!sourceContactId || !targetContactId || !relationshipType) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields: sourceContactId, targetContactId, relationshipType"
      });
    }

    // Validate relationship type
    const validTypes = ['knows', 'reports_to', 'introduced_by', 'worked_with', 'colleague'];
    if (!validTypes.includes(relationshipType)) {
      return res.status(400).json({
        success: false,
        error: `Invalid relationship type. Must be one of: ${validTypes.join(', ')}`
      });
    }

    // Validate strength if provided
    if (strength !== undefined && (strength < 1 || strength > 5)) {
      return res.status(400).json({
        success: false,
        error: "Strength must be between 1 and 5"
      });
    }

    // Verify both contacts exist
    const sourceContact = contactRepo.getContactById(sourceContactId);
    const targetContact = contactRepo.getContactById(targetContactId);

    if (!sourceContact || !targetContact) {
      return res.status(404).json({
        success: false,
        error: "One or both contacts not found"
      });
    }

    // Create the relationship
    const relationship = contactRelationshipsRepo.createRelationship({
      sourceContactId,
      targetContactId,
      relationshipType,
      strength,
      context,
      createdBy: req.user?.email || "system"
    });

    res.status(201).json({
      success: true,
      data: relationship
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating contact relationship:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create contact relationship",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PUT /api/crm/network/contacts/relationships/:id
 * @desc Update a contact relationship
 * @access Private
 */
router.put('/contacts/relationships/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { strength, context } = req.body;

    const updated = contactRelationshipsRepo.updateRelationship(id, {
      strength,
      context,
      updatedBy: req.user?.email || "system"
    });

    if (!updated) {
      return res.status(404).json({
        success: false,
        error: "Relationship not found"
      });
    }

    res.json({
      success: true,
      data: updated
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating contact relationship:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update contact relationship",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route DELETE /api/crm/network/contacts/relationships/:id
 * @desc Delete a contact relationship
 * @access Private
 */
router.delete('/contacts/relationships/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const deleted = contactRelationshipsRepo.deleteRelationship(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: "Relationship not found"
      });
    }

    res.json({
      success: true,
      message: "Relationship deleted successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error deleting contact relationship:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to delete contact relationship",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm/network/team/coverage/:companyId
 * @desc Get team coverage for a company
 * @access Private
 */
router.get('/team/coverage/:companyId', async (req, res) => {
  try {
    const { companyId } = req.params;

    const coverage = teamCoverageRepo.getCompanyCoverage(companyId);

    if (!coverage) {
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    res.json({
      success: true,
      data: coverage
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching company coverage:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch company coverage",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm/network/team/stale-relationships
 * @desc Get relationships that haven't been touched in 30+ days
 * @access Private
 */
router.get('/team/stale-relationships', async (req, res) => {
  try {
    const { days = '30' } = req.query;
    const daysNum = parseInt(days as string, 10);

    if (isNaN(daysNum) || daysNum < 1) {
      return res.status(400).json({
        success: false,
        error: "Invalid days parameter"
      });
    }

    const staleRelationships = teamCoverageRepo.getStaleRelationships(daysNum);

    res.json({
      success: true,
      data: staleRelationships
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching stale relationships:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch stale relationships",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm/network/team/coverage
 * @desc Create or update team coverage for a contact
 * @access Private
 */
router.post('/team/coverage', async (req, res) => {
  try {
    const {
      contactId,
      teamMemberId,
      relationshipStrength,
      lastInteractionDate,
      lastInteractionType,
      notes
    } = req.body;

    // Validate required fields
    if (!contactId || !teamMemberId || !relationshipStrength) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields: contactId, teamMemberId, relationshipStrength"
      });
    }

    // Validate relationship strength
    const validStrengths = ['primary', 'secondary', 'minimal'];
    if (!validStrengths.includes(relationshipStrength)) {
      return res.status(400).json({
        success: false,
        error: `Invalid relationship strength. Must be one of: ${validStrengths.join(', ')}`
      });
    }

    // Verify contact exists
    const contact = contactRepo.getContactById(contactId);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found"
      });
    }

    const coverage = teamCoverageRepo.upsertCoverage({
      contactId,
      teamMemberId,
      relationshipStrength,
      lastInteractionDate,
      lastInteractionType,
      notes
    });

    res.json({
      success: true,
      data: coverage
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating/updating team coverage:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create/update team coverage",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm/network/contacts/:id/project-history
 * @desc Get project history for a contact based on Harvest time entries
 * @access Private
 */
router.get('/contacts/:id/project-history', async (req, res) => {
  try {
    const { id } = req.params;

    // Get contact to check for harvest_user_id
    const contact = contactRepo.getContactById(id);
    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found"
      });
    }

    // Check if contact has a Harvest user ID
    if (!contact.harvestUserId) {
      return res.json({
        success: true,
        data: {
          projects: [],
          message: "Contact is not linked to a Harvest user"
        }
      });
    }

    // Get time entries for this user from Harvest
    // We'll get the last 12 months of data
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 12);

    // Get time entries for this user from Harvest
    const harvestService = getHarvestService();
    const timeEntries = await harvestService.getClient().getTimeEntries({
      from: startDate.toISOString().split('T')[0],
      to: endDate.toISOString().split('T')[0],
      user_id: contact.harvestUserId
    });

    // Group by project and calculate summary
    const projectMap = new Map();

    timeEntries.forEach(entry => {
      const projectId = entry.project.id.toString();
      
      if (!projectMap.has(projectId)) {
        projectMap.set(projectId, {
          projectId,
          projectName: entry.project.name,
          clientName: entry.client.name,
          totalHours: 0,
          firstEntry: entry.spent_date,
          lastEntry: entry.spent_date,
          tasks: new Set(),
          entries: []
        });
      }

      const project = projectMap.get(projectId);
      project.totalHours += entry.hours;
      project.tasks.add(entry.task.name);
      project.entries.push({
        date: entry.spent_date,
        hours: entry.hours,
        task: entry.task.name,
        notes: entry.notes
      });

      // Update date range
      if (entry.spent_date < project.firstEntry) {
        project.firstEntry = entry.spent_date;
      }
      if (entry.spent_date > project.lastEntry) {
        project.lastEntry = entry.spent_date;
      }
    });

    // Convert to array and format
    const projects = Array.from(projectMap.values()).map(project => ({
      ...project,
      tasks: Array.from(project.tasks),
      entries: project.entries.sort((a: any, b: any) => 
        new Date(b.date).getTime() - new Date(a.date).getTime()
      )
    }));

    // Sort projects by most recent activity
    projects.sort((a, b) => 
      new Date(b.lastEntry).getTime() - new Date(a.lastEntry).getTime()
    );

    res.json({
      success: true,
      data: {
        contactId: id,
        harvestUserId: contact.harvestUserId,
        projects,
        totalProjects: projects.length,
        dateRange: {
          from: startDate.toISOString().split('T')[0],
          to: endDate.toISOString().split('T')[0]
        }
      }
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching contact project history:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch project history",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm/network/opportunities/:companyId
 * @desc Get opportunity intelligence for a company
 * @access Private
 */
router.get('/opportunities/:companyId', async (req, res) => {
  try {
    const { companyId } = req.params;
    
    // Get company details
    const company = companyRepo.getById(companyId);
    if (!company) {
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    const opportunities: any[] = [];

    // 1. Check for low engagement (no activity in 60+ days)
    // This would need activity tracking which we don't have yet, so skip for now

    // 2. Check for team coverage gaps
    const coverage = teamCoverageRepo.getCompanyCoverage(companyId);
    if (coverage && coverage.riskLevel === 'high') {
      opportunities.push({
        id: `coverage-gap-${companyId}`,
        companyId,
        type: "risk",
        title: "High Coverage Risk",
        description: `Only ${coverage.coveredRoles} out of ${coverage.totalRoles} key roles have team coverage. Consider assigning team members to uncovered roles.`,
        score: 85,
        metadata: {
          coverageGap: Math.round((1 - coverage.coveredRoles / coverage.totalRoles) * 100),
          uncoveredRoles: coverage.totalRoles - coverage.coveredRoles
        },
        detectedAt: new Date().toISOString()
      });
    }

    // 3. Check for project endings (if Harvest connected)
    if (company.harvestId) {
      try {
        const harvestService = getHarvestService();
        const activeProjects = await harvestService.getClientProjects(company.harvestId);
        
        // Find projects ending in next 30 days
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        
        activeProjects.forEach((project: any) => {
          if (project.ends_on && new Date(project.ends_on) <= thirtyDaysFromNow && project.is_active) {
            opportunities.push({
              id: `project-ending-${project.id}`,
              companyId,
              type: "renewal",
              title: "Project Ending Soon",
              description: `Project "${project.name}" is ending on ${new Date(project.ends_on).toLocaleDateString()}. Good time to discuss renewal or follow-up work.`,
              score: 75,
              metadata: {
                projectEndDate: project.ends_on,
                projectName: project.name,
                projectBudget: project.budget
              },
              detectedAt: new Date().toISOString()
            });
          }
        });
      } catch (error) {
        console.error('Error fetching Harvest projects:', error);
      }
    }

    // 4. Check for expansion opportunities based on relationship network size
    const network = contactRelationshipsRepo.getRelationshipNetwork(companyId, 'company', 1);
    if (network.nodes.length > 5) {
      opportunities.push({
        id: `expansion-network-${companyId}`,
        companyId,
        type: "expansion",
        title: "Strong Relationship Network",
        description: `This company has ${network.nodes.length - 1} connected relationships. Consider leveraging these connections for referrals or expanded services.`,
        score: 60,
        metadata: {
          networkSize: network.nodes.length - 1,
          connectionTypes: [...new Set(network.links.map(l => l.type))]
        },
        detectedAt: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: opportunities
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error generating opportunities:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to generate opportunities",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

export default router;