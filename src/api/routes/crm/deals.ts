import express from "express";
import { DealRepository } from "../../repositories/deal-repository";
import { ContactRoleRepository } from "../../repositories/relationships/contact-role-repository";
import { DealEstimateRepository } from "../../repositories/relationships/deal-estimate-repository";
import { NoteRepository } from "../../repositories/note-repository";
import { EstimateDraftsRepository } from "../../repositories/estimate-drafts-repository";
import {
  DealCreate,
  DealUpdate,
  NoteCreate,
} from "../../../frontend/types/crm-types";
import activityLogger from "../../../utils/activity-logger";
import db from "../../services/db-service";
import { updateEstimateFromDeal } from "../../../utils/estimate-deal-sync";
import { batchApiLimiter } from "../../middleware/security";

// TypeScript interfaces for this file
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

interface HarvestEstimate {
  id: number | string;
  subject?: string;
  projectName?: string;
  amount?: string | number;
  startDate?: string;
  endDate?: string;
  invoiceFrequency?: string;
  paymentTerms?: number;
  client?: {
    id: number;
    name: string;
  };
  project?: {
    id: number;
    name: string;
  };
}

const router = express.Router();
const dealRepository = new DealRepository();
const contactRoleRepository = new ContactRoleRepository();
const dealEstimateRepository = new DealEstimateRepository();
const noteRepository = new NoteRepository();

/**
 * @route GET /api/crm/deals
 * @desc Get all deals
 * @access Private
 */
router.get("/", async (req, res) => {
  try {
    const deals = dealRepository.getAllDeals();

    // Add estimates to each deal
    for (const deal of deals) {
      const estimateLinks = dealEstimateRepository.getEstimatesForDeal(deal.id);
      deal.estimates = estimateLinks.map((link) => ({
        id: link.estimateId,
        estimateId: link.estimateId,
        estimateType: link.estimateType,
        type: link.estimateType as "internal" | "harvest",
        linkedAt: link.linkedAt,
        linkedBy: link.linkedBy || "system",
      }));
    }

    res.json({
      success: true,
      data: deals,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting deals:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get deals",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route GET /api/crm/deals/:id
 * @desc Get a deal by ID
 * @access Private
 */
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const deal = dealRepository.getDealById(id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    // Get linked estimates
    const estimateLinks = dealEstimateRepository.getEstimatesForDeal(id);
    deal.estimates = estimateLinks.map((link) => ({
      id: link.estimateId,
      estimateId: link.estimateId,
      estimateType: link.estimateType,
      type: link.estimateType as "internal" | "harvest",
      linkedAt: link.linkedAt,
      linkedBy: link.linkedBy || "system",
    }));

    console.log("GET /api/crm/deals/:id - Returning deal:", {
      id: deal.id,
      value: deal.value,
      startDate: deal.startDate,
      endDate: deal.endDate,
      invoiceFrequency: deal.invoiceFrequency,
      paymentTerms: deal.paymentTerms,
      estimates: deal.estimates?.length || 0,
    });

    res.json({
      success: true,
      data: deal,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/deals
 * @desc Create a new deal
 * @access Private
 */
router.post("/", async (req, res) => {
  try {
    const dealData: DealCreate = req.body;

    // Validate required fields
    if (!dealData.name || !dealData.stage) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        requiredFields: ["name", "stage"],
      });
    }

    const deal = dealRepository.createDeal(dealData);

    if (!deal) {
      return res.status(500).json({
        success: false,
        error: "Failed to create deal",
      });
    }

    // Log deal creation activity
    try {
      await activityLogger.logDealCreated(
        deal.id,
        deal.name,
        req.session?.userInfo?.sub || "unknown-user",
      );
    } catch (activityError) {
      console.error("Error logging deal creation activity:", activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: deal,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error creating deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route PUT /api/crm/deals/:id
 * @desc Update a deal
 * @access Private
 */
router.put("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const dealData: DealUpdate = req.body;

    const deal = dealRepository.updateDeal(id, dealData);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    // Log deal update activity
    try {
      await activityLogger.logDealUpdated(
        deal.id,
        deal.name,
        dealData, // Pass the update data as changes
        req.session?.userInfo?.sub || "unknown-user",
      );
    } catch (activityError) {
      console.error("Error logging deal update activity:", activityError);
      // Don't fail the request if activity logging fails
    }

    // If deal name was updated and deal has a linked estimate, sync the name
    if (dealData.name) {
      // Get linked internal estimates for this deal
      const linkedEstimates = dealEstimateRepository.getEstimatesForDeal(
        deal.id,
      );
      const internalEstimates = linkedEstimates.filter(
        (e) => e.estimateType === "internal",
      );

      // Sync deal name to all linked internal estimates
      for (const estimate of internalEstimates) {
        updateEstimateFromDeal(deal.id, estimate.estimateId).catch((err) => {
          console.error(
            `Error syncing deal name to estimate ${estimate.estimateId}:`,
            err,
          );
        });
      }
    }

    res.json({
      success: true,
      data: deal,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error updating deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route DELETE /api/crm/deals/:id
 * @desc Delete a deal
 * @access Private
 */
router.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const success = dealRepository.deleteDeal(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    res.json({
      success: true,
      message: "Deal deleted successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error deleting deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to delete deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/deals/:dealId/contacts/:contactId
 * @desc Associate a contact with a deal
 * @access Private
 */
router.post("/:dealId/contacts/:contactId", async (req, res) => {
  try {
    const { dealId, contactId } = req.params;
    const { role } = req.body;

    console.log(
      `Attempting to associate contact ${contactId} with deal ${dealId} in role: ${role}`,
    );

    const relationship = contactRoleRepository.addContactToDeal(
      dealId,
      contactId,
      role,
    );
    const success = !!relationship;

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Deal or contact not found",
      });
    }

    res.json({
      success: true,
      message: "Contact associated with deal successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error associating contact with deal:", apiError);

    // Provide more specific error messages
    let statusCode = 500;
    let errorMessage = "Failed to associate contact with deal";

    if (
      apiError.message?.includes("Deal with ID") &&
      apiError.message?.includes("does not exist")
    ) {
      statusCode = 404;
      errorMessage = "Deal not found";
    } else if (
      apiError.message?.includes("Contact with ID") &&
      apiError.message?.includes("does not exist")
    ) {
      statusCode = 404;
      errorMessage = "Contact not found";
    } else if (apiError.code === "SQLITE_CONSTRAINT_FOREIGNKEY") {
      statusCode = 400;
      errorMessage = "Invalid deal or contact reference";
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route DELETE /api/crm/deals/:dealId/contacts/:contactId
 * @desc Disassociate a contact from a deal
 * @access Private
 */
router.delete("/:dealId/contacts/:contactId", async (req, res) => {
  try {
    const { dealId, contactId } = req.params;

    const success = contactRoleRepository.removeContactFromDeal(
      dealId,
      contactId,
    );

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Association not found",
      });
    }

    res.json({
      success: true,
      message: "Contact disassociated from deal successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error disassociating contact from deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to disassociate contact from deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/deals/:dealId/notes
 * @desc Add a note to a deal
 * @access Private
 */
router.post("/:dealId/notes", async (req, res) => {
  try {
    const { dealId } = req.params;
    const { content, createdBy } = req.body;

    // Validate required fields
    if (!content) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        requiredFields: ["content"],
      });
    }

    const noteData: NoteCreate = {
      dealId,
      content,
      createdBy,
    };

    const note = noteRepository.createNote(noteData);

    if (!note) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    // Log note addition activity
    try {
      // Get deal name for the activity log
      const deal = dealRepository.getDealById(dealId);
      const dealName = deal ? deal.name : "Unknown Deal";

      await activityLogger.logNoteAdded(
        dealId,
        dealName,
        content,
        req.session?.userInfo?.sub || "unknown-user",
      );
    } catch (activityError) {
      console.error("Error logging note addition activity:", activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: note,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error adding note to deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to add note to deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route GET /api/crm/deals/:id/estimates
 * @desc Get all estimates linked to a deal
 * @access Private
 */
router.get("/:id/estimates", async (req, res) => {
  try {
    const { id } = req.params;

    // Check if deal exists
    const deal = dealRepository.getDealById(id);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    const estimateLinks = dealEstimateRepository.getEstimatesForDeal(id);

    // Map database estimate types to frontend types
    const estimates = estimateLinks.map((link) => ({
      ...link,
      type: link.estimateType as "internal" | "harvest",
    }));

    res.json({
      success: true,
      data: estimates,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting deal estimates:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get deal estimates",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/deals/:id/estimates
 * @desc Link an estimate to a deal
 * @access Private
 */
router.post("/:id/estimates", async (req, res) => {
  try {
    const { id } = req.params;
    const { estimateId, estimateType, linkedBy } = req.body;

    // Validate required fields
    if (!estimateId || !estimateType) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        requiredFields: ["estimateId", "estimateType"],
      });
    }

    // Validate estimate type - only allow internal estimates
    if (estimateType !== "internal") {
      return res.status(400).json({
        success: false,
        error: "Only internal estimates can be linked to deals",
        validTypes: ["internal"],
      });
    }

    // Check if deal exists
    const deal = dealRepository.getDealById(id);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    // Map frontend estimate type to database estimate type
    const dbEstimateType = "internal"; // Only internal estimates allowed

    // Link the estimate to the deal
    try {
      dealEstimateRepository.linkDealToEstimate(
        id,
        estimateId,
        dbEstimateType,
        linkedBy,
      );
      // Link successful
    } catch (linkError) {
      console.error("Error linking estimate to deal:", linkError);
      return res.status(500).json({
        success: false,
        error: "Failed to link estimate to deal",
        message: linkError.message,
      });
    }

    // Update deal information based on the estimate
    try {
      let estimateData = null;

      // Get estimate data based on type
      if (estimateType === "internal") {
        // Get draft estimate data
        const estimateDraftsRepository = new EstimateDraftsRepository();
        estimateData =
          estimateDraftsRepository.getDraftEstimateById(estimateId);

        console.log(
          "Draft estimate data:",
          JSON.stringify(estimateData, null, 2),
        );
        console.log("Current deal data:", JSON.stringify(deal, null, 2));
        console.log(
          'Deal before linking - startDate: ", deal.startDate, "endDate: ", deal.endDate, "invoiceFrequency: ", deal.invoiceFrequency, "paymentTerms:',
          deal.paymentTerms,
        );
        console.log("Estimate totalFees:", estimateData?.totalFees);

        if (estimateData) {
          // Import the field ownership functions
          const {
            setFieldOwner,
            setFieldOwnerForEntity,
            getFieldOwner,
          } = require("../../../utils/deal-tracking");

          // Prepare deal update data
          const dealUpdateData: Partial<DealUpdate> = {};

          // Always update estimate-controlled fields, regardless of whether they're already set
          // This ensures the deal always reflects the latest estimate data

          // Set field ownership - estimate's project_name is now controlled by the deal
          if (estimateData.projectName) {
            setFieldOwnerForEntity(
              "estimate",
              estimateId,
              "project_name",
              "Deal",
            );
            console.log("Set estimate project_name ownership to Deal");
          }

          // Always update these fields from the estimate
          if (estimateData.startDate) {
            dealUpdateData.startDate = estimateData.startDate;
            console.log("Setting deal start date to:", estimateData.startDate);
            // Set field ownership to Estimate
            setFieldOwner(id, "startDate", "Estimate");
          }

          if (estimateData.endDate) {
            dealUpdateData.endDate = estimateData.endDate;
            console.log("Setting deal end date to:", estimateData.endDate);
            // Set field ownership to Estimate
            setFieldOwner(id, "endDate", "Estimate");
          }

          if (
            estimateData.invoiceFrequency !== undefined &&
            estimateData.invoiceFrequency !== null
          ) {
            dealUpdateData.invoiceFrequency = estimateData.invoiceFrequency;
            console.log(
              "Setting deal invoice frequency to:",
              estimateData.invoiceFrequency,
            );
            // Set field ownership to Estimate
            setFieldOwner(id, "invoiceFrequency", "Estimate");
          } else {
            console.log(
              "Estimate invoiceFrequency is null or undefined:",
              estimateData.invoiceFrequency,
            );
          }

          if (
            estimateData.paymentTerms !== undefined &&
            estimateData.paymentTerms !== null
          ) {
            // Convert to number if it's a string
            const paymentTermsValue =
              typeof estimateData.paymentTerms === "string"
                ? parseFloat(estimateData.paymentTerms)
                : estimateData.paymentTerms;

            dealUpdateData.paymentTerms = paymentTermsValue;
            console.log(
              'Setting deal payment terms to: ", paymentTermsValue, "(original: ", estimateData.paymentTerms, ")',
            );
            // Set field ownership to Estimate
            setFieldOwner(id, "paymentTerms", "Estimate");
          } else {
            console.log(
              "Estimate paymentTerms is null or undefined:",
              estimateData.paymentTerms,
            );
          }

          // Use the totalFees value from the estimate
          if (
            estimateData.totalFees !== undefined &&
            estimateData.totalFees !== null
          ) {
            dealUpdateData.value = estimateData.totalFees;
            dealUpdateData.currency = "AUD"; // Default to AUD
            console.log(
              "Setting deal value to estimate total fees:",
              estimateData.totalFees,
            );

            // Set field ownership for the value field
            setFieldOwner(id, "value", "Estimate");

            // Set probability based on deal stage if it's not already set
            if (deal.probability === null || deal.probability === undefined) {
              // Helper function to get default probability based on stage
              function getDefaultProbability(stage: string): number {
                switch (stage) {
                  case "Identified":
                    return 0.1; // 10%
                  case "Qualified":
                    return 0.3; // 30%
                  case "Solution proposal":
                    return 0.5; // 50%
                  case "Solution presentation":
                    return 0.7; // 70%
                  case "Objection handling":
                    return 0.8; // 80%
                  case "Finalising terms":
                    return 0.9; // 90%
                  case "Closed won":
                    return 1.0; // 100%
                  default:
                    return 0.5; // 50% default
                }
              }

              dealUpdateData.probability = getDefaultProbability(deal.stage);
              console.log(
                "Setting deal probability based on stage:",
                dealUpdateData.probability,
              );

              // Set field ownership for the probability field
              setFieldOwner(id, "probability", "Estimate");
            }
          } else {
            console.warn("No total fees value found in estimate data");
          }

          // Apply the updates
          if (Object.keys(dealUpdateData).length > 0) {
            console.log("Updating deal with estimate data:", dealUpdateData);
            const updatedDeal = dealRepository.updateDeal(
              id,
              dealUpdateData,
              "Estimate",
            );
            console.log("Updated deal with estimate data result:", updatedDeal);
            console.log(
              'Deal after update - startDate: ", updatedDeal?.startDate, "endDate: ", updatedDeal?.endDate, "invoiceFrequency: ", updatedDeal?.invoiceFrequency, "paymentTerms:',
              updatedDeal?.paymentTerms,
            );
          } else {
            console.log("No deal updates needed");
          }
        }
      }
    } catch (updateError) {
      // Log the error but don't fail the request
      console.error("Error updating deal from estimate data:", updateError);
    }

    // Fetch the updated deal to return it with the response
    const updatedDeal = dealRepository.getDealById(id);
    console.log("Returning updated deal after linking estimate:", {
      id: updatedDeal?.id,
      value: updatedDeal?.value,
      startDate: updatedDeal?.startDate,
      endDate: updatedDeal?.endDate,
      invoiceFrequency: updatedDeal?.invoiceFrequency,
      paymentTerms: updatedDeal?.paymentTerms,
    });

    res.status(201).json({
      success: true,
      message: "Estimate linked to deal successfully",
      deal: updatedDeal,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error linking estimate to deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to link estimate to deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route DELETE /api/crm/deals/:id/estimates/:estimateId
 * @desc Unlink an estimate from a deal
 * @access Private
 */
router.delete("/:id/estimates/:estimateId", async (req, res) => {
  try {
    const { id, estimateId } = req.params;
    const { estimateType } = req.query;

    // Validate estimate type - only allow unlinking internal estimates
    if (!estimateType || estimateType !== "internal") {
      return res.status(400).json({
        success: false,
        error: "Only internal estimates can be unlinked from deals",
        validTypes: ["internal"],
      });
    }

    // Check if deal exists
    const deal = dealRepository.getDealById(id);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found",
      });
    }

    const success = dealEstimateRepository.unlinkDealFromEstimate(
      id,
      estimateId,
    );

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Estimate link not found",
      });
    }

    // Clear field ownership for estimate-controlled fields
    try {
      const {
        getFieldOwner,
        clearFieldOwnership,
        clearFieldOwnershipForEntity,
      } = require("../../../utils/deal-tracking");
      const estimateControlledFields = [
        "value",
        "startDate",
        "endDate",
        "invoiceFrequency",
        "paymentTerms",
        "probability",
      ];

      // Clear field ownership for deal fields controlled by estimate
      const dealFieldsToClear = [];
      for (const fieldName of estimateControlledFields) {
        const owner = getFieldOwner(id, fieldName);
        // Check both cases since database stores lowercase
        if (owner === "Estimate" || owner === "estimate") {
          dealFieldsToClear.push(fieldName);
        }
      }
      if (dealFieldsToClear.length > 0) {
        clearFieldOwnership(id, dealFieldsToClear);
      }

      // Clear field ownership for estimate project_name controlled by deal
      clearFieldOwnershipForEntity("estimate", estimateId, ["project_name"]);
    } catch (ownershipError) {
      console.error("Error clearing field ownership:", ownershipError);
      // Don't fail the request, just log the error
    }

    res.json({
      success: true,
      message: "Estimate unlinked from deal successfully",
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error unlinking estimate from deal:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to unlink estimate from deal",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route GET /api/crm/deals/:id/field-ownership
 * @desc Get field ownership information for a deal
 * @access Private
 */
router.get("/:id/field-ownership", async (req, res) => {
  try {
    const { id } = req.params;

    // Import the field ownership function
    const { getDealFieldOwnership } = require("../../../utils/deal-tracking");

    // Get field ownership for the deal
    const fieldOwnership = getDealFieldOwnership(id);

    // Convert to a more frontend-friendly format
    const ownershipMap: Record<string, string> = {};
    fieldOwnership.forEach((ownership) => {
      ownershipMap[ownership.fieldName] = ownership.ownerSource;
    });

    res.json({
      success: true,
      dealId: id,
      fieldOwnership: ownershipMap,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error getting field ownership:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get field ownership",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

/**
 * @route POST /api/crm/deals/batch-estimates
 * @desc Get estimates for multiple deals in a single request
 * @access Private
 */
router.post("/batch-estimates", batchApiLimiter, async (req, res) => {
  try {
    const { dealIds } = req.body;

    // Validate input
    if (!dealIds || !Array.isArray(dealIds) || dealIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Invalid request",
        message: "dealIds must be a non-empty array",
      });
    }

    // Limit the number of deals to prevent abuse
    if (dealIds.length > 100) {
      console.log(
        `Batch estimates request rejected: ${dealIds.length} deals requested (max 100)`,
      );
      return res.status(400).json({
        success: false,
        error: "Too many deals requested",
        message: "Maximum 100 deals can be fetched at once",
      });
    }

    console.log(
      `Processing batch estimates request for ${dealIds.length} deals`,
    );

    // Get estimates for all requested deals
    const result: Record<string, any[]> = {};

    for (const dealId of dealIds) {
      try {
        // Check if deal exists
        const deal = dealRepository.getDealById(dealId);
        if (!deal) {
          result[dealId] = []; // Empty array for non-existent deals
          continue;
        }

        // Get estimates for this deal
        const estimateLinks =
          dealEstimateRepository.getEstimatesForDeal(dealId);

        // Map to the expected format
        result[dealId] = estimateLinks.map((link) => ({
          id: link.estimateId,
          estimateId: link.estimateId,
          estimateType: link.estimateType,
          type: link.estimateType as "internal" | "harvest",
          linkedAt: link.linkedAt,
          linkedBy: link.linkedBy || "system",
        }));
      } catch (error) {
        console.error(`Error fetching estimates for deal ${dealId}:`, error);
        result[dealId] = []; // Empty array on error
      }
    }

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error("Error in batch estimates fetch:", apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to fetch batch estimates",
      message: apiError.message || "Unknown error occurred",
    });
  }
});

export default router;
