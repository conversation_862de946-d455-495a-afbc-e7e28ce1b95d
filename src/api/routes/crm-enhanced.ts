import express from "express";
import enhancedRepository from "../repositories/enhanced-repository";
import {
  DealUpdate,
  ContactCreate,
  ContactUpdate,
  ContactCompanyAssociation,
  ContactCompanyRelationship,
  CompanyRelationshipCreate,
  DealContactRole,
  DealPrimaryCompany
} from "../../frontend/types/crm-types";

// TypeScript interfaces for this file
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

const router = express.Router();

/**
 * =======================================================
 * ENHANCED CONTACT ENDPOINTS WITH RELATIONSHIP SUPPORT
 * =======================================================
 */

/**
 * @route GET /api/crm-enhanced/contacts
 * @desc Get all contacts with enhanced fields and relationships
 * @access Private
 */
router.get('/contacts', async (req, res) => {
  try {
    const includeDeleted = req.query.includeDeleted === 'true';
    const contacts = enhancedRepository.getAllContacts(includeDeleted);

    res.json({
      success: true,
      data: contacts
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting enhanced contacts:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contacts",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/contacts/deleted
 * @desc Get all soft-deleted contacts
 * @access Private
 */
router.get('/contacts/deleted', async (req, res) => {
  try {
    const contacts = enhancedRepository.getDeletedContacts();

    res.json({
      success: true,
      data: contacts
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting deleted contacts:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get deleted contacts",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/contacts/:id
 * @desc Get a contact by ID with enhanced fields and relationships
 * @access Private
 */
router.get('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const includeDeleted = req.query.includeDeleted === 'true';

    const contact = enhancedRepository.getContactById(id, includeDeleted);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found"
      });
    }

    res.json({
      success: true,
      data: contact
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting enhanced contact:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contact",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/contacts
 * @desc Create a new contact with enhanced fields and relationships
 * @access Private
 */
router.post('/contacts', async (req, res) => {
  try {
    const contactData: ContactCreate = req.body;

    // Validate required fields
    if (!contactData.firstName) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        requiredFields: ['firstName']
      });
    }

    const createdBy = req.body.createdBy || req.session?.user?.email || "system";

    const contact = enhancedRepository.createContact(contactData, createdBy);

    if (!contact) {
      return res.status(500).json({
        success: false,
        error: "Failed to create contact"
      });
    }

    res.status(201).json({
      success: true,
      data: contact
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating enhanced contact:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create contact",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PUT /api/crm-enhanced/contacts/:id
 * @desc Update a contact with enhanced fields
 * @access Private
 */
router.put('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const contactData: ContactUpdate = req.body;

    const updatedBy = req.body.updatedBy || req.session?.user?.email || "system";

    const contact = enhancedRepository.updateContact(id, contactData, updatedBy);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: "Contact not found"
      });
    }

    res.json({
      success: true,
      data: contact
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating enhanced contact:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update contact",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route DELETE /api/crm-enhanced/contacts/:id
 * @desc Soft delete a contact
 * @access Private
 * @query hardDelete=true for permanent deletion
 */
router.delete('/contacts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const hardDelete = req.query.hardDelete === 'true';

    const deletedBy = req.session?.user?.email || "system";

    let success;

    if (hardDelete) {
      success = enhancedRepository.permanentlyDeleteContact(id);
    } else {
      success = enhancedRepository.softDeleteContact(id, deletedBy);
    }

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact not found"
      });
    }

    res.json({
      success: true,
      message: `Contact ${hardDelete ? 'permanently deleted' : "soft deleted"} successfully`
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error deleting contact:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to delete contact",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PATCH /api/crm-enhanced/contacts/:id/restore
 * @desc Restore a soft-deleted contact
 * @access Private
 */
router.patch('/contacts/:id/restore', async (req, res) => {
  try {
    const { id } = req.params;
    const restoredBy = req.session?.user?.email || "system";

    const success = enhancedRepository.restoreContact(id, restoredBy);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact not found or not deleted"
      });
    }

    res.json({
      success: true,
      message: "Contact restored successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error restoring contact:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to restore contact",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/contacts/:id/companies
 * @desc Get all company relationships for a contact
 * @access Private
 */
router.get('/contacts/:id/companies', async (req, res) => {
  try {
    const { id } = req.params;

    const relationships = enhancedRepository.getContactCompanyRelationships(id);

    res.json({
      success: true,
      data: relationships
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting contact company relationships:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contact company relationships",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/contacts/:contactId/companies/:companyId
 * @desc Associate a contact with a company
 * @access Private
 */
router.post('/contacts/:contactId/companies/:companyId', async (req, res) => {
  try {
    const { contactId, companyId } = req.params;
    const { role, isPrimary } = req.body;
    const createdBy = req.body.createdBy || req.session?.user?.email || "system";

    const success = enhancedRepository.associateContactWithCompany(
      contactId,
      companyId,
      role || "employee",
      isPrimary === true,
      createdBy
    );

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact or company not found"
      });
    }

    res.json({
      success: true,
      message: "Contact associated with company successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error associating contact with company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to associate contact with company",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PATCH /api/crm-enhanced/contacts/:contactId/companies/:companyId
 * @desc Update a contact-company relationship
 * @access Private
 */
router.patch('/contacts/:contactId/companies/:companyId', async (req, res) => {
  try {
    const { contactId, companyId } = req.params;
    const { role, isPrimary } = req.body;
    const updatedBy = req.body.updatedBy || req.session?.user?.email || "system";

    // At least one field must be provided to update
    if (role === undefined && isPrimary === undefined) {
      return res.status(400).json({
        success: false,
        error: "Missing update fields",
        requiredFields: ['role', 'isPrimary']
      });
    }

    const updates: Partial<Pick<ContactCompanyRelationship, 'role' | "isPrimary">> = {};

    if (role !== undefined) {
      updates.role = role;
    }

    if (isPrimary !== undefined) {
      updates.isPrimary = isPrimary;
    }

    const success = enhancedRepository.updateContactCompanyRelationship(
      contactId,
      companyId,
      updates,
      updatedBy
    );

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact-company relationship not found"
      });
    }

    res.json({
      success: true,
      message: "Contact-company relationship updated successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating contact-company relationship:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update contact-company relationship",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route DELETE /api/crm-enhanced/contacts/:contactId/companies/:companyId
 * @desc Disassociate a contact from a company
 * @access Private
 */
router.delete('/contacts/:contactId/companies/:companyId', async (req, res) => {
  try {
    const { contactId, companyId } = req.params;

    const success = enhancedRepository.disassociateContactFromCompany(contactId, companyId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact-company relationship not found"
      });
    }

    res.json({
      success: true,
      message: "Contact disassociated from company successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error disassociating contact from company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to disassociate contact from company",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/contacts/:contactId/companies/:companyId/primary
 * @desc Set a company as the primary company for a contact
 * @access Private
 */
router.post('/contacts/:contactId/companies/:companyId/primary', async (req, res) => {
  try {
    const { contactId, companyId } = req.params;
    const updatedBy = req.body.updatedBy || req.session?.user?.email || "system";

    const success = enhancedRepository.setContactPrimaryCompany(contactId, companyId, updatedBy);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Contact or company not found"
      });
    }

    res.json({
      success: true,
      message: "Primary company set successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error setting primary company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to set primary company",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/contacts/companies/batch
 * @desc Batch create contact-company associations
 * @access Private
 */
router.post('/contacts/companies/batch', async (req, res) => {
  try {
    const { associations } = req.body;

    if (!associations || !Array.isArray(associations) || associations.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Missing or invalid associations array"
      });
    }

    // Validate associations
    for (const assoc of associations) {
      if (!assoc.contactId || !assoc.companyId) {
        return res.status(400).json({
          success: false,
          error: "Invalid association object",
          requiredFields: ['contactId', 'companyId']
        });
      }
    }

    const createdBy = req.body.createdBy || req.session?.user?.email || "system";

    const success = enhancedRepository.batchCreateContactCompanyAssociations(
      associations,
      createdBy
    );

    if (!success) {
      return res.status(500).json({
        success: false,
        error: "Failed to create batch associations"
      });
    }

    res.json({
      success: true,
      message: "Batch associations created successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating batch associations:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create batch associations",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/companies/:id/contacts
 * @desc Get contacts for a company
 * @access Private
 */
router.get('/companies/:id/contacts', async (req, res) => {
  try {
    const { id } = req.params;
    const includeDeleted = req.query.includeDeleted === 'true';

    const contacts = enhancedRepository.getContactsForCompany(id, includeDeleted);

    res.json({
      success: true,
      data: contacts
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting contacts for company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contacts for company",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * =======================================================
 * COMPANY RELATIONSHIP ENDPOINTS
 * =======================================================
 */

/**
 * @route GET /api/crm-enhanced/companies/:id/relationships
 * @desc Get all relationships for a company
 * @access Private
 */
router.get('/companies/:id/relationships', async (req, res) => {
  try {
    const { id } = req.params;

    const relationships = enhancedRepository.getAllCompanyRelationships(id);

    res.json({
      success: true,
      data: relationships
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting company relationships:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get company relationships",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/companies/:id/children
 * @desc Get child companies for a parent company
 * @access Private
 */
router.get('/companies/:id/children', async (req, res) => {
  try {
    const { id } = req.params;

    const children = enhancedRepository.getChildCompanies(id);

    res.json({
      success: true,
      data: children
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting child companies:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get child companies",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/companies/:id/parents
 * @desc Get parent companies for a child company
 * @access Private
 */
router.get('/companies/:id/parents', async (req, res) => {
  try {
    const { id } = req.params;

    const parents = enhancedRepository.getParentCompanies(id);

    res.json({
      success: true,
      data: parents
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting parent companies:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get parent companies",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/companies/:parentId/relationships/:childId
 * @desc Create a relationship between two companies
 * @access Private
 */
router.post('/companies/:parentId/relationships/:childId', async (req, res) => {
  try {
    const { parentId, childId } = req.params;
    const { relationshipType } = req.body;

    if (!relationshipType) {
      return res.status(400).json({
        success: false,
        error: "Missing relationship type",
        requiredFields: ['relationshipType']
      });
    }

    // Validate relationship type
    const validTypes = ['parent', 'subsidiary', 'partner', 'acquisition'];
    if (!validTypes.includes(relationshipType)) {
      return res.status(400).json({
        success: false,
        error: "Invalid relationship type",
        validTypes
      });
    }

    const createdBy = req.body.createdBy || req.session?.user?.email || "system";

    const relationship = enhancedRepository.createCompanyRelationship({
      parentCompanyId: parentId,
      childCompanyId: childId,
      relationshipType
    });

    if (!relationship) {
      return res.status(404).json({
        success: false,
        error: "Companies not found or invalid relationship"
      });
    }

    res.json({
      success: true,
      message: "Company relationship created successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating company relationship:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create company relationship",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route DELETE /api/crm-enhanced/companies/:parentId/relationships/:childId
 * @desc Delete a relationship between two companies
 * @access Private
 */
router.delete('/companies/:parentId/relationships/:childId', async (req, res) => {
  try {
    const { parentId, childId } = req.params;

    const success = enhancedRepository.deleteCompanyRelationship(parentId, childId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Relationship not found"
      });
    }

    res.json({
      success: true,
      message: "Company relationship deleted successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error deleting company relationship:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to delete company relationship",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/companies/relationships/batch
 * @desc Batch create company relationships
 * @access Private
 */
router.post('/companies/relationships/batch', async (req, res) => {
  try {
    const { relationships } = req.body;

    if (!relationships || !Array.isArray(relationships) || relationships.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Missing or invalid relationships array"
      });
    }

    // Validate relationships
    for (const rel of relationships) {
      if (!rel.parentCompanyId || !rel.childCompanyId || !rel.relationshipType) {
        return res.status(400).json({
          success: false,
          error: "Invalid relationship object",
          requiredFields: ['parentCompanyId', 'childCompanyId', 'relationshipType']
        });
      }

      // Validate relationship type
      const validTypes = ['parent', 'subsidiary', 'partner', 'acquisition'];
      if (!validTypes.includes(rel.relationshipType)) {
        return res.status(400).json({
          success: false,
          error: "Invalid relationship type",
          validTypes
        });
      }
    }

    const createdBy = req.body.createdBy || req.session?.user?.email || "system";

    const created = enhancedRepository.batchCreateCompanyRelationships(relationships);

    if (!created || created.length === 0) {
      return res.status(500).json({
        success: false,
        error: "Failed to create batch relationships"
      });
    }

    res.json({
      success: true,
      message: "Batch relationships created successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating batch relationships:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to create batch relationships",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/companies/:id/full
 * @desc Get a company with all its relationships
 * @access Private
 */
router.get('/companies/:id/full', async (req, res) => {
  try {
    const { id } = req.params;
    const includeDeleted = req.query.includeDeleted === 'true';

    const company = enhancedRepository.getCompanyWithRelationships(id, includeDeleted);

    if (!company) {
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    res.json({
      success: true,
      data: company
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting company with relationships:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get company with relationships",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * =======================================================
 * DEAL ENHANCEMENT ENDPOINTS
 * =======================================================
 */

/**
 * @route GET /api/crm-enhanced/deals/:id
 * @desc Get a deal by ID with enhanced relationships
 * @access Private
 */
router.get('/deals/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const includeDeleted = req.query.includeDeleted === 'true';

    const deal = enhancedRepository.getDealByIdEnhanced(id, includeDeleted);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found"
      });
    }

    res.json({
      success: true,
      data: deal
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting enhanced deal:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get deal",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PUT /api/crm-enhanced/deals/:id
 * @desc Update a deal with enhanced fields
 * @access Private
 */
router.put('/deals/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const dealData: DealUpdate = req.body;

    const updatedBy = req.body.updatedBy || req.session?.user?.email || "system";

    const deal = enhancedRepository.updateDealEnhanced(id, dealData, updatedBy);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: "Deal not found"
      });
    }

    res.json({
      success: true,
      data: deal
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating enhanced deal:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update deal",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route DELETE /api/crm-enhanced/deals/:id
 * @desc Soft delete a deal
 * @access Private
 */
router.delete('/deals/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const deletedBy = req.session?.user?.email || "system";

    const success = enhancedRepository.softDeleteDeal(id, deletedBy);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Deal not found"
      });
    }

    res.json({
      success: true,
      message: "Deal soft deleted successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error soft deleting deal:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to soft delete deal",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PATCH /api/crm-enhanced/deals/:id/restore
 * @desc Restore a soft-deleted deal
 * @access Private
 */
router.patch('/deals/:id/restore', async (req, res) => {
  try {
    const { id } = req.params;
    const restoredBy = req.session?.user?.email || "system";

    const success = enhancedRepository.restoreDeal(id, restoredBy);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Deal not found or not deleted"
      });
    }

    res.json({
      success: true,
      message: "Deal restored successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error restoring deal:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to restore deal",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route PATCH /api/crm-enhanced/deals/:dealId/contacts/:contactId/role
 * @desc Update the role of a contact in a deal
 * @access Private
 */
router.patch('/deals/:dealId/contacts/:contactId/role', async (req, res) => {
  try {
    const { dealId, contactId } = req.params;
    const { role } = req.body;

    if (!role) {
      return res.status(400).json({
        success: false,
        error: "Missing role",
        requiredFields: ['role']
      });
    }

    const success = enhancedRepository.updateContactRoleInDeal(dealId, contactId, role);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Deal-contact association not found"
      });
    }

    res.json({
      success: true,
      message: "Contact role updated successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating contact role in deal:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update contact role in deal",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/deals/:id/contacts/roles
 * @desc Get contacts for a deal with their roles
 * @access Private
 */
router.get('/deals/:id/contacts/roles', async (req, res) => {
  try {
    const { id } = req.params;

    const contactsWithRoles = enhancedRepository.getContactsWithRolesForDeal(id);

    res.json({
      success: true,
      data: contactsWithRoles
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting contacts with roles for deal:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get contacts with roles for deal",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/deals/:dealId/company/:companyId
 * @desc Set the primary company for a deal
 * @access Private
 */
router.post('/deals/:dealId/company/:companyId', async (req, res) => {
  try {
    const { dealId, companyId } = req.params;
    const updatedBy = req.body.updatedBy || req.session?.user?.email || "system";

    const success = enhancedRepository.setDealPrimaryCompany(dealId, companyId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: "Deal or company not found"
      });
    }

    res.json({
      success: true,
      message: "Deal primary company set successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error setting deal primary company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to set deal primary company",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route POST /api/crm-enhanced/deals/contacts/roles/batch
 * @desc Batch update contact roles in a deal
 * @access Private
 */
router.post('/deals/contacts/roles/batch', async (req, res) => {
  try {
    const { dealContactRoles } = req.body;

    if (!dealContactRoles || !Array.isArray(dealContactRoles) || dealContactRoles.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Missing or invalid dealContactRoles array"
      });
    }

    // Validate dealContactRoles
    for (const dcr of dealContactRoles) {
      if (!dcr.dealId || !dcr.contactId || !dcr.role) {
        return res.status(400).json({
          success: false,
          error: "Invalid deal contact role object",
          requiredFields: ['dealId', 'contactId', 'role']
        });
      }
    }

    const success = enhancedRepository.batchUpdateDealContactRoles(dealContactRoles);

    if (!success) {
      return res.status(500).json({
        success: false,
        error: "Failed to update batch deal contact roles"
      });
    }

    res.json({
      success: true,
      message: "Batch deal contact roles updated successfully"
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating batch deal contact roles:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to update batch deal contact roles",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

/**
 * @route GET /api/crm-enhanced/companies/:id/deals
 * @desc Get deals by company ID
 * @access Private
 */
router.get('/companies/:id/deals', async (req, res) => {
  try {
    const { id } = req.params;
    const includeDeleted = req.query.includeDeleted === 'true';

    const deals = enhancedRepository.getDealsByCompanyId(id);

    res.json({
      success: true,
      data: deals
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting deals by company ID:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: "Failed to get deals by company ID",
      message: apiError.message || "Unknown error occurred"
    });
  }
});

export default router;