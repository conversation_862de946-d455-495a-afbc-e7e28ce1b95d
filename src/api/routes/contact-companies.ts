/**
 * API routes for contact-company relationships
 */

import express from "express";
import { createContactCompanyRepository } from "../repositories/contact-company-repository";
// Using any type for Database to avoid TypeScript namespace issues

export function setupContactCompanyRoutes(app: express.Express, db: any) {
  const router = express.Router();
  const contactCompanyRepo = createContactCompanyRepository(db);

  /**
   * Associate a contact with a company
   * POST /api/contact-companies
   */
  router.post('/", (req, res) => {
    try {
      const { contactId, companyId, isPrimary, role } = req.body;
      
      if (!contactId || !companyId) {
        return res.status(400).json({
          success: false,
          error: "Missing required fields: contactId, companyId"
        });
      }

      const createdBy = req.session?.user?.id || "system";
      const relationship = contactCompanyRepo.associateContactWithCompany(
        contactId,
        companyId,
        !!isPrimary,
        role,
        createdBy
      );

      res.json({
        success: true,
        data: relationship
      });
    } catch (error) {
      console.error('Error associating contact with company:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Remove a contact from a company
   * DELETE /api/contact-companies/:contactId/:companyId
   */
  router.delete('/:contactId/:companyId", (req, res) => {
    try {
      const { contactId, companyId } = req.params;
      
      const success = contactCompanyRepo.disassociateContactFromCompany(contactId, companyId);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          error: "Association not found"
        });
      }

      res.json({
        success: true,
        message: "Contact disassociated from company successfully"
      });
    } catch (error) {
      console.error('Error disassociating contact from company:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Update a contact's role at a company
   * PATCH /api/contact-companies/:contactId/:companyId/role
   */
  router.patch('/:contactId/:companyId/role", (req, res) => {
    try {
      const { contactId, companyId } = req.params;
      const { role } = req.body;
      
      const success = contactCompanyRepo.updateContactRole(contactId, companyId, role);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          error: "Association not found"
        });
      }

      res.json({
        success: true,
        message: "Contact role updated successfully"
      });
    } catch (error) {
      console.error('Error updating contact role:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Set a contact's primary company
   * PATCH /api/contact-companies/:contactId/:companyId/primary
   */
  router.patch('/:contactId/:companyId/primary", (req, res) => {
    try {
      const { contactId, companyId } = req.params;
      
      const success = contactCompanyRepo.setContactPrimaryCompany(contactId, companyId);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          error: "Association not found"
        });
      }

      res.json({
        success: true,
        message: "Primary company set successfully"
      });
    } catch (error) {
      console.error('Error setting primary company:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get all companies for a contact
   * GET /api/contact-companies/:contactId/companies
   */
  router.get('/:contactId/companies', (req, res) => {
    try {
      const { contactId } = req.params;
      
      const companies = contactCompanyRepo.getContactCompanies(contactId);
      
      res.json({
        success: true,
        data: companies
      });
    } catch (error) {
      console.error('Error getting contact companies:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Check if a contact is associated with a company
   * GET /api/contact-companies/:contactId/:companyId
   */
  router.get('/:contactId/:companyId', (req, res) => {
    try {
      const { contactId, companyId } = req.params;
      
      const isAssociated = contactCompanyRepo.isContactAssociatedWithCompany(contactId, companyId);
      
      res.json({
        success: true,
        data: { isAssociated }
      });
    } catch (error) {
      console.error('Error checking contact-company association:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  app.use('/api/contact-companies", router);
}