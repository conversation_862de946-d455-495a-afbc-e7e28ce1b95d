import express from "express";
import { getHarvestService } from "../../services/harvest";
import { ProjectSetting } from "../../services/harvest/project-budget-service";
import { readFileSync, writeFileSync, existsSync, mkdirSync, unlinkSync } from "fs";
import { join } from "path";
import { addDays } from "date-fns";
import { updateDealsFromEstimate } from "../../utils/estimate-deal-sync";
import activityLogger from "../../utils/activity-logger";

// TypeScript interfaces for route handling
interface ApiError extends Error {
  message: string;
  statusCode?: number;
  response?: {
    data?: {
      message?: string;
    };
  };
}

interface HarvestQueryParams {
  client_id?: string | number;
  from?: string;
  to?: string;
  state?: string;
  is_active?: string;
}

type QueryParamValue = string | number | undefined;

const router = express.Router();

/**
 * Check if user is authenticated
 * This middleware checks if the user is authenticated with Harvest
 */
const checkAuth = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.log('[DEBUG PRE-SESSION] Request to', req.originalUrl, 'Cookies:', req.headers.cookie);
  // Check if session exists and has tokenSet
  if (!req.session) {
    console.error('No session found, authentication check failed');
    return res.status(401).json({
      success: false,
      message: "Authentication required. Please authenticate with Harvest first."
    });
  }

  // Log session ID and whether tokenSet exists for debugging
  console.log('[DEBUG POST-SESSION] Request to', req.originalUrl,
    'SessionID:', req.session.id,
    'Has tokenSet:', !!req.session.tokenSet);

  // Here we're just checking session existence - actual token validation happens in the client
  next();
};

// Determine the data directory based on environment - using same pattern as db-service.ts
let dataDir = '";

if (process.env.NODE_ENV === 'production') {
  // In production (Render), use the mounted persistent disk
  dataDir = '/data";

  // Log for debugging
  console.log('Harvest settings: Running in production mode, using data directory:', dataDir);
} else {
  // In development, use local directory - match the exact path pattern used for expenses
  dataDir = join(__dirname, '../../../data');
  console.log('Harvest settings: Running in development mode, using data directory:', dataDir);
}

const settingsPath = join(dataDir, 'project_settings.json');

// Ensure data directory exists
if (!existsSync(dataDir)) {
  mkdirSync(dataDir, { recursive: true });
}

// Helper to read settings
const readSettings = (): ProjectSetting[] => {
  try {
    if (existsSync(settingsPath)) {
      const data = readFileSync(settingsPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error reading project settings:', error);
  }
  return [];
};

// Check directory permissions - follow the same pattern as db-service.ts
try {
  // Check if we can write to the directory
  const testFile = join(dataDir, '.harvest-write-test');
  writeFileSync(testFile, 'test');
  if (existsSync(testFile)) {
    unlinkSync(testFile);
  }
  console.log(`Harvest settings: Successfully verified write access to ${dataDir}`);

  // Initialize settings file if it doesn't exist
  if (!existsSync(settingsPath)) {
    writeFileSync(settingsPath, JSON.stringify([], null, 2), 'utf8');
    console.log('Harvest settings: Initialized empty settings file');
  }
} catch (error) {
  console.error(`Harvest settings: ERROR: Cannot write to data directory ${dataDir}:`, error);
}

// Helper to write settings
const writeSettings = (settings: ProjectSetting[]): void => {
  try {
    writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
  } catch (error) {
    console.error('Error writing project settings:', error);
  }
};

// Convert invoice frequency to days
const frequencyToDays = (frequency: string): number => {
  switch (frequency) {
    case 'weekly': return 7;
    case 'biweekly': return 14;
    case 'monthly': return 30;
    default: return 14; // Default to biweekly
  }
};

/**
 * GET /api/harvest/projects
 * Get project data from Harvest
 */
router.get('/projects', async (req, res) => {
  try {
    const harvestService = getHarvestService();

    // Get project budgets from the service
    const projects = await harvestService.projectBudgets.getProjectBudgets();

    // Transform to frontend format
    const formattedProjects = projects.map(project => ({
      id: project.id,
      name: project.name,
      client: project.clientName,
      budgetRemaining: project.budgetRemaining,
      uninvoicedAmount: project.uninvoicedAmount,
      startDate: project.startDate,
      endDate: project.endDate,
      status: project.isActive ? 'Active' : "Inactive"  // Use isActive to determine status
    }));

    res.json({ success: true, data: formattedProjects });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting projects:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/project-settings
 * Get project settings
 */
router.get('/project-settings', (req, res) => {
  try {
    const settings = readSettings();
    res.json({ success: true, data: settings });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting project settings:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * POST /api/harvest/project-settings
 * Save project settings
 */
router.post('/project-settings', (req, res) => {
  try {
    const settings = req.body as ProjectSetting[];

    // Add invoiceIntervalDays if not present
    const processedSettings = settings.map(setting => ({
      ...setting,
      invoiceIntervalDays: setting.invoiceIntervalDays || frequencyToDays(setting.invoiceFrequency)
    }));

    writeSettings(processedSettings);
    res.json({ success: true });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error saving project settings:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/projected-invoices
 * Get projected invoices based on project settings
 * Note: Timeframe parameter is kept for backward compatibility, but we now
 * generate ALL invoices through project completion and let the frontend filter
 */
router.get('/projected-invoices', async (req, res) => {
  try {
    // Timeframe is no longer used to filter projected invoices
    // All invoices through project completion are returned
    const harvestService = getHarvestService();
    const settings = readSettings();

    // If no settings available, return an empty array
    if (settings.length === 0) {
      return res.json({ success: true, data: [] });
    }

    // Start date is today, but we'll still pass endDate far in the future
    // We're no longer filtering based on this in the service logic
    const startDate = new Date();
    const endDate = addDays(startDate, 3650); // ~10 years - effectively no limit

    // Generate projected income transactions - now includes ALL future invoices
    // Generate projected invoices
    const transactions = await harvestService.projectBudgets.generateProjectedIncome(
      startDate,
      endDate,
      settings
    );

    // Filter out any transactions with dates in the past
    const today = new Date();
    const futureTransactions = transactions.filter(transaction =>
      new Date(transaction.date) > today
    );

    // Get projects and map to get project names
    const projects = await harvestService.projectBudgets.getProjectBudgets();

    // Create a full map that includes both project name and client name
    const projectMap = new Map(projects.map(p => [
      p.id, {
        name: p.name,
        clientName: p.clientName,
        clientId: p.clientId,
        isActive: p.isActive
      }
    ]));

    // Transform to frontend format - use filtered future transactions
    const projectedInvoices = futureTransactions.map(transaction => {
      // Determine transaction type
      let type = '";
      // Note: We no longer include outstanding invoices as they're handled by the cashflow projection
      if (transaction.id.startsWith('uninvoiced-')) {
        type = 'uninvoiced_work";
      } else if (transaction.id.startsWith('future-work-')) {
        type = 'future_work";
      }

      // Get info from metadata or extract from transaction.what
      const projectId = transaction.metadata?.projectId || '";
      const projectName = transaction.metadata?.projectName ||
                         (transaction.description.includes(' - ') ? transaction.description.split(' - ')[1] : "Unknown Project");
      const clientName = transaction.metadata?.clientName || 'Unknown Client";

      // Get project-specific payment terms
      const projectSettings = settings.find(s => s.projectId === projectId);
      const paymentTerms = projectSettings?.paymentTerms || 14; // Default to 14 if not found

      // Calculate invoice date by subtracting payment terms from payment date
      const invoiceDate = addDays(transaction.date, -paymentTerms);

      // For debugging
      console.log(`Transaction: ${transaction.description}`);
      console.log(`  Project: ${projectName} (${clientName})`);
      console.log(`  Invoice Date: ${invoiceDate.toLocaleDateString()}, Payment Date: ${new Date(transaction.date).toLocaleDateString()}`);
      console.log(`  Amount: ${transaction.amount}, Type: ${type}, Payment Terms: ${paymentTerms} days`);

      return {
        id: transaction.id,
        projectId,
        projectName,
        clientName,
        amount: transaction.amount,
        type,
        invoiceDate: invoiceDate,
        paymentDate: transaction.date,
        metadata: {
          ...transaction.metadata,
          paymentTerms: paymentTerms
        }
      };
    });

    res.json({ success: true, data: projectedInvoices });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting projected invoices:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/clients
 * Get clients from Harvest
 */
router.get('/clients', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();

    // Get the HarvestClient instance from the service
    const harvestClient = harvestService.getClient();

    // Fetch clients from Harvest API
    const clients = await harvestClient.getClients();

    res.json({ success: true, data: clients });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting clients:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/users
 * Get users from Harvest, with optional filtering
 */
router.get('/users', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();
    const harvestClient = harvestService.getClient();

    // Check for is_active query parameter
    const isActive = req.query.is_active === 'true' ? true :
                    req.query.is_active === 'false' ? false : undefined;

    // Fetch users from Harvest API
    const users = await harvestClient.getUsers(isActive);

    res.json({ success: true, data: users });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting users:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/users/me
 * Get the currently authenticated user from Harvest
 */
router.get('/users/me', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();
    const harvestClient = harvestService.getClient();

    // Fetch current user from Harvest API
    const currentUser = await harvestClient.getCurrentUser();

    // Map roles to access_roles for frontend compatibility
    const mappedUser = {
      id: currentUser.id,
      first_name: currentUser.first_name,
      last_name: currentUser.last_name,
      email: currentUser.email,
      access_roles: currentUser.roles // Map roles to access_roles
    };

    res.json({ success: true, data: mappedUser });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting current user:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * POST /api/harvest/estimates
 * Create an estimate in Harvest
 */
router.post('/estimates', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();
    const harvestClient = harvestService.getClient();

    // Get estimate data from request body
    const estimateData = req.body;

    // Create estimate in Harvest API
    const createdEstimate = await harvestClient.createEstimate(estimateData);

    // Log the Harvest estimate creation activity
    try {
      await activityLogger.logEstimateCreated(
        createdEstimate.id.toString(),
        `${createdEstimate.client?.name || "Unknown Client"} - ${createdEstimate.name || "Untitled Estimate"}`,
        req.session?.userInfo?.sub || "harvest-user"
      );
    } catch (activityError) {
      console.error('Error logging Harvest estimate creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    // Update any deals that might be linked to this estimate
    // Run this asynchronously so it doesn't block the response
    updateDealsFromEstimate(createdEstimate.id.toString(), 'harvest').catch(err => {
      console.error('Error updating linked deals for new Harvest estimate:', err);
    });

    res.json({ success: true, data: createdEstimate });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating estimate:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/estimates
 * Get estimates from Harvest with optional filtering
 */
router.get('/estimates', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();
    const harvestClient = harvestService.getClient();

    // Extract query parameters
    const { client_id, from, to, state } = req.query;

    // Prepare params for Harvest API
    const params: Record<string, QueryParamValue> = {};
    if (client_id) params.client_id = client_id;
    if (from) params.from = from;
    if (to) params.to = to;
    if (state) params.state = state;

    // Fetch estimates from Harvest API
    const estimates = await harvestClient.getEstimates(params);

    res.json({ success: true, data: estimates });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error fetching estimates:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/estimates/:id
 * Get a single estimate by ID
 */
router.get('/estimates/:id', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();
    const harvestClient = harvestService.getClient();

    // Get estimate ID from route params
    const estimateId = parseInt(req.params.id);
    if (isNaN(estimateId)) {
      return res.status(400).json({ success: false, message: "Invalid estimate ID" });
    }

    // Fetch estimate from Harvest API
    const estimate = await harvestClient.getEstimate(estimateId);

    res.json({ success: true, data: estimate });
  } catch (error) {
    const apiError = error as ApiError;
    console.error(`Error fetching estimate ${req.params.id}:`, apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/reports/utilization
 * Get staff utilization report
 */
router.get('/reports/utilization', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();

    // Get date parameters from query
    const { from, to } = req.query;

    // Validate required parameters
    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: "Both "from " and "to' parameters are required in YYYY-MM-DD format'
      });
    }

    // Get utilization data
    const utilizationData = await harvestService.timeReports.getStaffUtilization(
      from as string,
      to as string
    );

    res.json({ success: true, data: utilizationData });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting staff utilization:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/reports/fixed-price-income
 * Get fixed price project income for a given time period
 */
router.get('/reports/fixed-price-income', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();

    // Get date parameters from query
    const { from, to } = req.query;

    // Validate required parameters
    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: "Both "from " and "to' parameters are required in YYYY-MM-DD format'
      });
    }

    // Get fixed price project income data
    const fixedPriceIncome = await harvestService.timeReports.getFixedPriceProjectIncome(
      from as string,
      to as string
    );

    res.json({ success: true, data: fixedPriceIncome });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting fixed price project income:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

/**
 * GET /api/harvest/late-invoices
 * Get late (overdue) invoices from Harvest
 */
router.get('/late-invoices', checkAuth, async (req, res) => {
  try {
    const harvestService = getHarvestService();

    // Get late invoices
    const lateInvoices = await harvestService.lateInvoices.getLateInvoices();

    // Calculate total amount
    const totalAmount = lateInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);

    res.json({
      success: true,
      data: {
        invoices: lateInvoices,
        totalAmount,
        count: lateInvoices.length
      }
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting late invoices from Harvest:', apiError);
    res.status(500).json({ success: false, message: apiError.message });
  }
});

export default router;
