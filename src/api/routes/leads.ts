import { Router } from "express";
import { LeadsController } from "../controllers/leads";
import { LeadsRepository } from "../repositories/leads-repository";
import { getHarvestService } from "../../services/harvest";
import { getDatabase } from "../../database";

const router = Router();

// Authentication middleware
const checkAuth = (req: any, res: any, next: any) => {
  // Log authentication check for debugging
  console.log(
    `[AUTH] Request to /leads${req.path}. SessionID: ${req.sessionID}. Has tokenSet: ${!!req.session?.tokenSet}`,
  );
  console.log(`[AUTH] Request cookies:`, req.headers.cookie || "None");

  if (!req.session || !req.session.tokenSet) {
    console.log(
      `[AUTH] Authentication failed for /leads${req.path}. Session ID: ${req.sessionID}`,
    );
    return res.status(401).json({ success: false, message: "Unauthorized" });
  }

  console.log(
    `[AUTH] User authenticated for /leads${req.path}. Session ID: ${req.sessionID}`,
  );
  next();
};

// Initialize dependencies
const db = getDatabase();
const leadsRepository = new LeadsRepository(db);
const harvestClient = getHarvestService().getClient();
const leadsController = new LeadsController(leadsRepository, harvestClient);

// Define routes
router.get("/companies", checkAuth, leadsController.getRadarCompanies);
router.put(
  "/companies/:id/radar",
  checkAuth,
  leadsController.updateRadarCompany,
);
router.get(
  "/harvest/companies",
  checkAuth,
  leadsController.getHarvestCompanies,
);
router.post(
  "/companies/add-to-radar",
  checkAuth,
  leadsController.addCompanyToRadar,
);
router.delete(
  "/companies/:id/remove-from-radar",
  checkAuth,
  leadsController.removeCompanyFromRadar,
);

export default router;
