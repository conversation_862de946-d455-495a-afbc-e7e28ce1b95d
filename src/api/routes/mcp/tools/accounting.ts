// Accounting-related tools for Xero API

export const accountingTools = [
  {
    name: "list_invoices",
    description: "List invoices from Xero. Returns invoice details including number, customer, amount, and status.",
    input_schema: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "Page number for pagination (default: 1)"
        },
        pageSize: {
          type: "integer",
          description: "Number of items per page (default: 10)"
        },
        status: {
          type: "string",
          description: "Filter by status (DRAFT, AUTHORISED, PAID)"
        }
      },
      required: []
    }
  },
  {
    name: "list_contacts",
    description: "List contacts (customers and suppliers) from Xero.",
    input_schema: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "Page number for pagination (default: 1)"
        },
        pageSize: {
          type: "integer",
          description: "Number of items per page (default: 10)"
        }
      },
      required: []
    }
  },
  {
    name: "list_accounts",
    description: "List chart of accounts from Xero. Shows account codes, names, and types.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "list_payments",
    description: "List payments from Xero.",
    input_schema: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "Page number for pagination (default: 1)"
        },
        pageSize: {
          type: "integer",
          description: "Number of items per page (default: 10)"
        }
      },
      required: []
    }
  },
  {
    name: "list_bank_transactions",
    description: "List bank transactions from Xero.",
    input_schema: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "Page number for pagination (default: 1)"
        },
        pageSize: {
          type: "integer",
          description: "Number of items per page (default: 10)"
        }
      },
      required: []
    }
  },
  {
    name: "list_bills",
    description: "List bills (accounts payable) from Xero.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "get_organisation_info",
    description: "Get organization information from Xero.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "get_bank_balance",
    description: "Get current bank account balances from Xero. Shows all bank accounts with their current balances.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "list_credit_notes",
    description: "List credit notes from Xero.",
    input_schema: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "Page number for pagination (default: 1)"
        },
        pageSize: {
          type: "integer",
          description: "Number of items per page (default: 10)"
        }
      },
      required: []
    }
  },
  {
    name: "list_items",
    description: "List items (products/services) from Xero.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "list_quotes",
    description: "List quotes from Xero.",
    input_schema: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          description: "Page number for pagination (default: 1)"
        }
      },
      required: []
    }
  },
  {
    name: "list_tax_rates",
    description: "List tax rates from Xero.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  }
];