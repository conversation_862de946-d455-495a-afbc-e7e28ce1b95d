// Reporting and financial analysis tools for Xero API

export const reportingTools = [
  {
    name: "get_balance_sheet",
    description: "Get balance sheet report from Xero for a specific date.",
    input_schema: {
      type: "object",
      properties: {
        date: {
          type: "string",
          description: "Date for the balance sheet (YYYY-MM-DD format)",
        },
      },
      required: [],
    },
  },
  {
    name: "get_profit_loss",
    description: "Get profit and loss report from Xero for a date range.",
    input_schema: {
      type: "object",
      properties: {
        fromDate: {
          type: "string",
          description: "Start date (YYYY-MM-DD format)",
        },
        toDate: {
          type: "string",
          description: "End date (YYYY-MM-DD format)",
        },
      },
      required: [],
    },
  },
  {
    name: "get_trial_balance",
    description: "Get trial balance report from Xero.",
    input_schema: {
      type: "object",
      properties: {
        date: {
          type: "string",
          description: "Date for the trial balance (YYYY-MM-DD format)",
        },
      },
      required: [],
    },
  },
  {
    name: "list_aged_receivables_by_contact",
    description: "Get aged receivables report for a specific contact.",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "Contact ID to get aged receivables for",
        },
        date: {
          type: "string",
          description: "Date for the report (YYYY-MM-DD format)",
        },
      },
      required: ["contactId"],
    },
  },
  {
    name: "list_aged_payables_by_contact",
    description: "Get aged payables report for a specific contact.",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "Contact ID to get aged payables for",
        },
        date: {
          type: "string",
          description: "Date for the report (YYYY-MM-DD format)",
        },
      },
      required: ["contactId"],
    },
  },
];
