// Payroll-related tools for Xero Payroll API

export const payrollTools = [
  {
    name: "list_payroll_employees",
    description: "List payroll employees from Xero.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "list_payroll_employee_leave",
    description: "List leave records for a payroll employee.",
    input_schema: {
      type: "object",
      properties: {
        employeeId: {
          type: "string",
          description: "Employee ID to get leave records for"
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: "list_payroll_employee_leave_balances",
    description: "List leave balances for a payroll employee.",
    input_schema: {
      type: "object",
      properties: {
        employeeId: {
          type: "string",
          description: "Employee ID to get leave balances for"
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: "list_payroll_employee_leave_types",
    description: "List leave types for a payroll employee.",
    input_schema: {
      type: "object",
      properties: {
        employeeId: {
          type: "string",
          description: "Employee ID to get leave types for"
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: "list_payroll_leave_periods",
    description: "List leave periods for a payroll employee.",
    input_schema: {
      type: "object",
      properties: {
        employeeId: {
          type: "string",
          description: "Employee ID to get leave periods for"
        }
      },
      required: ['employeeId']
    }
  },
  {
    name: "list_payroll_leave_types",
    description: "List all available leave types in Xero Payroll.",
    input_schema: {
      type: "object",
      properties: {},
      required: []
    }
  },
  {
    name: "get_payroll_timesheet",
    description: "Get a specific payroll timesheet.",
    input_schema: {
      type: "object",
      properties: {
        timesheetId: {
          type: "string",
          description: "Timesheet ID to retrieve"
        }
      },
      required: ['timesheetId']
    }
  },
  {
    name: "create_payroll_timesheet",
    description: "Create a new payroll timesheet in Xero.",
    input_schema: {
      type: "object",
      properties: {
        employeeId: {
          type: "string",
          description: "Employee ID for the timesheet"
        },
        startDate: {
          type: "string",
          description: "Start date for the timesheet (YYYY-MM-DD format)"
        },
        endDate: {
          type: "string",
          description: "End date for the timesheet (YYYY-MM-DD format)"
        }
      },
      required: ['employeeId', 'startDate', 'endDate']
    }
  },
  {
    name: "update_payroll_timesheet_line",
    description: "Update a line on an existing payroll timesheet.",
    input_schema: {
      type: "object",
      properties: {
        timesheetId: {
          type: "string",
          description: "Timesheet ID to update"
        },
        timesheetLineId: {
          type: "string",
          description: "Timesheet line ID to update"
        },
        hours: {
          type: "number",
          description: "Number of hours worked"
        },
        date: {
          type: "string",
          description: "Date for the timesheet line (YYYY-MM-DD format)"
        }
      },
      required: ['timesheetId', 'timesheetLineId']
    }
  },
  {
    name: "add_payroll_timesheet_line",
    description: "Add a new line to an existing payroll timesheet.",
    input_schema: {
      type: "object",
      properties: {
        timesheetId: {
          type: "string",
          description: "Timesheet ID to add line to"
        },
        hours: {
          type: "number",
          description: "Number of hours worked"
        },
        date: {
          type: "string",
          description: "Date for the timesheet line (YYYY-MM-DD format)"
        },
        earningsRateId: {
          type: "string",
          description: "Earnings rate ID for the line"
        }
      },
      required: ['timesheetId', 'hours', 'date']
    }
  },
  {
    name: "approve_payroll_timesheet",
    description: "Approve a payroll timesheet.",
    input_schema: {
      type: "object",
      properties: {
        timesheetId: {
          type: "string",
          description: "Timesheet ID to approve"
        }
      },
      required: ['timesheetId']
    }
  },
  {
    name: "revert_payroll_timesheet",
    description: "Revert an approved payroll timesheet.",
    input_schema: {
      type: "object",
      properties: {
        timesheetId: {
          type: "string",
          description: "Timesheet ID to revert"
        }
      },
      required: ['timesheetId']
    }
  },
  {
    name: "delete_payroll_timesheet",
    description: "Delete an existing payroll timesheet.",
    input_schema: {
      type: "object",
      properties: {
        timesheetId: {
          type: "string",
          description: "Timesheet ID to delete"
        }
      },
      required: ['timesheetId']
    }
  }
];