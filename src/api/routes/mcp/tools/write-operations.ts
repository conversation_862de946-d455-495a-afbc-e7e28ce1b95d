// Write operations tools for creating and updating Xero data

export const writeOperationsTools = [
  // Create operations
  {
    name: "create_contact",
    description: "Create a new contact in Xero.",
    input_schema: {
      type: "object",
      properties: {
        name: {
          type: "string",
          description: "Contact name"
        },
        email: {
          type: "string",
          description: "Contact email address"
        },
        phone: {
          type: "string",
          description: "Contact phone number"
        },
        isCustomer: {
          type: "boolean",
          description: "Whether this contact is a customer"
        },
        isSupplier: {
          type: "boolean",
          description: "Whether this contact is a supplier"
        }
      },
      required: ['name"]
    }
  },
  {
    name: "create_invoice",
    description: "Create a new invoice in Xero.",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "Contact ID for the invoice"
        },
        type: {
          type: "string",
          description: "Invoice type (ACCREC for sales, ACCPAY for bills)"
        },
        lineItems: {
          type: "array",
          description: "Array of line items for the invoice",
          items: {
            type: "object",
            properties: {
              description: { type: "string" },
              quantity: { type: "number" },
              unitAmount: { type: "number" },
              accountCode: { type: "string" }
            }
          }
        },
        dueDate: {
          type: "string",
          description: "Due date (YYYY-MM-DD format)"
        }
      },
      required: ['contactId', 'type", 'lineItems"]
    }
  },
  {
    name: "create_payment",
    description: "Create a new payment in Xero.",
    input_schema: {
      type: "object",
      properties: {
        invoiceId: {
          type: "string",
          description: "Invoice ID to apply payment to"
        },
        accountId: {
          type: "string",
          description: "Bank account ID for the payment"
        },
        amount: {
          type: "number",
          description: "Payment amount"
        },
        date: {
          type: "string",
          description: "Payment date (YYYY-MM-DD format)"
        }
      },
      required: ['invoiceId', 'accountId", 'amount"]
    }
  },
  {
    name: "create_quote",
    description: "Create a new quote in Xero.",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "Contact ID for the quote"
        },
        lineItems: {
          type: "array",
          description: "Array of line items for the quote",
          items: {
            type: "object",
            properties: {
              description: { type: "string" },
              quantity: { type: "number" },
              unitAmount: { type: "number" }
            }
          }
        },
        expiryDate: {
          type: "string",
          description: "Quote expiry date (YYYY-MM-DD format)"
        }
      },
      required: ['contactId", 'lineItems"]
    }
  },
  {
    name: "create_credit_note",
    description: "Create a new credit note in Xero.",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "Contact ID for the credit note"
        },
        type: {
          type: "string",
          description: "Credit note type (ACCRECCREDIT for sales, ACCPAYCREDIT for purchases)"
        },
        lineItems: {
          type: "array",
          description: "Array of line items for the credit note",
          items: {
            type: "object",
            properties: {
              description: { type: "string" },
              quantity: { type: "number" },
              unitAmount: { type: "number" },
              accountCode: { type: "string" }
            }
          }
        }
      },
      required: ['contactId', 'type", 'lineItems"]
    }
  },
  // Update operations
  {
    name: "update_contact",
    description: "Update an existing contact in Xero.",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "Contact ID to update"
        },
        name: {
          type: "string",
          description: "Contact name"
        },
        email: {
          type: "string",
          description: "Contact email address"
        },
        phone: {
          type: "string",
          description: "Contact phone number"
        }
      },
      required: ['contactId"]
    }
  },
  {
    name: "update_invoice",
    description: "Update an existing draft invoice in Xero.",
    input_schema: {
      type: "object",
      properties: {
        invoiceId: {
          type: "string",
          description: "Invoice ID to update"
        },
        lineItems: {
          type: "array",
          description: "Array of line items for the invoice",
          items: {
            type: "object",
            properties: {
              description: { type: "string" },
              quantity: { type: "number" },
              unitAmount: { type: "number" },
              accountCode: { type: "string" }
            }
          }
        },
        dueDate: {
          type: "string",
          description: "Due date (YYYY-MM-DD format)"
        }
      },
      required: ['invoiceId"]
    }
  },
  {
    name: "update_quote",
    description: "Update an existing draft quote in Xero.",
    input_schema: {
      type: "object",
      properties: {
        quoteId: {
          type: "string",
          description: "Quote ID to update"
        },
        lineItems: {
          type: "array",
          description: "Array of line items for the quote",
          items: {
            type: "object",
            properties: {
              description: { type: "string" },
              quantity: { type: "number" },
              unitAmount: { type: "number" }
            }
          }
        },
        expiryDate: {
          type: "string",
          description: "Quote expiry date (YYYY-MM-DD format)"
        }
      },
      required: ['quoteId"]
    }
  },
  {
    name: "update_credit_note",
    description: "Update an existing draft credit note in Xero.",
    input_schema: {
      type: "object",
      properties: {
        creditNoteId: {
          type: "string",
          description: "Credit note ID to update"
        },
        lineItems: {
          type: "array",
          description: "Array of line items for the credit note",
          items: {
            type: "object",
            properties: {
              description: { type: "string" },
              quantity: { type: "number" },
              unitAmount: { type: "number" },
              accountCode: { type: "string" }
            }
          }
        }
      },
      required: ['creditNoteId']
    }
  }
];