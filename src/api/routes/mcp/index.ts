import { Router, Request, Response } from "express";
import { randomUUID } from "crypto";
import Anthropic from "@anthropic-ai/sdk";
import { AVAILABLE_TOOLS } from "./tools";
import { executeTool } from "./tool-executor";
import { mcpSessionManager, MCPSession } from "./mcp-session-manager";

// Extend the Express Request type to include session
declare module 'express-session' {
  interface SessionData {
    tokenSet?: any;
    tenantId?: string;
    userInfo?: {
      name?: string;
      email?: string;
      sub?: string;
    };
  }
}

const router = Router();

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || '',
});

/**
 * Initialize MCP session
 */
router.post('/init', async (req: Request, res: Response) => {
  try {
    const userId = req.session?.userInfo?.email || req.session?.userInfo?.sub || 'anonymous';

    console.log(`Initializing MCP session for user ${userId}`);

    // Check if we have Xero authentication
    if (!req.session?.tokenSet) {
      return res.status(401).json({
        error: 'Xero authentication required',
        message: 'Please authenticate with Xero first'
      });
    }

    // Check if Anthropic API key is configured
    if (!process.env.ANTHROPIC_API_KEY) {
      return res.status(500).json({
        error: 'Anthropic API not configured',
        message: 'Please set ANTHROPIC_API_KEY environment variable'
      });
    }

    // Create new session
    const session = mcpSessionManager.createSession(userId);

    res.json({
      sessionId: session.id,
      tools: AVAILABLE_TOOLS,
      status: "connected"
    });

  } catch (error) {
    console.error('Error initializing MCP session:', error);
    res.status(500).json({
      error: 'Failed to initialize MCP session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Send message to Claude and get response
 */
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { sessionId, message } = req.body;

    const session = mcpSessionManager.getSession(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Add user message to history
    mcpSessionManager.addMessage(session.id, { role: "user", content: message });

    console.log(`Processing message for session ${sessionId}: ${message}`);

    try {
      // Create the message with tools
      const response = await anthropic.messages.create({
        model: 'claude-3-5-sonnet-********',
        max_tokens: 4096,
        temperature: 0,
        system: `You are a helpful assistant integrated with Xero accounting software. You can help users access their financial data through the available tools. Be concise and helpful in your responses. Format data in a clear, readable way.`,
        messages: session.messages,
        tools: AVAILABLE_TOOLS as any,
      });

      // Process the response
      let assistantMessage = '';
      const toolCalls: any[] = [];

      for (const content of response.content) {
        if (content.type === 'text') {
          assistantMessage += content.text;
        } else if (content.type === 'tool_use') {
          console.log(`Claude wants to use tool: ${content.name} with args:`, content.input);

          try {
            // Execute the tool
            const toolResult = await executeTool(content.name, content.input, req);

            toolCalls.push({
              tool: content.name,
              arguments: content.input,
              result: toolResult
            });

            // Add tool use and result to message history
            mcpSessionManager.addMessage(session.id, {
              role: "assistant",
              content: [
                {
                  type: 'text',
                  text: assistantMessage
                },
                {
                  type: 'tool_use',
                  id: content.id,
                  name: content.name,
                  input: content.input
                }
              ]
            });

            mcpSessionManager.addMessage(session.id, {
              role: "user",
              content: [
                {
                  type: 'tool_result',
                  tool_use_id: content.id,
                  content: JSON.stringify(toolResult)
                }
              ]
            });

            // Get final response from Claude after tool execution
            const finalResponse = await anthropic.messages.create({
              model: 'claude-3-5-sonnet-********',
              max_tokens: 4096,
              temperature: 0,
              system: `You are a helpful assistant integrated with Xero accounting software. You can help users access their financial data through the available tools. Be concise and helpful in your responses. Format data in a clear, readable way.`,
              messages: session.messages,
              tools: AVAILABLE_TOOLS as any,
            });

            // Extract final text response
            for (const finalContent of finalResponse.content) {
              if (finalContent.type === 'text') {
                assistantMessage += finalContent.text;
              }
            }

          } catch (toolError) {
            console.error(`Error executing tool ${content.name}:`, toolError);
            toolCalls.push({
              tool: content.name,
              arguments: content.input,
              error: toolError instanceof Error ? toolError.message : 'Unknown error'
            });
          }
        }
      }

      // Add final assistant response to history
      mcpSessionManager.addMessage(session.id, {
        role: "assistant",
        content: assistantMessage
      });

      res.json({
        response: assistantMessage,
        toolCalls,
        sessionId
      });

    } catch (anthropicError) {
      console.error('Error calling Anthropic API:', anthropicError);
      res.status(500).json({
        error: 'Failed to get response from Claude',
        details: anthropicError instanceof Error ? anthropicError.message : 'Unknown error'
      });
    }

  } catch (error) {
    console.error('Error in chat endpoint:', error);
    res.status(500).json({
      error: 'Failed to process chat message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get chat history for a session
 */
router.get('/history/:sessionId', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;

    const session = mcpSessionManager.getSession(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Convert messages to a more readable format
    const history = session.messages.map(msg => ({
      role: msg.role,
      content: Array.isArray(msg.content) 
        ? msg.content
            .filter((c: any) => c.type === 'text')
            .map((c: any) => c.text)
            .join('')
        : msg.content
    }));

    res.json({
      history
    });

  } catch (error) {
    console.error('Error getting chat history:', error);
    res.status(500).json({
      error: 'Failed to get chat history',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Close MCP session
 */
router.post('/close', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.body;

    const success = mcpSessionManager.deleteSession(sessionId);

    res.json({
      success,
      message: success ? 'Session closed' : 'Session not found'
    });

  } catch (error) {
    console.error('Error closing session:', error);
    res.status(500).json({
      error: 'Failed to close session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Health check
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    status: "ok",
    activeSessions: mcpSessionManager.getActiveSessionCount(),
    anthropicConfigured: !!process.env.ANTHROPIC_API_KEY
  });
});

export default router;