import { randomUUID } from "crypto";
import Anthropic from "@anthropic-ai/sdk";

export interface MCPSession {
  id: string;
  userId: string;
  lastActivity: Date;
  messages: Anthropic.MessageParam[];
}

class MCPSessionManager {
  private sessions: Map<string, MCPSession> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up inactive sessions after 30 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  /**
   * Create a new MCP session
   */
  createSession(userId: string): MCPSession {
    const sessionId = randomUUID();
    console.log(`Creating MCP session ${sessionId} for user ${userId}`);

    const session: MCPSession = {
      id: sessionId,
      userId,
      lastActivity: new Date(),
      messages: []
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Get an existing session by ID
   */
  getSession(sessionId: string): MCPSession | null {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Update last activity
      session.lastActivity = new Date();
    }
    return session || null;
  }

  /**
   * Delete a session
   */
  deleteSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  /**
   * Get the number of active sessions
   */
  getActiveSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Add a message to a session
   */
  addMessage(sessionId: string, message: Anthropic.MessageParam): boolean {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.messages.push(message);
      session.lastActivity = new Date();
      return true;
    }
    return false;
  }

  /**
   * Clean up inactive sessions (older than 30 minutes)
   */
  private cleanupInactiveSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      const inactiveMinutes = (now.getTime() - session.lastActivity.getTime()) / 1000 / 60;
      if (inactiveMinutes > 30) {
        expiredSessions.push(sessionId);
      }
    }

    expiredSessions.forEach(sessionId => {
      console.log(`Cleaning up inactive MCP session: ${sessionId}`);
      this.sessions.delete(sessionId);
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.sessions.clear();
  }
}

// Export a singleton instance
export const mcpSessionManager = new MCPSessionManager();