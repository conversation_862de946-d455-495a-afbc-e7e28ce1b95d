/**
 * HubSpot MCP Tool Executor
 *
 * Executes HubSpot API operations based on tool calls from <PERSON>
 */

import { backendLogger as logger } from "../../../utils/backend-logger";
import { getHubSpotService } from "../../services/hubspot";
import { ContactRepository } from "../../repositories/contact-repository";
import { CompanyRepository } from "../../repositories/company-repository";
import { DealRepository } from "../../repositories/deal-repository";
import { ContactRelationshipsRepository } from "../../repositories/contact-relationships-repository";

interface ToolContext {
  accessToken: string;
}

/**
 * Execute a HubSpot tool with the given parameters
 */
export async function executeHubSpotTool(
  toolName: string,
  params: any,
  context: ToolContext,
): Promise<any> {
  logger.info("Executing HubSpot tool", { toolName, params });

  try {
    // Initialize repositories
    const contactRepo = new ContactRepository();
    const companyRepo = new CompanyRepository();
    const dealRepo = new DealRepository();
    const relationshipsRepo = new ContactRelationshipsRepository();
    const hubspotService = getHubSpotService();

    switch (toolName) {
      // Contact tools
      case "list_contacts":
        return await listContacts(params, contactRepo);

      case "search_contacts":
        return await searchContacts(params, contactRepo);

      case "get_contact":
        return await getContact(params, contactRepo);

      case "create_contact":
        return await createContact(params, contactRepo, hubspotService);

      case "update_contact":
        return await updateContact(params, contactRepo, hubspotService);

      // Company tools
      case "list_companies":
        return await listCompanies(params, companyRepo);

      case "search_companies":
        return await searchCompanies(params, companyRepo);

      case "get_company":
        return await getCompany(params, companyRepo);

      case "create_company":
        return await createCompany(params, companyRepo, hubspotService);

      case "update_company":
        return await updateCompany(params, companyRepo, hubspotService);

      // Deal tools
      case "list_deals":
        return await listDeals(params, dealRepo);

      case "search_deals":
        return await searchDeals(params, dealRepo);

      case "get_deal":
        return await getDeal(params, dealRepo);

      case "create_deal":
        return await createDeal(params, dealRepo, hubspotService);

      case "update_deal":
        return await updateDeal(params, dealRepo, hubspotService);

      // Relationship tools
      case "get_contact_companies":
        return await getContactCompanies(params, relationshipsRepo);

      case "get_company_contacts":
        return await getCompanyContacts(params, relationshipsRepo);

      case "associate_contact_company":
        return await associateContactCompany(params, relationshipsRepo);

      // Sync tools
      case "sync_hubspot_data":
        return await syncHubSpotData(params, hubspotService);

      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  } catch (error: any) {
    logger.error("Error executing HubSpot tool", {
      toolName,
      error: error.message,
    });
    throw error;
  }
}

// Contact tool implementations
async function listContacts(params: any, contactRepo: ContactRepository) {
  const { limit = 20, offset = 0 } = params;
  const contacts = contactRepo.getAllContacts();
  return {
    contacts: contacts.slice(offset, offset + limit),
    total: contacts.length,
    hasMore: offset + limit < contacts.length,
  };
}

async function searchContacts(params: any, contactRepo: ContactRepository) {
  const { query, limit = 20 } = params;
  const contacts = contactRepo.searchContacts({ search: query, limit });
  return { contacts, count: contacts.length };
}

async function getContact(params: any, contactRepo: ContactRepository) {
  const { contactId } = params;
  const contact = contactRepo.getContactById(contactId);
  if (!contact) {
    throw new Error(`Contact ${contactId} not found`);
  }
  return contact;
}

async function createContact(
  params: any,
  contactRepo: ContactRepository,
  hubspotService: any,
) {
  const contactData = {
    firstName: params.firstName,
    lastName: params.lastName,
    email: params.email,
    phone: params.phone,
    jobTitle: params.jobTitle,
    notes: params.notes,
    source: "HubSpot MCP",
  };

  const contact = contactRepo.createContact(contactData, "hubspot-mcp");

  // If HubSpot sync is enabled, sync to HubSpot
  if (params.syncToHubSpot !== false) {
    try {
      await hubspotService.syncContactToHubSpot(contact);
    } catch (error) {
      logger.error("Failed to sync contact to HubSpot", { error });
    }
  }

  return contact;
}

async function updateContact(
  params: any,
  contactRepo: ContactRepository,
  hubspotService: any,
) {
  const { contactId, ...updateData } = params;
  const updated = contactRepo.updateContact(
    contactId,
    updateData,
    "HubSpot MCP",
  );

  if (!updated) {
    throw new Error(`Failed to update contact ${contactId}`);
  }

  // If HubSpot sync is enabled, sync to HubSpot
  if (params.syncToHubSpot !== false && updated.hubspotId) {
    try {
      await hubspotService.syncContactToHubSpot(updated);
    } catch (error) {
      logger.error("Failed to sync contact update to HubSpot", { error });
    }
  }

  return updated;
}

// Company tool implementations
async function listCompanies(params: any, companyRepo: CompanyRepository) {
  const { limit = 20, offset = 0 } = params;
  const companies = companyRepo.getAllCompanies();
  return {
    companies: companies.slice(offset, offset + limit),
    total: companies.length,
    hasMore: offset + limit < companies.length,
  };
}

async function searchCompanies(params: any, companyRepo: CompanyRepository) {
  const { query, limit = 20 } = params;
  const companies = companyRepo.searchCompanies({ search: query, limit });
  return { companies, count: companies.length };
}

async function getCompany(params: any, companyRepo: CompanyRepository) {
  const { companyId } = params;
  const company = companyRepo.getCompanyById(companyId);
  if (!company) {
    throw new Error(`Company ${companyId} not found`);
  }
  return company;
}

async function createCompany(
  params: any,
  companyRepo: CompanyRepository,
  hubspotService: any,
) {
  const companyData = {
    name: params.name,
    domain: params.domain,
    industry: params.industry,
    phone: params.phone,
    address: params.address,
    city: params.city,
    state: params.state,
    country: params.country,
    numberOfEmployees: params.numberOfEmployees,
    annualRevenue: params.annualRevenue,
    description: params.description,
    website: params.website,
    source: "HubSpot MCP",
  };

  const company = companyRepo.createCompany(companyData, "hubspot-mcp");

  // If HubSpot sync is enabled, sync to HubSpot
  if (params.syncToHubSpot !== false) {
    try {
      await hubspotService.syncCompanyToHubSpot(company);
    } catch (error) {
      logger.error("Failed to sync company to HubSpot", { error });
    }
  }

  return company;
}

async function updateCompany(
  params: any,
  companyRepo: CompanyRepository,
  hubspotService: any,
) {
  const { companyId, ...updateData } = params;
  const updated = companyRepo.updateCompany(
    companyId,
    updateData,
    "HubSpot MCP",
  );

  if (!updated) {
    throw new Error(`Failed to update company ${companyId}`);
  }

  // If HubSpot sync is enabled, sync to HubSpot
  if (params.syncToHubSpot !== false && updated.hubspotId) {
    try {
      await hubspotService.syncCompanyToHubSpot(updated);
    } catch (error) {
      logger.error("Failed to sync company update to HubSpot", { error });
    }
  }

  return updated;
}

// Deal tool implementations
async function listDeals(params: any, dealRepo: DealRepository) {
  const { limit = 20, includeProjectionStatus = false } = params;
  const deals = dealRepo.getAllDeals(includeProjectionStatus);
  return {
    deals: deals.slice(0, limit),
    total: deals.length,
  };
}

async function searchDeals(params: any, dealRepo: DealRepository) {
  const { stage, minValue, maxValue } = params;
  let deals = dealRepo.getAllDeals(true);

  // Filter by stage
  if (stage) {
    deals = deals.filter((d) => d.stage === stage);
  }

  // Filter by value range
  if (minValue !== undefined) {
    deals = deals.filter((d) => (d.value || 0) >= minValue);
  }
  if (maxValue !== undefined) {
    deals = deals.filter((d) => (d.value || 0) <= maxValue);
  }

  return { deals, count: deals.length };
}

async function getDeal(params: any, dealRepo: DealRepository) {
  const { dealId } = params;
  const deal = dealRepo.getDealById(dealId);
  if (!deal) {
    throw new Error(`Deal ${dealId} not found`);
  }
  return deal;
}

async function createDeal(
  params: any,
  dealRepo: DealRepository,
  hubspotService: any,
) {
  const dealData = {
    name: params.name,
    stage: params.stage || "Identified",
    value: params.value,
    currency: params.currency || "AUD",
    probability: params.probability,
    expectedCloseDate: params.expectedCloseDate,
    companyId: params.companyId,
    description: params.description,
    owner: params.owner,
    source: "HubSpot MCP",
  };

  const deal = dealRepo.createDeal(dealData, "HubSpot MCP");

  // If HubSpot sync is enabled, sync to HubSpot
  if (params.syncToHubSpot !== false) {
    try {
      await hubspotService.syncDealToHubSpot(deal);
    } catch (error) {
      logger.error("Failed to sync deal to HubSpot", { error });
    }
  }

  return deal;
}

async function updateDeal(
  params: any,
  dealRepo: DealRepository,
  hubspotService: any,
) {
  const { dealId, ...updateData } = params;
  const updated = dealRepo.updateDeal(dealId, updateData, "HubSpot MCP");

  if (!updated) {
    throw new Error(`Failed to update deal ${dealId}`);
  }

  // If HubSpot sync is enabled, sync to HubSpot
  if (params.syncToHubSpot !== false && updated.hubspotId) {
    try {
      await hubspotService.syncDealToHubSpot(updated);
    } catch (error) {
      logger.error("Failed to sync deal update to HubSpot", { error });
    }
  }

  return updated;
}

// Relationship tool implementations
async function getContactCompanies(
  params: any,
  relationshipsRepo: ContactRelationshipsRepository,
) {
  const { contactId } = params;
  const companies = relationshipsRepo.getContactCompanies(contactId);
  return { companies, count: companies.length };
}

async function getCompanyContacts(
  params: any,
  relationshipsRepo: ContactRelationshipsRepository,
) {
  const { companyId } = params;
  const contacts = relationshipsRepo.getCompanyContacts(companyId);
  return { contacts, count: contacts.length };
}

async function associateContactCompany(
  params: any,
  relationshipsRepo: ContactRelationshipsRepository,
) {
  const { contactId, companyId, isPrimary = false } = params;
  const success = relationshipsRepo.associateContactWithCompany(
    contactId,
    companyId,
    isPrimary,
  );

  if (!success) {
    throw new Error(
      `Failed to associate contact ${contactId} with company ${companyId}`,
    );
  }

  return { success: true, message: "Association created successfully" };
}

// Sync tool implementation
async function syncHubSpotData(params: any, hubspotService: any) {
  const { entityType = "all", limit = 100 } = params;

  const results = {
    contacts: { synced: 0, errors: 0 },
    companies: { synced: 0, errors: 0 },
    deals: { synced: 0, errors: 0 },
  };

  try {
    if (entityType === "all" || entityType === "contacts") {
      const contactResult = await hubspotService.syncContacts(limit);
      results.contacts = contactResult;
    }

    if (entityType === "all" || entityType === "companies") {
      const companyResult = await hubspotService.syncCompanies(limit);
      results.companies = companyResult;
    }

    if (entityType === "all" || entityType === "deals") {
      const dealResult = await hubspotService.syncDeals(limit);
      results.deals = dealResult;
    }

    return {
      success: true,
      results,
      message: `Synchronization completed successfully`,
    };
  } catch (error: any) {
    logger.error("Error during HubSpot sync", { error: error.message });
    return {
      success: false,
      results,
      error: error.message,
    };
  }
}
