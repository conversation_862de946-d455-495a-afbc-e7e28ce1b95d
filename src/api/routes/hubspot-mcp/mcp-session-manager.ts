/**
 * HubSpot MCP Session Manager
 *
 * Manages MCP sessions for HubSpot AI interactions
 * Handles session lifecycle, message history, and cleanup
 */

import { v4 as uuidv4 } from "uuid";
import { backendLogger as logger } from "../../../utils/backend-logger";

export interface HubSpotMCPSession {
  id: string;
  userId: string;
  messages: any[];
  created: Date;
  lastActivity: Date;
}

/**
 * Manages HubSpot MCP sessions with automatic cleanup
 */
export class HubSpotMCPSessionManager {
  private sessions: Map<string, HubSpotMCPSession> = new Map();
  private cleanupInterval: NodeJS.Timeout;
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveSessions();
    }, this.CLEANUP_INTERVAL);

    logger.info("HubSpot MCP Session Manager initialized");
  }

  /**
   * Create a new session
   */
  createSession(userId: string, sessionId?: string): string {
    const id = sessionId || uuidv4();
    const session: HubSpotMCPSession = {
      id,
      userId,
      messages: [],
      created: new Date(),
      lastActivity: new Date(),
    };

    this.sessions.set(id, session);
    logger.info("HubSpot MCP session created", { sessionId: id, userId });

    return id;
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): HubSpotMCPSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) {
      logger.warn("HubSpot MCP session not found", { sessionId });
      return null;
    }
    return session;
  }

  /**
   * Update session activity timestamp
   */
  updateActivity(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastActivity = new Date();
    }
  }

  /**
   * Close a session
   */
  closeSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.sessions.delete(sessionId);
      logger.info("HubSpot MCP session closed", {
        sessionId,
        userId: session.userId,
        messageCount: session.messages.length,
        duration: Date.now() - session.created.getTime(),
      });
    }
  }

  /**
   * Clean up inactive sessions
   */
  private cleanupInactiveSessions(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      const inactiveTime = now - session.lastActivity.getTime();
      if (inactiveTime > this.SESSION_TIMEOUT) {
        this.closeSession(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info("Cleaned up inactive HubSpot MCP sessions", {
        count: cleanedCount,
      });
    }
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): HubSpotMCPSession[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Get session count
   */
  getSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Destroy the session manager
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.sessions.clear();
    logger.info("HubSpot MCP Session Manager destroyed");
  }
}
