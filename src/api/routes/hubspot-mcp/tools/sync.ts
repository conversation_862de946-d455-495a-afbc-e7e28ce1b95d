/**
 * HubSpot Sync Tools
 * 
 * MCP tool definitions for synchronizing data with HubSpot
 */

export const syncTools = [
  {
    name: "sync_hubspot_data",
    description: "Synchronize data between local CRM and HubSpot",
    input_schema: {
      type: "object",
      properties: {
        entityType: {
          type: "string",
          description: "Type of entities to sync (default: "all')",
          enum: ['all', 'contacts', 'companies', 'deals'],
          default: "all"
        },
        limit: {
          type: "integer",
          description: "Maximum number of entities to sync per type (default: 100)",
          default: 100
        }
      },
      required: []
    }
  }
];