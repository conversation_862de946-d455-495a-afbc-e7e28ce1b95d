/**
 * HubSpot Relationship Tools
 * 
 * MCP tool definitions for managing relationships between entities
 */

export const relationshipTools = [
  {
    name: "get_contact_companies",
    description: "Get all companies associated with a specific contact",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "The unique ID of the contact"
        }
      },
      required: ['contactId"]
    }
  },
  {
    name: "get_company_contacts",
    description: "Get all contacts associated with a specific company",
    input_schema: {
      type: "object",
      properties: {
        companyId: {
          type: "string",
          description: "The unique ID of the company"
        }
      },
      required: ['companyId"]
    }
  },
  {
    name: "associate_contact_company",
    description: "Create an association between a contact and a company",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "The unique ID of the contact"
        },
        companyId: {
          type: "string",
          description: "The unique ID of the company"
        },
        isPrimary: {
          type: "boolean",
          description: "Whether this is the primary company for the contact (default: false)",
          default: false
        }
      },
      required: ['contactId", 'companyId']
    }
  }
];