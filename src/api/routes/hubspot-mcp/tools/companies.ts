/**
 * HubSpot Company Tools
 * 
 * MCP tool definitions for company operations
 */

export const companyTools = [
  {
    name: "list_companies",
    description: "List companies from the CRM database with pagination support",
    input_schema: {
      type: "object",
      properties: {
        limit: {
          type: "integer",
          description: "Maximum number of companies to return (default: 20, max: 100)",
          default: 20
        },
        offset: {
          type: "integer",
          description: "Number of companies to skip for pagination (default: 0)",
          default: 0
        }
      },
      required: []
    }
  },
  {
    name: "search_companies",
    description: "Search for companies by name, domain, or other properties",
    input_schema: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query to match against company names, domains, etc."
        },
        limit: {
          type: "integer",
          description: "Maximum number of results to return (default: 20)",
          default: 20
        }
      },
      required: ['query']
    }
  },
  {
    name: "get_company",
    description: "Get detailed information about a specific company",
    input_schema: {
      type: "object",
      properties: {
        companyId: {
          type: "string",
          description: "The unique ID of the company to retrieve"
        }
      },
      required: ['companyId']
    }
  },
  {
    name: "create_company",
    description: "Create a new company in the CRM",
    input_schema: {
      type: "object",
      properties: {
        name: {
          type: "string",
          description: "Company name"
        },
        domain: {
          type: "string",
          description: "Company website domain"
        },
        industry: {
          type: "string",
          description: "Company industry"
        },
        phone: {
          type: "string",
          description: "Company phone number"
        },
        address: {
          type: "string",
          description: "Company street address"
        },
        city: {
          type: "string",
          description: "Company city"
        },
        state: {
          type: "string",
          description: "Company state or region"
        },
        country: {
          type: "string",
          description: "Company country"
        },
        numberOfEmployees: {
          type: "integer",
          description: "Number of employees"
        },
        annualRevenue: {
          type: "number",
          description: "Annual revenue in dollars"
        },
        description: {
          type: "string",
          description: "Company description"
        },
        website: {
          type: "string",
          description: "Company website URL"
        },
        syncToHubSpot: {
          type: "boolean",
          description: "Whether to sync this company to HubSpot (default: true)",
          default: true
        }
      },
      required: ['name']
    }
  },
  {
    name: "update_company",
    description: "Update an existing company\"s information',
    input_schema: {
      type: "object",
      properties: {
        companyId: {
          type: "string",
          description: "The unique ID of the company to update"
        },
        name: {
          type: "string",
          description: "Updated company name"
        },
        domain: {
          type: "string",
          description: "Updated website domain"
        },
        industry: {
          type: "string",
          description: "Updated industry"
        },
        phone: {
          type: "string",
          description: "Updated phone number"
        },
        address: {
          type: "string",
          description: "Updated street address"
        },
        city: {
          type: "string",
          description: "Updated city"
        },
        state: {
          type: "string",
          description: "Updated state or region"
        },
        country: {
          type: "string",
          description: "Updated country"
        },
        numberOfEmployees: {
          type: "integer",
          description: "Updated number of employees"
        },
        annualRevenue: {
          type: "number",
          description: "Updated annual revenue"
        },
        description: {
          type: "string",
          description: "Updated description"
        },
        website: {
          type: "string",
          description: "Updated website URL"
        },
        syncToHubSpot: {
          type: "boolean",
          description: "Whether to sync updates to HubSpot (default: true)",
          default: true
        }
      },
      required: ['companyId']
    }
  }
];