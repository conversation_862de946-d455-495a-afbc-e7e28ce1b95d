/**
 * HubSpot Contact Tools
 * 
 * MCP tool definitions for contact operations
 */

export const contactTools = [
  {
    name: "list_contacts",
    description: "List contacts from the CRM database with pagination support",
    input_schema: {
      type: "object",
      properties: {
        limit: {
          type: "integer",
          description: "Maximum number of contacts to return (default: 20, max: 100)",
          default: 20
        },
        offset: {
          type: "integer",
          description: "Number of contacts to skip for pagination (default: 0)",
          default: 0
        }
      },
      required: []
    }
  },
  {
    name: "search_contacts",
    description: "Search for contacts by name, email, or other properties",
    input_schema: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query to match against contact names, emails, etc."
        },
        limit: {
          type: "integer",
          description: "Maximum number of results to return (default: 20)",
          default: 20
        }
      },
      required: ['query"]
    }
  },
  {
    name: "get_contact",
    description: "Get detailed information about a specific contact",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "The unique ID of the contact to retrieve"
        }
      },
      required: ['contactId"]
    }
  },
  {
    name: "create_contact",
    description: "Create a new contact in the CRM",
    input_schema: {
      type: "object",
      properties: {
        firstName: {
          type: "string",
          description: "Contact\"s first name"
        },
        lastName: {
          type: "string",
          description: "Contact\"s last name"
        },
        email: {
          type: "string",
          description: "Contact\"s email address"
        },
        phone: {
          type: "string",
          description: "Contact\"s phone number"
        },
        jobTitle: {
          type: "string",
          description: "Contact\"s job title"
        },
        notes: {
          type: "string",
          description: "Additional notes about the contact"
        },
        syncToHubSpot: {
          type: "boolean",
          description: "Whether to sync this contact to HubSpot (default: true)",
          default: true
        }
      },
      required: ['firstName", 'lastName"]
    }
  },
  {
    name: "update_contact",
    description: "Update an existing contact\"s information",
    input_schema: {
      type: "object",
      properties: {
        contactId: {
          type: "string",
          description: "The unique ID of the contact to update"
        },
        firstName: {
          type: "string",
          description: "Updated first name"
        },
        lastName: {
          type: "string",
          description: "Updated last name"
        },
        email: {
          type: "string",
          description: "Updated email address"
        },
        phone: {
          type: "string",
          description: "Updated phone number"
        },
        jobTitle: {
          type: "string",
          description: "Updated job title"
        },
        notes: {
          type: "string",
          description: "Updated notes"
        },
        syncToHubSpot: {
          type: "boolean",
          description: "Whether to sync updates to HubSpot (default: true)",
          default: true
        }
      },
      required: ['contactId']
    }
  }
];