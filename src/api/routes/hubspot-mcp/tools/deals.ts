/**
 * HubSpot Deal Tools
 * 
 * MCP tool definitions for deal operations
 */

export const dealTools = [
  {
    name: "list_deals",
    description: "List deals from the CRM database with optional projection status",
    input_schema: {
      type: "object",
      properties: {
        limit: {
          type: "integer",
          description: "Maximum number of deals to return (default: 20, max: 100)",
          default: 20
        },
        includeProjectionStatus: {
          type: "boolean",
          description: "Include whether deals are valid for financial projections",
          default: false
        }
      },
      required: []
    }
  },
  {
    name: "search_deals",
    description: "Search for deals by stage, value range, or other criteria",
    input_schema: {
      type: "object",
      properties: {
        stage: {
          type: "string",
          description: "Filter by deal stage (e.g., "Qualified", "Closed won')',
          enum: [
            'Identified',
            'Qualified',
            'Solution proposal',
            'Solution presentation',
            'Objection handling',
            'Finalising terms',
            'Closed won',
            'Closed lost',
            'Abandoned'
          ]
        },
        minValue: {
          type: "number",
          description: "Minimum deal value"
        },
        maxValue: {
          type: "number",
          description: "Maximum deal value"
        }
      },
      required: []
    }
  },
  {
    name: "get_deal",
    description: "Get detailed information about a specific deal including contacts and notes",
    input_schema: {
      type: "object",
      properties: {
        dealId: {
          type: "string",
          description: "The unique ID of the deal to retrieve"
        }
      },
      required: ['dealId']
    }
  },
  {
    name: "create_deal",
    description: "Create a new deal in the CRM",
    input_schema: {
      type: "object",
      properties: {
        name: {
          type: "string",
          description: "Deal name"
        },
        stage: {
          type: "string",
          description: "Deal stage (default: 'Identified')",
          enum: [
            'Identified',
            'Qualified',
            'Solution proposal',
            'Solution presentation',
            'Objection handling',
            'Finalising terms',
            'Closed won',
            'Closed lost',
            'Abandoned'
          ],
          default: "Identified"
        },
        value: {
          type: "number",
          description: "Deal value amount"
        },
        currency: {
          type: "string",
          description: "Currency code (default: 'AUD')",
          enum: ['AUD', 'USD', 'EUR', 'GBP'],
          default: "AUD"
        },
        probability: {
          type: "number",
          description: "Probability of closing (0-1)",
          minimum: 0,
          maximum: 1
        },
        expectedCloseDate: {
          type: "string",
          description: "Expected close date (ISO 8601 format)"
        },
        companyId: {
          type: "string",
          description: "ID of the associated company"
        },
        description: {
          type: "string",
          description: "Deal description"
        },
        owner: {
          type: "string",
          description: "Deal owner name"
        },
        syncToHubSpot: {
          type: "boolean",
          description: "Whether to sync this deal to HubSpot (default: true)",
          default: true
        }
      },
      required: ['name', 'companyId']
    }
  },
  {
    name: "update_deal",
    description: "Update an existing deal\"s information',
    input_schema: {
      type: "object",
      properties: {
        dealId: {
          type: "string",
          description: "The unique ID of the deal to update"
        },
        name: {
          type: "string",
          description: "Updated deal name"
        },
        stage: {
          type: "string",
          description: "Updated deal stage",
          enum: [
            'Identified',
            'Qualified',
            'Solution proposal',
            'Solution presentation',
            'Objection handling',
            'Finalising terms',
            'Closed won',
            'Closed lost',
            'Abandoned'
          ]
        },
        value: {
          type: "number",
          description: "Updated deal value"
        },
        currency: {
          type: "string",
          description: "Updated currency code",
          enum: ['AUD', 'USD', 'EUR', 'GBP']
        },
        probability: {
          type: "number",
          description: "Updated probability (0-1)",
          minimum: 0,
          maximum: 1
        },
        expectedCloseDate: {
          type: "string",
          description: "Updated expected close date (ISO 8601 format)"
        },
        description: {
          type: "string",
          description: "Updated description"
        },
        owner: {
          type: "string",
          description: "Updated owner"
        },
        syncToHubSpot: {
          type: "boolean",
          description: "Whether to sync updates to HubSpot (default: true)",
          default: true
        }
      },
      required: ['dealId']
    }
  }
];