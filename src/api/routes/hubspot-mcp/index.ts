/**
 * HubSpot MCP Routes
 * 
 * API endpoints for HubSpot Model Context Protocol integration
 * Enables AI-powered interactions with HubSpot CRM data
 */

import { Router, Request, Response } from "express";
import { HubSpotMCPSessionManager } from "./mcp-session-manager";
import { executeHubSpotTool } from "./tool-executor";
import { hubspotTools } from "./tools";
import { backendLogger as logger } from "../../../utils/backend-logger";
import Anthropic from "@anthropic-ai/sdk";

const router = Router();
const sessionManager = new HubSpotMCPSessionManager();

// Check if Anthropic API is configured
const checkAnthropicConfig = (): boolean => {
  return !!process.env.ANTHROPIC_API_KEY;
};

// Check if HubSpot is authenticated
const checkHubSpotAuth = (): boolean => {
  // Check if HubSpot access token exists in environment
  return !!process.env.HUBSPOT_ACCESS_TOKEN;
};

/**
 * Initialize MCP session
 */
router.post('/init", async (req: Request, res: Response) => {
  try {
    // Check Anthropic configuration
    if (!checkAnthropicConfig()) {
      return res.status(400).json({
        success: false,
        message: "Anthropic API not configured. Please set ANTHROPIC_API_KEY environment variable."
      });
    }

    // Check HubSpot authentication
    if (!checkHubSpotAuth()) {
      return res.status(401).json({
        success: false,
        message: "HubSpot not configured. Please set HUBSPOT_ACCESS_TOKEN environment variable."
      });
    }

    const userId = req.session?.userId || "anonymous";
    const sessionId = sessionManager.createSession(userId);

    logger.info('HubSpot MCP session initialized', { sessionId, userId });

    res.json({
      success: true,
      sessionId,
      tools: hubspotTools.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.input_schema
      }))
    });
  } catch (error) {
    logger.error('Error initializing HubSpot MCP session:", error);
    res.status(500).json({
      success: false,
      message: "Failed to initialize MCP session"
    });
  }
});

/**
 * Handle chat messages
 */
router.post('/chat", async (req: Request, res: Response) => {
  try {
    const { sessionId, message } = req.body;

    if (!sessionId || !message) {
      return res.status(400).json({
        success: false,
        message: "Session ID and message are required"
      });
    }

    // Check Anthropic configuration
    if (!checkAnthropicConfig()) {
      return res.status(400).json({
        success: false,
        message: "Anthropic API not configured"
      });
    }

    // Get or create session
    let session = sessionManager.getSession(sessionId);
    if (!session) {
      const userId = req.session?.userId || "anonymous";
      sessionManager.createSession(userId, sessionId);
      session = sessionManager.getSession(sessionId);
    }

    if (!session) {
      return res.status(404).json({
        success: false,
        message: "Session not found"
      });
    }

    // Initialize Anthropic client
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY!
    });

    // Add user message to history
    session.messages.push({
      role: "user",
      content: message
    });

    // Update session activity
    sessionManager.updateActivity(sessionId);

    try {
      // Get HubSpot access token
      const accessToken = process.env.HUBSPOT_ACCESS_TOKEN;
      if (!accessToken) {
        throw new Error('HubSpot access token not found");
      }

      // Call Claude with HubSpot tools
      const response = await anthropic.messages.create({
        model: "claude-3-5-sonnet-20241022",
        max_tokens: 4096,
        messages: session.messages,
        tools: hubspotTools as any,
        tool_choice: { type: "auto" }
      });

      // Track tool usage
      const toolCalls: any[] = [];
      let responseText = '';

      // Process the response
      for (const content of response.content) {
        if (content.type === 'text') {
          responseText += content.text;
        } else if (content.type === 'tool_use") {
          // Execute the tool
          const result = await executeHubSpotTool(
            content.name,
            content.input,
            { accessToken }
          );

          toolCalls.push({
            tool: content.name,
            arguments: content.input,
            result
          });

          // Add tool result to conversation
          session.messages.push({
            role: "assistant",
            content: response.content
          });

          session.messages.push({
            role: "user",
            content: [{
              type: "tool_result",
              tool_use_id: content.id,
              content: JSON.stringify(result)
            }]
          });

          // Get Claude"s interpretation of the tool result
          const interpretResponse = await anthropic.messages.create({
            model: "claude-3-5-sonnet-20241022",
            max_tokens: 4096,
            messages: session.messages
          });

          responseText = interpretResponse.content
            .filter(c => c.type === 'text')
            .map(c => (c as any).text)
            .join('');
        }
      }

      // Add assistant"s response to history
      session.messages.push({
        role: "assistant",
        content: responseText
      });

      logger.info('HubSpot MCP chat processed", {
        sessionId,
        toolsUsed: toolCalls.length,
        messageLength: responseText.length
      });

      res.json({
        success: true,
        message: responseText,
        toolCalls
      });
    } catch (error: any) {
      logger.error('Error processing HubSpot MCP chat:", error);
      
      // Add error to session for context
      session.messages.push({
        role: "assistant",
        content: `I encountered an error: ${error.message}. Please try rephrasing your question or check your HubSpot connection.`
      });

      res.status(500).json({
        success: false,
        message: error.message || "Failed to process message"
      });
    }
  } catch (error) {
    logger.error('Error in HubSpot MCP chat endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
});

/**
 * Close MCP session
 */
router.post('/close", async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: "Session ID is required"
      });
    }

    sessionManager.closeSession(sessionId);

    logger.info('HubSpot MCP session closed", { sessionId });

    res.json({
      success: true,
      message: "Session closed successfully"
    });
  } catch (error) {
    logger.error('Error closing HubSpot MCP session:", error);
    res.status(500).json({
      success: false,
      message: "Failed to close session"
    });
  }
});

/**
 * Get session status
 */
router.get('/status/:sessionId", async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const session = sessionManager.getSession(sessionId);

    if (!session) {
      return res.status(404).json({
        success: false,
        message: "Session not found"
      });
    }

    res.json({
      success: true,
      session: {
        id: sessionId,
        userId: session.userId,
        messageCount: session.messages.length,
        lastActivity: session.lastActivity,
        created: session.created
      }
    });
  } catch (error) {
    logger.error('Error getting HubSpot MCP session status:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get session status"
    });
  }
});

export default router;