import express from "express";
import { estimateDraftsRepository } from "../repositories/estimate-drafts-repository";
import { EstimateAllocationRepository } from "../repositories/estimate-allocation-repository";
import { DraftEstimateCreate, DraftEstimateUpdate } from "../../types/api";
import { updateDealsFromEstimate } from "../../utils/estimate-deal-sync";
import activityLogger from "../../utils/activity-logger";
// Removed unused Session import

// Initialize repository
const allocationRepository = new EstimateAllocationRepository();

// Extended Session interface with user property
declare module 'express-session' {
  interface Session {
    user?: {
      id: string;
      [key: string]: any;
    };
  }
}

const router = express.Router();

/**
 * Get all draft estimates (visible to all authenticated users)
 */
router.get('/drafts', async (req, res) => {
  try {
    // Authentication is handled by ensureAuthenticated middleware
    // Removed redundant check: if (!req.session?.user?.id)

    // Call findAll without userId to get all drafts
    const drafts = await estimateDraftsRepository.findAll();
    console.log('API - GET /drafts - Returning drafts:', drafts.map(d => ({
      uuid: d.uuid,
      projectName: d.projectName,
      totalFees: d.totalFees
    })));
    return res.json(drafts);
  } catch (error) {
    console.error('Error getting all draft estimates:', error);
    return res.status(500).json({
      error: "Failed to retrieve draft estimates",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get a specific draft estimate by UUID
 */
router.get('/drafts/:uuid', async (req, res) => {
  try {
    const { uuid } = req.params;
    const draft = await estimateDraftsRepository.findByUuid(uuid);

    if (!draft) {
      return res.status(404).json({ error: "Draft estimate not found" });
    }

    return res.json(draft);
  } catch (error) {
    console.error('Error getting draft estimate:', error);
    return res.status(500).json({
      error: "Failed to retrieve draft estimate",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Create a new draft estimate
 */
router.post('/drafts', async (req, res) => {
  try {
    const draftData = req.body as DraftEstimateCreate;

    // Log the request body for debugging
    console.log('API - Create draft estimate - Request body:', {
      invoiceFrequency: draftData.invoiceFrequency,
      paymentTerms: draftData.paymentTerms,
      fullBody: req.body
    });

    // Add user ID from session (using correct path: userInfo.sub)
    // Provide a fallback, although ensureAuthenticated should guarantee session exists
    draftData.userId = req.session?.userInfo?.sub || "unknown-user";

    // Validate required fields
    if (!draftData.companyId || !draftData.clientName || !draftData.startDate || !draftData.endDate) {
      return res.status(400).json({
        error: "Missing required fields",
        requiredFields: ['companyId', 'clientName', 'startDate', 'endDate']
      });
    }

    const createdDraft = await estimateDraftsRepository.create(draftData);

    // Log the estimate creation activity
    try {
      await activityLogger.logEstimateCreated(
        createdDraft.uuid,
        `${createdDraft.clientName} - ${createdDraft.projectName || "Untitled Project"}`,
        draftData.userId
      );
    } catch (activityError) {
      console.error('Error logging estimate creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    return res.status(201).json(createdDraft);
  } catch (error) {
    console.error('Error creating draft estimate:', error);
    return res.status(500).json({
      error: "Failed to create draft estimate",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Update an existing draft estimate
 */
router.put('/drafts/:uuid', async (req, res) => {
  try {
    const { uuid } = req.params;
    const draftData = req.body as DraftEstimateUpdate;

    // Log the request body for debugging
    console.log('API - Update draft estimate - Request body:', {
      uuid,
      invoiceFrequency: draftData.invoiceFrequency,
      paymentTerms: draftData.paymentTerms,
      fullBody: req.body
    });

    // Verify user has access to this draft
    const existingDraft = await estimateDraftsRepository.findByUuid(uuid);
    if (!existingDraft) {
      return res.status(404).json({ error: "Draft estimate not found" });
    }

    // Ensure user is authenticated (using consistent path)
    if (!req.session?.userInfo?.sub) { // <--- Changed from user.id
      return res.status(401).json({ error: "Unauthorized" });
    }

    const updatedDraft = await estimateDraftsRepository.update(uuid, draftData);
    if (!updatedDraft) {
      return res.status(404).json({ error: "Failed to update draft estimate" });
    }

    // Update any linked deals with the new estimate data
    // Run this asynchronously so it doesn't block the response
    updateDealsFromEstimate(uuid, 'internal').catch(err => {
      console.error('Error updating linked deals:', err);
    });

    return res.json(updatedDraft);
  } catch (error) {
    console.error('Error updating draft estimate:', error);
    return res.status(500).json({
      error: "Failed to update draft estimate",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Delete a draft estimate
 */
router.delete('/drafts/:uuid', async (req, res) => {
  try {
    const { uuid } = req.params;

    // Verify user has access to this draft
    const existingDraft = await estimateDraftsRepository.findByUuid(uuid);
    if (!existingDraft) {
      return res.status(404).json({ 
        error: "The estimate may have already been deleted or does not exist."
      });
    }

    // Ensure user is authenticated (using consistent path)
    if (!req.session?.userInfo?.sub) { // <--- Changed from user.id
      return res.status(401).json({ error: "Unauthorized" });
    }

    const success = await estimateDraftsRepository.delete(uuid);
    if (!success) {
      return res.status(500).json({ error: "Failed to delete draft estimate" });
    }

    return res.status(204).end();
  } catch (error) {
    console.error('Error deleting draft estimate:', error);
    return res.status(500).json({
      error: "Failed to delete draft estimate",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Mark a draft as published when saved to Harvest
 */
router.post('/drafts/:uuid/publish', async (req, res) => {
  try {
    const { uuid } = req.params;
    const { harvestEstimateId } = req.body;

    if (!harvestEstimateId) {
      return res.status(400).json({ error: "Missing required field: harvestEstimateId" });
    }

    // Verify user has access to this draft
    const existingDraft = await estimateDraftsRepository.findByUuid(uuid);
    if (!existingDraft) {
      return res.status(404).json({ error: "Draft estimate not found" });
    }

    // Ensure user is authenticated (using consistent path)
    if (!req.session?.userInfo?.sub) { // <--- Changed from user.id
      return res.status(401).json({ error: "Unauthorized" });
    }

    const updatedDraft = await estimateDraftsRepository.markAsPublished(uuid, harvestEstimateId);
    if (!updatedDraft) {
      return res.status(500).json({ error: "Failed to mark draft as published" });
    }

    // Log the estimate publishing activity
    try {
      await activityLogger.logEstimatePublished(
        updatedDraft.uuid,
        `${updatedDraft.clientName} - ${updatedDraft.projectName || "Untitled Project"}`,
        req.session?.userInfo?.sub || "unknown-user"
      );
    } catch (activityError) {
      console.error('Error logging estimate publishing activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    // Update any linked deals with the new estimate data
    // Run this asynchronously so it doesn't block the response
    updateDealsFromEstimate(uuid, 'internal').catch(err => {
      console.error('Error updating linked deals after publishing:', err);
    });

    // Also update any deals that might be linked to the new Harvest estimate
    updateDealsFromEstimate(harvestEstimateId.toString(), 'harvest').catch(err => {
      console.error('Error updating linked deals for Harvest estimate:', err);
    });

    return res.json(updatedDraft);
  } catch (error) {
    console.error('Error publishing draft estimate:', error);
    return res.status(500).json({
      error: "Failed to publish draft estimate",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Update allocation order for an estimate
 * Used for drag-and-drop reordering in the time allocation grid
 */
router.put('/drafts/:estimateId/allocation-order', async (req, res) => {
  try {
    const { estimateId } = req.params;
    const { orderedAllocationIds } = req.body;

    if (!orderedAllocationIds || !Array.isArray(orderedAllocationIds)) {
      return res.status(400).json({ 
        error: "Missing or invalid orderedAllocationIds array" 
      });
    }

    // Ensure user is authenticated
    if (!req.session?.userInfo?.sub) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    // Update the allocation order
    const success = allocationRepository.updateAllocationOrder(estimateId, orderedAllocationIds);
    
    if (!success) {
      return res.status(500).json({ 
        error: "Failed to update allocation order" 
      });
    }

    // Log the activity
    try {
      // Skip activity logging for now - logActivity method doesn't exist
      // TODO: Implement proper activity logging for allocation reordering
    } catch (activityError) {
      console.error('Error logging allocation reorder activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    return res.json({ 
      success: true, 
      message: "Allocation order updated successfully" 
    });
  } catch (error) {
    console.error('Error updating allocation order:', error);
    return res.status(500).json({
      error: "Failed to update allocation order",
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;
