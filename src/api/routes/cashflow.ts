import express from "express";
import { CashflowController } from "../controllers/cashflow";
import { CashflowSnapshotController } from "../controllers/cashflow-snapshot";

const router = express.Router();
const cashflowController = new CashflowController();
const cashflowSnapshotController = new CashflowSnapshotController();

// Deprecated routes have been removed

/**
 * @route GET /api/cashflow/projection/:days
 * @desc Get forward cashflow projection for specified days (7, 30, 60, or 90)
 * @access Private
 */
router.get("/projection/:days", cashflowController.getForwardProjection);

/**
 * @route GET /api/cashflow/projection
 * @desc Get forward cashflow projection (default 30 days)
 * @access Private
 */
router.get("/projection", cashflowController.getForwardProjection);

/**
 * @route GET /api/cashflow/forward-projection
 * @desc Get forward cashflow projection with query params
 * @access Private
 */
router.get("/forward-projection", cashflowController.getForwardProjection);

/**
 * @route POST /api/cashflow/forward-projection
 * @desc Get forward cashflow projection with custom expenses in request body
 * @access Private
 */
router.post("/forward-projection", cashflowController.getForwardProjection);

/**
 * @route GET /api/cashflow/projection/with-deals
 * @desc Get cashflow projection with deal scenarios
 * @access Private
 */
router.get(
  "/projection/with-deals",
  cashflowController.getCashflowProjectionWithDeals,
);

/**
 * @route POST /api/cashflow/projection/with-deals
 * @desc Get cashflow projection with deal scenarios and custom expenses
 * @access Private
 */
router.post(
  "/projection/with-deals",
  cashflowController.getCashflowProjectionWithDeals,
);

/**
 * @route GET /api/cashflow/snapshots/dates
 * @desc Get available snapshot dates
 * @access Private
 */
router.get("/snapshots/dates", cashflowSnapshotController.getAvailableDates);

/**
 * @route POST /api/cashflow/snapshots/create
 * @desc Create a manual snapshot of the current cashflow projection
 * @access Private
 */
router.post(
  "/snapshots/create",
  cashflowSnapshotController.createManualSnapshot,
);

/**
 * @route GET /api/cashflow/snapshots/:date
 * @desc Get snapshot for a specific date
 * @access Private
 */
router.get("/snapshots/:date", cashflowSnapshotController.getSnapshot);

// Export as both default and named export for better compatibility
export const cashflowRouter = router;
export default router;
