/**
 * Custom Expenses API Routes
 *
 * This module provides RESTful API endpoints for managing custom expenses.
 * These expenses are used in cashflow projections to simulate recurring
 * and one-off financial events.
 *
 * Endpoints:
 * - GET /api/expenses - List all expenses
 * - POST /api/expenses - Create a new expense
 * - PUT /api/expenses/:id - Update an existing expense
 * - DELETE /api/expenses/:id - Delete an expense
 *
 * @module api/routes/expenses
 */

import express from "express";
import { v4 as uuidv4 } from "uuid";
import { CustomExpense } from "../../types";
import { ExpensesRepository } from "../repositories/expenses-repository";
import { EXPENSE_TYPES } from "../../constants/expense-types";
import activityLogger from "../../utils/activity-logger";

const router = express.Router();
const expensesRepository = new ExpensesRepository();

// Validate that a date is forward-looking (today or later)
const isForwardLookingDate = (date: Date): boolean => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day
  return date >= today;
};

/**
 * @route GET /api/expenses
 * @desc Get all custom expenses
 * @access Private (requires authentication)
 *
 * @returns {Object} Response object
 * @returns {boolean} Response.success - Operation success indicator
 * @returns {CustomExpense[]} Response.data - Array of expense objects sorted by date
 *
 * @example
 * // Success response:
 * {
 *   "success": true,
 *   "data": [
 *     {
 *       "id": "abc123",
 *       "name": "Office Rent",
 *       "type": "Monthly Payroll",
 *       "amount": 2500,
 *       "date": "2023-05-01T00:00:00.000Z",
 *       "frequency": "monthly"
 *     },
 *     // ... more expenses
 *   ]
 * }
 *
 * // Error response:
 * {
 *   "success": false,
 *   "error": "Failed to read expenses",
 *   "message": "Error details..."
 * }
 */
router.get('/', async (req, res) => {
  try {
    const expenses = expensesRepository.getAll();

    // Sort by date (soonest first)
    expenses.sort((a, b) => a.date.getTime() - b.date.getTime());

    res.json({
      success: true,
      data: expenses
    });
  } catch (error: any) {
    console.error('Error reading expenses:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to read expenses',
      message: error.message
    });
  }
});

/**
 * @route POST /api/expenses
 * @desc Create a new custom expense
 * @access Private (requires authentication)
 *
 * @body {Object} expense - The expense to create
 * @body {string} expense.name - Name/description of the expense
 * @body {string} expense.type - Type of expense (Monthly Payroll, Software, etc.)
 * @body {number} expense.amount - Amount in dollars (positive number)
 * @body {string} expense.date - Date of expense (ISO format)
 * @body {string} expense.frequency - Frequency (weekly, monthly, quarterly, one-off)
 *
 * @returns {Object} Response object
 * @returns {boolean} Response.success - Operation success indicator
 * @returns {CustomExpense} Response.data - Created expense object
 *
 * @example
 * // Request body:
 * {
 *   "name": "Office Rent",
 *   "type": "Monthly Payroll",
 *   "amount": 2500,
 *   "date": "2023-05-01T00:00:00.000Z",
 *   "frequency": "monthly"
 * }
 *
 * // Success response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "abc123",
 *     "name": "Office Rent",
 *     "type": "Monthly Payroll",
 *     "amount": 2500,
 *     "date": "2023-05-01T00:00:00.000Z",
 *     "frequency": "monthly"
 *   }
 * }
 *
 * // Error response:
 * {
 *   "success": false,
 *   "error": "All fields are required"
 * }
 */
router.post('/', async (req, res) => {
  try {
    const { name, type, amount, date, frequency, repeatCount, source, description, metadata } = req.body;

    // Validate all required fields
    if (!name || !type || !amount || !date || !frequency) {
      return res.status(400).json({
        success: false,
        error: 'All fields are required'
      });
    }

    // Validate expense type
    if (!EXPENSE_TYPES.includes(type as CustomExpense['type'])) {
      return res.status(400).json({
        success: false,
        error: 'Invalid expense type'
      });
    }

    // Validate frequency
    const validFrequencies = ['weekly', 'fortnightly', 'monthly', 'quarterly', 'one-off'];
    if (!validFrequencies.includes(frequency)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid frequency'
      });
    }

    // Validate amount is a number
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be a positive number'
      });
    }

    // Validate date is forward-looking
    const parsedDate = new Date(date);
    if (!isForwardLookingDate(parsedDate)) {
      return res.status(400).json({
        success: false,
        error: 'Date must be today or later'
      });
    }

    console.log('Creating expense:', { name, type, amount: parsedAmount, date: parsedDate, frequency });

    // Create new expense
    const newExpense = expensesRepository.create({
      name,
      type: type as CustomExpense['type'],
      amount: parsedAmount,
      date: parsedDate,
      frequency: frequency as CustomExpense['frequency'],
      ...(repeatCount && frequency !== 'one-off' ? { repeatCount: parseInt(repeatCount) } : {}),
      source,
      description,
      metadata
    });

    console.log('Expense created successfully with ID:', newExpense.id);

    // Log activity for expense creation
    try {
      console.log('Expense creation activity logging debug:', {
        expenseId: newExpense.id,
        expenseName: newExpense.name,
        source: source,
        isXeroSource: source && source.includes('xero')
      });

      if (source && source.includes('xero')) {
        // Determine the sync type based on the source
        let syncType = 'expenses';
        if (source.includes('payroll') || source.includes('netpay')) {
          syncType = 'payroll';
        } else if (source.includes('superannuation')) {
          syncType = 'superannuation';
        } else if (source.includes('tax')) {
          syncType = 'tax-statements';
        }

        console.log('Detected Xero expense creation, logging as sync activity:', {
          source,
          syncType,
          expenseName: newExpense.name
        });
        await activityLogger.logXeroSyncCompleted(syncType, 1);
      } else {
        // Log regular expense creation
        await activityLogger.logExpenseCreated(
          newExpense.id,
          newExpense.name,
          newExpense.amount,
          'user' // TODO: Get actual user from session when user management is implemented
        );
      }
    } catch (activityError) {
      console.error('Error logging expense creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: newExpense
    });
  } catch (error: any) {
    console.error('Error creating expense:', error);

    // Provide more specific error information based on the error type
    let statusCode = 500;
    let errorMessage = 'Failed to create expense';

    if (error.message && error.message.includes('database')) {
      errorMessage = `Database error: ${error.message}`;
    } else if (error.code === 'SQLITE_CONSTRAINT') {
      statusCode = 409;
      errorMessage = 'A similar expense already exists';
    } else if (error.code === 'SQLITE_READONLY') {
      errorMessage = 'Database is read-only. Check file permissions.';
    } else if (error.code === 'SQLITE_CANTOPEN') {
      errorMessage = 'Cannot open database file. Check file permissions.';
    } else if (error.code && error.code.startsWith('SQLITE_')) {
      errorMessage = `SQLite error: ${error.code}`;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
});

/**
 * @route PUT /api/expenses/:id
 * @desc Update an existing custom expense
 * @access Private (requires authentication)
 *
 * @param {string} id - ID of the expense to update
 * @body {Object} expense - The updated expense data
 * @body {string} expense.name - Name/description of the expense
 * @body {string} expense.type - Type of expense (Monthly Payroll, Software, etc.)
 * @body {number} expense.amount - Amount in dollars (positive number)
 * @body {string} expense.date - Date of expense (ISO format)
 * @body {string} expense.frequency - Frequency (weekly, monthly, quarterly, one-off)
 *
 * @returns {Object} Response object
 * @returns {boolean} Response.success - Operation success indicator
 * @returns {CustomExpense} Response.data - Updated expense object
 *
 * @example
 * // Request to PUT /api/expenses/abc123 with body:
 * {
 *   "name": "Updated Office Rent",
 *   "type": "Monthly Payroll",
 *   "amount": 2700,
 *   "date": "2023-05-01T00:00:00.000Z",
 *   "frequency": "monthly"
 * }
 *
 * // Success response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "abc123",
 *     "name": "Updated Office Rent",
 *     "type": "Monthly Payroll",
 *     "amount": 2700,
 *     "date": "2023-05-01T00:00:00.000Z",
 *     "frequency": "monthly"
 *   }
 * }
 *
 * // Error response if expense not found:
 * {
 *   "success": false,
 *   "error": "Expense not found"
 * }
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, amount, date, frequency, repeatCount, source, description, metadata } = req.body;

    // Validate all required fields
    if (!name || !type || !amount || !date || !frequency) {
      return res.status(400).json({
        success: false,
        error: 'All fields are required'
      });
    }

    // Validate expense type
    if (!EXPENSE_TYPES.includes(type as CustomExpense['type'])) {
      return res.status(400).json({
        success: false,
        error: 'Invalid expense type'
      });
    }

    // Validate frequency
    const validFrequencies = ['weekly', 'fortnightly', 'monthly', 'quarterly', 'one-off'];
    if (!validFrequencies.includes(frequency)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid frequency'
      });
    }

    // Validate amount is a number
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be a positive number'
      });
    }

    // Validate date is forward-looking
    const parsedDate = new Date(date);
    if (!isForwardLookingDate(parsedDate)) {
      return res.status(400).json({
        success: false,
        error: 'Date must be today or later'
      });
    }

    // Get the original expense for change tracking
    const originalExpense = expensesRepository.getById(id);

    if (!originalExpense) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    // Update the expense
    const updatedExpense = expensesRepository.update(id, {
      name,
      type: type as CustomExpense['type'],
      amount: parsedAmount,
      date: parsedDate,
      frequency: frequency as CustomExpense['frequency'],
      ...(repeatCount && frequency !== 'one-off' ? { repeatCount: parseInt(repeatCount) } : {}),
      source,
      description,
      metadata
    });

    if (!updatedExpense) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    // Log expense update activity
    try {
      const changes: Record<string, any> = {};
      if (originalExpense.name !== updatedExpense.name) changes.name = { from: originalExpense.name, to: updatedExpense.name };
      if (originalExpense.type !== updatedExpense.type) changes.type = { from: originalExpense.type, to: updatedExpense.type };
      if (originalExpense.amount !== updatedExpense.amount) changes.amount = { from: originalExpense.amount, to: updatedExpense.amount };
      if (originalExpense.frequency !== updatedExpense.frequency) changes.frequency = { from: originalExpense.frequency, to: updatedExpense.frequency };

      console.log('Expense update activity logging debug:', {
        expenseId: updatedExpense.id,
        expenseName: updatedExpense.name,
        source: source,
        changesDetected: Object.keys(changes).length,
        changes: changes,
        isXeroSource: source && source.includes('xero')
      });

      // Check if this is a Xero-sourced expense update
      if (source && source.includes('xero')) {
        // For Xero sync operations, always log activity regardless of field changes
        // because the sync operation itself is significant
        let syncType = 'expenses';
        if (source.includes('netpay') || source.includes('payroll')) {
          syncType = 'payroll';
        } else if (source.includes('superannuation')) {
          syncType = 'superannuation';
        } else if (source.includes('tax')) {
          syncType = 'tax-statements';
        }

        console.log('Detected Xero expense update, logging as sync activity:', {
          source,
          syncType,
          expenseName: updatedExpense.name
        });
        await activityLogger.logXeroSyncCompleted(syncType, 1);
      } else if (Object.keys(changes).length > 0) {
        // Log regular expense update only if there are changes
        await activityLogger.logExpenseUpdated(
          updatedExpense.id,
          updatedExpense.name,
          changes,
          'user' // TODO: Get actual user from session when user management is implemented
        );
      }
    } catch (activityError) {
      console.error('Error logging expense update activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.json({
      success: true,
      data: updatedExpense
    });
  } catch (error: any) {
    console.error('Error updating expense:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update expense',
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/expenses/:id
 * @desc Delete a custom expense
 * @access Private (requires authentication)
 *
 * @param {string} id - ID of the expense to delete
 *
 * @returns {Object} Response object
 * @returns {boolean} Response.success - Operation success indicator
 * @returns {string} Response.message - Success message
 *
 * @example
 * // Request to DELETE /api/expenses/abc123
 *
 * // Success response:
 * {
 *   "success": true,
 *   "message": "Expense deleted successfully"
 * }
 *
 * // Error response if expense not found:
 * {
 *   "success": false,
 *   "error": "Expense not found"
 * }
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get expense details before deleting for activity logging
    const expense = expensesRepository.getById(id);

    if (!expense) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    // Delete the expense
    const deleted = expensesRepository.delete(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    // Log expense deletion activity
    try {
      await activityLogger.logExpenseDeleted(
        expense.id,
        expense.name,
        expense.amount,
        'user' // TODO: Get actual user from session when user management is implemented
      );
    } catch (activityError) {
      console.error('Error logging expense deletion activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.json({
      success: true,
      message: 'Expense deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting expense:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete expense',
      message: error.message
    });
  }
});

export default router;