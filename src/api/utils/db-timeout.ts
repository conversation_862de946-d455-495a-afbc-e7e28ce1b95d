import { Statement, Database, RunResult } from "better-sqlite3";

// TypeScript interfaces for database operations
interface DatabaseError extends Error {
  code?: string;
}

interface TimeoutStatement extends Statement {
  getWithTimeout: <T = unknown>(params?: unknown[], timeout?: number) => Promise<T>;
  allWithTimeout: <T = unknown[]>(params?: unknown[], timeout?: number) => Promise<T>;
  runWithTimeout: (params?: unknown[], timeout?: number) => Promise<RunResult>;
}

interface TimeoutDatabase extends Database {
  prepare: (sql: string) => TimeoutStatement;
}

/**
 * Default query timeout in milliseconds
 */
const DEFAULT_QUERY_TIMEOUT = 5000; // 5 seconds

/**
 * Custom error class for query timeouts
 */
export class QueryTimeoutError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'QueryTimeoutError';
  }
}

/**
 * Execute a database query with a timeout
 * @param statement The prepared statement to execute
 * @param method The method to call on the statement (get, all, run)
 * @param params Query parameters
 * @param timeoutMs Timeout in milliseconds
 * @returns Query result
 */
export async function executeWithTimeout<T>(
  statement: Statement,
  method: "get" | "all" | "run",
  params: unknown[] = [],
  timeoutMs: number = DEFAULT_QUERY_TIMEOUT
): Promise<T> {
  return new Promise((resolve, reject) => {
    let completed = false;
    
    // Set up timeout
    const timeoutId = setTimeout(() => {
      if (!completed) {
        completed = true;
        reject(new QueryTimeoutError(`Query timeout after ${timeoutMs}ms`));
      }
    }, timeoutMs);
    
    try {
      // Execute query
      let result: unknown;
      
      switch (method) {
        case 'get':
          result = statement.get(...params);
          break;
        case 'all':
          result = statement.all(...params);
          break;
        case 'run':
          result = statement.run(...params);
          break;
        default:
          throw new Error(`Unknown method: ${method}`);
      }
      
      // Clear timeout and resolve
      clearTimeout(timeoutId);
      completed = true;
      resolve(result as T);
    } catch (error) {
      // Clear timeout and reject
      clearTimeout(timeoutId);
      completed = true;
      reject(error);
    }
  });
}

/**
 * Create a timeout-wrapped database object
 * @param db The database instance
 * @param defaultTimeout Default timeout for all queries
 * @returns Wrapped database object with timeout support
 */
export function createTimeoutDb(db: Database, defaultTimeout: number = DEFAULT_QUERY_TIMEOUT): TimeoutDatabase {
  return {
    ...db,
    
    /**
     * Prepare a statement with timeout support
     */
    prepare(sql: string) {
      const statement = db.prepare(sql);
      
      return {
        ...statement,
        
        async getWithTimeout<T = unknown>(params?: unknown[], timeout?: number): Promise<T> {
          return executeWithTimeout(statement, 'get', params, timeout || defaultTimeout);
        },
        
        async allWithTimeout<T = unknown[]>(params?: unknown[], timeout?: number): Promise<T> {
          return executeWithTimeout(statement, 'all', params, timeout || defaultTimeout);
        },
        
        async runWithTimeout(params?: unknown[], timeout?: number): Promise<RunResult> {
          return executeWithTimeout(statement, 'run', params, timeout || defaultTimeout);
        },
        
        // Keep original sync methods for backward compatibility
        get: statement.get.bind(statement),
        all: statement.all.bind(statement),
        run: statement.run.bind(statement)
      };
    }
  };
}

/**
 * Middleware to add timeout context to database errors
 */
export function handleDatabaseError(error: unknown, query?: string): Error {
  const dbError = error as DatabaseError;
  if (error instanceof QueryTimeoutError) {
    console.error('Database query timeout:', {
      query: query?.substring(0, 100) + '...',
      timeout: DEFAULT_QUERY_TIMEOUT
    });
    
    return new Error('Database operation timed out. Please try again with a smaller data set.');
  }
  
  if (dbError.code === 'SQLITE_BUSY') {
    console.error('Database busy error:', {
      query: query?.substring(0, 100) + '...'
    });
    
    return new Error('Database is busy. Please try again in a moment.');
  }
  
  if (dbError.code === 'SQLITE_LOCKED') {
    console.error('Database locked error:', {
      query: query?.substring(0, 100) + '...'
    });
    
    return new Error('Database is locked. Another operation is in progress.');
  }
  
  // Return original error for other cases
  return dbError instanceof Error ? dbError : new Error(String(dbError));
}