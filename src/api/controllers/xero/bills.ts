import { Request, Response } from "express";
import {
  getXeroService,
  BillService
} from "../../../services/xero";
import { ExpensesRepository } from "../../repositories/expenses-repository";
import { CustomExpense } from "../../../types";
import { XERO_SOURCES } from "../../../constants/xero-backend";
import activityLogger from "../../../utils/activity-logger";

export class XeroBillsController {
  /**
   * Get bills from Xero
   */
  public getBills = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication first
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: "Xero not authenticated"
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: "Missing Xero client or tenant ID"
        });
        return;
      }

      // Get days parameter from query (defaults to 30)
      const days = parseInt(req.query.days as string) || 30;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create bill service instance
      const billService = new BillService(client);

      // Fetch bills from Xero
      const bills = await billService.getBillsForDisplay(tenantId, sinceDate);

      res.json({
        success: true,
        data: bills
      });
    } catch (error) {
      console.error('Error getting bills from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: "Authentication error",
          code: "AUTH_ERROR"
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: "Failed to get bills from Xero"
      });
    }
  };

  /**
   * Convert a Xero bill to a custom expense
   */
  public convertBillToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { bill, expenseType } = req.body;

      if (!bill) {
        res.status(400).json({
          success: false,
          message: "Missing bill data"
        });
        return;
      }

      if (!expenseType) {
        res.status(400).json({
          success: false,
          message: "Missing expense type"
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert bill to expense format
      // Ensure date is a Date object, not a string
      const billDate = bill.dueDate ? new Date(bill.dueDate) : new Date(bill.date);

      const expense: Omit<CustomExpense, 'id'> = {
        // Include reference in name to ensure we can match it later
        name: bill.reference
          ? `${bill.reference} - ${bill.vendor}`
          : `${bill.vendor} - ${bill.invoiceNumber}`,
        type: expenseType, // Use the provided expense type
        amount: bill.amount,
        date: billDate, // Already a Date object
        frequency: "one-off", // Bills are one-off by default
        source: XERO_SOURCES.BILL, // Mark as coming from Xero
        description: `Bill from ${bill.vendor} ${bill.reference ? `(${bill.reference})` : ""}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date - prevent timezone issues by using date only
      const normalizedDate = new Date(expense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      expense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(expense);

      // Log Xero sync activity
      try {
        await activityLogger.logXeroSyncCompleted('bills', 1);
      } catch (activityError) {
        console.error('Error logging Xero bill sync activity:', activityError);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting bill to expense:', error);
      res.status(500).json({
        success: false,
        message: "Failed to convert bill to expense"
      });
    }
  };

  /**
   * Get late bills from Xero
   */
  public getLateBills = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication first
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: "Xero not authenticated"
        });
        return;
      }

      const tenantId = xeroService.getActiveTenantId();

      if (!tenantId) {
        res.status(400).json({
          success: false,
          message: "Missing Xero tenant ID"
        });
        return;
      }

      // Fetch bills from Xero using the existing bill service
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all bills
      const allBills = await xeroService.bills.getBillsForDisplay(tenantId);

      // Filter to only get bills that are due before today (late bills)
      const lateBills = allBills.filter(bill => {
        const dueDate = new Date(bill.dueDate);
        return dueDate < today;
      });

      // Calculate days past due for each bill
      const lateBillsWithDaysPastDue = lateBills.map(bill => {
        const dueDate = new Date(bill.dueDate);
        const diffTime = Math.abs(today.getTime() - dueDate.getTime());
        const daysPastDue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return {
          ...bill,
          daysPastDue
        };
      });

      // Calculate total amount
      const totalAmount = lateBillsWithDaysPastDue.reduce((sum, bill) => sum + bill.amount, 0);

      res.json({
        success: true,
        data: {
          bills: lateBillsWithDaysPastDue,
          totalAmount,
          count: lateBillsWithDaysPastDue.length
        }
      });
    } catch (error) {
      console.error('Error getting late bills from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: "Authentication error",
          code: "AUTH_ERROR"
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: "Failed to get late bills from Xero",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  };
}