import { Request, Response, NextFunction } from "express";
import { getXeroService } from "../../../services/xero";
import { getHarvestService } from "../../../services/harvest";

interface XeroProfitAndLossReport {
  Reports?: XeroReport[];
  reports?: XeroReport[];
}

interface XeroReport {
  ReportID?: string;
  ReportName?: string;
  Rows?: XeroReportRow[];
}

interface XeroReportRow {
  RowType?: string;
  Title?: string;
  Rows?: XeroReportRow[];
  Cells?: XeroReportCell[];
}

interface XeroReportCell {
  Value?: string;
  Attributes?: XeroReportAttribute[];
}

interface XeroReportAttribute {
  Value?: string;
  Id?: string;
  value?: string;
  id?: string;
}

interface XeroReportRowData {
  RowType?: string;
  Title?: string;
  Rows?: XeroReportRowData[];
  Cells?: XeroReportCell[];
  title?: string;
  rowType?: string;
  rows?: XeroReportRowData[];
  cells?: XeroReportCell[];
}

interface ProjectSetting {
  id?: string;
  name?: string;
  settings?: Record<string, unknown>;
  [key: string]: unknown;
}

export class XeroReportsController {
  /**
   * Get cash flow forecast
   */
  public getCashFlowForecast = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const tenantId = xeroService.getActiveTenantId();

      if (!tenantId) {
        res.status(401).json({ error: "No active Xero organization found. Please authenticate first." });
        return;
      }

      // Parse date parameters
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date();
      let endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date(startDate);

      // Default to 6 months if no end date provided
      if (startDate.getTime() === endDate.getTime()) {
        endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 6);
      }

      // Format dates for API
      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];

      // Create an array of API calls to make in parallel
      const apiCalls = [
        // 1. Get bank summary report - shows current cash positions
        xeroService.getClient().accountingApi.getReportBankSummary(
          tenantId,
          formattedStartDate,
          formattedEndDate
        ),

        // 2. Get balance sheet - shows assets and liabilities
        xeroService.getClient().accountingApi.getReportBalanceSheet(
          tenantId,
          formattedEndDate
        ),

        // 3. Get profit and loss - shows income and expenses
        xeroService.getClient().accountingApi.getReportProfitAndLoss(
          tenantId,
          formattedStartDate,
          formattedEndDate
        ),

        // 4. Get outstanding invoices (money owed to us)
        xeroService.getClient().accountingApi.getInvoices(
          tenantId,
          undefined,
          'Status== 'AUTHORISED'', // Only get approved invoices not yet paid
          'Date'
        ),

        // 5. Get bills (money we owe)
        xeroService.getClient().accountingApi.getInvoices(
          tenantId,
          undefined,
          'Type=="ACCPAY" AND Status== 'AUTHORISED'', // Only get approved bills not yet paid
          'Date'
        )
      ];

      // Execute all API calls in parallel for better performance
      const [
        bankSummary,
        balanceSheet,
        profitAndLoss,
        receivableInvoices,
        payableInvoices
      ] = await Promise.all(apiCalls);

      // Combine the data into a cash flow forecast
      const cashFlowForecast = {
        startDate,
        endDate,
        // Current cash position
        bankSummary: bankSummary.body,
        balanceSheet: balanceSheet.body,
        // Historical patterns
        profitAndLoss: profitAndLoss.body,
        // Outstanding receivables (money coming in)
        receivableInvoices: receivableInvoices.body,
        // Upcoming payables (money going out)
        payableInvoices: payableInvoices.body
      };

      res.json(cashFlowForecast);
    } catch (error) {
      console.error('Error fetching cash flow forecast:', error);
      next(error);
    }
  };

  /**
   * Get GST data from Xero Profit and Loss Report
   */
  public getGSTData = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const harvestService = getHarvestService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: "Xero not authenticated"
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: "Missing Xero client or tenant ID"
        });
        return;
      }

      // Get the current quarter date range
      const { fromDate, toDate, daysElapsed, daysInQuarter } = this.getCurrentQuarterDateRange();
      console.log(`Getting Profit and Loss report for period: ${fromDate} to ${toDate}`);

      // Get the Profit and Loss report for the current quarter
      console.log('Requesting Profit and Loss report from Xero');
      const profitAndLossResponse = await client.accountingApi.getReportProfitAndLoss(
        tenantId,
        fromDate,
        toDate,
        undefined, // periods
        undefined, // timeframe
        undefined, // trackingCategoryID
        undefined, // trackingCategoryID2
        undefined, // trackingOptionID
        undefined, // trackingOptionID2
        undefined, // standardLayout
        undefined  // paymentsOnly
      );
      console.log('Received Profit and Loss report from Xero');

      // For debugging, log a sample of the response
      console.log('Profit and Loss response received');
      try {
        // Log the structure as a stringified object to see all properties
        console.log('Profit and Loss response structure: ', JSON.stringify(profitAndLossResponse.body, null, 2).substring(0, 500) + "...');
      } catch (logError) {
        console.error('Error logging profit and loss structure:', logError);
      }

      // Extract total sales from the Profit and Loss report and calculate GST
      // THIS IS THE CRITICAL PART - we keep the original accrued GST calculation unchanged
      const gstData = this.calculateGSTFromProfitAndLoss(profitAndLossResponse.body);

      // Calculate the next payment due date
      const nextPaymentDate = this.calculateGSTDueDate();

      // Get projected revenue for the remainder of the quarter
      const today = new Date();
      const endOfQuarter = new Date(toDate);

      // Get project settings for revenue projections
      const projectSettings = await this.getProjectSettings();

      // Get projected invoices for the remainder of the quarter
      // The generateProjectedIncome function already filters out real invoices
      // It only returns projected invoices that don't have corresponding real invoices in Harvest
      // This ensures we're only looking at truly future invoices not already counted in Xero
      const projectedInvoices = await harvestService.projectBudgets.generateProjectedIncome(
        today,
        endOfQuarter,
        projectSettings
      );

      // Calculate projected revenue (sum of all positive amounts)
      const projectedRevenue = projectedInvoices
        .filter(invoice => invoice.amount > 0)
        .reduce((sum, invoice) => sum + invoice.amount, 0);

      // Calculate projected GST (10% of projected revenue from future invoices)
      // Since generateProjectedIncome already filters out real invoices,
      // we can be confident this only includes invoices not yet in Xero
      const projectedGST = projectedRevenue * 0.1;

      // Total projected GST is current accrued GST plus projected GST
      // The accrued amount from Xero (for existing invoices) + GST from projected future invoices
      const totalProjectedGST = gstData.amount + projectedGST;

      res.json({
        success: true,
        data: {
          ...gstData,  // Original accrued amount is preserved here
          dueDate: nextPaymentDate,
          projectedGST: projectedGST,
          totalProjectedGST: totalProjectedGST,
          // Include additional data for frontend display
          daysElapsed,
          daysInQuarter,
          projectedRevenue,
          projectedInvoiceCount: projectedInvoices.filter(invoice => invoice.amount > 0).length
        }
      });
    } catch (error) {
      console.error('Error getting GST data from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: "Authentication error",
          code: "AUTH_ERROR"
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: "Failed to get GST data from Xero",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Get balance sheet report from Xero
   */
  public getBalanceSheet = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication first
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: "Xero not authenticated"
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: "Missing Xero client or tenant ID"
        });
        return;
      }

      // Get date parameter from query (defaults to today)
      const date = req.query.date ? new Date(req.query.date as string) : new Date();

      // Format date for API (YYYY-MM-DD format as required by Xero)
      // Xero API requires date in YYYY-MM-DD format
      const formattedDate = date.toISOString().split('T')[0];
      console.log(`Requesting balance sheet for date: ${formattedDate}`);

      // Optional parameters
      const periods = req.query.periods ? parseInt(req.query.periods as string) : undefined;

      // Validate timeframe parameter
      let timeframe: "MONTH" | "QUARTER" | "YEAR" | undefined = undefined;
      const timeframeParam = req.query.timeframe as string;
      if (timeframeParam) {
        if (["MONTH", "QUARTER", "YEAR"].includes(timeframeParam)) {
          timeframe = timeframeParam as "MONTH" | "QUARTER" | "YEAR";
        } else {
          console.warn(`Invalid timeframe parameter: ${timeframeParam}. Must be one of: MONTH, QUARTER, YEAR. Using default.`);
        }
      }

      const standardLayout = req.query.standardLayout === 'true';

      console.log(`Balance sheet request parameters:
        - date: ${formattedDate}
        - periods: ${periods}
        - timeframe: ${timeframe}
        - standardLayout: ${standardLayout}
      `);

      // Log the parameters being sent to Xero API
      console.log(`Calling Xero API with parameters:
        - tenantId: ${tenantId}
        - date: ${formattedDate}
        - periods: ${periods}
        - timeframe: ${timeframe}
        - standardLayout: ${standardLayout}
      `);

      // Get the balance sheet report using our custom client method
      // This fixes the API method mismatch issue by using our wrapper method
      // that handles rate limiting and error detection for missing methods
      const balanceSheetResponse = await client.getBalanceSheetReport(
        tenantId,
        date,
        periods,
        timeframe,
        undefined, // trackingOptionID1
        undefined, // trackingOptionID2
        standardLayout,
        false // paymentsOnly
      );

      console.log('Successfully retrieved balance sheet from Xero');

      res.json({
        success: true,
        data: balanceSheetResponse.body,
        date: formattedDate
      });
    } catch (error) {
      console.error('Error getting balance sheet from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: "Authentication error",
          code: "AUTH_ERROR"
        });
        return;
      }

      // Handle API compatibility issues
      if (error instanceof Error &&
          (error.message.includes('not a function') ||
           error.message.includes('compatibility issue'))) {
        res.status(500).json({
          success: false,
          message: "Xero API compatibility issue",
          error: error.message,
          code: "API_COMPATIBILITY_ERROR"
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: "Failed to get balance sheet from Xero",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  };

  /**
   * Get the date range for the current quarter
   */
  private getCurrentQuarterDateRange = (): {
    fromDate: string,
    toDate: string,
    daysElapsed: number,
    daysInQuarter: number
  } => {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0-indexed (0 = January)
    const currentYear = today.getFullYear();

    let fromMonth: number;
    let toMonth: number;
    let fromYear: number = currentYear;
    let toYear: number = currentYear;

    // Determine the quarter date range based on the current month
    if (currentMonth >= 0 && currentMonth <= 2) {
      // Q3 of financial year (Jan-Mar)
      fromMonth = 0; // January
      toMonth = 2; // March
    } else if (currentMonth >= 3 && currentMonth <= 5) {
      // Q4 of financial year (Apr-Jun)
      fromMonth = 3; // April
      toMonth = 5; // June
    } else if (currentMonth >= 6 && currentMonth <= 8) {
      // Q1 of financial year (Jul-Sep)
      fromMonth = 6; // July
      toMonth = 8; // September
    } else {
      // Q2 of financial year (Oct-Dec)
      fromMonth = 9; // October
      toMonth = 11; // December
    }

    // Create date objects for the first and last day of the quarter
    const fromDate = new Date(fromYear, fromMonth, 1);
    const toDate = new Date(toYear, toMonth + 1, 0); // Last day of the month

    // Calculate days elapsed and total days in quarter
    const daysElapsed = Math.floor((today.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysInQuarter = Math.floor((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Format dates as YYYY-MM-DD for Xero API
    const formatDate = (date: Date): string => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    return {
      fromDate: formatDate(fromDate),
      toDate: formatDate(toDate),
      daysElapsed,
      daysInQuarter
    };
  };

  /**
   * Calculate GST from Profit and Loss report
   */
  private calculateGSTFromProfitAndLoss = (profitAndLoss: XeroProfitAndLossReport): { amount: number, accountId: string } => {
    let totalSales = 0;
    let accountId = '';

    try {
      console.log('Calculating GST from Profit and Loss report');

      // Find the Reports array
      const reports = profitAndLoss.Reports || profitAndLoss.reports || [];

      if (!reports.length) {
        console.log('No reports found in Profit and Loss response');
        return { amount: 0, accountId: "" };
      }

      // Get the first report
      const report = reports[0];

      // Find the rows array
      const rows = report.Rows || report.rows || [];

      if (!rows.length) {
        console.log('No rows found in Profit and Loss report');
        return { amount: 0, accountId: "" };
      }

      // Find the Income section
      const incomeSection = rows.find((row: XeroReportRowData) => {
        const rowType = row.RowType || row.rowType;
        const title = row.Title || row.title;
        return rowType === 'Section' && title === 'Income';
      });

      if (!incomeSection) {
        console.log('Income section not found in Profit and Loss report');
        return { amount: 0, accountId: "" };
      }

      // Get the rows in the Income section
      const incomeRows = incomeSection.Rows || incomeSection.rows || [];

      // Find the Total Income row
      const totalIncomeRow = incomeRows.find((row: XeroReportRowData) => {
        const rowType = row.RowType || row.rowType;
        if (rowType !== 'SummaryRow') return false;

        const cells = row.Cells || row.cells || [];
        if (!cells.length) return false;

        const value = cells[0].Value || cells[0].value || "";
        return value === 'Total Income';
      });

      if (totalIncomeRow) {
        // Get the total income value
        const cells = totalIncomeRow.Cells || totalIncomeRow.cells || [];
        if (cells.length >= 2) {
          const valueCell = cells[1];
          const valueStr = valueCell.Value || valueCell.value || "0";
          totalSales = parseFloat(valueStr) || 0;
          console.log('Extracted total sales:', totalSales);

          // Try to get account ID if available
          const attributes = valueCell.Attributes || valueCell.attributes || [];
          const accountAttr = attributes.find((attr: XeroReportAttribute) => {
            return (attr.Id === 'account' || attr.id === 'account');
          });

          if (accountAttr) {
            accountId = accountAttr.Value || accountAttr.value || "";
          }
        }
      } else {
        console.log('Total Income row not found in Income section');

        // If we can't find the summary row, try to sum up all the individual income rows
        let sum = 0;
        incomeRows.forEach((row: XeroReportRowData) => {
          const rowType = row.RowType || row.rowType;
          if (rowType === 'Row') {
            const cells = row.Cells || row.cells || [];
            if (cells.length >= 2) {
              const valueStr = cells[1].Value || cells[1].value || "0";
              const value = parseFloat(valueStr) || 0;
              sum += value;
            }
          }
        });

        if (sum > 0) {
          totalSales = sum;
          console.log('Calculated total sales by summing rows:', totalSales);
        }
      }

      // Calculate GST as 10% of total sales
      const gstAmount = totalSales * 0.1;
      console.log('Calculated GST amount (10% of total sales):', gstAmount);

      return { amount: gstAmount, accountId };
    } catch (error) {
      console.error('Error calculating GST from Profit and Loss report:', error);
      return { amount: 0, accountId: "" };
    }
  };

  /**
   * Get project settings for revenue projections
   */
  private getProjectSettings = async (): Promise<ProjectSetting[]> => {
    try {
      // Read project settings from disk
      const fs = require('fs');
      const path = require('path');

      // Determine the data directory based on environment
      let dataDir = '';
      if (process.env.NODE_ENV === 'production') {
        dataDir = '/data';
      } else {
        dataDir = path.join(__dirname, '../../../../data');
      }

      // Path to settings file
      const settingsPath = path.join(dataDir, 'project_settings.json');

      // Read settings file
      if (fs.existsSync(settingsPath)) {
        const settingsData = fs.readFileSync(settingsPath, 'utf8');
        const settings = JSON.parse(settingsData);
        console.log(`Loaded ${settings.length} project settings for projection calculations`);
        return settings;
      } else {
        console.log('No project settings file found - using empty settings array');
        return [];
      }
    } catch (error) {
      console.error('Error getting project settings:', error);
      return []; // Return empty array on error
    }
  };

  /**
   * Calculate the next GST due date based on current quarter
   * For businesses with bookkeepers, BAS is due on the 26th of the month following the month after quarter end
   */
  private calculateGSTDueDate = (): Date => {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    let dueMonth: number;
    let dueYear: number = currentYear;

    // Determine the next due date based on the current month (0-indexed)
    if (currentMonth >= 0 && currentMonth <= 2) {
      // Q3 of financial year (Jan-Mar) → due in May (4) - month after April
      dueMonth = 4; // May
    } else if (currentMonth >= 3 && currentMonth <= 5) {
      // Q4 of financial year (Apr-Jun) → due in August (7) - month after July
      dueMonth = 7; // August
    } else if (currentMonth >= 6 && currentMonth <= 8) {
      // Q1 of financial year (Jul-Sep) → due in November (10) - month after October
      dueMonth = 10; // November
    } else {
      // Q2 of financial year (Oct-Dec) → due in February (1) of next year - month after January
      dueMonth = 1; // February
      dueYear = currentYear + 1;
    }

    // Create the due date (26th of the month)
    const dueDate = new Date(dueYear, dueMonth, 26);

    return dueDate;
  };
}