import { Request, Response, NextFunction } from "express";
import { getXeroService } from "../../../services/xero";
import { jwtDecode } from "jwt-decode";

// Type definitions for Xero OAuth tokens
interface XeroTokenSet {
  access_token: string;
  token_type: string;
  expires_at?: number;
  refresh_token?: string;
  scope?: string;
  id_token?: string;
}

interface XeroDecodedToken {
  sub: string;
  iss: string;
  aud: string;
  exp: number;
  iat: number;
  auth_time?: number;
  at_hash?: string;
  email?: string;
  given_name?: string;
  family_name?: string;
  sid?: string;
  global_session_id?: string;
  name?: string;
}

interface XeroTenant {
  tenantId: string;
  tenantName: string;
  tenantType: string;
  createdDateUtc: string;
  updatedDateUtc: string;
}

interface XeroOrganization {
  name?: string;
  organisationID?: string;
  legalName?: string;
  shortCode?: string;
  [key: string]: unknown;
}

// Extend the Express Request type to include session
declare module 'express-session' {
  interface SessionData {
    tokenSet?: XeroTokenSet;
    decodedIdToken?: XeroDecodedToken;
    decodedAccessToken?: XeroDecodedToken;
    allTenants?: XeroTenant[];
    activeTenant?: XeroTenant;
    userInfo?: {
      name?: string;
      email?: string;
      sub?: string;
    };
    previewMode?: boolean;
  }
}

export class XeroAuthController {
  /**
   * Initiate the OAuth flow
   */
  public initiateAuth = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const consentUrl = await xeroService.buildConsentUrl();
      res.redirect(consentUrl);
    } catch (error) {
      console.error('Error initiating auth:', error);
      res.status(500).json({
        error: "Failed to initiate authorization",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Handle the OAuth callback
   */
  public handleCallback = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Force HTTPS protocol for production environments
      const protocol = process.env.NODE_ENV === 'production' ? 'https' : (req.protocol || "http");
      const host = req.get('host') || "";
      const fullUrl = `${protocol}://${host}${req.originalUrl}`;

      console.log('Full callback URL being used:', fullUrl);
      console.log('Request headers:', {
        host: req.get('host'),
        forwardedHost: req.get('x-forwarded-host'),
        forwardedProto: req.get('x-forwarded-proto'),
        origin: req.get('origin')
      });

      const tokenSet = await xeroService.handleCallback(fullUrl);
      console.log('Token set received:', {
        hasIdToken: !!tokenSet.id_token,
        hasAccessToken: !!tokenSet.access_token,
        hasRefreshToken: !!tokenSet.refresh_token,
        expiresAt: tokenSet.expires_at
      });

      // Store token info in session
      if (req.session) {
        req.session.tokenSet = tokenSet;

        if (tokenSet.id_token) {
          const decodedIdToken = jwtDecode<XeroDecodedToken>(tokenSet.id_token);
          req.session.decodedIdToken = decodedIdToken;

          // Store user info for easy access
          req.session.userInfo = {
            name: decodedIdToken.name as string,
            email: decodedIdToken.email as string,
            sub: decodedIdToken.sub as string
          };
        }

        if (tokenSet.access_token) {
          req.session.decodedAccessToken = jwtDecode(tokenSet.access_token);
        }

        // Update tenants information
        await xeroService.getClient().updateTenants();
        const tenants = xeroService.getTenants();
        console.log(`Found ${tenants.length} tenants`);

        if (tenants.length > 0) {
          req.session.allTenants = tenants;
          req.session.activeTenant = tenants[0];
          console.log('Active tenant set:', tenants[0].tenantName);
        } else {
          console.warn('No tenants found after authentication');
        }
      }

      // Determine the frontend URL for redirection
      const isPreviewDeployment = process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview');
      let frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173";

      // CRITICAL: Use the same host that the request came from to maintain cookie context
      const requestHost = req.get('host');
      if (requestHost) {
        console.log(`Using request host for redirect: ${requestHost}`);
        frontendUrl = `${req.protocol}://${requestHost}`;
      } else if (isPreviewDeployment) {
        console.log('Preview deployment detected, using RENDER_EXTERNAL_HOSTNAME for redirect');
        frontendUrl = `https://${process.env.RENDER_EXTERNAL_HOSTNAME}`;
      }

      // Set cookies using the same domain as the request to ensure they're sent back
      const cookieOptions = {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: false,
        sameSite: isPreviewDeployment ? 'none' as const : "lax" as const,
        path: "/",
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        // Don't set domain explicitly - let the browser use the current domain
        // This ensures cookies work across both custom domains and render.com domains
      };

      // Log cookie settings for debugging
      console.log('Setting cookies with options:', {
        secure: cookieOptions.secure,
        sameSite: cookieOptions.sameSite,
        domain: "(default - current domain)",
        host: req.get('host')
      });

      // Set a cookie that can be used to test if cookies are working
      res.cookie('auth_test', 'true', cookieOptions);

      // Force the session to save before redirecting to ensure cookie is set
      console.log('[DEBUG XERO CALLBACK] Attempting req.session.save()...');
      req.session.save((err) => {
        if (err) {
          console.error('[DEBUG XERO CALLBACK] Error saving session before redirect:', err);
        } else {
          console.log('[DEBUG XERO CALLBACK] req.session.save() completed successfully.');
        }

        // Add success parameter to redirect URL
        const redirectUrl = new URL(`${frontendUrl}/`);
        redirectUrl.searchParams.append('auth_success', 'true');
        redirectUrl.searchParams.append('sid', req.sessionID);
        redirectUrl.searchParams.append('ts', Date.now().toString());

        console.log(`Redirecting to frontend URL: ${redirectUrl.toString()}`);
        res.redirect(redirectUrl.toString());
      });
    } catch (error) {
      console.error('Error handling callback:', error);
      res.status(500).json({
        error: "Failed to complete authorization",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Check auth status
   */
  public checkAuthStatus = async (req: Request, res: Response): Promise<void> => {
    // Mock auth mode - bypass all authentication
    if (process.env.USE_MOCK_AUTH === 'true' && process.env.NODE_ENV !== 'production') {
      console.log('[MOCK AUTH] Returning mock authenticated status');
      res.json({
        authenticated: true,
        tenants: [{
          tenantId: "mock-tenant-123",
          tenantName: "Mock Test Company",
          tenantType: "ORGANISATION"
        }],
        activeTenant: {
          tenantId: "mock-tenant-123",
          tenantName: "Mock Test Company",
          tenantType: "ORGANISATION"
        },
        user: {
          name: "Mock User",
          email: "<EMAIL>",
          sub: "mock-user-123"
        },
        sessionId: req.sessionID,
        mockMode: true
      });
      return;
    }

    try {
      console.log('Check auth status request received, session ID:', req.sessionID);
      console.log('Request cookies:', req.headers.cookie);
      console.log('Request host: ", req.get("host'));
      console.log('Request origin: ", req.get("origin'));
      console.log('Request referer: ", req.get("referer'));

      // For preview deployments, add additional diagnostics
      const isPreviewDeployment = process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview') || false;
      if (isPreviewDeployment) {
        console.log('Preview deployment detected in auth status check');
        console.log('RENDER_EXTERNAL_HOSTNAME:', process.env.RENDER_EXTERNAL_HOSTNAME);
        console.log('X-Forwarded-Host: ", req.get("x-forwarded-host'));
        console.log('X-Forwarded-Proto: ", req.get("x-forwarded-proto'));
      }

      // Check if we're in preview and it's the first load - for preview deployments,
      // don't redirect directly but return a redirect URL for the client to use
      const isFirstLoad = req.query.first_load === 'true';
      const hasAuthorizationCode = req.query.code && req.query.state;

      if (isPreviewDeployment && (isFirstLoad || hasAuthorizationCode)) {
        console.log('Preview first load or authorization code detected, sending redirect URL');

        // Instead of directly redirecting, return a URL for the client to use
        const xeroService = getXeroService();
        const consentUrl = await xeroService.buildConsentUrl();

        // Return a JSON response with the redirect URL
        res.json({
          authenticated: false,
          redirectUrl: consentUrl,
          reason: "Preview deployment first load",
          sessionId: req.sessionID,
          preview: true
        });
        return;
      }

      // Verify we have a valid session with required data
      if (!req.session || !req.session.tokenSet) {
        console.log('No valid session found, returning not authenticated');
        res.json({
          authenticated: false,
          reason: "No valid session found",
          sessionId: req.sessionID,
          hasCookie: !!req.headers.cookie,
          preview: isPreviewDeployment
        });
        return;
      }

      console.log('Session data:', {
        hasTokenSet: !!req.session.tokenSet,
        hasDecodedIdToken: !!req.session.decodedIdToken,
        hasUserInfo: !!req.session.userInfo,
        hasTenants: !!(req.session.allTenants && req.session.allTenants.length > 0)
      });

      const xeroService = getXeroService();
      const isAuthenticated = await xeroService.isAuthenticated();

      console.log('isAuthenticated result:', isAuthenticated);
      console.log('Tenants available:', xeroService.getTenants().length);

      // If not authenticated in preview, force new login
      if (isPreviewDeployment && !isAuthenticated) {
        console.log('Preview deployment detected but not authenticated, redirecting to auth');
        return this.initiateAuth(req, res);
      }

      const response = {
        authenticated: isAuthenticated,
        tenants: isAuthenticated ? xeroService.getTenants() : [],
        activeTenant: isAuthenticated ? xeroService.getTenants()[0] : null,
        user: req.session?.userInfo || null,
        sessionId: req.sessionID, // Include session ID for debugging
        preview: isPreviewDeployment,
        host: req.get('host'),
        previewContext: isPreviewDeployment,
        renderHost: process.env.RENDER_EXTERNAL_HOSTNAME,
        // Additional data for debugging
        requestData: {
          method: req.method,
          protocol: req.protocol,
          secure: req.secure,
          hasSessionCookie: req.headers.cookie?.includes('connect.sid') || false,
          hasPreviewAuthCookie: req.headers.cookie?.includes('preview_auth') || false,
          origin: req.get('origin'),
          referer: req.get('referer')
        },
        timestamp: new Date().toISOString()
      };

      console.log('Sending auth status response:', {
        authenticated: response.authenticated,
        hasTenants: response.tenants.length > 0,
        hasActiveTenant: !!response.activeTenant,
        hasUser: !!response.user,
        preview: isPreviewDeployment
      });

      // Explicitly save session before responding
      if (req.session.save) {
        req.session.save(err => {
          if (err) {
            console.error('Error saving session in auth-status:', err);
          }
          res.json(response);
        });
      } else {
        res.json(response);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      res.status(500).json({
        error: "Failed to check authentication status",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Get user information
   */
  public getUserInfo = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.session?.decodedIdToken) {
        res.status(401).json({ error: "Not authenticated" });
        return;
      }

      res.json(req.session.decodedIdToken);
    } catch (error) {
      console.error('Error getting user info:', error);
      res.status(500).json({
        error: "Failed to get user information",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Logout from Xero
   */
  public logout = async (req: Request, res: Response): Promise<void> => {
    try {
      if (req.session) {
        // Clear session data
        delete req.session.tokenSet;
        delete req.session.decodedIdToken;
        delete req.session.decodedAccessToken;
        delete req.session.allTenants;
        delete req.session.activeTenant;
        delete req.session.userInfo;
      }

      res.json({ success: true, message: "Logged out successfully" });
    } catch (error) {
      console.error('Error logging out:', error);
      res.status(500).json({
        error: "Failed to logout",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Get account information including user and organization details
   */
  public getAccountInfo = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.session?.tokenSet) {
        res.status(401).json({ error: "Not authenticated" });
        return;
      }

      const xeroService = getXeroService();
      const tenantId = xeroService.getActiveTenantId();

      // Ensure we have an authenticated session
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({ error: "Not authenticated" });
        return;
      }

      // Get user info from session
      const userInfo = req.session.userInfo || {};

      // Get organization info
      let organizationInfo: XeroOrganization = {};
      try {
        const orgResponse = await xeroService.getClient().accountingApi.getOrganisations(tenantId);
        organizationInfo = orgResponse.body.organisations?.[0] || {};
      } catch (error) {
        console.error('Error fetching organization info:', error);
        // Continue with partial data if organization info fails
      }

      res.json({
        success: true,
        user: {
          name: userInfo.name || "User",
          email: userInfo.email || "No email provided",
          id: userInfo.sub
        },
        organization: {
          name: organizationInfo.name || "Your Organization",
          id: tenantId
        }
      });
    } catch (error) {
      console.error('Error getting account info:', error);
      res.status(500).json({
        error: "Failed to get account information",
        details: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };

  /**
   * Get Xero organization info
   */
  public getOrganization = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const xeroService = getXeroService();
      const tenantId = xeroService.getActiveTenantId();

      if (!tenantId) {
        res.status(401).json({ error: "No active Xero organization found. Please authenticate first." });
        return;
      }

      const response = await xeroService.getClient().accountingApi.getOrganisations(tenantId);
      res.json(response.body);
    } catch (error) {
      next(error);
    }
  };
}