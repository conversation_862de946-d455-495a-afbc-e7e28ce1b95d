import { Request, Response } from "express";
import {
  getXeroService,
  ActivityStatementService
} from "../../../services/xero";
import { ExpensesRepository } from "../../repositories/expenses-repository";
import { CustomExpense } from "../../../types";
import { XERO_SOURCES } from "../../../constants/xero-backend";
import activityLogger from "../../../utils/activity-logger";

export class XeroActivityController {
  /**
   * Get activity statements from Xero
   */
  public getActivityStatements = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get days parameter from query (defaults to 180 for activity statements since they're less frequent)
      const days = parseInt(req.query.days as string) || 180;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create activity statement service instance
      const activityStatementService = new ActivityStatementService(client);

      try {
        // Fetch activity statements from Xero - with NO fallback to sample data
        const statements = await activityStatementService.getActivityStatementsForDisplay(tenantId, sinceDate);

        res.json({
          success: true,
          data: statements
        });
      } catch (activityError) {
        // Handle specific service errors with appropriate status codes
        console.error('Error in Xero activity statements service:', activityError);

        if (activityError instanceof Error) {
          // Differentiate between different error types
          if (activityError.message.includes('Missing accounting.reports scope')) {
            res.status(403).json({
              success: false,
              message: activityError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required accounting.reports scope. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (activityError.message.includes('No report data available')) {
            res.status(404).json({
              success: false,
              message: activityError.message,
              code: 'NO_DATA',
              details: 'No activity statement data was found in your Xero account.'
            });
            return;
          } else if (activityError.message.includes('Could not locate tax information')) {
            res.status(404).json({
              success: false,
              message: activityError.message,
              code: 'NO_TAX_DATA',
              details: 'No tax information was found in your Xero account. Please ensure you have configured tax settings in Xero.'
            });
            return;
          }
        }

        // Generic service error
        res.status(500).json({
          success: false,
          message: activityError instanceof Error ? activityError.message : 'Unknown activity statement service error',
          code: 'TAX_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting activity statements from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get activity statements from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Convert an activity statement to a custom expense
   */
  public convertActivityStatementToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { statement } = req.body;

      if (!statement) {
        res.status(400).json({
          success: false,
          message: 'Missing statement data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate tax type
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: `${statement.type}: ${statement.taxPeriod}`,
        type: 'Taxes', // Use predefined type for tax payments
        amount: statement.amount,
        date: new Date(statement.dueDate),
        frequency: statement.frequency as CustomExpense['frequency'],
        source: XERO_SOURCES.TAX_STATEMENT, // Mark as coming from Xero
        description: `${statement.type} tax payment from Xero for period ${statement.taxPeriod}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero tax statement sync activity...');
        await activityLogger.logXeroSyncCompleted('tax-statements', 1);
        console.log('Successfully logged Xero tax statement sync activity');
      } catch (activityError) {
        console.error('Error logging Xero tax statement sync activity:', activityError);
        console.error('Activity error stack:', activityError.stack);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting activity statement to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert activity statement to expense'
      });
    }
  };

  /**
   * Convert GST data to a custom expense
   */
  public convertGSTToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { gstData } = req.body;

      if (!gstData) {
        res.status(400).json({
          success: false,
          message: 'Missing GST data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Format the due date for display in Australian format (DD/MM/YYYY)
      const dueDate = new Date(gstData.dueDate);
      const dueDateStr = `${dueDate.getDate()}/${dueDate.getMonth() + 1}/${dueDate.getFullYear()}`;

      // Check if this is using a predicted amount
      const isUsingPredictedAmount = gstData.metadata?.usedProjectedAmount === true;

      // Create a description that includes whether this is a predicted amount
      const amountTypeDesc = isUsingPredictedAmount
        ? 'predicted amount (accrued GST plus GST from projected future invoices)'
        : 'accrued amount (current GST liability)';

      // Convert to expense format with appropriate tax type
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: `GST Payment (BAS) - Due ${dueDateStr}`,
        type: 'Taxes', // Use predefined type for tax payments
        amount: gstData.amount,
        date: new Date(gstData.dueDate),
        frequency: 'one-off', // One-off payment for this quarter
        source: XERO_SOURCES.GST,
        description: `GST payment due on ${dueDateStr} (10% of sales from Xero Profit and Loss) - Using ${amountTypeDesc}`,
        metadata: {
          usedProjectedAmount: isUsingPredictedAmount,
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Check if a similar GST expense already exists to prevent duplicates
      let existingExpenseId = null;
      try {
        // Get all expenses
        const allExpenses = expensesRepository.getAll();

        // Find an expense that matches our criteria for GST
        const existingExpense = allExpenses.find(exp => {
          // Check if it's a GST expense from Xero
          const isGSTExpense = exp.type === 'Taxes' &&
                              // First check if it's from Xero using source or metadata
                              ((exp.source && exp.source.includes('xero-gst')) ||
                               (exp.metadata?.isFromXero === true &&
                                ((exp.name.toLowerCase().includes('gst') && exp.name.toLowerCase().includes('payment')) ||
                                 (exp.name.toLowerCase().includes('bas') && exp.name.toLowerCase().includes('payment')))));

          // Check if it's for the same due date (within 1 day)
          const isSameDueDate = Math.abs(
            exp.date.getTime() - normalizedDate.getTime()
          ) < 24 * 60 * 60 * 1000; // 1 day in milliseconds

          return isGSTExpense && isSameDueDate;
        });

        if (existingExpense) {
          existingExpenseId = existingExpense.id;
          console.log(`Found existing GST expense with ID: ${existingExpenseId}`);
        }
      } catch (error) {
        console.error('Error checking for existing GST expenses:', error);
        // Continue with creating a new expense if we can't check for existing ones
      }

      let result: CustomExpense | null;

      // Update existing expense or create a new one
      if (existingExpenseId) {
        console.log(`Updating existing GST expense with ID: ${existingExpenseId}`);
        result = expensesRepository.update(existingExpenseId, customExpense);
        console.log('Successfully updated GST expense');

        // If update fails, create a new expense as fallback
        if (!result) {
          console.log('Update failed, creating new GST expense instead');
          result = expensesRepository.create(customExpense);
        }
      } else {
        console.log('Creating new GST expense');
        result = expensesRepository.create(customExpense);
        console.log('Successfully created GST expense');
      }

      // Ensure we have a valid result
      if (!result) {
        throw new Error('Failed to create or update GST expense');
      }

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Error converting GST to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert GST to expense'
      });
    }
  };
}