import { Request, Response } from "express";
import { CashflowSnapshotService } from "../../services/cashflow/snapshot-service";
import { CashflowService } from "../../services/cashflow";
import { getUserTenantId } from "../../utils/auth";

/**
 * Controller for cashflow snapshot API endpoints
 * Handles requests for snapshot data and operations
 */
export class CashflowSnapshotController {
  private snapshotService: CashflowSnapshotService;
  private cashflowService: CashflowService;

  constructor() {
    this.snapshotService = new CashflowSnapshotService();
    this.cashflowService = new CashflowService();
  }

  /**
   * Get available snapshot dates
   *
   * @route GET /api/cashflow/snapshots/dates
   */
  getAvailableDates = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get tenant ID from authenticated user
      const tenantId = getUserTenantId(req);
      if (!tenantId) {
        res.status(400).json({
          success: false,
          message: 'No active Xero tenant found'
        });
        return;
      }

      // Get days ahead from query params (default to 90)
      const daysAhead = req.query.daysAhead ?
        parseInt(req.query.daysAhead as string, 10) :
        90;

      // Get available dates
      const dates = await this.snapshotService.getAvailableDates(
        tenantId,
        daysAhead
      );

      res.json({
        success: true,
        dates
      });
    } catch (error: any) {
      console.error('Error getting snapshot dates:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get snapshot dates'
      });
    }
  };

  /**
   * Get snapshot for a specific date
   *
   * @route GET /api/cashflow/snapshots/:date
   */
  getSnapshot = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get tenant ID from authenticated user
      const tenantId = getUserTenantId(req);
      if (!tenantId) {
        res.status(400).json({
          success: false,
          message: 'No active Xero tenant found'
        });
        return;
      }

      // Get date from URL params
      const { date } = req.params;
      if (!date) {
        res.status(400).json({
          success: false,
          message: 'Date parameter is required'
        });
        return;
      }

      // Get days ahead from query params (default to 90)
      const daysAhead = req.query.daysAhead ?
        parseInt(req.query.daysAhead as string, 10) :
        90;

      // Get snapshot
      const snapshot = await this.snapshotService.getSnapshot(
        tenantId,
        date,
        daysAhead
      );

      if (!snapshot) {
        res.status(404).json({
          success: false,
          message: `No snapshot found for date ${date}`
        });
        return;
      }

      res.json({
        success: true,
        isHistorical: true,
        snapshotDate: date,
        data: snapshot
      });
    } catch (error: any) {
      console.error('Error getting snapshot:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to get snapshot'
      });
    }
  };

  /**
   * Create a manual snapshot of the current cashflow projection
   *
   * @route POST /api/cashflow/snapshots/create
   */
  createManualSnapshot = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get tenant ID from authenticated user
      const tenantId = getUserTenantId(req);
      if (!tenantId) {
        res.status(400).json({
          success: false,
          message: 'No active Xero tenant found'
        });
        return;
      }

      // Get days ahead from query params (default to 90)
      const daysAhead = req.body.daysAhead || 90;

      // Ensure custom expenses have proper Date objects
      const customExpenses = (req.body.customExpenses || []).map((expense: any) => ({
        ...expense,
        date: expense.date ? new Date(expense.date) : new Date()
      }));

      // Generate current projection
      const forecast = await this.cashflowService.generateForwardProjection(
        tenantId,
        daysAhead,
        customExpenses
      );

      // Create snapshot
      const snapshotDate = await this.snapshotService.createSnapshot(
        tenantId,
        forecast,
        daysAhead
      );

      // Return success response
      res.json({
        success: true,
        message: 'Snapshot created successfully',
        date: snapshotDate
      });
    } catch (error: any) {
      console.error('Error creating manual snapshot:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to create snapshot'
      });
    }
  };
}
