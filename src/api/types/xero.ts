import { Transaction } from "../../types/api";

export interface XeroConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
}

/**
 * Interface for payroll calendar types
 */
export enum CalendarType {
  WEEKLY = 'WEEKLY',
  FORTNIGHTLY = 'FORTNIGHTLY',
  FOURWEEKLY = 'FOURWEEKLY',
  MONTHLY = 'MONTHLY',
  TWICEMONTHLY = 'TWICEMONTHLY',
  QUARTERLY = 'QUARTERLY'
}

/**
 * Interface for a payroll calendar
 */
export interface PayrollCalendar {
  payrollCalendarID: string;
  name: string;
  calendarType: CalendarType;
  startDate: string;
  paymentDate: string;
  updatedDateUTC?: Date;
}

/**
 * Interface for a payroll run
 */
export interface PayRun {
  payRunID: string;
  payrollCalendarID: string;
  periodStartDate: string;
  periodEndDate: string;
  paymentDate: string;
  totalCost: number;
  totalPay: number;
  payRunStatus: string;
  calendarType?: string;
  name?: string;
  estimatedNetPay?: number;
}

/**
 * Interface for projected payroll runs
 */
export interface ProjectedPayRun {
  payrollCalendarID: string;
  calendarType: string;
  paymentDate: string;
  estimatedNetPay: number;
  name: string;
}

export interface XeroTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface XeroContact {
  contactID: string;
  name: string;
  emailAddress?: string;
}

export interface XeroInvoice extends Transaction {
  invoiceID: string;
  contact: XeroContact;
  invoiceNumber: string;
  reference?: string;
  dueDate: Date;
  lineItems: XeroLineItem[];
  subTotal: number;
  totalTax: number;
  total: number;
  currencyCode: string;
  type: 'invoice';
}

export interface XeroLineItem {
  lineItemID: string;
  description: string;
  quantity: number;
  unitAmount: number;
  taxAmount: number;
  lineAmount: number;
  accountCode?: string;
}

export interface XeroPayment extends Omit<Transaction, 'status'> {
  paymentID: string;
  invoiceID: string;
  paymentNumber?: string;
  paymentDate: Date;
  amount: number;
  currencyCode: string;
  paymentType: 'ACCRECPAYMENT' | 'ACCPAYPAYMENT';
  xeroStatus: 'AUTHORISED' | 'PAID';
  status: Transaction['status'];
  type: 'payment';
}

export interface XeroBill extends Transaction {
  billID: string;
  contact: XeroContact;
  reference?: string;
  dueDate: Date;
  lineItems: XeroLineItem[];
  subTotal: number;
  totalTax: number;
  total: number;
  currencyCode: string;
  type: 'bill';
}

export interface XeroApiResponse<T> {
  Id: string;
  Status: string;
  ProviderName: string;
  DateTimeUTC: string;
  Items: T[];
}

// Xero API specific error types
export interface XeroApiError {
  ErrorNumber: number;
  Type: string;
  Message: string;
  Elements?: unknown[];
}

export interface XeroErrorResponse {
  ErrorNumber: number;
  Type: string;
  Message: string;
  Elements: {
    ValidationErrors: {
      Message: string;
    }[];
  }[];
}

/**
 * Xero bank account information
 */
export interface XeroBankAccount {
  accountID: string;
  name: string;
  code: string;
  type: string;
  currencyCode: string;
  balance: number;
}

/**
 * Xero repeating transaction (recurring invoice or bill)
 */
export interface XeroRepeatingTransaction {
  repeatingTransactionID: string;
  type: 'ACCRECPAYMENT' | 'ACCPAYPAYMENT';
  contact: XeroContact;
  schedule: {
    period: number;
    unit: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
    dueDate: number;
    startDate: Date;
    endDate?: Date;
    nextDate: Date;
  };
  amount: number;
  currencyCode: string;
  reference?: string;
  description?: string;
}

/**
 * Xero cash flow data structure
 */
export interface XeroCashFlowData {
  startDate: Date;
  endDate: Date;
  initialBalance: number;
  dataPoints: XeroCashFlowDataPoint[];
  events: XeroCashFlowEvent[];
}

/**
 * Xero cash flow data point
 */
export interface XeroCashFlowDataPoint {
  date: Date;
  cashBalance: number;
  isProjected: boolean;
}

/**
 * Xero cash flow event
 */
export interface XeroCashFlowEvent {
  date: Date;
  amount: number;
  description: string;
  type: 'invoice' | 'bill' | 'recurring' | 'tax' | 'other';
  entityId: string;
  isProjected: boolean;
}

/**
 * Interface for Xero bill display
 */
export interface XeroBillDisplay {
  id: string;
  invoiceNumber: string;
  reference: string;
  date: Date;
  dueDate: Date;
  amount: number;
  vendor: string;
  status: string;
  lineItems: any[];
  description?: string;
  isAlreadyAdded?: boolean;
}

/**
 * Interface for Xero payroll expense display
 */
export interface XeroPayrollExpenseDisplay {
  id: string;
  payRunId: string;
  paymentDate: Date;
  periodEndDate: Date;
  periodStartDate: Date;
  amount: number;
  wages?: number;
  superannuation?: number;
  tax?: number;
  reimbursement?: number;
  employeeCount: number;
  status: string;
  description: string;
  detailedDescription?: string;
  frequency: string;
  isAlreadyAdded?: boolean;
  isTemplate?: boolean;
  hasRecurringExpense?: boolean;
  source?: string;
}

/**
 * Interface for Xero expense breakdown with separated expense types
 */
export interface XeroExpenseBreakdown {
  // Common fields
  id: string;
  payRunId: string;
  paymentDate: Date;
  periodStartDate: Date;
  periodEndDate: Date;
  employeeCount: number;
  status: string;
  frequency: string;
  source: string;
  description: string;

  // Separated expense types
  wages: {
    amount: number;
    paymentDate: Date; // Same as payroll payment date
    isAdded: boolean;
    hasRecurringExpense?: boolean;
  };
  tax: {
    amount: number;
    paymentDate: Date; // 10th of the following month
    isAdded: boolean;
    hasRecurringExpense?: boolean;
  };
  superannuation: {
    amount: number;
    paymentDate: Date; // Same as payroll payment date
    isAdded: boolean;
    hasRecurringExpense?: boolean;
  };
}

/**
 * Interface for Xero superannuation expense display
 */
export interface XeroSuperannuationExpenseDisplay {
  id: string;
  provider: string;
  paymentDate: Date;
  amount: number;
  status: string;
  description: string;
  employeeCount: number;
  frequency: string;
  isAlreadyAdded?: boolean;
}

/**
 * Interface for Xero activity statement display
 */
export interface XeroActivityStatementDisplay {
  id: string;
  type: 'BAS' | 'IAS' | 'Other';
  taxPeriod: string;
  dueDate: Date;
  amount: number;
  status: string;
  description: string;
  frequency: 'monthly' | 'quarterly' | 'annual';
  isAlreadyAdded?: boolean;
}

/**
 * Interface for Xero GST data
 */
export interface XeroGSTData {
  amount: number;            // Original accrued amount from Xero (unchanged)
  dueDate: Date;
  accountId: string;
  hasRecurringExpense?: boolean;
  isAdded?: boolean;
  // New fields for improved projection
  projectedGST?: number;     // GST from projected future invoices
  totalProjectedGST?: number; // Combined accrued + projected GST
  daysElapsed?: number;
  daysInQuarter?: number;
  projectedRevenue?: number;
  projectedInvoiceCount?: number; // Number of projected invoices
  metadata?: Record<string, any>;
}
