/**
 * Activity Service
 *
 * This service handles business logic for the activity feed system,
 * including real-time updates via Socket.IO and event publishing.
 */

import { EventEmitter } from "events";
import { Server as SocketIOServer } from "socket.io";
import { ActivityRepository } from "../repositories/activity-repository";
import { 
  Activity, 
  ActivityCreate, 
  ActivityUpdate, 
  ActivityFilters, 
  ActivityFeedResponse,
  ActivityStats,
  ActivityLogger
} from "../../frontend/types/activity-types";

/**
 * Activity service class
 */
export class ActivityService extends EventEmitter implements ActivityLogger {
  private activityRepository: ActivityRepository;
  private io?: SocketIOServer;

  constructor(io?: SocketIOServer) {
    super();
    this.activityRepository = new ActivityRepository();
    this.io = io;
  }

  /**
   * Set Socket.IO server instance
   */
  setSocketIO(io: SocketIOServer): void {
    this.io = io;
  }

  /**
   * Create a new activity and emit real-time updates
   */
  async createActivity(data: ActivityCreate): Promise<Activity> {
    try {
      const activity = await this.activityRepository.createActivity(data);
      
      // Emit Socket.IO event for real-time updates
      if (this.io) {
        this.io.emit('activity:created', activity);
      }
      
      // Emit EventEmitter event for internal listeners
      this.emit('activity-created', activity);
      
      console.log(`Activity created: ${activity.type} - ${activity.subject}`);
      return activity;
    } catch (error) {
      console.error('Error creating activity:', error);
      throw error;
    }
  }

  /**
   * Get activity by ID
   */
  async getActivity(id: string): Promise<Activity | null> {
    try {
      return await this.activityRepository.getActivityById(id);
    } catch (error) {
      console.error(`Error getting activity ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update an activity
   */
  async updateActivity(id: string, data: ActivityUpdate): Promise<Activity | null> {
    try {
      const activity = await this.activityRepository.updateActivity(id, data);
      
      if (activity && this.io) {
        this.io.emit('activity:updated', activity);
      }
      
      return activity;
    } catch (error) {
      console.error(`Error updating activity ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an activity
   */
  async deleteActivity(id: string): Promise<boolean> {
    try {
      const success = await this.activityRepository.deleteActivity(id);
      
      if (success && this.io) {
        this.io.emit('activity:deleted', { id });
      }
      
      return success;
    } catch (error) {
      console.error(`Error deleting activity ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get activity feed with filters and pagination
   */
  async getActivityFeed(filters: ActivityFilters = {}): Promise<ActivityFeedResponse> {
    try {
      const activities = await this.activityRepository.getActivities(filters);
      
      // Get total count for pagination
      const totalFilters = { ...filters };
      delete totalFilters.limit;
      delete totalFilters.offset;
      const allActivities = await this.activityRepository.getActivities(totalFilters);
      const total = allActivities.length;
      
      const hasMore = filters.limit ? 
        (filters.offset || 0) + activities.length < total : 
        false;

      return {
        activities,
        total,
        hasMore
      };
    } catch (error) {
      console.error('Error getting activity feed:', error);
      throw error;
    }
  }

  /**
   * Get activities for a specific entity
   */
  async getActivitiesForEntity(entityType: string, entityId: string, limit: number = 50): Promise<Activity[]> {
    try {
      return await this.activityRepository.getActivitiesForEntity(entityType, entityId, limit);
    } catch (error) {
      console.error(`Error getting activities for entity ${entityType}:${entityId}:`, error);
      throw error;
    }
  }

  /**
   * Get recent activities
   */
  async getRecentActivities(limit: number = 20, offset: number = 0): Promise<Activity[]> {
    try {
      return await this.activityRepository.getRecentActivities(limit, offset);
    } catch (error) {
      console.error('Error getting recent activities:', error);
      throw error;
    }
  }

  /**
   * Get unread activity count
   */
  async getUnreadCount(userId?: string): Promise<number> {
    try {
      return await this.activityRepository.getUnreadCount(userId);
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Mark activities as read
   */
  async markAsRead(activityIds: string[]): Promise<boolean> {
    try {
      const success = await this.activityRepository.markAsRead(activityIds);
      
      if (success && this.io) {
        this.io.emit('activity:marked-read', { activityIds });
      }
      
      return success;
    } catch (error) {
      console.error('Error marking activities as read:', error);
      throw error;
    }
  }

  /**
   * Get activity statistics
   */
  async getActivityStats(): Promise<ActivityStats> {
    try {
      return await this.activityRepository.getActivityStats();
    } catch (error) {
      console.error('Error getting activity stats:', error);
      throw error;
    }
  }

  /**
   * Log a user action
   */
  async logUserAction(type: any, subject: string, details: Partial<ActivityCreate> = {}): Promise<Activity> {
    const activityData: ActivityCreate = {
      type,
      subject,
      source: "ui",
      createdBy: details.createdBy || "user",
      ...details
    };

    return this.createActivity(activityData);
  }

  /**
   * Log a system event
   */
  async logSystemEvent(type: any, subject: string, details: Partial<ActivityCreate> = {}): Promise<Activity> {
    const activityData: ActivityCreate = {
      type,
      subject,
      source: "system",
      createdBy: "system",
      ...details
    };

    return this.createActivity(activityData);
  }

  /**
   * Log an integration event
   */
  async logIntegrationEvent(source: any, type: any, details: Partial<ActivityCreate> = {}): Promise<Activity> {
    const activityData: ActivityCreate = {
      type,
      subject: details.subject || `${source} integration event`,
      source,
      createdBy: "system",
      ...details
    };

    console.log('ActivityService.logIntegrationEvent called:', { source, type, subject: activityData.subject });
    
    try {
      const result = await this.createActivity(activityData);
      console.log('ActivityService.logIntegrationEvent success:', result.id);
      return result;
    } catch (error) {
      console.error('ActivityService.logIntegrationEvent failed:', error);
      throw error;
    }
  }

  /**
   * Log entity changes
   */
  async logEntityChange(entityType: any, entityId: string, changes: Record<string, any>): Promise<Activity> {
    const changesList = Object.entries(changes)
      .map(([field, { oldValue, newValue }]) => `${field}: ${oldValue} → ${newValue}`)
      .join(', ');

    const activityData: ActivityCreate = {
      type: `${entityType}_updated` as any,
      subject: `${entityType} updated`,
      description: `Changes: ${changesList}`,
      entityType,
      entityId,
      source: "system",
      createdBy: "system",
      metadata: { changes }
    };

    return this.createActivity(activityData);
  }

  /**
   * Batch create activities (useful for bulk operations)
   */
  async createActivities(activities: ActivityCreate[]): Promise<Activity[]> {
    try {
      const createdActivities: Activity[] = [];
      
      for (const activityData of activities) {
        const activity = await this.activityRepository.createActivity(activityData);
        createdActivities.push(activity);
      }
      
      // Emit batch event for real-time updates
      if (this.io && createdActivities.length > 0) {
        this.io.emit('activity:batch-created', createdActivities);
      }
      
      return createdActivities;
    } catch (error) {
      console.error('Error creating batch activities:', error);
      throw error;
    }
  }

  /**
   * Clean up old activities (for maintenance)
   */
  async cleanupOldActivities(daysToKeep: number = 365): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();
      
      // Get activities to delete
      const oldActivities = await this.activityRepository.getActivities({
        dateTo: cutoffDate
      });
      
      // Delete old activities
      let deletedCount = 0;
      for (const activity of oldActivities) {
        const success = await this.activityRepository.deleteActivity(activity.id);
        if (success) {
          deletedCount++;
        }
      }
      
      console.log(`Cleaned up ${deletedCount} old activities`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up old activities:', error);
      throw error;
    }
  }
}

// Export singleton instance
let activityServiceInstance: ActivityService | null = null;

export function getActivityService(io?: SocketIOServer): ActivityService {
  if (!activityServiceInstance) {
    activityServiceInstance = new ActivityService(io);
  } else if (io && !activityServiceInstance['io']) {
    activityServiceInstance.setSocketIO(io);
  }
  
  return activityServiceInstance;
}
