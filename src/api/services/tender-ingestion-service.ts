/**
 * Tender Ingestion Service
 * 
 * Handles parsing and processing of TendersWA emails
 */

import { v4 as uuidv4 } from "uuid";
import { TenderRepository, TenderCreate } from "../repositories/tender-repository";
import { CompanyRepository } from "../repositories/company-repository";
import { getActivityService } from "./activity-service";
import { BaseTender, TenderQualificationStatus } from "../../types/shared-types";
import Anthropic from "@anthropic-ai/sdk";

// Email parsing result
interface TenderEmailData {
  requestNo: string;
  status: string;
  type: string;
  summary: string;
  issuedBy: string;
  unspsc?: string;
  closingDate: string;
  tenderUrl?: string;
}

/**
 * Service for ingesting and processing tender emails
 */
export class TenderIngestionService {
  private tenderRepository: TenderRepository;
  private companyRepository: CompanyRepository;
  private activityService = getActivityService();

  constructor() {
    this.tenderRepository = new TenderRepository();
    this.companyRepository = new CompanyRepository();
  }

  /**
   * Parse tender email content using regex
   * Expected format:
   * Request No.
   * (Status) &
   * Type    Request Summary Dates
   * GEN250120
   * (Current)
   * Tender  Internal Audit Services
   * Issued by Edith Cowan University
   * UNSPSC: Management advisory services - (100%)
   * closing 26 Jun, 2025 2:00 PM
   */
  parseTenderEmail(emailContent: string): TenderEmailData | null {
    try {
      // Clean up the content
      const lines = emailContent.split('\n').map(line => line.trim()).filter(line => line);
      
      // Find the tender data block
      let requestNo = '';
      let status = '';
      let type = 'Tender';
      let summary = '';
      let issuedBy = '';
      let unspsc = '';
      let closingDate = '';
      let tenderUrl = '';
      
      // Look for request number pattern (e.g., GEN250120)
      const requestNoMatch = emailContent.match(/([A-Z]{3}\d{6})/);
      if (requestNoMatch) {
        requestNo = requestNoMatch[1];
      }
      
      // Look for status in parentheses
      const statusMatch = emailContent.match(/\(([^)]+)\)/);
      if (statusMatch) {
        status = statusMatch[1];
      }
      
      // Look for "Tender" followed by the summary
      const summaryMatch = emailContent.match(/Tender\s+([^\n]+?)(?:\s+Issued by|\n)/i);
      if (summaryMatch) {
        summary = summaryMatch[1].trim();
      }
      
      // Look for issued by
      const issuedByMatch = emailContent.match(/Issued by\s+([^\n]+?)(?:\s+UNSPSC:|\n)/i);
      if (issuedByMatch) {
        issuedBy = issuedByMatch[1].trim();
      }
      
      // Look for UNSPSC
      const unspscMatch = emailContent.match(/UNSPSC:\s+([^\n]+)/i);
      if (unspscMatch) {
        unspsc = unspscMatch[1].trim();
      }
      
      // Look for closing date
      const closingMatch = emailContent.match(/closing\s+(\d{1,2}\s+\w+,?\s+\d{4}\s+\d{1,2}:\d{2}\s+[AP]M)/i);
      if (closingMatch) {
        // Parse the date format "26 Jun, 2025 2:00 PM"
        const dateStr = closingMatch[1];
        const parsedDate = new Date(dateStr);
        if (!isNaN(parsedDate.getTime())) {
          closingDate = parsedDate.toISOString();
        }
      }
      
      // Look for tender URL (if the summary contains a link)
      const urlMatch = emailContent.match(/(https?:\/\/[^\s]+)/);
      if (urlMatch) {
        tenderUrl = urlMatch[1];
      }
      
      // Validate required fields
      if (!requestNo || !summary || !issuedBy || !closingDate) {
        console.error('Missing required tender fields:', {
          requestNo: !!requestNo,
          summary: !!summary,
          issuedBy: !!issuedBy,
          closingDate: !!closingDate
        });
        return null;
      }
      
      return {
        requestNo,
        status: status || 'Current',
        type,
        summary,
        issuedBy,
        unspsc,
        closingDate,
        tenderUrl
      };
    } catch (error) {
      console.error('Error parsing tender email:', error);
      return null;
    }
  }

  /**
   * Find or create a company based on the issuer name
   */
  private async findOrCreateCompany(issuerName: string): Promise<string | null> {
    try {
      // First, try to find an existing company by name
      const companies = this.companyRepository.searchCompanies({ 
        search: issuerName,
        limit: 1 
      });
      
      if (companies.length > 0) {
        // Check if the name is a close enough match
        const existingCompany = companies[0];
        const normalizedExisting = existingCompany.name.toLowerCase().trim();
        const normalizedIssuer = issuerName.toLowerCase().trim();
        
        // Simple matching - could be enhanced with fuzzy matching
        if (normalizedExisting === normalizedIssuer || 
            normalizedExisting.includes(normalizedIssuer) ||
            normalizedIssuer.includes(normalizedExisting)) {
          return existingCompany.id;
        }
      }
      
      // Create a new company
      const newCompany = this.companyRepository.createCompany({
        name: issuerName,
        source: 'TenderIngestion' as any
      }, 'TenderIngestion');
      
      if (newCompany) {
        // Log activity
        await this.activityService.createActivity({
          type: 'company_created',
          subject: `Company "${issuerName}" created from tender`,
          entityType: 'company',
          entityId: newCompany.id,
          source: 'system',
          createdBy: 'tender_ingestion',
          metadata: {
            source: 'tender_email'
          }
        });
        
        return newCompany.id;
      }
      
      return null;
    } catch (error) {
      console.error('Error finding or creating company:', error);
      return null;
    }
  }

  /**
   * Parse tender email using AI when regex parsing fails
   */
  private async parseWithAI(emailContent: string): Promise<TenderEmailData | null> {
    // Only use AI if API key is configured
    if (!process.env.ANTHROPIC_API_KEY) {
      console.log('AI parsing not available - no API key');
      return null;
    }

    try {
      const anthropic = new Anthropic({ 
        apiKey: process.env.ANTHROPIC_API_KEY 
      });
      
      const prompt = `Extract tender/RFP/procurement information from this email. 
      
      Return ONLY valid JSON with these fields:
      - requestNo: tender/RFP number (if not found, generate one like "AUTO" + today's date YYYYMMDD)
      - summary: brief description of what's being tendered
      - issuedBy: organization issuing the tender
      - closingDate: deadline in ISO format (YYYY-MM-DDTHH:mm:ssZ)
      - status: current status (default: "Current")
      - unspsc: category if mentioned (optional)
      - tenderUrl: URL if present (optional)
      
      If this doesn't look like a tender/RFP email, return null.
      
      Email content:
      ${emailContent}`;
      
      const response = await anthropic.messages.create({
        model: "claude-3-haiku-20240307", // Fast and cheap
        max_tokens: 500,
        temperature: 0, // Deterministic parsing
        messages: [{ 
          role: "user", 
          content: prompt 
        }]
      });
      
      // Extract JSON from response
      const content = response.content[0].type === 'text' 
        ? response.content[0].text 
        : '';
      
      // Try to parse JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.log('AI response did not contain valid JSON');
        return null;
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate required fields
      if (!parsed.summary || !parsed.issuedBy || !parsed.closingDate) {
        console.log('AI parsed data missing required fields');
        return null;
      }
      
      // Ensure we have a request number
      if (!parsed.requestNo) {
        parsed.requestNo = `AUTO${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`;
      }
      
      return parsed as TenderEmailData;
    } catch (error) {
      console.error('AI parsing error:', error);
      return null;
    }
  }

  /**
   * Process an incoming tender email
   */
  async processTenderEmail(emailContent: string): Promise<BaseTender | null> {
    try {
      // Try regex parsing first (fast and free)
      let tenderData = this.parseTenderEmail(emailContent);
      
      // If regex fails and AI is available, try AI parsing
      if (!tenderData && process.env.ANTHROPIC_API_KEY) {
        console.log('Regex parsing failed, trying AI parser...');
        tenderData = await this.parseWithAI(emailContent);
        
        if (tenderData) {
          console.log('AI successfully parsed tender:', tenderData.requestNo);
        }
      }
      
      if (!tenderData) {
        console.error('Failed to parse tender email with both regex and AI');
        return null;
      }
      
      // Check if tender already exists
      const existingTender = this.tenderRepository.getTenderByRequestNo(tenderData.requestNo);
      if (existingTender) {
        console.log(`Tender ${tenderData.requestNo} already exists`);
        return existingTender;
      }
      
      // Find or create the company
      const companyId = await this.findOrCreateCompany(tenderData.issuedBy);
      
      // Create the tender
      const createData: TenderCreate = {
        ...tenderData,
        sourceEmail: emailContent,
        companyId: companyId || undefined,
        qualificationStatus: 'new'
      };
      
      const tender = this.tenderRepository.createTender(createData);
      
      if (tender) {
        // Log activity
        await this.activityService.createActivity({
          type: 'tender_received',
          subject: `Tender ${tender.requestNo} received: ${tender.summary}`,
          entityType: 'tender',
          entityId: tender.id,
          source: 'system',
          createdBy: 'tender_ingestion',
          metadata: {
            requestNo: tender.requestNo,
            issuedBy: tender.issuedBy,
            closingDate: tender.closingDate
          }
        });
      }
      
      return tender;
    } catch (error) {
      console.error('Error processing tender email:', error);
      return null;
    }
  }

  /**
   * Qualify a tender (move through workflow)
   */
  async qualifyTender(
    tenderId: string, 
    status: TenderQualificationStatus,
    reason?: string,
    qualifiedBy?: string
  ): Promise<BaseTender | null> {
    try {
      const tender = this.tenderRepository.getTenderById(tenderId);
      if (!tender) {
        console.error('Tender not found:', tenderId);
        return null;
      }
      
      // Update tender status
      const updatedTender = this.tenderRepository.qualifyTender(
        tenderId, 
        status, 
        reason, 
        qualifiedBy
      );
      
      if (!updatedTender) {
        return null;
      }
      
      // Log qualification activity
      const activityType = status === 'interested' ? 'tender_interested' : 
                          status === 'not_interested' ? 'tender_disqualified' : 
                          'tender_qualified';
      
      await this.activityService.createActivity({
        type: activityType,
        subject: `Tender ${tender.requestNo} marked as ${status}`,
        entityType: 'tender',
        entityId: tender.id,
        source: 'user',
        createdBy: qualifiedBy || 'system',
        metadata: {
          previousStatus: tender.qualificationStatus,
          newStatus: status,
          reason
        }
      });
      
      return updatedTender;
    } catch (error) {
      console.error('Error qualifying tender:', error);
      return null;
    }
  }


  /**
   * Get tender statistics
   */
  getTenderStats(): {
    total: number;
    new: number;
    reviewing: number;
    interested: number;
    notInterested: number;
    closingSoon: number;
  } {
    try {
      const allTenders = this.tenderRepository.getAllTenders();
      const closingSoon = this.tenderRepository.getTendersClosingSoon(7);
      
      return {
        total: allTenders.length,
        new: allTenders.filter(t => t.qualificationStatus === 'new').length,
        reviewing: allTenders.filter(t => t.qualificationStatus === 'reviewing').length,
        interested: allTenders.filter(t => t.qualificationStatus === 'interested').length,
        notInterested: allTenders.filter(t => t.qualificationStatus === 'not_interested').length,
        closingSoon: closingSoon.length
      };
    } catch (error) {
      console.error('Error getting tender stats:', error);
      return {
        total: 0,
        new: 0,
        reviewing: 0,
        interested: 0,
        notInterested: 0,
        closingSoon: 0
      };
    }
  }
}