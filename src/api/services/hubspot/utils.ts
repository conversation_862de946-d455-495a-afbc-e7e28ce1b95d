import db from "../db-service";
import { v4 as uuidv4 } from "uuid";

/**
 * Utility functions for HubSpot service
 */
export class HubSpotUtils {
  /**
   * Helper to compare objects and track changes
   */
  static trackChanges(original: any, updated: any, fieldsToTrack: string[]): string[] {
    const changes: string[] = [];

    for (const field of fieldsToTrack) {
      const originalValue = original[field];
      const updatedValue = updated[field];

      // Skip if both are null/undefined
      if (!originalValue && !updatedValue) continue;

      // Check if values are different
      if (originalValue !== updatedValue) {
        if (!originalValue && updatedValue) {
          changes.push(`Added ${field}: "${updatedValue}"`);
        } else if (originalValue && !updatedValue) {
          changes.push(`Removed ${field}`);
        } else {
          changes.push(`Updated ${field}: "${originalValue}" → "${updatedValue}"`);
        }
      }
    }

    return changes;
  }

  /**
   * Record import activity in the database
   */
  static recordImport(data: {
    type: "companies" | "deals" | "contacts" | "full_import" | "quick_import";
    success: boolean;
    count: number;
    errors: Array<{ item: string; error: string }>;
    createdCount?: number;
    updatedCount?: number;
    duration?: number;
    metadata?: { 
      companiesCount?: number; 
      dealsCount?: number; 
      contactsCount?: number;
      notesCount?: number;
      associationsCount?: number;
      importType?: "quick" | "full";
      hasPartialSuccess?: boolean;
      deletedCount?: number;
    };
  }): void {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();
      
      const stmt = db.prepare(`
        INSERT INTO hubspot_imports (
          id, type, success, count, errors, created_count, updated_count, duration, imported_at, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        id,
        data.type,
        data.success ? 1 : 0,
        data.count,
        JSON.stringify(data.errors),
        data.createdCount || 0,
        data.updatedCount || 0,
        data.duration || 0,
        now,
        data.metadata ? JSON.stringify(data.metadata) : null
      );

      console.log(`Recorded ${data.type} import: ${data.success ? 'success" : "failure"} - ${data.count} items`);
    } catch (error) {
      console.error('Error recording import:', error);
    }
  }

  /**
   * Ensure the hubspot_imports table exists
   */
  static ensureImportsTableExists(): boolean {
    try {
      const tableExists = db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      `).get();

      if (!tableExists) {
        console.log('Creating hubspot_imports table...');

        db.prepare(`
          CREATE TABLE hubspot_imports (
            id TEXT PRIMARY KEY,
            type TEXT NOT NULL,
            success INTEGER NOT NULL,
            count INTEGER NOT NULL,
            errors TEXT,
            created_count INTEGER DEFAULT 0,
            updated_count INTEGER DEFAULT 0,
            duration INTEGER DEFAULT 0,
            imported_at TEXT NOT NULL,
            metadata TEXT
          )
        `).run();

        console.log('hubspot_imports table created successfully');
      } else {
        // Add metadata column if it doesn't exist
        try {
          const hasMetadata = db.prepare(`
            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          `).get();
          
          if (!hasMetadata) {
            db.prepare(`ALTER TABLE hubspot_imports ADD COLUMN metadata TEXT`).run();
            console.log('Added metadata column to hubspot_imports table');
          }
        } catch (err) {
          // Column might already exist
        }
      }

      return true;
    } catch (error) {
      console.error('Error ensuring hubspot_imports table exists:", error);
      return false;
    }
  }

  /**
   * Sleep for a given number of milliseconds
   */
  static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Handle HubSpot API rate limiting with exponential backoff
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }

        // Check if it's a rate limit error
        if (error && typeof error === 'object' && 'status' in error) {
          const status = (error as any).status;
          if (status === 429) {
            const delay = baseDelay * Math.pow(2, attempt);
            console.log(`Rate limited, retrying in ${delay}ms...`);
            await this.sleep(delay);
            continue;
          }
        }

        // For other errors, wait a shorter time
        await this.sleep(baseDelay);
      }
    }

    throw lastError!;
  }

  /**
   * Chunk an array into smaller arrays of specified size
   */
  static chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Sanitize HubSpot property values
   */
  static sanitizePropertyValue(value: any): string | undefined {
    if (value === null || value === undefined) {
      return undefined;
    }
    return String(value).trim();
  }

  /**
   * Parse date from HubSpot timestamp
   */
  static parseHubSpotDate(timestamp: string | undefined): Date | undefined {
    if (!timestamp) return undefined;
    try {
      const date = new Date(parseInt(timestamp));
      return isNaN(date.getTime()) ? undefined : date;
    } catch {
      return undefined;
    }
  }

  /**
   * Format currency value
   */
  static formatCurrency(value: string | undefined): number | undefined {
    if (!value) return undefined;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? undefined : parsed;
  }

  /**
   * Get import history from the database
   */
  static getImportHistory(): Array<{
    id: string;
    type: string;
    success: boolean;
    count: number;
    errors: Array<{ item: string; error: string }>;
    createdCount: number;
    updatedCount: number;
    duration: number;
    importedAt: string;
    metadata?: { companiesCount?: number; dealsCount?: number; contactsCount?: number } | null;
  }> {
    try {
      // Ensure table exists first
      this.ensureImportsTableExists();

      const stmt = db.prepare(`
        SELECT * FROM hubspot_imports 
        ORDER BY imported_at DESC 
        LIMIT 50
      `);

      const rows = stmt.all() as Array<{
        id: string;
        type: string;
        success: number;
        count: number;
        errors: string;
        created_count: number;
        updated_count: number;
        duration: number;
        imported_at: string;
        metadata: string | null;
      }>;

      return rows.map(row => {
        const metadata = row.metadata ? JSON.parse(row.metadata) : null;
        return {
          id: row.id,
          type: row.type,
          success: row.success === 1,
          count: row.count,
          errors: JSON.parse(row.errors || "[]"),
          createdCount: row.created_count,
          updatedCount: row.updated_count,
          duration: row.duration,
          importedAt: row.imported_at,
          metadata
        };
      });
    } catch (error) {
      console.error('Error getting import history:", error);
      return [];
    }
  }
}