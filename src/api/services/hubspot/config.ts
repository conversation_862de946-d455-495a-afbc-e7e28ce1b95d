import { Client } from "@hubspot/api-client";

/**
 * HubSpot configuration and client management
 */
export class HubSpotConfig {
  private client: Client | null = null;

  /**
   * Get the HubSpot access token from environment variable
   */
  getAccessToken(): string | null {
    return process.env.HUBSPOT_ACCESS_TOKEN || null;
  }

  /**
   * Save access token - deprecated, now using environment variable
   * @deprecated Use HUBSPOT_ACCESS_TOKEN environment variable instead
   */
  saveAccessToken(token: string): boolean {
    console.warn('saveAccessToken is deprecated. Please set HUBSPOT_ACCESS_TOKEN environment variable instead.');
    return false;
  }

  /**
   * Initialize and get the HubSpot client
   */
  getClient(): Client | null {
    if (this.client) {
      return this.client;
    }

    const accessToken = this.getAccessToken();
    if (!accessToken) {
      console.log('No HubSpot access token found');
      return null;
    }

    try {
      this.client = new Client({ accessToken });
      return this.client;
    } catch (error) {
      console.error('Error initializing HubSpot client:', error);
      return null;
    }
  }

  /**
   * Check if HubSpot is properly configured
   */
  isConfigured(): boolean {
    return this.getAccessToken() !== null;
  }

  /**
   * Clear configuration - deprecated, now using environment variable
   * @deprecated Use HUBSPOT_ACCESS_TOKEN environment variable instead
   */
  clearConfiguration(): boolean {
    console.warn('clearConfiguration is deprecated. Please remove HUBSPOT_ACCESS_TOKEN environment variable instead.');
    this.client = null;
    return false;
  }
}