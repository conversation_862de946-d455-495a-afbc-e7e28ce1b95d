import { Client } from "@hubspot/api-client";
import { CompanyRepository } from "../../repositories/company-repository";
import { HubSpotProgressTracker, ImportResult } from "./progress-tracker";
import { HubSpotUtils } from "./utils";

// Define types for HubSpot objects
interface HubSpotObject {
  id: string;
  properties: {
    [key: string]: string | undefined;
  };
}

/**
 * HubSpot companies import functionality
 */
export class HubSpotCompanies {
  private companyRepository: CompanyRepository;
  private progressTracker: HubSpotProgressTracker;

  constructor(progressTracker: HubSpotProgressTracker) {
    this.companyRepository = new CompanyRepository();
    this.progressTracker = progressTracker;
  }

  /**
   * Get companies from HubSpot for linking
   */
  async getCompaniesForLinking(client: Client): Promise<Array<{ 
    id: string; 
    name: string; 
    industry?: string; 
    website?: string 
  }>> {
    try {
      const properties = ['name', 'industry', 'website'];
      
      let allCompanies: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;

      // Implement pagination to fetch all companies
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await client.crm.companies.searchApi.doSearch(publicObjectSearchRequest);
        allCompanies = [...allCompanies, ...response.results];

        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      return allCompanies.map(company => ({
        id: company.id,
        name: company.properties.name || 'Unknown Company',
        industry: company.properties.industry,
        website: company.properties.website
      }));

    } catch (error) {
      console.error('Error fetching companies for linking:', error);
      throw error;
    }
  }

  /**
   * Import companies from HubSpot with progress tracking
   */
  async importCompanies(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const updates: Array<{ item: string; changes: string[] }> = [];
    const created: Array<{ item: string }> = [];

    try {
      // Get companies from HubSpot using the search API with pagination
      const properties = [
        'name',
        'industry',
        'numberofemployees',
        'website',
        'address',
        'description',
        'createdate',
        'hs_lastmodifieddate'
      ];

      let allCompanies: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;
      let totalFound = 0;

      console.log('Fetching companies from HubSpot...');

      // Implement pagination to fetch all companies
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await HubSpotUtils.withRetry(
          () => client.crm.companies.searchApi.doSearch(publicObjectSearchRequest)
        );

        allCompanies = [...allCompanies, ...response.results];
        totalFound += response.results.length;

        console.log(`Fetched page of ${response.results.length} companies. Total so far: ${totalFound}`);

        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      console.log(`Found ${allCompanies.length} companies in HubSpot`);

      // Start progress tracking
      this.progressTracker.startStep('companies', allCompanies.length);

      let importedCount = 0;

      // Process each company
      for (let i = 0; i < allCompanies.length; i++) {
        const company = allCompanies[i];
        const companyName = company.properties.name || 'Unknown Company';

        this.progressTracker.updateProgress(i + 1, `Processing company: ${companyName}`);

        try {
          const hubspotId = company.id;

          // Check if the company already exists by HubSpot ID
          let existingCompany = null;
          try {
            existingCompany = this.companyRepository.getCompanyByHubspotId(hubspotId);
          } catch (error) {
            console.log('Note: Could not check for existing company by HubSpot ID, will create new company');
          }

          if (existingCompany) {
            // Update the existing company
            console.log(`Updating existing company "${existingCompany.name}" with HubSpot ID ${hubspotId}`);

            const newData = {
              name: companyName,
              industry: HubSpotUtils.sanitizePropertyValue(company.properties.industry),
              size: HubSpotUtils.sanitizePropertyValue(company.properties.numberofemployees),
              website: HubSpotUtils.sanitizePropertyValue(company.properties.website),
              address: HubSpotUtils.sanitizePropertyValue(company.properties.address),
              description: HubSpotUtils.sanitizePropertyValue(company.properties.description)
            };

            const fieldsToTrack = ['name', 'industry', 'size', 'website', 'address', 'description'];
            const changes = HubSpotUtils.trackChanges(existingCompany, newData, fieldsToTrack);

            const updatedCompany = this.companyRepository.updateCompany(existingCompany.id, newData);

            if (updatedCompany) {
              importedCount++;
              if (changes.length > 0) {
                updates.push({
                  item: companyName,
                  changes: changes
                });
              }
            }
          } else {
            // Create new company
            const companyData = {
              name: companyName,
              industry: HubSpotUtils.sanitizePropertyValue(company.properties.industry),
              size: HubSpotUtils.sanitizePropertyValue(company.properties.numberofemployees),
              website: HubSpotUtils.sanitizePropertyValue(company.properties.website),
              address: HubSpotUtils.sanitizePropertyValue(company.properties.address),
              description: HubSpotUtils.sanitizePropertyValue(company.properties.description),
              hubspotId: hubspotId
            };

            console.log(`Creating new company "${companyData.name}" with HubSpot ID ${hubspotId}`);
            
            try {
              const createdCompany = this.companyRepository.createCompany(companyData, 'HubSpot');
              if (createdCompany) {
                importedCount++;
                created.push({ item: companyName });
              }
            } catch (error) {
              // If the hubspot_id column doesn't exist yet, try creating without it
              console.log('Error creating company with HubSpot ID, trying without HubSpot ID:', error);

              const simplifiedCompanyData = {
                name: companyName,
                industry: HubSpotUtils.sanitizePropertyValue(company.properties.industry),
                size: HubSpotUtils.sanitizePropertyValue(company.properties.numberofemployees),
                website: HubSpotUtils.sanitizePropertyValue(company.properties.website),
                address: HubSpotUtils.sanitizePropertyValue(company.properties.address),
                description: HubSpotUtils.sanitizePropertyValue(company.properties.description)
              };

              const createdCompany = this.companyRepository.createCompany(simplifiedCompanyData, 'HubSpot');
              if (createdCompany) {
                importedCount++;
                created.push({ item: companyName });
              }
            }
          }
        } catch (companyError) {
          const errorMessage = companyError instanceof Error ? companyError.message : 'Unknown error';
          console.error(`Error processing company "${companyName}":`, errorMessage);
          this.progressTracker.addError(companyName, errorMessage);
          errors.push({
            item: companyName,
            error: errorMessage
          });
        }
      }

      this.progressTracker.completeStep();

      // Record the import
      HubSpotUtils.recordImport({
        type: 'companies',
        success: true,
        count: importedCount,
        errors: errors,
        createdCount: created.length,
        updatedCount: updates.length
      });

      return this.progressTracker.createResult({
        success: true,
        count: importedCount,
        errors: errors,
        updates: updates,
        created: created
      });

    } catch (error) {
      console.error('Error importing companies from HubSpot:', error);

      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error && typeof error === 'object' && 'body' in error) {
        errorMessage = JSON.stringify((error as any).body);
      }

      HubSpotUtils.recordImport({
        type: 'companies',
        success: false,
        count: 0,
        errors: [{ item: 'Import failed', error: errorMessage }]
      });

      return this.progressTracker.createResult({
        success: false,
        count: 0,
        error: errorMessage
      });
    }
  }
}