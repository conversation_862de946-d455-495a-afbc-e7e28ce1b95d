/**
 * Database initialization service
 *
 * This file is kept for backward compatibility and redirects to the new database module.
 */

// Import the database instance and functions from the new database module
import db, { initializeDatabase as newInitializeDatabase, isDatabaseInitialized as newIsDatabaseInitialized } from "../../database";

/**
 * Initialize the complete database schema
 * @deprecated Use the new database module instead
 */
export function initializeDatabase() {
  console.log('Legacy database initialization is deprecated. Using new database module instead.');
  return newInitializeDatabase();
}

/**
 * Check if the database has been initialized with the complete schema
 * @deprecated Use the new database module instead
 */
export function isDatabaseInitialized(): boolean {
  console.log('Legacy database check is deprecated. Using new database module instead.');
  return newIsDatabaseInitialized();
}

// Export the database instance
export default db;
