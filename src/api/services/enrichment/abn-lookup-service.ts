/**
 * ABN Lookup Service
 *
 * Integrates with the Australian Business Register (ABR) API to enrich company data
 * with official Australian business information.
 *
 * API Documentation: https://abr.business.gov.au/Tools/WebServices
 */

import axios from "axios";
import { parseStringPromise } from "xml2js";

interface ABNLookupConfig {
  guid: string; // Authentication GUID from ABR registration
  maxResults?: number;
}

export interface ABNEntity {
  abn: string;
  abnStatus: string;
  entityTypeCode: string;
  entityTypeName: string;
  gstStatus?: string;
  entityName: string;
  tradingNames?: string[];
  businessLocation?: {
    stateCode: string;
    postcode: string;
  };
  asicNumber?: string;
}

export interface ABNSearchResult {
  abn: string;
  name: string;
  status: string;
  score: number; // Relevance score
}

/**
 * Service for looking up Australian Business Numbers and enriching company data
 */
export class ABNLookupService {
  private readonly baseUrl =
    "https://abr.business.gov.au/abrxmlsearch/AbrXmlSearch.asmx";
  private readonly guid: string;
  private readonly maxResults: number;

  constructor(config?: ABNLookupConfig) {
    // In production, this should come from environment variables
    this.guid = config?.guid || process.env.ABN_LOOKUP_GUID || "";
    this.maxResults = config?.maxResults || 10;

    if (!this.guid) {
      console.warn(
        "ABN Lookup GUID not configured. ABN enrichment will be disabled.",
      );
    }
  }

  /**
   * Check if the service is configured and ready to use
   */
  isConfigured(): boolean {
    return !!this.guid;
  }

  /**
   * Search for ABN by company name
   */
  async searchByName(
    companyName: string,
    postcode?: string,
  ): Promise<ABNSearchResult[]> {
    if (!this.isConfigured()) {
      throw new Error("ABN Lookup service is not configured");
    }

    try {
      // Clean the company name
      const cleanName = this.cleanCompanyName(companyName);

      // Build the search URL
      const params = new URLSearchParams({
        searchString: cleanName,
        includeHistoricalDetails: "N",
        authenticationGuid: this.guid,
        maxSearchResults: this.maxResults.toString(),
      });

      if (postcode) {
        params.append("postcode", postcode);
      }

      const url = `${this.baseUrl}/ABRXMLSearchByName?${params}`;
      const response = await axios.get(url, {
        timeout: 10000, // 10 second timeout
        headers: {
          Accept: "application/xml",
        },
      });

      // Parse XML response
      const parsed = await parseStringPromise(response.data, {
        explicitArray: false,
        ignoreAttrs: true,
      });

      const searchResponse = parsed?.ABRPayloadSearchResults?.response;

      if (!searchResponse || searchResponse.exception) {
        console.error("ABN search exception:", searchResponse?.exception);
        return [];
      }

      // Extract search results
      const searchResultsList = searchResponse.searchResultsList;
      if (!searchResultsList || !searchResultsList.searchResultsRecord) {
        return [];
      }

      // Ensure we have an array
      const records = Array.isArray(searchResultsList.searchResultsRecord)
        ? searchResultsList.searchResultsRecord
        : [searchResultsList.searchResultsRecord];

      return records
        .map((record: any) => ({
          abn: this.formatABN(record.ABN?.identifierValue || ""),
          name: this.extractBusinessName(record),
          status: record.ABN?.identifierStatus || "Unknown",
          score: parseFloat(record.relevancyScore || "0"),
        }))
        .filter((result) => result.abn && result.status === "Active");
    } catch (error) {
      console.error("Error searching ABN:", error);
      throw new Error(`Failed to search ABN: ${error.message}`);
    }
  }

  /**
   * Get detailed information for a specific ABN
   */
  async getByABN(abn: string): Promise<ABNEntity | null> {
    if (!this.isConfigured()) {
      throw new Error("ABN Lookup service is not configured");
    }

    try {
      // Clean the ABN
      const cleanABN = abn.replace(/\s/g, "");

      const params = new URLSearchParams({
        searchString: cleanABN,
        includeHistoricalDetails: "N",
        authenticationGuid: this.guid,
      });

      const url = `${this.baseUrl}/ABRXMLSearchByABN?${params}`;
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          Accept: "application/xml",
        },
      });

      // Parse XML response
      const parsed = await parseStringPromise(response.data, {
        explicitArray: false,
        ignoreAttrs: true,
      });

      const businessEntity =
        parsed?.ABRPayloadSearchResults?.response?.businessEntity;

      if (!businessEntity) {
        return null;
      }

      // Extract entity information
      const entity: ABNEntity = {
        abn: this.formatABN(businessEntity.ABN?.identifierValue || ""),
        abnStatus: businessEntity.ABN?.identifierStatus || "Unknown",
        entityTypeCode: businessEntity.entityType?.entityTypeCode || "",
        entityTypeName: businessEntity.entityType?.entityDescription || "",
        gstStatus: businessEntity.goodsAndServicesTax?.effectiveFrom
          ? "Registered"
          : "Not Registered",
        entityName: this.extractEntityName(businessEntity),
        asicNumber: businessEntity.ASICNumber || undefined,
      };

      // Extract business location
      if (businessEntity.mainBusinessPhysicalAddress) {
        const address = businessEntity.mainBusinessPhysicalAddress;
        entity.businessLocation = {
          stateCode: address.stateCode || "",
          postcode: address.postcode || "",
        };
      }

      // Extract trading names
      if (businessEntity.businessName) {
        const businessNames = Array.isArray(businessEntity.businessName)
          ? businessEntity.businessName
          : [businessEntity.businessName];

        entity.tradingNames = businessNames
          .filter((bn: any) => bn.organisationName)
          .map((bn: any) => bn.organisationName);
      }

      return entity;
    } catch (error) {
      console.error("Error getting ABN details:", error);
      return null;
    }
  }

  /**
   * Clean company name for search
   */
  private cleanCompanyName(name: string): string {
    return name
      .replace(/pty\.?\s*ltd\.?/gi, "")
      .replace(/limited/gi, "")
      .replace(/proprietary/gi, "")
      .replace(/incorporated/gi, "")
      .replace(/\([^)]*\)/g, "") // Remove parentheses content
      .replace(/[^\w\s]/g, " ") // Replace special chars with space
      .replace(/\s+/g, " ") // Multiple spaces to single
      .trim();
  }

  /**
   * Format ABN with spaces for readability
   */
  private formatABN(abn: string): string {
    const clean = abn.replace(/\s/g, "");
    if (clean.length !== 11) return clean;

    // Format as XX XXX XXX XXX
    return `${clean.substr(0, 2)} ${clean.substr(2, 3)} ${clean.substr(5, 3)} ${clean.substr(8, 3)}`;
  }

  /**
   * Extract business name from search record
   */
  private extractBusinessName(record: any): string {
    // Try main name first
    if (record.mainName?.organisationName) {
      return record.mainName.organisationName;
    }

    // Try business name
    if (record.businessName?.organisationName) {
      return record.businessName.organisationName;
    }

    // Try legal name
    if (record.legalName) {
      if (record.legalName.givenName && record.legalName.familyName) {
        return `${record.legalName.givenName} ${record.legalName.familyName}`;
      }
      if (record.legalName.fullName) {
        return record.legalName.fullName;
      }
    }

    return "Unknown";
  }

  /**
   * Extract entity name from business entity
   */
  private extractEntityName(entity: any): string {
    // Try main name
    if (entity.mainName?.organisationName) {
      return entity.mainName.organisationName;
    }

    // Try main trading name
    if (entity.mainTradingName?.organisationName) {
      return entity.mainTradingName.organisationName;
    }

    // Try legal name
    if (entity.legalName) {
      if (entity.legalName.givenName && entity.legalName.familyName) {
        return `${entity.legalName.givenName} ${entity.legalName.familyName}`;
      }
      if (entity.legalName.fullName) {
        return entity.legalName.fullName;
      }
    }

    return "Unknown";
  }
}

// Export a singleton instance
export const abnLookupService = new ABNLookupService();
