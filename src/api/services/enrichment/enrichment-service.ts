/**
 * Enrichment Service
 *
 * Orchestrates data enrichment from multiple sources
 */

import { ABNLookupService, abnLookupService } from "./abn-lookup-service";
import { EnrichmentRepository } from "../../repositories/enrichment-repository";
import { Company } from "../../../types/company-types";
import { Contact } from "../../../frontend/types/crm-types";

export type EnrichmentSource = "abn_lookup" | "clearbit" | "apollo" | "manual";

export interface EnrichmentResult {
  source: EnrichmentSource;
  success: boolean;
  data?: any;
  error?: string;
  confidence: number;
}

export interface CompanyEnrichmentData {
  // ABN specific fields
  abn?: string;
  abnStatus?: string;
  entityType?: string;
  gstStatus?: string;
  tradingNames?: string[];
  asicNumber?: string;

  // General enrichment fields
  industry?: string;
  size?: string;
  revenue?: number;
  website?: string;
  address?: string;
  phone?: string;
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };

  // Metadata
  lastVerified?: Date;
  dataQuality?: "high" | "medium" | "low";
}

/**
 * Main enrichment service that coordinates different data sources
 */
export class EnrichmentService {
  private enrichmentRepository: EnrichmentRepository;
  private abnLookupService: ABNLookupService;

  constructor() {
    this.enrichmentRepository = new EnrichmentRepository();
    this.abnLookupService = abnLookupService;
  }

  /**
   * Enrich a company with data from available sources
   */
  async enrichCompany(
    company: Company,
    sources?: EnrichmentSource[],
  ): Promise<EnrichmentResult[]> {
    const results: EnrichmentResult[] = [];
    const availableSources =
      sources || this.getAvailableSourcesForCompany(company);

    for (const source of availableSources) {
      try {
        const result = await this.enrichCompanyFromSource(company, source);
        results.push(result);

        if (result.success) {
          // Store enrichment data
          await this.enrichmentRepository.saveCompanyEnrichment({
            companyId: company.id,
            source,
            data: result.data,
            confidence: result.confidence,
            expiresAt: this.calculateExpiryDate(source),
          });
        }
      } catch (error) {
        console.error(`Error enriching company from ${source}:`, error);
        results.push({
          source,
          success: false,
          error: error.message,
          confidence: 0,
        });
      }
    }

    // Update company enrichment status
    await this.updateCompanyEnrichmentStatus(company.id, results);

    return results;
  }

  /**
   * Enrich company from a specific source
   */
  private async enrichCompanyFromSource(
    company: Company,
    source: EnrichmentSource,
  ): Promise<EnrichmentResult> {
    switch (source) {
      case "abn_lookup":
        return this.enrichFromABNLookup(company);

      case "clearbit":
        // TODO: Implement Clearbit integration
        return {
          source: "clearbit",
          success: false,
          error: "Clearbit integration not yet implemented",
          confidence: 0,
        };

      case "apollo":
        // TODO: Implement Apollo integration
        return {
          source: "apollo",
          success: false,
          error: "Apollo integration not yet implemented",
          confidence: 0,
        };

      default:
        return {
          source,
          success: false,
          error: `Unknown enrichment source: ${source}`,
          confidence: 0,
        };
    }
  }

  /**
   * Enrich company from ABN Lookup
   */
  private async enrichFromABNLookup(
    company: Company,
  ): Promise<EnrichmentResult> {
    if (!this.abnLookupService.isConfigured()) {
      return {
        source: "abn_lookup",
        success: false,
        error: "ABN Lookup service not configured",
        confidence: 0,
      };
    }

    try {
      // First try to search by name
      const searchResults = await this.abnLookupService.searchByName(
        company.name,
      );

      if (searchResults.length === 0) {
        return {
          source: "abn_lookup",
          success: false,
          error: "No matching ABN found",
          confidence: 0,
        };
      }

      // Get the best match (highest score)
      const bestMatch = searchResults[0];

      // Get detailed information
      const abnDetails = await this.abnLookupService.getByABN(bestMatch.abn);

      if (!abnDetails) {
        return {
          source: "abn_lookup",
          success: false,
          error: "Failed to get ABN details",
          confidence: 0,
        };
      }

      // Calculate confidence based on name similarity and score
      const confidence = this.calculateABNConfidence(
        company.name,
        bestMatch.name,
        bestMatch.score,
      );

      // Build enrichment data
      const enrichmentData: CompanyEnrichmentData = {
        abn: abnDetails.abn,
        abnStatus: abnDetails.abnStatus,
        entityType: abnDetails.entityTypeName,
        gstStatus: abnDetails.gstStatus,
        tradingNames: abnDetails.tradingNames,
        asicNumber: abnDetails.asicNumber,
        lastVerified: new Date(),
        dataQuality:
          confidence > 0.8 ? "high" : confidence > 0.6 ? "medium" : "low",
      };

      // Update address if we have location data
      if (abnDetails.businessLocation) {
        enrichmentData.address = `${abnDetails.businessLocation.stateCode} ${abnDetails.businessLocation.postcode}`;
      }

      return {
        source: "abn_lookup",
        success: true,
        data: enrichmentData,
        confidence,
      };
    } catch (error) {
      console.error("ABN Lookup error:", error);
      return {
        source: "abn_lookup",
        success: false,
        error: error.message,
        confidence: 0,
      };
    }
  }

  /**
   * Calculate confidence score for ABN match
   */
  private calculateABNConfidence(
    originalName: string,
    matchedName: string,
    score: number,
  ): number {
    // Normalize names for comparison
    const normalize = (name: string) =>
      name.toLowerCase().replace(/[^a-z0-9]/g, "");
    const normalized1 = normalize(originalName);
    const normalized2 = normalize(matchedName);

    // Calculate similarity
    const similarity = this.calculateSimilarity(normalized1, normalized2);

    // Combine ABN score and similarity
    return (score / 100) * 0.6 + similarity * 0.4;
  }

  /**
   * Calculate string similarity (simple Levenshtein-based)
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1.0;
    }

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1,
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Determine which sources are available for a company
   */
  private getAvailableSourcesForCompany(company: Company): EnrichmentSource[] {
    const sources: EnrichmentSource[] = [];

    // ABN Lookup for Australian companies
    if (this.isAustralianCompany(company)) {
      sources.push("abn_lookup");
    }

    // Add other sources as they become available
    // sources.push('clearbit', 'apollo');

    return sources;
  }

  /**
   * Check if company is likely Australian
   */
  private isAustralianCompany(company: Company): boolean {
    // Check website domain
    if (company.website?.includes(".au")) {
      return true;
    }

    // Check address
    if (company.address?.toLowerCase().includes("australia")) {
      return true;
    }

    // Check common Australian state abbreviations
    const ausStates = ["nsw", "vic", "qld", "wa", "sa", "tas", "act", "nt"];
    const addressLower = company.address?.toLowerCase() || "";
    if (ausStates.some((state) => addressLower.includes(state))) {
      return true;
    }

    // Default to true for now (most companies in the system are Australian)
    return true;
  }

  /**
   * Calculate when enrichment data should expire
   */
  private calculateExpiryDate(source: EnrichmentSource): Date {
    const now = new Date();

    switch (source) {
      case "abn_lookup":
        // ABN data is relatively stable, refresh every 90 days
        return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);

      case "clearbit":
      case "apollo":
        // Company data changes more frequently, refresh every 30 days
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      default:
        // Default to 30 days
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Update company enrichment status in the database
   */
  private async updateCompanyEnrichmentStatus(
    companyId: string,
    results: EnrichmentResult[],
  ): Promise<void> {
    const status: any = {
      lastEnriched: new Date().toISOString(),
      sources: {},
    };

    for (const result of results) {
      status.sources[result.source] = {
        success: result.success,
        confidence: result.confidence,
        error: result.error,
      };
    }

    await this.enrichmentRepository.updateCompanyEnrichmentStatus(
      companyId,
      status,
    );
  }

  /**
   * Get enrichment data for a company
   */
  async getCompanyEnrichment(
    companyId: string,
    source?: EnrichmentSource,
  ): Promise<any> {
    return this.enrichmentRepository.getCompanyEnrichment(companyId, source);
  }

  /**
   * Check if company needs enrichment
   */
  async companyNeedsEnrichment(company: Company): Promise<boolean> {
    // Check if we have any enrichment data
    const enrichments = await this.enrichmentRepository.getCompanyEnrichment(
      company.id,
    );
    if (!enrichments || enrichments.length === 0) {
      return true;
    }

    // Check if any enrichment has expired
    const now = new Date();
    for (const enrichment of enrichments) {
      if (enrichment.expiresAt && new Date(enrichment.expiresAt) < now) {
        return true;
      }
    }

    return false;
  }
}

// Export singleton instance
export const enrichmentService = new EnrichmentService();
