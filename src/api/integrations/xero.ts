import { XeroClient as OriginalXeroClient } from "xero-node";
import { XeroConfig } from "../types/xero";

// TypeScript interfaces for Xero API responses and data structures
interface XeroApiError extends Error {
  response?: {
    statusCode?: number;
    headers?: Record<string, string>;
    body?: unknown;
  };
  statusCode?: number;
}

interface XeroApiResponse<T = unknown> {
  body: T;
  response?: {
    statusCode: number;
    headers: Record<string, string>;
  };
}

interface XeroTokenSet {
  access_token?: string;
  refresh_token?: string;
  scope?: string;
  expires_at?: number;
}

interface XeroReportCell {
  Value?: string | number;
  Attributes?: Array<{ Value?: string; Id?: string }>;
}

interface XeroReportRow {
  RowType?: string;
  Title?: string;
  Cells?: XeroReportCell[];
  Rows?: XeroReportRow[];
}

interface XeroReport {
  ReportID?: string;
  ReportName?: string;
  ReportType?: string;
  ReportTitles?: string[];
  ReportDate?: string;
  UpdatedDateUTC?: string;
  Rows?: XeroReportRow[];
}

interface XeroReportsResponse {
  Reports?: XeroReport[];
  reports?: XeroReport[];
}

interface XeroBalanceSheetResponse {
  Reports?: XeroReport[];
  reports?: XeroReport[];
}

interface XeroBankSummaryResponse {
  Reports?: XeroReport[];
  reports?: XeroReport[];
}

interface XeroRepeatingInvoicesResponse {
  RepeatingInvoices?: unknown[];
  repeatingInvoices?: unknown[];
}

interface XeroBillsResponse {
  Invoices?: unknown[];
  invoices?: unknown[];
}

interface XeroPayRun {
  PayRunID?: string;
  PayRunPeriodStartDate?: string;
  PayRunPeriodEndDate?: string;
  PaymentDate?: string;
  PayRunStatus?: string;
  TotalCost?: number;
  PaySlips?: unknown[];
}

interface XeroPayrollResponse {
  PayRuns?: XeroPayRun[];
  payRuns?: XeroPayRun[];
}

interface XeroSuperFund {
  SuperFundID?: string;
  Name?: string;
  Type?: string;
  ABN?: string;
  EmployerContributionRate?: number;
  EmployeeCount?: number;
}

interface XeroSuperannuationResponse {
  SuperFunds?: XeroSuperFund[];
  superFunds?: XeroSuperFund[];
  superannuationPayments?: unknown[];
}

interface XeroReportListItem {
  ReportName: string;
  reportName?: string;
  reportID: string;
  ReportID?: string;
}

interface XeroReportsListResponse {
  reports: XeroReportListItem[];
  Reports?: XeroReportListItem[];
}

interface XeroPayrollApi {
  getPayRuns: (tenantId: string, createdDateFrom?: Date, createdDateTo?: Date, filter?: string, page?: number, pageSize?: number) => Promise<XeroApiResponse<XeroPayrollResponse>>;
  getSuperfunds: (tenantId: string, ifModifiedSince?: Date, where?: string, order?: string, page?: number) => Promise<XeroApiResponse<XeroSuperannuationResponse>>;
}

interface XeroAccountingApi {
  getReportBalanceSheet: (tenantId: string, date?: string, periods?: number, timeframe?: string, trackingOptionID1?: string, trackingOptionID2?: string, standardLayout?: boolean, paymentsOnly?: boolean) => Promise<XeroApiResponse<XeroBalanceSheetResponse>>;
  getReportBankSummary: (tenantId: string) => Promise<XeroApiResponse<XeroBankSummaryResponse>>;
  getRepeatingInvoices: (tenantId: string) => Promise<XeroApiResponse<XeroRepeatingInvoicesResponse>>;
  getInvoices: (tenantId: string, ifModifiedSince?: Date, where?: string, order?: string, invoiceNumbers?: string[], contactIDs?: string[], statuses?: string[], page?: number, includeArchived?: boolean) => Promise<XeroApiResponse<XeroBillsResponse>>;
  getReportsList: (tenantId: string) => Promise<XeroApiResponse<XeroReportsListResponse>>;
  getReportFromId: (tenantId: string, reportId: string) => Promise<XeroApiResponse<XeroReportsResponse>>;
  getReportProfitAndLoss: (tenantId: string, fromDate?: string, toDate?: string, periods?: number, timeframe?: string, trackingCategoryID?: string, trackingOptionID?: string, trackingCategoryID2?: string, trackingOptionID2?: string, standardLayout?: boolean, payments?: boolean) => Promise<XeroApiResponse<XeroReportsResponse>>;
}

interface ExtendedXeroClient {
  accountingApi: XeroAccountingApi;
  payrollAUApi?: XeroPayrollApi;
  payrollUKApi?: XeroPayrollApi;
  payrollNZApi?: XeroPayrollApi;
  payrollUSApi?: XeroPayrollApi;
  readTokenSet: () => XeroTokenSet | null;
}

/**
 * Extended XeroClient class with our additional functionality
 *
 * This class extends the official Xero Node SDK client with additional
 * methods specifically for financial reporting and cashflow projection.
 *
 * It adds:
 * - Robust error handling with retries for rate limiting
 * - Methods for retrieving bank balances and reports
 * - Methods for fetching and processing repeating invoices
 *
 * The client handles authentication, token refresh, and maintains Xero tenant information.
 */
export class XeroClient extends OriginalXeroClient {
  // Property to store the payroll API instance if available
  private payrollApi: XeroPayrollApi | null = null;

  /**
   * Validates that all required API methods exist in the Xero SDK
   * This helps catch API compatibility issues early
   * @private
   */
  private validateApiMethods() {
    // Check accounting API methods
    const requiredMethods = [
      'getReportBalanceSheet',
      'getReportBankSummary',
      'getRepeatingInvoices'
    ];

    if (!this.accountingApi) {
      console.error('Xero accountingApi not available!');
      return;
    }

    for (const method of requiredMethods) {
      if (typeof (this.accountingApi as unknown as Record<string, unknown>)[method] !== 'function') {
        console.warn(`Warning: Xero API method "${method}" not found - some features may not work correctly`);
      } else {
        console.log(`\u2713 Xero API method "${method}" available`);
      }
    }
  }

  constructor(config: XeroConfig) {
    // The key problem: The xero-node SDK requires a different config format
    // from what the environment variables suggest

    // Log the full config for debugging (sensitive data masked)
    console.log('Creating Xero client with config:', {
      clientId: config.clientId ? `${config.clientId.substring(0, 5)}...` : 'missing',
      clientSecret: config.clientSecret ? 'present' : 'missing',
      redirectUri: config.redirectUri,
      scopes: config.scopes
    });

    // Use the client ID as provided - we'll ensure it's properly set in the environment
    const clientId = config.clientId || '';

    // Create the SDK config with exactly the right property names and format
    super({
      clientId: clientId, // Use uppercase client ID
      clientSecret: config.clientSecret,
      redirectUris: [config.redirectUri],
      scopes: config.scopes,
      state: 'xero-state',
      httpTimeout: 30000
    });

    // Validate that required API methods exist
    this.validateApiMethods();

    // Initialize the payroll API if available in the SDK
    // Check for either payrollAUApi (Australia) or other regional payroll APIs
    const extendedClient = this as unknown as ExtendedXeroClient;
    if (extendedClient.payrollAUApi) {
      console.log('Found Australian Payroll API');
      this.payrollApi = extendedClient.payrollAUApi;
    } else if (extendedClient.payrollUKApi) {
      console.log('Found UK Payroll API');
      this.payrollApi = extendedClient.payrollUKApi;
    } else if (extendedClient.payrollNZApi) {
      console.log('Found New Zealand Payroll API');
      this.payrollApi = extendedClient.payrollNZApi;
    } else if (extendedClient.payrollUSApi) {
      console.log('Found US Payroll API');
      this.payrollApi = extendedClient.payrollUSApi;
    } else {
      console.log('No Payroll API found in this Xero integration');
      this.payrollApi = null;
    }

    // Log the actual config the SDK is using
    console.log('Xero SDK initialized with:', {
      clientId: clientId ? `${clientId.substring(0, 5)}...` : 'missing',
      redirectUri: config.redirectUri,
      scopes: config.scopes.length,
      hasPayrollApi: !!this.payrollApi
    });
  }

  /**
   * Get the Balance Sheet report from Xero with rate limiting support
   *
   * Retrieves the balance sheet report for a specific date from Xero.
   * Includes built-in retry logic for handling API rate limits.
   *
   * The balance sheet provides a snapshot of the organization's financial position
   * including assets, liabilities, and equity.
   *
   * @param tenantId Xero tenant ID
   * @param date Date for the balance sheet
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero Balance Sheet report response
   * @throws Error if the request fails after maximum retry attempts
   */
  async getBalanceSheetReport(
    tenantId: string,
    date: Date,
    periods?: number,
    timeframe?: "MONTH" | "QUARTER" | "YEAR",
    trackingOptionID1?: string,
    trackingOptionID2?: string,
    standardLayout?: boolean,
    paymentsOnly?: boolean,
    retryCount = 0
  ): Promise<XeroApiResponse<XeroBalanceSheetResponse>> {
    try {
      // Format the date for the Xero API
      const dateString = date.toISOString().split('T')[0];

      console.log(`Using enhanced getBalanceSheetReport wrapper with parameters:
        - tenantId: ${tenantId}
        - date: ${dateString}
        - periods: ${periods}
        - timeframe: ${timeframe}
        - standardLayout: ${standardLayout}
        - paymentsOnly: ${paymentsOnly}
      `);

      // Get the balance sheet report from Xero
      return await this.accountingApi.getReportBalanceSheet(
        tenantId,
        dateString,
        periods,
        timeframe,
        trackingOptionID1,
        trackingOptionID2,
        standardLayout,
        paymentsOnly
      );
    } catch (error) {
      const apiError = error as XeroApiError;
      // Check for method not found errors
      if (apiError instanceof TypeError && apiError.message.includes('not a function')) {
        console.error('getReportBalanceSheet method not found in Xero SDK');
        throw new Error('Xero API compatibility issue: Balance Sheet report method not available');
      }
      // Check for rate limiting (429) errors and handle with backoff retry
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        // Get the Retry-After header if available
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const dayLimitRemaining = apiError.response?.headers?.['x-daylimit-remaining'];
        const minLimitRemaining = apiError.response?.headers?.['x-minlimit-remaining'];

        console.log(`Rate limit info - Day limit remaining: ${dayLimitRemaining}, Minute limit remaining: ${minLimitRemaining}`);

        // Calculate backoff time based on Retry-After header or use exponential backoff
        let backoffTime = 0;

        if (retryAfter && !isNaN(parseInt(retryAfter))) {
          // Retry-After header value is in seconds, convert to milliseconds
          // Add extra seconds to be safe
          backoffTime = (parseInt(retryAfter) + 5) * 1000;
          console.log(`Rate limit hit for balance sheet. Server requested wait of ${retryAfter} seconds.`);
        } else {
          // Use exponential backoff with a larger base value
          backoffTime = Math.pow(2, retryCount + 2) * 1000; // Higher base: 4s, 8s, 16s...
        }

        console.warn(`Rate limited by Xero API. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getBalanceSheetReport(tenantId, date, retryCount + 1);
      }

      // If not a rate limit error or we've exhausted retries, log and throw
      console.error('Error getting balance sheet from Xero:', apiError);
      throw new Error('Failed to get balance sheet from Xero');
    }
  }

  /**
   * Get bank summary report which includes account balances
   *
   * Retrieves the bank summary report from Xero, which provides
   * details about all bank accounts including current balances.
   *
   * This is a critical method for cashflow projections as it establishes
   * the starting point for all future projections.
   *
   * Includes built-in retry logic for handling API rate limits.
   *
   * @param tenantId Xero tenant ID
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero Bank Summary report response
   * @throws Error if the request fails after maximum retry attempts
   */
  async getBankSummaryReport(tenantId: string, retryCount = 0): Promise<XeroApiResponse<XeroBankSummaryResponse>> {
    try {
      console.log('Getting bank summary report directly');

      // Make direct request to BankSummary endpoint without parameters
      const url = 'Reports/BankSummary';

      // Use the correct method from the SDK
      const response = await this.accountingApi.getReportBankSummary(tenantId);

      // Log the response structure to help with debugging
      console.log('Got bank summary report with response keys:', Object.keys(response.body || {}));

      return response;
    } catch (error) {
      const apiError = error as XeroApiError;
      // Check for rate limiting (429) errors and handle with backoff retry
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        // Get the Retry-After header if available
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const dayLimitRemaining = apiError.response?.headers?.['x-daylimit-remaining'];
        const minLimitRemaining = apiError.response?.headers?.['x-minlimit-remaining'];

        console.log(`Rate limit info - Day limit remaining: ${dayLimitRemaining}, Minute limit remaining: ${minLimitRemaining}`);

        // Calculate backoff time based on Retry-After header or use exponential backoff
        let backoffTime = 0;

        if (retryAfter && !isNaN(parseInt(retryAfter))) {
          backoffTime = (parseInt(retryAfter) + 5) * 1000;
          console.log(`Rate limit hit for bank summary. Server requested wait of ${retryAfter} seconds.`);
        } else {
          backoffTime = Math.pow(2, retryCount + 2) * 1000;
        }

        console.warn(`Rate limited by Xero API. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getBankSummaryReport(tenantId, retryCount + 1);
      }

      // If not a rate limit error or we've exhausted retries, log and throw
      console.error('Error getting bank summary from Xero:', apiError);
      throw new Error('Failed to get bank summary from Xero');
    }
  }

  /**
   * Get repeating invoices from Xero with rate limiting support
   *
   * Retrieves all repeating invoices from Xero, which are critical
   * for projecting recurring future expenses and income.
   *
   * This method is particularly important for forecasting regular payments
   * that are managed within Xero (e.g., rent, regular supplier payments).
   *
   * Includes built-in retry logic for handling API rate limits.
   *
   * @param tenantId Xero tenant ID
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero Repeating Invoices response
   * @throws Error if the request fails after maximum retry attempts
   */
  async getRepeatingInvoices(tenantId: string, retryCount = 0): Promise<XeroApiResponse<XeroRepeatingInvoicesResponse>> {
    try {
      return await this.accountingApi.getRepeatingInvoices(tenantId);
    } catch (error) {
      const apiError = error as XeroApiError;
      // Check for rate limiting (429) errors and handle with backoff retry
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        // Get the Retry-After header if available
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const backoffTime = retryAfter ?
          (parseInt(retryAfter) + 5) * 1000 :
          Math.pow(2, retryCount + 2) * 1000;

        console.warn(`Rate limited by Xero API when fetching repeating invoices. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getRepeatingInvoices(tenantId, retryCount + 1);
      }

      // If not a rate limit error or we've exhausted retries, log and throw
      console.error('Error getting repeating invoices from Xero:', apiError);
      throw new Error('Failed to get repeating invoices from Xero');
    }
  }

  /**
   * Get bills from Xero with rate limiting support
   *
   * Retrieves bills (ACCPAY invoices) from Xero for a given timeframe.
   * Includes built-in retry logic for handling API rate limits.
   *
   * @param tenantId Xero tenant ID
   * @param since Optional date to filter bills from
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero API response with bills
   * @throws Error if the request fails after maximum retry attempts
   */
  async getBills(tenantId: string, since?: Date, retryCount = 0): Promise<XeroApiResponse<XeroBillsResponse>> {
    try {
      // Build filter for bills (ACCPAY type invoices)
      let where = 'Type=="ACCPAY'';

      // Add date filtering if provided
      if (since) {
        const formattedDate = since.toISOString().split('T')[0];
        where += ` AND Date >= DateTime(${formattedDate})`;
      }

      // Call the Xero Accounting API's getInvoices endpoint
      return await this.accountingApi.getInvoices(
        tenantId,   // tenantId
        undefined,  // ifModifiedSince
        where,      // where clause for filtering
        'Date',     // order (by date)
        undefined,  // invoiceNumbers
        undefined,  // contactIDs
        undefined,  // statuses
        undefined,  // page
        undefined   // includeArchived
      );
    } catch (error) {
      const apiError = error as XeroApiError;
      // Check for rate limiting (429) errors and handle with backoff retry
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        // Get the Retry-After header if available
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const dayLimitRemaining = apiError.response?.headers?.['x-daylimit-remaining'];
        const minLimitRemaining = apiError.response?.headers?.['x-minlimit-remaining'];

        console.log(`Rate limit info - Day limit remaining: ${dayLimitRemaining}, Minute limit remaining: ${minLimitRemaining}`);

        // Calculate backoff time based on Retry-After header or use exponential backoff
        let backoffTime = 0;

        if (retryAfter && !isNaN(parseInt(retryAfter))) {
          backoffTime = (parseInt(retryAfter) + 5) * 1000;
          console.log(`Rate limit hit for bills. Server requested wait of ${retryAfter} seconds.`);
        } else {
          backoffTime = Math.pow(2, retryCount + 2) * 1000;
        }

        console.warn(`Rate limited by Xero API. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getBills(tenantId, since, retryCount + 1);
      }

      // If not a rate limit error or we've exhausted retries, log and throw
      console.error('Error getting bills from Xero:', apiError);
      throw new Error('Failed to get bills from Xero');
    }
  }

  /**
   * Get payroll pay runs from Xero
   * Retrieves upcoming and recent pay runs with payment information.
   *
   * @param tenantId Xero tenant ID
   * @param status Optional filter for pay run status (DRAFT, SCHEDULED, COMPLETED, POSTED)
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero API response with pay runs
   * @throws Error if the request fails after maximum retry attempts
   */
  async getPayrollPayRuns(tenantId: string, status?: string, retryCount = 0): Promise<XeroApiResponse<XeroPayrollResponse>> {
    try {
      // Check if we have the payroll API initialized
      if (!this.payrollApi) {
        console.warn('Payroll API not available in this Xero client');
        throw new Error('Xero Payroll API not available - please ensure your Xero connection has the "payroll.payruns" or "payroll.payruns.read' scope');
      }

      // Configure status filter
      let filter = '';
      if (status) {
        filter = `Status=="${status}"`;
      }

      console.log('Fetching payroll data from Xero payroll AU API...');

      // Call the Xero Payroll API's pay runs endpoint
      const response = await this.payrollApi.getPayRuns(
        tenantId,
        undefined, // createdDateFrom
        undefined, // createdDateTo
        filter ? filter : undefined, // status filter
        undefined, // page
        20 // page size (adjust as needed)
      );

      // Log the raw response structure for debugging
      console.log('Raw Xero payroll API response structure:', {
        hasBody: !!response?.body,
        responseKeys: response?.body ? Object.keys(response.body) : [],
        hasPayRuns: !!response?.body?.PayRuns,
        payRunsCount: response?.body?.PayRuns?.length || 0
      });

      // If we have a response but no PayRuns array, check for alternative field names
      if (response?.body && !response.body.PayRuns) {
        // Log all keys in the response body to help identify the correct field
        console.log('Response body keys:', Object.keys(response.body));

        // Check for common variations in field naming
        const possibleFieldNames = ['PayRuns', 'payRuns', 'payruns', 'Payruns', 'payrolls', 'Payrolls'];

        const responseBody = response.body as Record<string, unknown>;
        for (const fieldName of possibleFieldNames) {
          if (responseBody[fieldName] && Array.isArray(responseBody[fieldName])) {
            console.log(`Found payroll data in field: ${fieldName}`);
            // Create a standardized response with the correct field name
            return {
              body: {
                PayRuns: responseBody[fieldName] as XeroPayRun[]
              }
            } as XeroApiResponse<XeroPayrollResponse>;
          }
        }
      }

      // If we have a valid response with PayRuns, return it directly
      return response;
    } catch (error) {
      const apiError = error as XeroApiError;
      // Handle rate limiting similar to other methods
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        // Get the Retry-After header if available
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const backoffTime = retryAfter ?
          (parseInt(retryAfter) + 5) * 1000 :
          Math.pow(2, retryCount + 2) * 1000;

        console.warn(`Rate limited by Xero API when fetching payroll. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getPayrollPayRuns(tenantId, status, retryCount + 1);
      }

      // Log detailed error information
      console.error('Error getting payroll pay runs from Xero:', {
        message: apiError.message,
        statusCode: apiError?.response?.statusCode,
        responseBody: apiError?.response?.body,
        stack: apiError.stack
      });

      throw new Error(`Failed to get payroll pay runs from Xero: ${apiError.message || 'Unknown error'}`);
    }
  }

  /**
   * Get superannuation information from Xero
   *
   * @param tenantId Xero tenant ID
   * @param fromDate Optional start date for filtering
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero API response with superannuation data
   * @throws Error if the request fails after maximum retry attempts
   */
  async getSuperannuationPayments(tenantId: string, fromDate?: Date, retryCount = 0): Promise<XeroApiResponse<XeroSuperannuationResponse>> {
    try {
      // Check if we have the payroll API initialized
      if (!this.payrollApi) {
        console.warn('Payroll API not available in this Xero client');
        throw new Error('Payroll API not initialized in Xero client');
      }

      // Check for necessary scopes
      const tokenSet = this.readTokenSet();
      const scopes = tokenSet?.scope?.split(' ') || [];

      const hasPayrollScope = scopes.some(scope => scope.includes('payroll.'));

      if (!hasPayrollScope) {
        console.warn('Missing payroll scopes in token; Xero API access may be limited');
      }

      // When using Accounting API, use parameters that match what's authorized
      // Format date for API if provided
      const formattedFromDate = fromDate ? fromDate : undefined;

      // Define where clause for filtering
      const where = fromDate ? `PaymentDate >= DateTime(${fromDate.toISOString().split('T')[0]})` : undefined;

      // Use the proper method from the SDK for Superfunds
      console.log('Fetching superfunds from Xero API...');
      const superfundsResponse = await this.payrollApi.getSuperfunds(
        tenantId,
        undefined, // ifModifiedSince
        where,     // where clause
        'PaymentDate DESC', // order
        undefined  // page
      );

      console.log('Successfully retrieved superfunds from Xero');

      // Transform to match our expected format
      const superannuationPayments = [];

      if (superfundsResponse?.body?.SuperFunds) {
        const superfunds = superfundsResponse.body.SuperFunds;
        console.log(`Found ${superfunds.length} superfunds to process`);

        // Process each superfund to extract payment information
        superfunds.forEach(fund => {
          // Try to find a payment date, either from payments array or defaulting to now + 1 month
          const defaultPaymentDate = new Date();
          defaultPaymentDate.setMonth(defaultPaymentDate.getMonth() + 1);

          const payment = {
            superannuationPaymentID: fund.SuperFundID,
            paymentDate: defaultPaymentDate,
            amount: fund.EmployerContributionRate || 0,
            status: "SCHEDULED",
            description: `Superannuation: ${fund.Name || 'Unknown Fund'}`,
            superFund: {
              name: fund.Name || 'Unknown Fund',
              type: fund.Type || 'REGULATED',
              abn: fund.ABN || ''
            },
            employeeCount: fund.EmployeeCount || 1,
            isAlreadyAdded: false
          };

          superannuationPayments.push(payment);
        });
      } else {
        console.log('No superfunds found in the Xero API response');
      }

      return {
        body: {
          superannuationPayments
        }
      };
    } catch (error) {
      const apiError = error as XeroApiError;
      // Handle rate limiting similar to other methods
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const backoffTime = retryAfter ?
          (parseInt(retryAfter) + 5) * 1000 :
          Math.pow(2, retryCount + 2) * 1000;

        console.warn(`Rate limited by Xero API when fetching superannuation. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getSuperannuationPayments(tenantId, fromDate, retryCount + 1);
      }

      console.error('Error getting superannuation information from Xero:', apiError);
      throw new Error(`Failed to get superannuation information from Xero: ${apiError.message || 'Unknown error'}`);
    }
  }

  /**
   * Get activity statements information from Xero
   * This includes BAS (Business Activity Statement) and IAS (Installment Activity Statement)
   *
   * @param tenantId Xero tenant ID
   * @param fromDate Optional start date for filtering
   * @param reportType Optional report type ('BAS' or 'GST')
   * @param retryCount Number of retries attempted (internal use)
   * @returns Xero API response with activity statements data
   * @throws Error if the request fails after maximum retry attempts
   */
  async getActivityStatements(tenantId: string, fromDate?: Date, reportType: string = 'BAS', retryCount = 0): Promise<XeroApiResponse<XeroReportsResponse>> {
    const BAS_REPORT_NAMES = ['BAS', 'Business Activity Statement', 'GST Return', 'GST Summary', 'VAT Return'];

    try {
      // First, get a list of all available reports
      console.log('Fetching list of available reports from Xero...');
      const reportsListResponse = await this.accountingApi.getReportsList(tenantId);

      if (!reportsListResponse?.body?.reports) {
        console.error('Failed to retrieve reports list from Xero');
        throw new Error('No reports available from Xero API');
      }

      // Find reports that might be related to BAS or GST
      const reports = reportsListResponse.body.reports;
      console.log(`Found ${reports.length} reports available in Xero`);

      // Log available report names for debugging
      const reportNames = reports.map((r: XeroReportListItem) => r.ReportName);
      console.log('Available reports:', reportNames.join(', '));

      // Find a report that looks like a BAS or GST report
      const taxReport = reports.find((report: XeroReportListItem) => {
        const name = report.ReportName;
        return BAS_REPORT_NAMES.some(pattern =>
          name.toUpperCase().includes(pattern.toUpperCase()));
      });

      // If we found a matching tax report, fetch its details
      if (taxReport) {
        console.log(`Found a tax report: ${taxReport.reportName} (ID: ${taxReport.reportID})`);

        // Get the specific report using its ID
        const reportResponse = await this.accountingApi.getReportFromId(
          tenantId,
          taxReport.reportID
        );

        console.log(`Successfully retrieved tax report: ${taxReport.reportName}`);
        return reportResponse;
      }

      // If no suitable tax report was found, try using a generic profit & loss report
      // This is not ideal but will give us some financial data
      console.log('No specific tax reports found. Trying to fetch a Profit & Loss report');

      // Format date for API if provided, otherwise use current date
      const dateString = fromDate ?
        fromDate.toISOString().split('T')[0] :
        new Date().toISOString().split('T')[0];

      // Create a 3-month period starting from the date
      const fromDateStr = dateString;
      const toDate = new Date(fromDate || new Date());
      toDate.setMonth(toDate.getMonth() + 3);
      const toDateStr = toDate.toISOString().split('T')[0];

      // Get a profit and loss report which includes GST/VAT info in many cases
      const plReport = await this.accountingApi.getReportProfitAndLoss(
        tenantId,
        fromDateStr,
        toDateStr,
        undefined, // periods
        undefined, // timeframe
        undefined, // trackingCategoryID
        undefined, // trackingOptionID
        undefined, // trackingCategoryID2
        undefined, // trackingOptionID2
        undefined, // standardLayout
        undefined  // payments
      );

      // If we have tax lines in the profit & loss, we can extract tax data
      return this.convertProfitLossToTaxReport(plReport, reportType);
    } catch (error) {
      const apiError = error as XeroApiError;
      // Handle rate limiting similar to other methods
      if (apiError?.response?.statusCode === 429 && retryCount < 3) {
        const retryAfter = apiError.response?.headers?.['retry-after'];
        const backoffTime = retryAfter ?
          (parseInt(retryAfter) + 5) * 1000 :
          Math.pow(2, retryCount + 2) * 1000;

        console.warn(`Rate limited by Xero API when fetching activity statements. Retrying in ${Math.round(backoffTime/1000)} seconds...`);

        // Wait for the backoff period
        await new Promise(resolve => setTimeout(resolve, backoffTime));

        // Retry with incremented count
        return this.getActivityStatements(tenantId, fromDate, reportType, retryCount + 1);
      }

      // For authentication errors, check scopes
      if (apiError?.response?.statusCode === 401 || apiError?.response?.statusCode === 403) {
        const tokenSet = (this as unknown as ExtendedXeroClient).readTokenSet();
        const scopes = tokenSet?.scope?.split(' ') || [];

        const hasReportScope = scopes.some(scope =>
          scope.includes('accounting.reports') ||
          scope.includes('reports')
        );

        if (!hasReportScope) {
          console.error('Missing accounting.reports scope for accessing tax reports');
        }
      }

      console.error('Error getting activity statements from Xero:', apiError);
      throw new Error(`Failed to get activity statements from Xero: ${apiError.message || 'Unknown error'}`);
    }
  }

  /**
   * Convert a profit and loss report to a tax-oriented report format
   * @param plReport The profit and loss report response from Xero
   * @param reportType The type of tax report ('BAS' or 'GST')
   * @returns A formatted response that matches the expected structure for tax reports
   */
  private convertProfitLossToTaxReport(plReport: XeroApiResponse<XeroReportsResponse>, reportType: string): XeroApiResponse<XeroReportsResponse> {
    // Check if we have any data to work with
    if (!plReport?.body?.Reports || plReport.body.Reports.length === 0) {
      throw new Error('No report data available to convert');
    }

    const report = plReport.body.Reports[0];
    const reportDate = new Date();

    // Extract date from report title if possible
    const title = report.ReportTitles && report.ReportTitles.length > 0 ?
      report.ReportTitles[0] : `${reportType} Report`;

    // Look for tax rows in the profit & loss
    let taxAmount = 0;
    let taxRows = [];

    if (report.Rows) {
      // Recursively search for tax-related rows
      const findTaxRows = (rows: XeroReportRow[]) => {
        rows.forEach(row => {
          // Look for GST, BAS, VAT or Tax related rows
          if (row.Title && (
            row.Title.includes('GST') ||
            row.Title.includes('VAT') ||
            row.Title.includes('Tax') ||
            row.Title.includes('BAS')
          )) {
            taxRows.push(row);

            // Try to extract amount if available
            if (row.Cells && row.Cells.length > 0) {
              const lastCell = row.Cells[row.Cells.length - 1];
              if (lastCell.Value && !isNaN(parseFloat(lastCell.Value))) {
                taxAmount += parseFloat(lastCell.Value);
              }
            }
          }

          // Recurse into sections
          if (row.Rows && row.Rows.length > 0) {
            findTaxRows(row.Rows);
          }
        });
      };

      findTaxRows(report.Rows);
    }

    // If we found at least one tax-related row, create a proper tax report structure
    if (taxRows.length > 0) {
      return {
        body: {
          Reports: [
            {
              ReportID: `synthetic-${reportType.toLowerCase()}-report`,
              ReportName: `${reportType} Report`,
              ReportType: reportType,
              ReportTitles: [title, 'Tax Report (extracted from financial data)'],
              ReportDate: reportDate.toISOString(),
              UpdatedDateUTC: reportDate.toISOString(),
              Rows: [
                ...taxRows,
                {
                  Title: 'Total Activity Statement',
                  Cells: [
                    { Value: 'Total' },
                    { Value: taxAmount.toFixed(2) }
                  ]
                }
              ]
            }
          ]
        }
      };
    } else {
      // If we couldn't find tax data, throw an error
      throw new Error('Could not locate tax information in financial reports');
    }
  }
}

/**
 * Create a Xero client instance
 */
export const createXeroClient = (config: XeroConfig): XeroClient => {
  return new XeroClient(config);
};