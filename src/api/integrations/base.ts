import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from "axios";
import { ApiClientConfig, RequestOptions, ApiResponse } from "../types/api";
import { ApiError, withErrorHandling, delay } from "../../utils/error";

/**
 * Base API client that implements standardized error handling, rate limiting, and retries
 *
 * All API-specific clients should extend this base class to ensure consistent:
 * - Error handling with proper error types
 * - Rate limiting with configurable throttling
 * - Retries with exponential backoff
 * - Timeout handling
 */
export class BaseApiClient {
  protected readonly axios: AxiosInstance;
  private readonly rateLimit: ApiClientConfig["rateLimit"];
  private readonly retryAttempts: number;
  private requestCount = 0;
  private lastRequestTime = 0;

  /**
   * Constructor
   * @param config API client configuration
   */
  constructor(config: ApiClientConfig) {
    this.axios = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout || 10000,
    });

    this.rateLimit = config.rateLimit;
    this.retryAttempts = config.retryAttempts || 3;

    // Add request interceptor for rate limiting
    this.axios.interceptors.request.use(async (config) => {
      if (this.rateLimit) {
        await this.handleRateLimit();
      }
      return config;
    });
  }

  /**
   * Handle rate limiting according to configured limits
   * @private
   */
  private async handleRateLimit(): Promise<void> {
    if (!this.rateLimit) return;

    const now = Date.now();
    const timeWindow = now - this.lastRequestTime;

    if (timeWindow < this.rateLimit.windowMs) {
      this.requestCount++;
      if (this.requestCount >= this.rateLimit.maxRequests) {
        const delayTime = this.rateLimit.retryAfterMs;
        await delay(delayTime);
        this.requestCount = 0;
        this.lastRequestTime = Date.now();
      }
    } else {
      this.requestCount = 1;
      this.lastRequestTime = now;
    }
  }

  /**
   * Make an API request with standardized error handling
   *
   * @param endpoint API endpoint to request
   * @param options Request options
   * @returns API response with data and metadata
   * @throws ApiError on request failure
   */
  protected async request<T>(
    endpoint: string,
    options: RequestOptions = {},
  ): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      method: options.method || "GET",
      url: endpoint,
      params: options.params,
      headers: options.headers,
      data: options.data,
    };

    const operationName = `${options.method || "GET"} ${endpoint}`;

    return withErrorHandling(
      async () => {
        const response = await this.axios.request<T>(config);
        return {
          data: response.data,
          meta: {
            page: Number(response.headers["x-page"]),
            perPage: Number(response.headers["x-per-page"]),
            total: Number(response.headers["x-total"]),
          },
        };
      },
      {
        operationName,
        maxRetries: this.retryAttempts,
        retryableErrors: (error) => {
          // Don't retry on client errors (except 429 Too Many Requests)
          if (error instanceof AxiosError && error.response) {
            const status = error.response.status;
            return status >= 500 || status === 429;
          }
          // Retry on network errors
          return true;
        },
        onError: (error) => {
          // Convert Axios errors to our ApiError format
          if (error instanceof AxiosError) {
            throw this.convertAxiosError(error);
          }
        },
      },
    );
  }

  /**
   * Convert Axios error to our standardized ApiError format
   *
   * @param error Axios error
   * @returns ApiError with standardized format
   * @private
   */
  private convertAxiosError(error: AxiosError): ApiError {
    const context: Record<string, unknown> = {
      url: error.config?.url,
      method: error.config?.method,
    };

    // Extract information from the response if available
    if (error.response) {
      return new ApiError(
        error.message,
        error.response.status,
        error.code || "unknown",
        error.response.data,
        context,
      );
    }

    // For network errors without a response
    return new ApiError(
      error.message,
      undefined,
      error.code || "network_error",
      undefined,
      context,
    );
  }
}
