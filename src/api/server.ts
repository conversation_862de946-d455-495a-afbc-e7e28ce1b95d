// @ts-nocheck
// Load environment variables first
import dotenv from "dotenv";
dotenv.config();

// Safety check for production environment
if (process.env.NODE_ENV === 'production') {
  const dangerousFlags = ['USE_MOCK_XERO', 'USE_MOCK_HARVEST', 'MOCK_AUTH_BYPASS', 'USE_MOCK_AUTH'];
  const foundFlags = dangerousFlags.filter(flag => process.env[flag] === 'true');
  
  if (foundFlags.length > 0) {
    console.error(`FATAL ERROR: Mock flags detected in production environment: ${foundFlags.join(', ')}`);
    console.error('This is a critical security issue. Shutting down immediately.');
    process.exit(1);
  }
}

// Development mode warnings
if (process.env.USE_MOCK_AUTH === 'true' && process.env.NODE_ENV !== 'production') {
  console.warn('⚠️  WARNING: Mock authentication is enabled (USE_MOCK_AUTH=true)');
  console.warn('⚠️  This bypasses all real authentication - use for development only!');
}

import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import session from "express-session";
import { RedisStore } from 'connect-redis'; // Use named import for RedisStore
import { createClient } from 'redis';    // Import the redis client
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { 
  generalLimiter, 
  authLimiter, 
  externalApiLimiter, 
  dataIntensiveLimiter,
  estimatesLimiter,
  bodySizeLimit 
} from "./middleware/security";
import { mockAuthMiddleware } from "./middleware/mock-auth";
import xeroRoutes from "./routes/xero";
import cashflowRoutes from "./routes/cashflow";
import expensesRoutes from "./routes/expenses";
import harvestRoutes from "./routes/harvest";
import harvestCacheRoutes from "./routes/harvest-cache";
import estimatesRoutes from "./routes/estimates";
import crmRoutes from "./routes/crm";
import crmEnhancedRoutes from "./routes/crm-enhanced";
import hubspotRoutes from "./routes/hubspot";
import feedbackRoutes from "./routes/feedback";
import leadsRoutes from "./routes/leads";
import mcpRoutes from "./routes/mcp";
import hubspotMcpRoutes from "./routes/hubspot-mcp";
import activityRoutes from "./routes/activity";
import knowledgeGraphRoutes from "./routes/knowledge-graph";
import enrichmentRoutes from "./routes/enrichment";
import tenderRoutes from "./routes/tenders";
import radarActionsRoutes from "./routes/radar-actions";
import featureFlagsRoutes from "./routes/feature-flags";
// import { setupCompanyRelationshipRoutes } from './routes/company-relationships'; // Route removed - table no longer exists
import { setupContactCompanyRoutes } from "./routes/contact-companies";
import { setupDealContactRoutes } from "./routes/deal-contacts";

// Define a custom error type for the application
interface AppError extends Error {
  type?: string;
  status?: number;
}

// Create Express app
export const app = express();

// Create HTTP server
const httpServer = createServer(app);

// Create Socket.IO server
export const io = new SocketIOServer(httpServer, {
  cors: {
    origin: function(origin, callback) {
      // Allow any origin that includes 'onbord', 'render', or localhost
      if (!origin ||
          origin.includes('onbord') ||
          origin.includes('localhost') ||
          origin.includes('render.com')) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST']
  }
});

// Trust proxy - critical for Render.com to correctly detect HTTPS
// Configure trust proxy based on environment for security
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', 1); // Trust first proxy only (Render.com)
  console.log("Proxy trust enabled for production (first proxy only)");
} else {
  app.set('trust proxy', false); // Disable in development for rate limiting security
  console.log("Proxy trust disabled for development (rate limiting security)");
}

// Middleware
app.use(cors({
  origin: function(origin, callback) {
    // Allow any origin that includes 'onbord', 'render', or localhost
    // This ensures production, preview deployments, and local development work
    if (!origin ||
        origin.includes('onbord') ||
        origin.includes('localhost') ||
        origin.includes('render.com')) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Add cookie parser middleware
import cookieParser from "cookie-parser";
app.use(cookieParser());

// Apply body size limits BEFORE parsing
app.use(express.json({ limit: bodySizeLimit.json }));
app.use(express.urlencoded({ extended: true, limit: bodySizeLimit.urlencoded }));
app.use(express.raw({ limit: bodySizeLimit.raw }));
app.use(express.text({ limit: bodySizeLimit.text }));

// Apply general rate limiting to all routes
app.use(generalLimiter);


// Session configuration for storing Xero tokens
const isLocalDevelopment = process.env.NODE_ENV === 'development' &&
                          (process.env.API_PORT === '3002' || process.env.API_PORT === '3001');

// Check if this is a preview deployment based on hostname
const isPreviewDeployment = process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview') || false;

// Get the domain for cookie configuration - important for preview deployments
// IMPORTANT: For preview environment, we're not setting a domain explicitly
// This allows the browser to use the current domain (upstream-preview.onbord.au)
// instead of the Render hostname (upstream-preview.onrender.com)
const cookieDomain = undefined;

console.log('Cookie domain configured as: ', cookieDomain || "default (current domain)");

// Log environment for debugging
console.log(`Session config: NODE_ENV=${process.env.NODE_ENV}, API_PORT=${process.env.API_PORT}, isLocalDevelopment=${isLocalDevelopment}, isPreviewDeployment=${isPreviewDeployment}`);

// Define the cookie options with proper TypeScript types
const sessionCookieOptions: {
  secure: boolean;
  maxAge: number;
  httpOnly: boolean;
  sameSite: "lax" | "none" | "strict" | boolean;
  domain: string | undefined;
  path?: string;
} = {
  // Only require secure cookies in production
  secure: isLocalDevelopment ? false : true,
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  httpOnly: true,
  // Use sameSite=none for all non-local environments to avoid cross-domain issues
  // This is especially important for preview deployments
  sameSite: isLocalDevelopment ? 'lax' : "none",
  // Set cookie domain based on environment
  domain: cookieDomain,
  // Ensure cookie is available for all paths
  path: "/"
};

// Enhanced logging for debugging session configuration
console.log('Using session configuration with cookie settings:', {
  secure: sessionCookieOptions.secure,
  maxAge: sessionCookieOptions.maxAge,
  httpOnly: sessionCookieOptions.httpOnly,
  sameSite: sessionCookieOptions.sameSite,
  domain: sessionCookieOptions.domain || "(default)",
  path: sessionCookieOptions.path || "(default)"
});

// Always use the proper domain for cookies in production
if (process.env.NODE_ENV === 'production') {
  console.log('Production environment detected for cookies');

  if (process.env.RENDER_EXTERNAL_HOSTNAME) {
    console.log(`Using Render hostname for cookies: ${process.env.RENDER_EXTERNAL_HOSTNAME}`);

    // Force SameSite=None for all non-local deployments to improve cross-domain compatibility
    sessionCookieOptions.sameSite = 'none';

    // Ensure secure is always true for production
    sessionCookieOptions.secure = true;

    // IMPORTANT: Do NOT set domain explicitly for preview environment
    // This allows the browser to use the current domain (upstream-preview.onbord.au)
    // instead of the Render hostname (upstream-preview.onrender.com)
    if (isPreviewDeployment) {
      console.log('Preview deployment detected, using special cookie settings');
      console.log('Cookie domain: (default - using browser default)');
      console.log('Cookie sameSite:', sessionCookieOptions.sameSite);
      console.log('Cookie secure:', sessionCookieOptions.secure);

      // Ensure domain is undefined to let browser use current domain
      sessionCookieOptions.domain = undefined;
    }
  }
}

// Initialize Redis Client
const redisClient = createClient({
  url: process.env.REDIS_URL // Get URL from environment variable set on Render
});
redisClient.connect().catch(console.error); // Connect to Redis

redisClient.on('error', (err) => console.error('Redis Client Error:', err));
redisClient.on('connect', () => console.log('Connected to Redis successfully.'));
redisClient.on('reconnecting', () => console.log('Reconnecting to Redis...'));

// Initialize Redis Store
// Create the store instance directly
const redisStore = new RedisStore({
  client: redisClient,
  prefix: "sess:", // Optional prefix for session keys in Redis
  ttl: 7 * 24 * 60 * 60 // Session TTL in seconds (matches cookie maxAge)
});


// Middleware to log incoming request cookies BEFORE session middleware
app.use((req, res, next) => {
  console.log(`[DEBUG PRE-SESSION] Request to ${req.path}. Cookies:`, req.headers.cookie || "None");
  next();
});

// Use Redis store for sessions
app.use(session({
  store: redisStore, // Use RedisStore instead of MemoryStore
  secret: process.env.SESSION_SECRET || "onbord-financial-analysis-secret",
  resave: false, // Recommended setting for RedisStore
  saveUninitialized: false, // Recommended setting for RedisStore
  cookie: sessionCookieOptions,
  // Set a unique session name to avoid conflicts
  // IMPORTANT: Use the same name for both preview and production to avoid issues
  // when switching between environments
  name: "onbord.sid"
}));

// Middleware to log session state AFTER session middleware
app.use((req, res, next) => {
  console.log(`[DEBUG POST-SESSION] Request to ${req.path}. SessionID: ${req.sessionID}. Has tokenSet: ${!!req.session?.tokenSet}`);
  next();
});

// Apply mock auth middleware if enabled (must be after session middleware)
if (process.env.USE_MOCK_AUTH === 'true' && process.env.NODE_ENV !== 'production') {
  console.log('[MOCK AUTH] Applying mock authentication middleware');
  app.use(mockAuthMiddleware);
}


// Log session middleware configuration
console.log('Session middleware configured with RedisStore:', {
  resave: false, // Updated
  saveUninitialized: false, // Updated
  cookieSecure: isLocalDevelopment ? false : true,
  cookieMaxAge: "7 days",
  sameSite: isLocalDevelopment ? 'lax' : "none",
  domain: cookieDomain || "default (browser determined)"
});

// Log preview deployment status
if (isPreviewDeployment) {
  console.log('PREVIEW DEPLOYMENT DETECTED');
  // Removed preview-specific debugging middleware
}

// Initialize services
import { getXeroService } from "../services/xero";
import { getHarvestService } from "../services/harvest";
import { initializeCashflowSnapshotJob } from "../services/cashflow";
import { initializeHubSpotSyncJob } from "../services/hubspot/job-initializer";
// Import database initialization
import { initializeDatabase } from "../database";
// Import HubSpot service
import { hubspotService } from "./services/hubspot-service";
// Import Activity service
import { getActivityService } from "./services/activity-service";

// Initialize services to ensure they're configured with environment variables
getXeroService();
getHarvestService();

// Connect HubSpot service to Socket.IO
hubspotService.setSocketIO(io);

// Connect Activity service to Socket.IO
const activityService = getActivityService(io);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected to Socket.IO:', socket.id);

  socket.on('disconnect', () => {
    console.log('Client disconnected from Socket.IO:', socket.id);
  });

  socket.on('error', (error) => {
    console.error('Socket.IO error:', error);
  });
});

// Initialize the database
console.log('Initializing database...');
let db;
try {
  db = initializeDatabase();
  console.log('Database initialization complete');

  // Set up relationship routes
  // setupCompanyRelationshipRoutes(app, db); // Route removed - table no longer exists
  setupContactCompanyRoutes(app, db);
  setupDealContactRoutes(app, db);

  // Test activity logging after database initialization
  try {
    console.log('Testing activity logging system...');
    activityService.logSystemEvent('system_started', 'Application server started', {
      description: "The Onbord Financial Dashboard server has started successfully",
      metadata: { port: process.env.API_PORT || 3002, timestamp: new Date().toISOString() }
    }).then(() => {
      console.log('✓ Activity logging test successful');
    }).catch((activityError) => {
      console.error('✗ Activity logging test failed:', activityError);
      console.error('Activity error stack:', activityError.stack);
    });
  } catch (testError) {
    console.error('Error testing activity logging:', testError);
  }
} catch (err) {
  console.error('Error initializing database:', err);
}

// Initialize scheduled jobs
if (process.env.NODE_ENV !== 'test') {
  console.log('Initializing scheduled jobs...');
  initializeCashflowSnapshotJob();
  initializeHubSpotSyncJob();
}

// Log environment configuration
console.log('Environment configuration:');
console.log(`- Xero integration: ${process.env.XERO_CLIENT_ID ? 'Configured' : "Not configured"}`);
console.log(`- Harvest integration: ${process.env.HARVEST_ACCESS_TOKEN ? 'Configured' : "Not configured"}`);

// Authentication middleware
const ensureAuthenticated = (req: Request, res: Response, next: NextFunction) => {
  // For preview deployments, add detailed debugging
  const isPreviewDeployment = process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview') || false;

  // Log detailed request information for debugging
  console.log(`[AUTH] Request to ${req.path}. SessionID: ${req.sessionID}. Has tokenSet: ${!!req.session?.tokenSet}`);
  console.log(`[AUTH] Request cookies:`, req.headers.cookie || "None");

  // Skip auth for xero routes (auth, callback, etc.) and health check
  if (req.path.startsWith('/xero/auth') ||
      req.path.startsWith('/xero/callback') ||
      req.path.startsWith('/xero/auth-status') ||
      req.path === '/health') {
    console.log(`[AUTH] Skipping auth check for ${req.path}`);
    return next();
  }

  // Standard session check - used for both preview and production
  if (req.session?.tokenSet) {
    // User has a tokenSet in their session - they're authenticated
    console.log(`[AUTH] User authenticated for ${req.path}. Session ID: ${req.sessionID}`);
    return next();
  }

  // Log authentication failure details
  console.log(`[AUTH] Authentication failed for ${req.path}. Session ID: ${req.sessionID}`);
  console.log(`[AUTH] Session exists: ${!!req.session}`);
  console.log(`[AUTH] Cookie header: ${req.headers.cookie ? 'Present' : "Missing"}`);

  // Authentication failed
  const redirectUrl = `/api/xero/auth`;

  // For preview, include timestamp to avoid caching issues
  const finalRedirectUrl = isPreviewDeployment ?
    `${redirectUrl}?_t=${Date.now()}` : redirectUrl;

  return res.status(401).json({
    error: "Authentication required",
    message: "You must be authenticated to access this resource",
    redirectUrl: finalRedirectUrl,
    timestamp: new Date().toISOString(),
    // Add debugging information
    debug: {
      sessionId: req.sessionID,
      hasCookieHeader: !!req.headers.cookie,
      path: req.path,
      isPreview: isPreviewDeployment
    }
  });
};

// API Routes
const router = express.Router();

// Apply auth middleware to all API routes
router.use(ensureAuthenticated);

// Apply specific rate limiters to different route groups
// Auth endpoints get stricter limits
router.use('/xero/auth', authLimiter);
router.use('/xero/callback', authLimiter);

// External API endpoints get their own limits to prevent hitting external rate limits
router.use('/xero', externalApiLimiter, xeroRoutes);
router.use('/harvest', externalApiLimiter, harvestRoutes);
router.use('/hubspot', externalApiLimiter, hubspotRoutes);

// Data-intensive endpoints get lower limits
router.use('/cashflow', dataIntensiveLimiter, cashflowRoutes);
router.use('/expenses', dataIntensiveLimiter, expensesRoutes);
router.use('/harvest-cache', dataIntensiveLimiter, harvestCacheRoutes);
// Estimates get their own limiter for better UX when saving drafts
router.use('/estimates', estimatesLimiter, estimatesRoutes);

// CRM endpoints get more generous limits since we've implemented batching
router.use('/crm', externalApiLimiter, crmRoutes);
router.use('/crm-enhanced', crmEnhancedRoutes);
router.use('/feedback', feedbackRoutes);
router.use('/leads', leadsRoutes);
router.use('/mcp', mcpRoutes);
router.use('/hubspot-mcp', hubspotMcpRoutes);
router.use('/activity', activityRoutes);
router.use('/knowledge-graph', knowledgeGraphRoutes);
router.use('/enrichment', enrichmentRoutes);
router.use('/tenders', tenderRoutes);
router.use('/radar-actions', radarActionsRoutes);
router.use('/feature-flags', featureFlagsRoutes);


/**
 * GET /api/health
 * Health check endpoint
 */
router.get('/health', (req, res) => {
  res.json({ status: "ok" });
});

/**
 * POST /api/reset-rate-limits
 * Reset rate limits (development only)
 */
if (process.env.NODE_ENV === 'development') {
  router.post('/reset-rate-limits', (req, res) => {
    // In development, we can reset rate limits by clearing the store
    // Note: This is a simplified approach for development
    console.log('Rate limits reset requested (development mode)');
    res.json({ 
      success: true, 
      message: "Rate limits reset. Note: This only works with in-memory rate limit stores." 
    });
  });
}

// Register the API routes
app.use('/api', router);

// Serve static files from the frontend build directory in production
if (process.env.NODE_ENV === 'production') {
  const path = require('path');

  // Use RENDER environment variable to detect if we're in Render deployment
  const isRenderDeployment = process.env.RENDER === 'true';

  if (isRenderDeployment) {
    // In Render deployment, frontend files are in dist directory
    const staticPath = path.resolve('dist');
    app.use(express.static(staticPath, {
      setHeaders: (res, path) => {
        if (path.endsWith('.js')) {
          res.setHeader('Content-Type', 'application/javascript');
        }
      }
    }));
    console.log(`Serving frontend static files from: ${staticPath} (Render deployment)`);

    // Handle all routes for React Router
    app.get('*', (req, res, next) => {
      // Skip API routes
      if (req.path.startsWith('/api')) {
        return next();
      }
      // Send the index.html file using absolute path
      const indexPath = path.join(staticPath, 'index.html');
      console.log(`Serving index.html from: ${indexPath}`);
      res.sendFile(indexPath);
    });
  } else {
    // Local production build: files are in dist directory
    const staticPath = path.resolve('dist');
    app.use(express.static(staticPath));
    console.log(`Serving frontend static files from: ${staticPath} (local build)`);

    // Handle all routes for React Router
    app.get('*', (req, res, next) => {
      // Skip API routes
      if (req.path.startsWith('/api')) {
        return next();
      }
      // Send the index.html file using absolute path
      const indexPath = path.join(staticPath, 'index.html');
      res.sendFile(indexPath);
    });
  }
}

// Root path handler - serve index.html in production, redirect to dev server in development
// (Only used in development - production uses the catch-all route above)
app.get('/', (req, res) => {
  if (process.env.NODE_ENV !== 'production') {
    res.redirect('http://localhost:5173');
  }
});

// Error handling middleware
app.use((err: AppError, req: Request, res: Response, next: NextFunction) => {
  console.error('Error:', err);

  let statusCode = 500;
  let errorMessage = 'Internal server error';
  let errorType = err.type || "UNKNOWN_ERROR";

  // Check for rate limit errors in the error stack
  const isRateLimitError = err.message?.includes('rate limit') ||
                           err.message?.includes('429') ||
                           err.stack?.includes('rate limit');

  // Check for timeout errors
  const isTimeoutError = err.message?.includes('timeout') ||
                         err.message?.includes('timed out') ||
                         err.stack?.includes('timeout');

  // Check for network errors
  const isNetworkError = err.message?.includes('network') ||
                         err.message?.includes('ECONNREFUSED') ||
                         err.message?.includes('ENOTFOUND');

  if (isRateLimitError) {
    statusCode = 429;
    errorMessage = 'API rate limit exceeded. Please try again later.';
    errorType = 'RATE_LIMIT_EXCEEDED';
  } else if (isTimeoutError) {
    statusCode = 504;
    errorMessage = 'Request timed out. Please try again with a shorter date range.';
    errorType = 'REQUEST_TIMEOUT';
  } else if (isNetworkError) {
    statusCode = 503;
    errorMessage = 'Network error connecting to external service. Please try again later.';
    errorType = 'NETWORK_ERROR';
  } else if (err.type) {
    // Handle application-specific error types
    if (err.type.includes('INVALID_DATE_RANGE') || err.type.includes('DATA_INCONSISTENCY')) {
      statusCode = 400;
      errorMessage = err.message;
    } else if (err.type.includes('PROJECTION_ERROR')) {
      statusCode = 422;
      errorMessage = err.message;
    } else if (err.type.includes('SYNC_FAILED')) {
      statusCode = 502;
      errorMessage = err.message;
    } else {
      statusCode = 500;
      errorMessage = err.message;
    }
  } else if (err.status) {
    statusCode = err.status;
    errorMessage = err.message;
  }

  // Add a user-friendly message for 500 errors
  if (statusCode === 500) {
    errorMessage = 'An unexpected error occurred. Our team has been notified.';
  }

  res.status(statusCode).json({
    error: errorMessage,
    type: errorType,
    timestamp: new Date().toISOString(),
    success: false
  });

  // Log additional details for server errors
  if (statusCode >= 500) {
    console.error('Server error details:', {
      url: req.originalUrl,
      method: req.method,
      statusCode,
      errorType,
      errorMessage: err.message,
      stack: err.stack
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: "Endpoint not found" });
});

// Export the httpServer for use in production builds
export { httpServer };

// Start server if not in test mode
if (process.env.NODE_ENV !== 'test') {
  const PORT = process.env.API_PORT || 3002;

  // First check if the port is in use
  const server = httpServer.listen(PORT, () => {
    console.log(`API server running on port ${PORT}`);
    console.log(`Socket.IO server running on port ${PORT}`);
    console.log(`Xero callback URL configured as: ${process.env.XERO_REDIRECT_URI}`);
  });

  server.on('error', (e: any) => {
    if (e.code === 'EADDRINUSE') {
      console.error(`Port ${PORT} is already in use! The application won't work correctly with Xero.`);
      console.error(`Please free up port ${PORT} and restart the application.`);
      process.exit(1);
    } else {
      console.error('Server error:', e);
    }
  });
}
