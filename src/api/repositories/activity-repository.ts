/**
 * Activity Repository
 *
 * This repository handles all database operations for the activity feed system.
 * It extends the BaseRepository to maintain consistency with existing patterns.
 */

import { BaseRepository } from "./base-repository";
import { v4 as uuidv4 } from "uuid";
import { Activity, ActivityCreate, ActivityUpdate, ActivityFilters, ActivityStats } from "../../frontend/types/activity-types";

// TypeScript interfaces for database operations
interface DatabaseRow {
  id: string;
  type: string;
  subject: string;
  description?: string;
  status: string;
  entity_type?: string;
  entity_id?: string;
  due_date?: string;
  completed_date?: string;
  company_id?: string;
  contact_id?: string;
  deal_id?: string;
  metadata?: string;
  is_read: number;
  importance: string;
  created_by?: string;
  source?: string;
  created_at: string;
  updated_at: string;
}

interface StatsRow {
  type?: string;
  source?: string;
  count: number;
}

type QueryParam = string | number | boolean | null | undefined;

/**
 * Activity repository class
 */
export class ActivityRepository extends BaseRepository {
  constructor() {
    super('activity_feed');
  }

  /**
   * Create a new activity
   */
  async createActivity(data: ActivityCreate): Promise<Activity> {
    return this.transaction(() => {
      const id = uuidv4();
      const now = new Date().toISOString();

      const activity: Activity = {
        id,
        type: data.type,
        subject: data.subject,
        description: data.description,
        status: data.status || "completed",
        entityType: data.entityType,
        entityId: data.entityId,
        dueDate: data.dueDate,
        completedDate: data.completedDate,
        companyId: data.companyId,
        contactId: data.contactId,
        dealId: data.dealId,
        metadata: data.metadata,
        isRead: false,
        importance: data.importance || "normal",
        createdBy: data.createdBy,
        source: data.source,
        createdAt: now,
        updatedAt: now,
      };

      const query = `
        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      this.db.prepare(query).run(
        activity.id,
        activity.type,
        activity.subject,
        activity.description,
        activity.status,
        activity.entityType,
        activity.entityId,
        activity.dueDate,
        activity.completedDate,
        activity.companyId,
        activity.contactId,
        activity.dealId,
        activity.metadata ? JSON.stringify(activity.metadata) : null,
        activity.isRead ? 1 : 0,
        activity.importance,
        activity.createdBy,
        activity.source,
        activity.createdAt,
        activity.updatedAt
      );

      return activity;
    });
  }

  /**
   * Get activity by ID
   */
  async getActivityById(id: string): Promise<Activity | null> {
    try {
      const result = this.getById<DatabaseRow>(id);
      return result ? this.mapDatabaseToActivity(result) : null;
    } catch (error) {
      console.error(`Error getting activity by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update an activity
   */
  async updateActivity(id: string, data: ActivityUpdate): Promise<Activity | null> {
    return this.transaction(() => {
      const now = new Date().toISOString();
      
      const updateFields: string[] = [];
      const updateValues: QueryParam[] = [];

      if (data.subject !== undefined) {
        updateFields.push('subject = ?');
        updateValues.push(data.subject);
      }
      if (data.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(data.description);
      }
      if (data.status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(data.status);
      }
      if (data.dueDate !== undefined) {
        updateFields.push('due_date = ?');
        updateValues.push(data.dueDate);
      }
      if (data.completedDate !== undefined) {
        updateFields.push('completed_date = ?');
        updateValues.push(data.completedDate);
      }
      if (data.metadata !== undefined) {
        updateFields.push('metadata = ?');
        updateValues.push(JSON.stringify(data.metadata));
      }
      if (data.isRead !== undefined) {
        updateFields.push('is_read = ?');
        updateValues.push(data.isRead ? 1 : 0);
      }
      if (data.importance !== undefined) {
        updateFields.push('importance = ?');
        updateValues.push(data.importance);
      }

      updateFields.push('updated_at = ?');
      updateValues.push(now);
      updateValues.push(id);

      const query = `UPDATE activity_feed SET ${updateFields.join(', ')} WHERE id = ?`;
      
      const result = this.db.prepare(query).run(...updateValues);
      
      if (result.changes === 0) {
        return null;
      }

      return this.getActivityById(id);
    });
  }

  /**
   * Delete an activity
   */
  async deleteActivity(id: string): Promise<boolean> {
    try {
      const result = this.deleteById(id, false); // Hard delete for activities
      return result > 0;
    } catch (error) {
      console.error(`Error deleting activity ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get activities with filters
   */
  async getActivities(filters: ActivityFilters = {}): Promise<Activity[]> {
    try {
      let query = 'SELECT * FROM activity_feed WHERE 1=1';
      const params: QueryParam[] = [];

      // Apply filters
      if (filters.type) {
        if (Array.isArray(filters.type)) {
          const placeholders = filters.type.map(() => '?').join(',');
          query += ` AND type IN (${placeholders})`;
          params.push(...filters.type);
        } else {
          query += ' AND type = ?';
          params.push(filters.type);
        }
      }

      if (filters.source) {
        if (Array.isArray(filters.source)) {
          const placeholders = filters.source.map(() => '?').join(',');
          query += ` AND source IN (${placeholders})`;
          params.push(...filters.source);
        } else {
          query += ' AND source = ?';
          params.push(filters.source);
        }
      }

      if (filters.entityType) {
        query += ' AND entity_type = ?';
        params.push(filters.entityType);
      }

      if (filters.entityId) {
        query += ' AND entity_id = ?';
        params.push(filters.entityId);
      }

      if (filters.createdBy) {
        query += ' AND created_by = ?';
        params.push(filters.createdBy);
      }

      if (filters.isRead !== undefined) {
        query += ' AND is_read = ?';
        params.push(filters.isRead ? 1 : 0);
      }

      if (filters.importance) {
        query += ' AND importance = ?';
        params.push(filters.importance);
      }

      if (filters.dateFrom) {
        query += ' AND created_at >= ?';
        params.push(filters.dateFrom);
      }

      if (filters.dateTo) {
        query += ' AND created_at <= ?';
        params.push(filters.dateTo);
      }

      if (filters.search) {
        query += ' AND (subject LIKE ? OR description LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm);
      }

      // Order by created_at DESC
      query += ' ORDER BY created_at DESC';

      // Apply pagination
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
        
        if (filters.offset) {
          query += ' OFFSET ?';
          params.push(filters.offset);
        }
      }

      const results = this.db.prepare(query).all(...params);
      return results.map(this.mapDatabaseToActivity);
    } catch (error) {
      console.error('Error getting activities:', error);
      throw error;
    }
  }

  /**
   * Get activities for a specific entity
   */
  async getActivitiesForEntity(entityType: string, entityId: string, limit: number = 50): Promise<Activity[]> {
    return this.getActivities({
      entityType: entityType as string,
      entityId,
      limit
    });
  }

  /**
   * Get recent activities
   */
  async getRecentActivities(limit: number = 20, offset: number = 0): Promise<Activity[]> {
    return this.getActivities({ limit, offset });
  }

  /**
   * Get unread count for activities
   */
  async getUnreadCount(userId?: string): Promise<number> {
    try {
      let query = 'SELECT COUNT(*) as count FROM activity_feed WHERE is_read = 0';
      const params: QueryParam[] = [];

      if (userId) {
        query += ' AND created_by != ?';
        params.push(userId);
      }

      const result = this.db.prepare(query).get(...params);
      return result?.count || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Mark activities as read
   */
  async markAsRead(activityIds: string[]): Promise<boolean> {
    return this.transaction(() => {
      const placeholders = activityIds.map(() => '?').join(',');
      const query = `UPDATE activity_feed SET is_read = 1, updated_at = ? WHERE id IN (${placeholders})`;
      const now = new Date().toISOString();
      
      const result = this.db.prepare(query).run(now, ...activityIds);
      return result.changes > 0;
    });
  }

  /**
   * Get activity statistics
   */
  async getActivityStats(): Promise<ActivityStats> {
    try {
      // Get total count
      const totalResult = this.db.prepare('SELECT COUNT(*) as count FROM activity_feed').get();
      const total = totalResult?.count || 0;

      // Get unread count
      const unreadResult = this.db.prepare('SELECT COUNT(*) as count FROM activity_feed WHERE is_read = 0').get();
      const unread = unreadResult?.count || 0;

      // Get recent count (last 24 hours)
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const recentResult = this.db.prepare('SELECT COUNT(*) as count FROM activity_feed WHERE created_at >= ?').get(yesterday);
      const recent = recentResult?.count || 0;

      // Get counts by type
      const typeResults = this.db.prepare('SELECT type, COUNT(*) as count FROM activity_feed GROUP BY type').all();
      const byType: Record<string, number> = {};
      typeResults.forEach((row: StatsRow) => {
        if (row.type) byType[row.type] = row.count;
      });

      // Get counts by source
      const sourceResults = this.db.prepare('SELECT source, COUNT(*) as count FROM activity_feed GROUP BY source').all();
      const bySource: Record<string, number> = {};
      sourceResults.forEach((row: StatsRow) => {
        if (row.source) bySource[row.source] = row.count;
      });

      return {
        total,
        unread,
        byType: byType as Record<string, number>,
        bySource: bySource as Record<string, number>,
        recent
      };
    } catch (error) {
      console.error('Error getting activity stats:', error);
      throw error;
    }
  }

  /**
   * Map database row to Activity object
   */
  private mapDatabaseToActivity(row: DatabaseRow): Activity {
    return {
      id: row.id,
      type: row.type,
      subject: row.subject,
      description: row.description,
      status: row.status,
      entityType: row.entity_type,
      entityId: row.entity_id,
      dueDate: row.due_date,
      completedDate: row.completed_date,
      companyId: row.company_id,
      contactId: row.contact_id,
      dealId: row.deal_id,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
      isRead: Boolean(row.is_read),
      importance: row.importance,
      createdBy: row.created_by,
      source: row.source,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}
