/**
 * Repository for managing change log records
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Interface for change log entity
 */
export interface ChangeLog {
  id: string;
  entityType: string;
  entityId: string;
  fieldName: string;
  oldValue?: string;
  newValue?: string;
  changeSource: string;
  changedAt: string;
  changedBy?: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new change log record
 */
export interface ChangeLogCreate {
  entityType: string;
  entityId: string;
  fieldName: string;
  oldValue?: string;
  newValue?: string;
  changeSource: string;
  changedBy?: string;
}

/**
 * Repository for managing change log records
 */
export class ChangeLogRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super("change_log");
  }

  /**
   * Get all change log records for an entity
   * @param entityType Entity type (e.g., 'deal', 'contact', 'company')
   * @param entityId Entity ID
   * @param limit Optional limit for results
   * @returns Array of change log records
   */
  getChangeLogByEntity(
    entityType: string,
    entityId: string,
    limit?: number,
  ): ChangeLog[] {
    try {
      let query = `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ?
        ORDER BY changed_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const changes = this.db
        .prepare(query)
        .all(entityType, entityId) as ChangeLog[];
      return changes;
    } catch (error) {
      console.error(
        `Error fetching change log for ${entityType} ${entityId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get change log records for a specific field
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param fieldName Field name
   * @param limit Optional limit for results
   * @returns Array of change log records
   */
  getChangeLogByField(
    entityType: string,
    entityId: string,
    fieldName: string,
    limit?: number,
  ): ChangeLog[] {
    try {
      let query = `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ? AND field_name = ?
        ORDER BY changed_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const changes = this.db
        .prepare(query)
        .all(entityType, entityId, fieldName) as ChangeLog[];
      return changes;
    } catch (error) {
      console.error(
        `Error fetching change log for ${entityType} ${entityId} field ${fieldName}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get change log records by entity type
   * @param entityType Entity type
   * @param limit Optional limit for results
   * @returns Array of change log records
   */
  getChangeLogByEntityType(entityType: string, limit?: number): ChangeLog[] {
    try {
      let query = `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE entity_type = ?
        ORDER BY changed_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const changes = this.db.prepare(query).all(entityType) as ChangeLog[];
      return changes;
    } catch (error) {
      console.error(
        `Error fetching change log for entity type ${entityType}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get change log records by change source
   * @param changeSource Change source (e.g., 'HubSpot', 'Manual', 'System')
   * @param limit Optional limit for results
   * @returns Array of change log records
   */
  getChangeLogBySource(changeSource: string, limit?: number): ChangeLog[] {
    try {
      let query = `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE change_source = ?
        ORDER BY changed_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const changes = this.db.prepare(query).all(changeSource) as ChangeLog[];
      return changes;
    } catch (error) {
      console.error(
        `Error fetching change log by source ${changeSource}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get change log records by user
   * @param changedBy User who made the changes
   * @param limit Optional limit for results
   * @returns Array of change log records
   */
  getChangeLogByUser(changedBy: string, limit?: number): ChangeLog[] {
    try {
      let query = `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE changed_by = ?
        ORDER BY changed_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const changes = this.db.prepare(query).all(changedBy) as ChangeLog[];
      return changes;
    } catch (error) {
      console.error(`Error fetching change log by user ${changedBy}:`, error);
      return [];
    }
  }

  /**
   * Get change log records within a date range
   * @param startDate Start date (ISO string)
   * @param endDate End date (ISO string)
   * @param limit Optional limit for results
   * @returns Array of change log records
   */
  getChangeLogByDateRange(
    startDate: string,
    endDate: string,
    limit?: number,
  ): ChangeLog[] {
    try {
      let query = `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE changed_at >= ? AND changed_at <= ?
        ORDER BY changed_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const changes = this.db
        .prepare(query)
        .all(startDate, endDate) as ChangeLog[];
      return changes;
    } catch (error) {
      console.error(
        `Error fetching change log for date range ${startDate} to ${endDate}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Create a new change log record
   * @param changeData Change log data
   * @returns Created change log record
   */
  createChangeLog(changeData: ChangeLogCreate): ChangeLog {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      this.db
        .prepare(
          `
        INSERT INTO ${this.tableName} (
          id,
          entity_type,
          entity_id,
          field_name,
          old_value,
          new_value,
          change_type,
          change_source,
          changed_at,
          changed_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        )
        .run(
          id,
          changeData.entityType,
          changeData.entityId,
          changeData.fieldName,
          changeData.oldValue || null,
          changeData.newValue || null,
          "update", // Default to 'update' - most common case
          changeData.changeSource,
          now,
          changeData.changedBy || null,
        );

      return this.getChangeLogById(id) as ChangeLog;
    } catch (error) {
      console.error("Error creating change log:", error);
      throw error;
    }
  }

  /**
   * Log a field change (convenience method)
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param fieldName Field name
   * @param oldValue Old value
   * @param newValue New value
   * @param changeSource Change source
   * @param changedBy User who made the change
   * @returns Created change log record
   */
  logFieldChange(
    entityType: string,
    entityId: string,
    fieldName: string,
    oldValue: any,
    newValue: any,
    changeSource: string,
    changedBy?: string,
  ): ChangeLog {
    try {
      return this.createChangeLog({
        entityType,
        entityId,
        fieldName,
        oldValue:
          oldValue !== null && oldValue !== undefined
            ? String(oldValue)
            : undefined,
        newValue:
          newValue !== null && newValue !== undefined
            ? String(newValue)
            : undefined,
        changeSource,
        changedBy,
      });
    } catch (error) {
      console.error("Error logging field change:", error);
      throw error;
    }
  }

  /**
   * Bulk create change log records
   * @param changes Array of change log data
   * @returns Array of created change log records
   */
  bulkCreateChangeLogs(changes: ChangeLogCreate[]): ChangeLog[] {
    try {
      const results: ChangeLog[] = [];

      const transaction = this.db.transaction(() => {
        for (const change of changes) {
          const result = this.createChangeLog(change);
          results.push(result);
        }
      });

      transaction();
      return results;
    } catch (error) {
      console.error("Error bulk creating change logs:", error);
      throw error;
    }
  }

  /**
   * Delete change log records older than specified days
   * @param days Number of days to keep (default: 365)
   * @returns Number of deleted records
   */
  deleteOldChangeLogs(days: number = 365): number {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      const cutoffIso = cutoffDate.toISOString();

      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE changed_at < ?
      `,
        )
        .run(cutoffIso);

      return result.changes || 0;
    } catch (error) {
      console.error(
        `Error deleting old change logs (older than ${days} days):`,
        error,
      );
      return 0;
    }
  }

  /**
   * Delete all change log records for an entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @returns Number of deleted records
   */
  deleteChangeLogByEntity(entityType: string, entityId: string): number {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ?
      `,
        )
        .run(entityType, entityId);

      return result.changes || 0;
    } catch (error) {
      console.error(
        `Error deleting change log for ${entityType} ${entityId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Get change log statistics
   * @returns Change log statistics
   */
  getChangeLogStatistics(): {
    totalChanges: number;
    entitiesWithChanges: number;
    changesBySource: Record<string, number>;
    changesByEntityType: Record<string, number>;
    changesByField: Record<string, number>;
    oldestChange: string | null;
    newestChange: string | null;
    totalUsers: number;
  } {
    try {
      // Get basic statistics
      const basicStats = this.db
        .prepare(
          `
        SELECT 
          COUNT(*) as totalChanges,
          COUNT(DISTINCT entity_type || ":" || entity_id) as entitiesWithChanges,
          COUNT(DISTINCT changed_by) as totalUsers,
          MIN(changed_at) as oldestChange,
          MAX(changed_at) as newestChange
        FROM ${this.tableName}
      `,
        )
        .get() as any;

      // Get changes by source
      const sourceStats = this.db
        .prepare(
          `
        SELECT change_source, COUNT(*) as count
        FROM ${this.tableName}
        GROUP BY change_source
        ORDER BY count DESC
      `,
        )
        .all() as Array<{ change_source: string; count: number }>;

      // Get changes by entity type
      const entityTypeStats = this.db
        .prepare(
          `
        SELECT entity_type, COUNT(*) as count
        FROM ${this.tableName}
        GROUP BY entity_type
        ORDER BY count DESC
      `,
        )
        .all() as Array<{ entity_type: string; count: number }>;

      // Get changes by field
      const fieldStats = this.db
        .prepare(
          `
        SELECT field_name, COUNT(*) as count
        FROM ${this.tableName}
        GROUP BY field_name
        ORDER BY count DESC
        LIMIT 20
      `,
        )
        .all() as Array<{ field_name: string; count: number }>;

      const changesBySource: Record<string, number> = {};
      sourceStats.forEach((stat) => {
        changesBySource[stat.change_source] = stat.count;
      });

      const changesByEntityType: Record<string, number> = {};
      entityTypeStats.forEach((stat) => {
        changesByEntityType[stat.entity_type] = stat.count;
      });

      const changesByField: Record<string, number> = {};
      fieldStats.forEach((stat) => {
        changesByField[stat.field_name] = stat.count;
      });

      return {
        totalChanges: basicStats.totalChanges || 0,
        entitiesWithChanges: basicStats.entitiesWithChanges || 0,
        changesBySource,
        changesByEntityType,
        changesByField,
        oldestChange: basicStats.oldestChange || null,
        newestChange: basicStats.newestChange || null,
        totalUsers: basicStats.totalUsers || 0,
      };
    } catch (error) {
      console.error("Error fetching change log statistics:", error);
      return {
        totalChanges: 0,
        entitiesWithChanges: 0,
        changesBySource: {},
        changesByEntityType: {},
        changesByField: {},
        oldestChange: null,
        newestChange: null,
        totalUsers: 0,
      };
    }
  }

  /**
   * Get recent activity summary
   * @param hours Number of hours to look back (default: 24)
   * @returns Recent activity summary
   */
  getRecentActivitySummary(hours: number = 24): {
    totalChanges: number;
    uniqueEntities: number;
    uniqueUsers: number;
    topChangedFields: Array<{ fieldName: string; count: number }>;
    topSources: Array<{ source: string; count: number }>;
  } {
    try {
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - hours);
      const cutoffIso = cutoffDate.toISOString();

      // Get basic recent statistics
      const basicStats = this.db
        .prepare(
          `
        SELECT 
          COUNT(*) as totalChanges,
          COUNT(DISTINCT entity_type || ":" || entity_id) as uniqueEntities,
          COUNT(DISTINCT changed_by) as uniqueUsers
        FROM ${this.tableName}
        WHERE changed_at >= ?
      `,
        )
        .get(cutoffIso) as any;

      // Get top changed fields in recent period
      const topFields = this.db
        .prepare(
          `
        SELECT field_name as fieldName, COUNT(*) as count
        FROM ${this.tableName}
        WHERE changed_at >= ?
        GROUP BY field_name
        ORDER BY count DESC
        LIMIT 10
      `,
        )
        .all(cutoffIso) as Array<{ fieldName: string; count: number }>;

      // Get top sources in recent period
      const topSources = this.db
        .prepare(
          `
        SELECT change_source as source, COUNT(*) as count
        FROM ${this.tableName}
        WHERE changed_at >= ?
        GROUP BY change_source
        ORDER BY count DESC
        LIMIT 10
      `,
        )
        .all(cutoffIso) as Array<{ source: string; count: number }>;

      return {
        totalChanges: basicStats.totalChanges || 0,
        uniqueEntities: basicStats.uniqueEntities || 0,
        uniqueUsers: basicStats.uniqueUsers || 0,
        topChangedFields: topFields,
        topSources: topSources,
      };
    } catch (error) {
      console.error(
        `Error fetching recent activity summary (${hours} hours):`,
        error,
      );
      return {
        totalChanges: 0,
        uniqueEntities: 0,
        uniqueUsers: 0,
        topChangedFields: [],
        topSources: [],
      };
    }
  }

  /**
   * Get change log by ID
   * @param id Change log ID
   * @returns Change log record or null if not found
   */
  private getChangeLogById(id: string): ChangeLog | null {
    try {
      const change = this.db
        .prepare(
          `
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          old_value as oldValue,
          new_value as newValue,
          change_source as changeSource,
          changed_at as changedAt,
          changed_by as changedBy
        FROM ${this.tableName}
        WHERE id = ?
      `,
        )
        .get(id) as ChangeLog | undefined;

      return change || null;
    } catch (error) {
      console.error(`Error fetching change log by ID ${id}:`, error);
      return null;
    }
  }
}
