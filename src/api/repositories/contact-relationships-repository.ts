/**
 * Repository for managing contact relationships
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Contact relationship types
 */
export type ContactRelationshipType = 
  | "knows"
  | "reports_to"
  | "introduced_by"
  | "worked_with"
  | "colleague";

/**
 * Contact relationship interface
 */
export interface ContactRelationship {
  id: string;
  sourceContactId: string;
  targetContactId: string;
  relationshipType: ContactRelationshipType;
  strength: number; // 1-5 scale
  context?: string;
  createdAt: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
  deletedAt?: string;
}

/**
 * Contact relationship with contact details
 */
export interface ContactRelationshipWithDetails extends ContactRelationship {
  sourceContact?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    jobTitle?: string;
    companyId?: string;
    companyName?: string;
  };
  targetContact?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    jobTitle?: string;
    companyId?: string;
    companyName?: string;
  };
}

/**
 * Contact relationship create interface
 */
export interface ContactRelationshipCreate {
  sourceContactId: string;
  targetContactId: string;
  relationshipType: ContactRelationshipType;
  strength?: number;
  context?: string;
  createdBy?: string;
}

/**
 * Contact relationship update interface
 */
export interface ContactRelationshipUpdate {
  strength?: number;
  context?: string;
  updatedBy?: string;
}

/**
 * Network node for visualization
 */
export interface NetworkNode {
  id: string;
  name: string;
  email?: string;
  company?: string;
  type: "contact";
  group: string; // Company ID for grouping
}

/**
 * Network link for visualization
 */
export interface NetworkLink {
  source: string;
  target: string;
  type: ContactRelationshipType;
  strength: number;
  context?: string;
}

/**
 * Repository for managing contact relationships
 */
export class ContactRelationshipsRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('contact_relationships');
  }

  /**
   * Create a new contact relationship
   * @param data Contact relationship data
   * @returns Created contact relationship
   */
  createRelationship(data: ContactRelationshipCreate): ContactRelationship {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      const relationship = {
        id,
        source_contact_id: data.sourceContactId,
        target_contact_id: data.targetContactId,
        relationship_type: data.relationshipType,
        strength: data.strength || 1,
        context: data.context || null,
        created_at: now,
        created_by: data.createdBy || null,
        updated_at: now,
        updated_by: data.createdBy || null
      };

      this.db.prepare(`
        INSERT INTO contact_relationships (
          id, source_contact_id, target_contact_id, relationship_type,
          strength, context, created_at, created_by, updated_at, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        relationship.id,
        relationship.source_contact_id,
        relationship.target_contact_id,
        relationship.relationship_type,
        relationship.strength,
        relationship.context,
        relationship.created_at,
        relationship.created_by,
        relationship.updated_at,
        relationship.updated_by
      );

      return {
        id: relationship.id,
        sourceContactId: relationship.source_contact_id,
        targetContactId: relationship.target_contact_id,
        relationshipType: relationship.relationship_type as ContactRelationshipType,
        strength: relationship.strength,
        context: relationship.context || undefined,
        createdAt: relationship.created_at,
        createdBy: relationship.created_by || undefined,
        updatedAt: relationship.updated_at,
        updatedBy: relationship.updated_by || undefined
      };
    } catch (error) {
      console.error('Error creating contact relationship:', error);
      throw error;
    }
  }

  /**
   * Get all relationships for a contact
   * @param contactId Contact ID
   * @returns Array of contact relationships with details
   */
  getContactRelationships(contactId: string): ContactRelationshipWithDetails[] {
    try {
      const relationships = this.db.prepare(`
        SELECT 
          cr.*,
          -- Source contact details
          sc.first_name as source_first_name,
          sc.last_name as source_last_name,
          sc.email as source_email,
          sc.job_title as source_job_title,
          cc1.company_id as source_company_id,
          c1.name as source_company_name,
          -- Target contact details
          tc.first_name as target_first_name,
          tc.last_name as target_last_name,
          tc.email as target_email,
          tc.job_title as target_job_title,
          cc2.company_id as target_company_id,
          c2.name as target_company_name
        FROM contact_relationships cr
        LEFT JOIN contact sc ON cr.source_contact_id = sc.id
        LEFT JOIN contact tc ON cr.target_contact_id = tc.id
        LEFT JOIN contact_company cc1 ON sc.id = cc1.contact_id AND cc1.is_primary = 1
        LEFT JOIN company c1 ON cc1.company_id = c1.id
        LEFT JOIN contact_company cc2 ON tc.id = cc2.contact_id AND cc2.is_primary = 1
        LEFT JOIN company c2 ON cc2.company_id = c2.id
        WHERE (cr.source_contact_id = ? OR cr.target_contact_id = ?)
          AND sc.deleted_at IS NULL
          AND tc.deleted_at IS NULL
      `).all(contactId, contactId) as any[];

      return relationships.map(this.mapRelationshipWithDetails);
    } catch (error) {
      console.error('Error fetching contact relationships:', error);
      return [];
    }
  }

  /**
   * Get relationship network for visualization
   * @param entityId Contact or Company ID
   * @param entityType Type of entity
   * @param depth Network depth (1-3)
   * @returns Network nodes and links
   */
  getRelationshipNetwork(
    entityId: string, 
    entityType: "contact" | "company",
    depth: number = 1
  ): { nodes: NetworkNode[], links: NetworkLink[] } {
    try {
      // For contact, get direct relationships
      if (entityType === 'contact') {
        return this.getContactNetwork(entityId, depth);
      } else {
        // For company, get all contacts and their relationships
        return this.getCompanyNetwork(entityId, depth);
      }
    } catch (error) {
      console.error('Error fetching relationship network:', error);
      return { nodes: [], links: [] };
    }
  }

  /**
   * Update a contact relationship
   * @param id Relationship ID
   * @param data Update data
   * @returns Updated relationship
   */
  updateRelationship(id: string, data: ContactRelationshipUpdate): ContactRelationship | null {
    try {
      const now = new Date().toISOString();
      
      const updateFields: string[] = ['updated_at = ?'];
      const updateValues: any[] = [now];

      if (data.strength !== undefined) {
        updateFields.push('strength = ?');
        updateValues.push(data.strength);
      }

      if (data.context !== undefined) {
        updateFields.push('context = ?');
        updateValues.push(data.context);
      }

      if (data.updatedBy) {
        updateFields.push('updated_by = ?');
        updateValues.push(data.updatedBy);
      }

      updateValues.push(id);

      this.db.prepare(`
        UPDATE contact_relationships
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `).run(...updateValues);

      return this.getRelationshipById(id);
    } catch (error) {
      console.error('Error updating contact relationship:', error);
      throw error;
    }
  }

  /**
   * Delete a contact relationship
   * @param id Relationship ID
   * @returns Number of affected rows
   */
  deleteRelationship(id: string): number {
    try {
      return this.db.prepare(`
        DELETE FROM contact_relationships WHERE id = ?
      `).run(id).changes;
    } catch (error) {
      console.error('Error deleting contact relationship:', error);
      throw error;
    }
  }

  /**
   * Get relationship by ID
   * @param id Relationship ID
   * @returns Contact relationship or null
   */
  private getRelationshipById(id: string): ContactRelationship | null {
    try {
      const relationship = this.db.prepare(`
        SELECT * FROM contact_relationships WHERE id = ?
      `).get(id) as any;

      if (!relationship) return null;

      return {
        id: relationship.id,
        sourceContactId: relationship.source_contact_id,
        targetContactId: relationship.target_contact_id,
        relationshipType: relationship.relationship_type,
        strength: relationship.strength,
        context: relationship.context,
        createdAt: relationship.created_at,
        createdBy: relationship.created_by,
        updatedAt: relationship.updated_at,
        updatedBy: relationship.updated_by
      };
    } catch (error) {
      console.error('Error fetching relationship by ID:', error);
      return null;
    }
  }

  /**
   * Get contact network for visualization
   * @param contactId Contact ID
   * @param depth Network depth
   * @returns Network nodes and links
   */
  private getContactNetwork(contactId: string, depth: number): { nodes: NetworkNode[], links: NetworkLink[] } {
    const nodes = new Map<string, NetworkNode>();
    const links: NetworkLink[] = [];
    const visited = new Set<string>();

    // Get the primary contact
    const primaryContact = this.db.prepare(`
      SELECT 
        c.*,
        cc.company_id,
        comp.name as company_name
      FROM contact c
      LEFT JOIN contact_company cc ON c.id = cc.contact_id AND cc.is_primary = 1
      LEFT JOIN company comp ON cc.company_id = comp.id
      WHERE c.id = ? AND c.deleted_at IS NULL
    `).get(contactId) as any;

    if (!primaryContact) return { nodes: [], links: [] };

    // Add primary node
    nodes.set(primaryContact.id, {
      id: primaryContact.id,
      name: `${primaryContact.first_name} ${primaryContact.last_name}`,
      email: primaryContact.email,
      company: primaryContact.company_name,
      type: "contact",
      group: primaryContact.company_id || "no-company"
    });

    // Recursively get relationships
    this.getRelationshipsRecursive(contactId, depth, nodes, links, visited);

    return { 
      nodes: Array.from(nodes.values()), 
      links 
    };
  }

  /**
   * Get company network for visualization
   * @param companyId Company ID
   * @param depth Network depth
   * @returns Network nodes and links
   */
  private getCompanyNetwork(companyId: string, depth: number): { nodes: NetworkNode[], links: NetworkLink[] } {
    const nodes = new Map<string, NetworkNode>();
    const links: NetworkLink[] = [];

    // Get all contacts at the company
    const companyContacts = this.db.prepare(`
      SELECT 
        c.*,
        comp.name as company_name
      FROM contact c
      JOIN contact_company cc ON c.id = cc.contact_id
      JOIN company comp ON cc.company_id = comp.id
      WHERE cc.company_id = ? AND c.deleted_at IS NULL
    `).all(companyId) as any[];

    // Add all company contacts as nodes
    companyContacts.forEach(contact => {
      nodes.set(contact.id, {
        id: contact.id,
        name: `${contact.first_name} ${contact.last_name}`,
        email: contact.email,
        company: contact.company_name,
        type: "contact",
        group: companyId
      });
    });

    // Get relationships between company contacts and external contacts
    const contactIds = companyContacts.map(c => c.id);
    if (contactIds.length > 0) {
      const placeholders = contactIds.map(() => '?').join(',');
      const relationships = this.db.prepare(`
        SELECT 
          cr.*,
          sc.first_name as source_first_name,
          sc.last_name as source_last_name,
          sc.email as source_email,
          cc1.company_id as source_company_id,
          c1.name as source_company_name,
          tc.first_name as target_first_name,
          tc.last_name as target_last_name,
          tc.email as target_email,
          cc2.company_id as target_company_id,
          c2.name as target_company_name
        FROM contact_relationships cr
        JOIN contact sc ON cr.source_contact_id = sc.id
        JOIN contact tc ON cr.target_contact_id = tc.id
        LEFT JOIN contact_company cc1 ON sc.id = cc1.contact_id AND cc1.is_primary = 1
        LEFT JOIN company c1 ON cc1.company_id = c1.id
        LEFT JOIN contact_company cc2 ON tc.id = cc2.contact_id AND cc2.is_primary = 1
        LEFT JOIN company c2 ON cc2.company_id = c2.id
        WHERE (cr.source_contact_id IN (${placeholders}) OR cr.target_contact_id IN (${placeholders}))
          AND sc.deleted_at IS NULL
          AND tc.deleted_at IS NULL
      `).all(...contactIds, ...contactIds) as any[];

      relationships.forEach(rel => {
        // Add external contacts as nodes
        if (!nodes.has(rel.source_contact_id)) {
          nodes.set(rel.source_contact_id, {
            id: rel.source_contact_id,
            name: `${rel.source_first_name} ${rel.source_last_name}`,
            email: rel.source_email,
            company: rel.source_company_name,
            type: "contact",
            group: rel.source_company_id || "external"
          });
        }

        if (!nodes.has(rel.target_contact_id)) {
          nodes.set(rel.target_contact_id, {
            id: rel.target_contact_id,
            name: `${rel.target_first_name} ${rel.target_last_name}`,
            email: rel.target_email,
            company: rel.target_company_name,
            type: "contact",
            group: rel.target_company_id || "external"
          });
        }

        // Add link
        links.push({
          source: rel.source_contact_id,
          target: rel.target_contact_id,
          type: rel.relationship_type,
          strength: rel.strength,
          context: rel.context
        });
      });
    }

    return { nodes: Array.from(nodes.values()), links };
  }

  /**
   * Recursively get relationships up to specified depth
   * @param contactId Current contact ID
   * @param remainingDepth Remaining depth to explore
   * @param nodes Node map
   * @param links Links array
   * @param visited Visited contact IDs
   */
  private getRelationshipsRecursive(
    contactId: string,
    remainingDepth: number,
    nodes: Map<string, NetworkNode>,
    links: NetworkLink[],
    visited: Set<string>
  ): void {
    if (remainingDepth <= 0 || visited.has(contactId)) return;
    visited.add(contactId);

    const relationships = this.db.prepare(`
      SELECT 
        cr.*,
        sc.first_name as source_first_name,
        sc.last_name as source_last_name,
        sc.email as source_email,
        cc1.company_id as source_company_id,
        c1.name as source_company_name,
        tc.first_name as target_first_name,
        tc.last_name as target_last_name,
        tc.email as target_email,
        cc2.company_id as target_company_id,
        c2.name as target_company_name
      FROM contact_relationships cr
      JOIN contact sc ON cr.source_contact_id = sc.id
      JOIN contact tc ON cr.target_contact_id = tc.id
      LEFT JOIN contact_company cc1 ON sc.id = cc1.contact_id AND cc1.is_primary = 1
      LEFT JOIN company c1 ON cc1.company_id = c1.id
      LEFT JOIN contact_company cc2 ON tc.id = cc2.contact_id AND cc2.is_primary = 1
      LEFT JOIN company c2 ON cc2.company_id = c2.id
      WHERE (cr.source_contact_id = ? OR cr.target_contact_id = ?)
        AND sc.deleted_at IS NULL
        AND tc.deleted_at IS NULL
    `).all(contactId, contactId) as any[];

    relationships.forEach(rel => {
      // Add nodes
      if (!nodes.has(rel.source_contact_id)) {
        nodes.set(rel.source_contact_id, {
          id: rel.source_contact_id,
          name: `${rel.source_first_name} ${rel.source_last_name}`,
          email: rel.source_email,
          company: rel.source_company_name,
          type: "contact",
          group: rel.source_company_id || "no-company"
        });
      }

      if (!nodes.has(rel.target_contact_id)) {
        nodes.set(rel.target_contact_id, {
          id: rel.target_contact_id,
          name: `${rel.target_first_name} ${rel.target_last_name}`,
          email: rel.target_email,
          company: rel.target_company_name,
          type: "contact",
          group: rel.target_company_id || "no-company"
        });
      }

      // Add link
      links.push({
        source: rel.source_contact_id,
        target: rel.target_contact_id,
        type: rel.relationship_type,
        strength: rel.strength,
        context: rel.context
      });

      // Recurse on the other contact
      const otherContactId = rel.source_contact_id === contactId 
        ? rel.target_contact_id 
        : rel.source_contact_id;
      
      this.getRelationshipsRecursive(otherContactId, remainingDepth - 1, nodes, links, visited);
    });
  }

  /**
   * Map database row to ContactRelationshipWithDetails
   * @param row Database row
   * @returns ContactRelationshipWithDetails
   */
  private mapRelationshipWithDetails(row: any): ContactRelationshipWithDetails {
    return {
      id: row.id,
      sourceContactId: row.source_contact_id,
      targetContactId: row.target_contact_id,
      relationshipType: row.relationship_type,
      strength: row.strength,
      context: row.context,
      createdAt: row.created_at,
      createdBy: row.created_by,
      updatedAt: row.updated_at,
      updatedBy: row.updated_by,
      sourceContact: {
        id: row.source_contact_id,
        firstName: row.source_first_name,
        lastName: row.source_last_name,
        email: row.source_email,
        jobTitle: row.source_job_title,
        companyId: row.source_company_id,
        companyName: row.source_company_name
      },
      targetContact: {
        id: row.target_contact_id,
        firstName: row.target_first_name,
        lastName: row.target_last_name,
        email: row.target_email,
        jobTitle: row.target_job_title,
        companyId: row.target_company_id,
        companyName: row.target_company_name
      }
    };
  }
}