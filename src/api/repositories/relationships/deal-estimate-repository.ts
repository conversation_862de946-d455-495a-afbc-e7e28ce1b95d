/**
 * Repository for managing deal-estimate relationships
 */

import { BaseRepository } from "../base-repository";
import { v4 as uuidv4 } from "uuid";

/**
 * Deal-estimate relationship interface
 */
export interface DealEstimateRelationship {
  id: string;
  deal_id: string;
  estimate_id: string;
  estimate_type: string;
  linked_at: string;
  linked_by: string;
}

/**
 * Estimate types
 */
export enum EstimateType {
  INTERNAL = 'internal',
  HARVEST = 'harvest'
}

/**
 * Repository for managing deal-estimate relationships
 */
export class DealEstimateRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('deal_estimate');
  }

  /**
   * Link a deal to an estimate (internal estimates only)
   * @param dealId Deal ID
   * @param estimateId Estimate ID
   * @param estimateType Type of estimate (must be 'internal')
   * @param linkedBy User who created the relationship
   * @returns Created relationship
   */
  linkDealToEstimate(
    dealId: string,
    estimateId: string,
    estimateType: string = EstimateType.INTERNAL,
    linkedBy: string = 'system",
    harvestEstimateId?: string
  ): DealEstimateRelationship {
    try {
      // Only allow linking internal estimates
      if (estimateType !== EstimateType.INTERNAL) {
        throw new Error('Only internal estimates can be linked to deals. Harvest estimates are read-only.');
      }
      
      // First, hard delete any soft-deleted links for this deal
      // This is necessary because of the UNIQUE constraint on deal_id
      this.db.prepare(`
        DELETE FROM deal_estimate
        WHERE deal_id = ? AND deleted_at IS NOT NULL
      `).run(dealId);
      
      // Also hard delete any soft-deleted links for this estimate
      // This is necessary because of the UNIQUE constraint on estimate_id
      this.db.prepare(`
        DELETE FROM deal_estimate
        WHERE estimate_id = ? AND deleted_at IS NOT NULL
      `).run(estimateId);
      
      // Check if the relationship already exists
      const existingRelationship = this.db.prepare(`
        SELECT * FROM deal_estimate
        WHERE deal_id = ? AND estimate_id = ? AND deleted_at IS NULL
      `).get(dealId, estimateId);

      if (existingRelationship) {
        // Update the estimate type if it exists
        this.db.prepare(`
          UPDATE deal_estimate
          SET estimate_type = ?, updated_at = datetime('now')
          WHERE deal_id = ? AND estimate_id = ?
        `).run(estimateType, dealId, estimateId);

        return {
          ...existingRelationship,
          estimate_type: estimateType
        };
      }
      
      // Check if the deal already has a different estimate linked
      const existingDealLink = this.db.prepare(`
        SELECT * FROM deal_estimate
        WHERE deal_id = ? AND deleted_at IS NULL
      `).get(dealId);
      
      if (existingDealLink) {
        throw new Error(`Deal already has an estimate linked. Please unlink the existing estimate first.`);
      }
      
      // Check if the estimate is already linked to a different deal
      const existingEstimateLink = this.db.prepare(`
        SELECT de.*, d.name as deal_name 
        FROM deal_estimate de
        JOIN deal d ON de.deal_id = d.id
        WHERE de.estimate_id = ? AND de.deleted_at IS NULL
      `).get(estimateId);
      
      if (existingEstimateLink && existingEstimateLink.deal_id !== dealId) {
        throw new Error(`This estimate is already linked to deal "${existingEstimateLink.deal_name}". Please unlink it from that deal first.`);
      }

      const now = new Date().toISOString();
      const id = uuidv4();

      this.db.prepare(`
        INSERT INTO deal_estimate (
          id,
          deal_id,
          estimate_id,
          estimate_type,
          harvest_estimate_id,
          linked_at,
          linked_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        dealId,
        estimateId,
        estimateType,
        estimateType === 'harvest' ? estimateId : null,
        now,
        linkedBy
      );

      // Return the relationship with the generated id
      const relationship: DealEstimateRelationship = {
        id: id,
        deal_id: dealId,
        estimate_id: estimateId,
        estimate_type: estimateType,
        linked_at: now,
        linked_by: linkedBy
      };

      return relationship;
    } catch (error) {
      console.error('Error linking deal to estimate:', error);
      throw error;
    }
  }

  /**
   * Unlink a deal from an estimate
   * @param dealId Deal ID
   * @param estimateId Estimate ID
   * @returns Boolean indicating success
   */
  unlinkDealFromEstimate(dealId: string, estimateId: string): boolean {
    try {
      const now = new Date().toISOString();
      const result = this.db.prepare(`
        UPDATE deal_estimate
        SET deleted_at = ?, updated_at = ?
        WHERE deal_id = ? AND estimate_id = ? AND deleted_at IS NULL
      `).run(now, now, dealId, estimateId);

      return result.changes > 0;
    } catch (error) {
      console.error('Error unlinking deal from estimate:', error);
      throw error;
    }
  }

  /**
   * Get all estimates for a deal
   * @param dealId Deal ID
   * @returns Array of estimate IDs with estimate types and linking information
   */
  getEstimatesForDeal(dealId: string): Array<{ estimateId: string; estimateType: string; linkedAt: string; linkedBy: string }> {
    try {
      const estimates = this.db.prepare(`
        SELECT
          estimate_id as estimateId,
          estimate_type as estimateType,
          linked_at as linkedAt,
          linked_by as linkedBy
        FROM deal_estimate
        WHERE deal_id = ? AND deleted_at IS NULL
      `).all(dealId);

      return estimates;
    } catch (error) {
      console.error('Error getting estimates for deal:', error);
      return [];
    }
  }

  /**
   * Get internal estimates for a deal
   * @param dealId Deal ID
   * @returns Array of estimate IDs
   */
  getInternalEstimatesForDeal(dealId: string): string[] {
    try {
      const estimates = this.db.prepare(`
        SELECT estimate_id as estimateId
        FROM deal_estimate
        WHERE deal_id = ? AND estimate_type = ? AND deleted_at IS NULL
      `).all(dealId, EstimateType.INTERNAL);

      return estimates.map(e => e.estimateId);
    } catch (error) {
      console.error('Error getting internal estimates for deal:', error);
      return [];
    }
  }

  /**
   * Get harvest estimates for a deal
   * @param dealId Deal ID
   * @returns Array of estimate IDs
   */
  getHarvestEstimatesForDeal(dealId: string): string[] {
    try {
      const estimates = this.db.prepare(`
        SELECT estimate_id as estimateId
        FROM deal_estimate
        WHERE deal_id = ? AND estimate_type = ? AND deleted_at IS NULL
      `).all(dealId, EstimateType.HARVEST);

      return estimates.map(e => e.estimateId);
    } catch (error) {
      console.error('Error getting harvest estimates for deal:', error);
      return [];
    }
  }

  /**
   * Get all deals for an estimate
   * @param estimateId Estimate ID
   * @returns Array of deal IDs with estimate types
   */
  getDealsForEstimate(estimateId: string): Array<{ dealId: string; estimateType: string }> {
    try {
      const deals = this.db.prepare(`
        SELECT deal_id as dealId, estimate_type as estimateType
        FROM deal_estimate
        WHERE estimate_id = ? AND deleted_at IS NULL
      `).all(estimateId);

      return deals;
    } catch (error) {
      console.error('Error getting deals for estimate:', error);
      return [];
    }
  }

  /**
   * Check if a deal is linked to an estimate
   * @param dealId Deal ID
   * @param estimateId Estimate ID
   * @returns Boolean indicating if the link exists
   */
  isDealLinkedToEstimate(dealId: string, estimateId: string): boolean {
    try {
      const result = this.db.prepare(`
        SELECT 1
        FROM deal_estimate
        WHERE deal_id = ? AND estimate_id = ? AND deleted_at IS NULL
      `).get(dealId, estimateId);

      return !!result;
    } catch (error) {
      console.error('Error checking if deal is linked to estimate:', error);
      return false;
    }
  }

  /**
   * Get all deal-estimate relationships
   * @returns Array of all deal-estimate relationships
   */
  getAllDealEstimates(): Array<{
    id: string;
    deal_id: string;
    estimate_id: string;
    estimate_type: string;
    linked_at: string;
    linked_by: string;
  }> {
    try {
      return this.db.prepare(`
        SELECT id, deal_id, estimate_id, estimate_type, linked_at, linked_by
        FROM deal_estimate
        ORDER BY linked_at DESC
      `).all();
    } catch (error) {
      console.error('Error fetching all deal-estimate relationships:', error);
      return [];
    }
  }

  /**
   * Get estimates by deal (alias for getEstimatesForDeal)
   * @param dealId The deal ID
   * @returns Array of estimates with relationship details
   */
  getEstimatesByDeal(dealId: string): Array<{
    deal_id: string;
    estimate_id: string;
    estimate_type: string;
  }> {
    try {
      return this.db.prepare(`
        SELECT deal_id, estimate_id, estimate_type
        FROM deal_estimate
        WHERE deal_id = ?
        ORDER BY linked_at DESC
      `).all(dealId);
    } catch (error) {
      console.error('Error fetching estimates by deal:', error);
      return [];
    }
  }

  /**
   * Get deals by estimate (alias for getDealsForEstimate)
   * @param estimateId The estimate ID
   * @returns Array of deals with relationship details
   */
  getDealsByEstimate(estimateId: string): Array<{
    deal_id: string;
    estimate_id: string;
    estimate_type: string;
  }> {
    try {
      return this.db.prepare(`
        SELECT deal_id, estimate_id, estimate_type
        FROM deal_estimate
        WHERE estimate_id = ?
        ORDER BY linked_at DESC
      `).all(estimateId);
    } catch (error) {
      console.error('Error fetching deals by estimate:", error);
      return [];
    }
  }
}
