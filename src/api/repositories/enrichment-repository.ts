/**
 * Repository for managing enrichment data
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";
import { EnrichmentSource } from "../services/enrichment/enrichment-service";

export interface CompanyEnrichmentCreate {
  companyId: string;
  source: EnrichmentSource;
  sourceId?: string;
  data: any;
  confidence: number;
  expiresAt?: Date;
  createdBy?: string;
}

export interface ContactEnrichmentCreate {
  contactId: string;
  source: EnrichmentSource;
  sourceId?: string;
  data: any;
  confidence: number;
  expiresAt?: Date;
  createdBy?: string;
}

export interface EnrichmentLogEntry {
  entityType: 'company' | 'contact';
  entityId: string;
  source: EnrichmentSource;
  status: "success" | 'failed' | 'no_match' | 'rate_limited';
  errorMessage?: string;
  responseTimeMs?: number;
  apiCreditsUsed?: number;
}

/**
 * Repository for enrichment data operations
 */
export class EnrichmentRepository extends BaseRepository {
  constructor() {
    super('company_enrichment'); // Use company_enrichment as the primary table
  }

  /**
   * Save company enrichment data
   */
  async saveCompanyEnrichment(data: CompanyEnrichmentCreate): Promise<string> {
    const id = uuidv4();
    const now = new Date().toISOString();

    try {
      this.db.prepare(`
        INSERT INTO company_enrichment (
          id, company_id, source, source_id, data, confidence_score,
          enriched_at, expires_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        data.companyId,
        data.source,
        data.sourceId || null,
        JSON.stringify(data.data),
        data.confidence,
        now,
        data.expiresAt ? data.expiresAt.toISOString() : null,
        data.createdBy || 'system'
      );

      return id;
    } catch (error) {
      console.error('Error saving company enrichment:', error);
      throw error;
    }
  }

  /**
   * Save contact enrichment data
   */
  async saveContactEnrichment(data: ContactEnrichmentCreate): Promise<string> {
    const id = uuidv4();
    const now = new Date().toISOString();

    try {
      this.db.prepare(`
        INSERT INTO contact_enrichment (
          id, contact_id, source, source_id, data, confidence_score,
          enriched_at, expires_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        data.contactId,
        data.source,
        data.sourceId || null,
        JSON.stringify(data.data),
        data.confidence,
        now,
        data.expiresAt ? data.expiresAt.toISOString() : null,
        data.createdBy || 'system'
      );

      return id;
    } catch (error) {
      console.error('Error saving contact enrichment:', error);
      throw error;
    }
  }

  /**
   * Get company enrichment data
   */
  async getCompanyEnrichment(companyId: string, source?: EnrichmentSource): Promise<any[]> {
    try {
      let query = `
        SELECT 
          id, company_id as companyId, source, source_id as sourceId,
          data, confidence_score as confidence, enriched_at as enrichedAt,
          expires_at as expiresAt, created_by as createdBy
        FROM company_enrichment
        WHERE company_id = ?
      `;

      const params: any[] = [companyId];

      if (source) {
        query += ' AND source = ?';
        params.push(source);
      }

      query += ' ORDER BY enriched_at DESC';

      const rows = this.db.prepare(query).all(...params) as any[];

      // Parse JSON data
      return rows.map(row => ({
        ...row,
        data: JSON.parse(row.data || '{}')
      }));
    } catch (error) {
      console.error('Error getting company enrichment:', error);
      return [];
    }
  }

  /**
   * Get contact enrichment data
   */
  async getContactEnrichment(contactId: string, source?: EnrichmentSource): Promise<any[]> {
    try {
      let query = `
        SELECT 
          id, contact_id as contactId, source, source_id as sourceId,
          data, confidence_score as confidence, enriched_at as enrichedAt,
          expires_at as expiresAt, created_by as createdBy
        FROM contact_enrichment
        WHERE contact_id = ?
      `;

      const params: any[] = [contactId];

      if (source) {
        query += ' AND source = ?';
        params.push(source);
      }

      query += ' ORDER BY enriched_at DESC';

      const rows = this.db.prepare(query).all(...params) as any[];

      // Parse JSON data
      return rows.map(row => ({
        ...row,
        data: JSON.parse(row.data || '{}')
      }));
    } catch (error) {
      console.error('Error getting contact enrichment:', error);
      return [];
    }
  }

  /**
   * Get latest enrichment for a company from a specific source
   */
  async getLatestCompanyEnrichment(companyId: string, source: EnrichmentSource): Promise<any | null> {
    try {
      const row = this.db.prepare(`
        SELECT 
          id, company_id as companyId, source, source_id as sourceId,
          data, confidence_score as confidence, enriched_at as enrichedAt,
          expires_at as expiresAt, created_by as createdBy
        FROM company_enrichment
        WHERE company_id = ? AND source = ?
        ORDER BY enriched_at DESC
        LIMIT 1
      `).get(companyId, source) as any;

      if (!row) return null;

      return {
        ...row,
        data: JSON.parse(row.data || '{}')
      };
    } catch (error) {
      console.error('Error getting latest company enrichment:', error);
      return null;
    }
  }

  /**
   * Update company enrichment status
   */
  async updateCompanyEnrichmentStatus(companyId: string, status: any): Promise<void> {
    try {
      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE company
        SET 
          enrichment_status = ?,
          last_enriched_at = ?
        WHERE id = ?
      `).run(
        JSON.stringify(status),
        now,
        companyId
      );
    } catch (error) {
      console.error('Error updating company enrichment status:', error);
      throw error;
    }
  }

  /**
   * Update contact enrichment status
   */
  async updateContactEnrichmentStatus(contactId: string, status: any): Promise<void> {
    try {
      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE contact
        SET 
          enrichment_status = ?,
          last_enriched_at = ?
        WHERE id = ?
      `).run(
        JSON.stringify(status),
        now,
        contactId
      );
    } catch (error) {
      console.error('Error updating contact enrichment status:', error);
      throw error;
    }
  }

  /**
   * Log enrichment attempt
   */
  async logEnrichmentAttempt(entry: EnrichmentLogEntry): Promise<void> {
    const id = uuidv4();
    const now = new Date().toISOString();

    try {
      this.db.prepare(`
        INSERT INTO enrichment_log (
          id, entity_type, entity_id, source, status, error_message,
          attempted_at, response_time_ms, api_credits_used
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        entry.entityType,
        entry.entityId,
        entry.source,
        entry.status,
        entry.errorMessage || null,
        now,
        entry.responseTimeMs || null,
        entry.apiCreditsUsed || 0
      );
    } catch (error) {
      console.error('Error logging enrichment attempt:', error);
      // Don't throw - logging should not break the main flow
    }
  }

  /**
   * Get companies that need enrichment
   */
  async getCompaniesNeedingEnrichment(limit: number = 100): Promise<any[]> {
    try {
      const now = new Date().toISOString();

      // Get companies that have never been enriched or have expired enrichment
      const companies = this.db.prepare(`
        SELECT 
          c.id, c.name, c.website, c.address,
          c.enrichment_status as enrichmentStatus,
          c.last_enriched_at as lastEnrichedAt
        FROM company c
        WHERE c.deleted_at IS NULL
          AND (
            c.last_enriched_at IS NULL
            OR EXISTS (
              SELECT 1 FROM company_enrichment ce
              WHERE ce.company_id = c.id
                AND ce.expires_at IS NOT NULL
                AND ce.expires_at < ?
            )
          )
        ORDER BY 
          c.last_enriched_at ASC NULLS FIRST,
          c.created_at DESC
        LIMIT ?
      `).all(now, limit) as any[];

      return companies.map(company => ({
        ...company,
        enrichmentStatus: company.enrichmentStatus ? JSON.parse(company.enrichmentStatus) : null
      }));
    } catch (error) {
      console.error('Error getting companies needing enrichment:', error);
      return [];
    }
  }

  /**
   * Get contacts that need enrichment
   */
  async getContactsNeedingEnrichment(limit: number = 100): Promise<any[]> {
    try {
      const now = new Date().toISOString();

      // Get contacts that have never been enriched or have expired enrichment
      const contacts = this.db.prepare(`
        SELECT 
          c.id, c.first_name as firstName, c.last_name as lastName,
          c.email, c.company_id as companyId,
          c.enrichment_status as enrichmentStatus,
          c.last_enriched_at as lastEnrichedAt
        FROM contact c
        WHERE c.deleted_at IS NULL
          AND (
            c.last_enriched_at IS NULL
            OR EXISTS (
              SELECT 1 FROM contact_enrichment ce
              WHERE ce.contact_id = c.id
                AND ce.expires_at IS NOT NULL
                AND ce.expires_at < ?
            )
          )
        ORDER BY 
          c.last_enriched_at ASC NULLS FIRST,
          c.created_at DESC
        LIMIT ?
      `).all(now, limit) as any[];

      return contacts.map(contact => ({
        ...contact,
        enrichmentStatus: contact.enrichmentStatus ? JSON.parse(contact.enrichmentStatus) : null
      }));
    } catch (error) {
      console.error('Error getting contacts needing enrichment:', error);
      return [];
    }
  }

  /**
   * Delete expired enrichment data
   */
  async deleteExpiredEnrichments(): Promise<number> {
    try {
      const now = new Date().toISOString();
      let deletedCount = 0;

      // Delete expired company enrichments
      const companyResult = this.db.prepare(`
        DELETE FROM company_enrichment
        WHERE expires_at IS NOT NULL AND expires_at < ?
      `).run(now);
      deletedCount += companyResult.changes;

      // Delete expired contact enrichments
      const contactResult = this.db.prepare(`
        DELETE FROM contact_enrichment
        WHERE expires_at IS NOT NULL AND expires_at < ?
      `).run(now);
      deletedCount += contactResult.changes;

      return deletedCount;
    } catch (error) {
      console.error('Error deleting expired enrichments:', error);
      return 0;
    }
  }
}