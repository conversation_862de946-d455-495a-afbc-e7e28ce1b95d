import db from "../services/db-service";
import { BaseRepositoryEnhanced, EnhancedQueryOptions } from "./base-repository-enhanced";
import { CustomExpense } from "../../types";
import { v4 as uuidv4 } from "uuid";

/**
 * Repository for managing expenses in SQLite
 */
export class ExpensesRepository extends BaseRepositoryEnhanced {
  constructor() {
    super('expense');
  }
  /**
   * Get all expenses
   * @param options Query options including soft delete handling
   */
  getAll(options: EnhancedQueryOptions = {}): CustomExpense[] {
    try {
      const { includeDeleted = false } = options;
      
      const expenses = this.db.prepare(`
        SELECT * FROM expense
        ${includeDeleted ? '' : 'WHERE deleted_at IS NULL'}
      `).all() as any[];

      // Convert date strings to Date objects and ensure source field is included
      return expenses.map(expense => {
        // Parse metadata JSON if it exists
        let metadata = null;
        if (expense.metadata) {
          try {
            metadata = JSON.parse(expense.metadata);
          } catch (e) {
            console.error('Error parsing metadata JSON:', e);
          }
        }

        return {
          ...expense,
          date: new Date(expense.date),
          amount: Number(expense.amount),
          repeatCount: expense.repeat_count ? Number(expense.repeat_count) : undefined,
          source: expense.source || null,
          description: expense.description || null,
          metadata: metadata
        };
      });
    } catch (error) {
      console.error('Error fetching all expenses from database:', error);
      // Return empty array instead of crashing
      return [];
    }
  }

  /**
   * Get expense by id
   * @param id Expense ID
   * @param options Query options including soft delete handling
   */
  getById(id: string, options: EnhancedQueryOptions = {}): CustomExpense | null {
    try {
      const { includeDeleted = false } = options;
      
      const expense = this.db.prepare(`
        SELECT * FROM expense 
        WHERE id = ?
          ${includeDeleted ? '' : 'AND deleted_at IS NULL'}
      `).get(id) as any;

      if (!expense) return null;

      // Parse metadata JSON if it exists
      let metadata = null;
      if (expense.metadata) {
        try {
          metadata = JSON.parse(expense.metadata);
        } catch (e) {
          console.error('Error parsing metadata JSON:', e);
        }
      }

      return {
        ...expense,
        date: new Date(expense.date),
        amount: Number(expense.amount),
        repeatCount: expense.repeat_count ? Number(expense.repeat_count) : undefined,
        source: expense.source || null,
        description: expense.description || null,
        metadata: metadata
      };
    } catch (error) {
      console.error('Error fetching expense by ID from database:', error);
      return null;
    }
  }

  /**
   * Create a new expense
   */
  create(expense: Omit<CustomExpense, 'id'>): CustomExpense {
    try {
      console.log('Creating expense with data:', JSON.stringify(expense));

      // Validate required fields
      if (!expense.name || !expense.type || expense.amount === undefined || !expense.date || !expense.frequency) {
        console.error('Cannot create expense: missing required fields');
        throw new Error('Missing required fields for expense creation');
      }

      const id = uuidv4();
      const now = new Date().toISOString();
      const newExpense: CustomExpense = {
        ...expense,
        id
      };

      // Convert metadata to JSON string if it exists
      const metadataJson = newExpense.metadata ? JSON.stringify(newExpense.metadata) : null;

      // Store date as ISO string
      this.db.prepare(`
        INSERT INTO expense (
          id, name, type, amount, date, frequency, repeat_count,
          source, description, metadata, editable, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        newExpense.name,
        newExpense.type,
        newExpense.amount,
        newExpense.date.toISOString(),
        newExpense.frequency,
        newExpense.repeatCount || null,
        newExpense.source || 'manual', // Default to 'manual' if source is not provided
        newExpense.description || null,
        metadataJson,
        1, // editable = true for manually created expenses
        now,
        now
      );

      console.log('Successfully created expense with ID:', id);
      return newExpense;
    } catch (error) {
      console.error('Error creating expense in database:', error);
      throw new Error(`Database error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update an existing expense
   */
  update(id: string, expense: Omit<CustomExpense, 'id'>): CustomExpense | null {
    try {
      // Check if expense exists
      const existing = this.getById(id);
      if (!existing) return null;

      // Check if expense is editable
      const editable = this.db.prepare('SELECT editable FROM expense WHERE id = ?').get(id) as { editable: number };
      if (editable && editable.editable === 0) {
        console.error(`Cannot update expense ${id} because it is not editable (synced from external system)`);
        throw new Error('Cannot update expense because it is synced from an external system');
      }

      // Validate required fields
      if (!expense.name || !expense.type || expense.amount === undefined || !expense.date || !expense.frequency) {
        console.error('Cannot update expense: missing required fields');
        throw new Error('Missing required fields for expense update');
      }

      const updatedExpense: CustomExpense = {
        ...expense,
        id
      };

      const now = new Date().toISOString();

      // Convert metadata to JSON string if it exists
      const metadataJson = updatedExpense.metadata ? JSON.stringify(updatedExpense.metadata) : null;

      // Update the expense
      this.db.prepare(`
        UPDATE expense
        SET name = ?, type = ?, amount = ?, date = ?, frequency = ?,
            repeat_count = ?, source = ?, description = ?, metadata = ?, updated_at = ?
        WHERE id = ?
      `).run(
        updatedExpense.name,
        updatedExpense.type,
        updatedExpense.amount,
        updatedExpense.date.toISOString(),
        updatedExpense.frequency,
        updatedExpense.repeatCount || null,
        updatedExpense.source || 'manual', // Default to 'manual' if source is not provided
        updatedExpense.description || null,
        metadataJson,
        now,
        id
      );

      return updatedExpense;
    } catch (error) {
      console.error('Error updating expense in database:', error);
      return null;
    }
  }

  /**
   * Delete an expense
   * @param id Expense ID
   * @param permanent Whether to permanently delete (default: false for soft delete)
   */
  delete(id: string, permanent: boolean = false): boolean {
    try {
      if (permanent) {
        const result = this.db.prepare('DELETE FROM expense WHERE id = ?').run(id);
        return result.changes > 0;
      } else {
        // Soft delete
        const now = new Date().toISOString();
        const result = this.db.prepare(`
          UPDATE expense SET deleted_at = ? WHERE id = ? AND deleted_at IS NULL
        `).run(now, id);
        return result.changes > 0;
      }
    } catch (error) {
      console.error('Error deleting expense from database:', error);
      return false;
    }
  }
}