/**
 * Repository for managing notes with conversation threading support
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";
import { Note, NoteCreate } from "../../frontend/types/crm-types";

/**
 * Conversation types
 */
export type ConversationType = 'email' | 'call' | 'meeting' | 'slack' | 'internal';

/**
 * Note status types
 */
export type NoteStatus = 'open' | 'resolved' | 'parked' | 'archived';

/**
 * Extended note interface with threading support
 */
export interface ThreadedNote extends Note {
  parentNoteId?: string;
  threadId?: string;
  participants?: string[]; // Array of contact IDs
  conversationType?: ConversationType;
  status?: NoteStatus;
  replies?: ThreadedNote[]; // Child notes in the thread
}

/**
 * Note create interface with threading support
 */
export interface ThreadedNoteCreate extends NoteCreate {
  parentNoteId?: string;
  threadId?: string;
  participants?: string[];
  conversationType?: ConversationType;
  status?: NoteStatus;
}

/**
 * Conversation thread summary
 */
export interface ConversationThread {
  threadId: string;
  entityType: string;
  entityId: string;
  topic: string; // First note's content as topic
  participantCount: number;
  messageCount: number;
  status: NoteStatus;
  conversationType?: ConversationType;
  lastActivity: string;
  createdAt: string;
}

/**
 * Repository for managing notes with conversation threading
 */
export class NoteRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('note');
  }

  /**
   * Get all notes
   * @returns Array of notes
   */
  getAllNotes(): Note[] {
    try {
      const notes = this.db.prepare(`
        SELECT
          id,
          entity_id as dealId,
          entity_type,
          content,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE deleted_at IS NULL
        ORDER BY created_at DESC
      `).all() as Note[];

      return notes;
    } catch (error) {
      console.error('Error fetching notes from database:', error);
      return [];
    }
  }

  /**
   * Get all notes with threading information
   * @returns Array of threaded notes
   */
  getAllThreadedNotes(): ThreadedNote[] {
    try {
      const notes = this.db.prepare(`
        SELECT
          id,
          entity_id as dealId,
          entity_type,
          content,
          parent_note_id as parentNoteId,
          thread_id as threadId,
          participants,
          conversation_type as conversationType,
          status,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE deleted_at IS NULL
        ORDER BY thread_id, created_at ASC
      `).all() as any[];

      return notes.map(note => ({
        ...note,
        participants: note.participants ? JSON.parse(note.participants) : []
      }));
    } catch (error) {
      console.error('Error fetching threaded notes from database:', error);
      return [];
    }
  }

  /**
   * Get note by ID
   * @param id Note ID
   * @returns Note or null if not found
   */
  getNoteById(id: string): Note | null {
    try {
      const note = this.db.prepare(`
        SELECT
          id,
          entity_id as dealId,
          entity_type,
          content,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE id = ? AND deleted_at IS NULL
      `).get(id) as Note | undefined;

      return note || null;
    } catch (error) {
      console.error('Error fetching note by ID from database:', error);
      return null;
    }
  }

  /**
   * Get notes by deal ID
   * @param dealId Deal ID
   * @returns Array of notes
   */
  getNotesByDealId(dealId: string): Note[] {
    try {
      const notes = this.db.prepare(`
        SELECT
          id,
          entity_id as dealId,
          entity_type,
          content,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE entity_type = 'deal' AND entity_id = ? AND deleted_at IS NULL
        ORDER BY created_at DESC
      `).all(dealId) as Note[];

      return notes;
    } catch (error) {
      console.error('Error fetching notes by deal ID from database:', error);
      return [];
    }
  }

  /**
   * Create a new note
   * @param noteData Note data
   * @returns Created note
   */
  createNote(noteData: NoteCreate): Note {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO note (
          id,
          entity_type,
          entity_id,
          content,
          note_type,
          created_at,
          created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        'deal', // Default to deal type for backward compatibility
        noteData.dealId,
        noteData.content,
        'general', // Default note type
        now,
        noteData.createdBy || 'system'
      );

      return this.getNoteById(id) as Note;
    } catch (error) {
      console.error('Error creating note in database:', error);
      throw error;
    }
  }

  /**
   * Update a note
   * @param id Note ID
   * @param content New content
   * @returns Updated note
   */
  updateNote(id: string, content: string): Note | null {
    try {
      const note = this.getNoteById(id);
      if (!note) {
        console.error(`Note with ID ${id} not found`);
        return null;
      }

      this.db.prepare(`
        UPDATE note
        SET content = ?
        WHERE id = ?
      `).run(content, id);

      return this.getNoteById(id);
    } catch (error) {
      console.error('Error updating note in database:', error);
      throw error;
    }
  }

  /**
   * Delete a note
   * @param id Note ID
   * @returns Boolean indicating success
   */
  deleteNote(id: string): boolean {
    try {
      const now = new Date().toISOString();
      this.db.prepare(`
        UPDATE note
        SET deleted_at = ?
        WHERE id = ? AND deleted_at IS NULL
      `).run(now, id);
      return true;
    } catch (error) {
      console.error('Error deleting note from database:', error);
      return false;
    }
  }

  /**
   * Delete all notes for a deal
   * @param dealId Deal ID
   * @returns Number of notes deleted
   */
  deleteNotesByDealId(dealId: string): number {
    try {
      const result = this.db.prepare(`
        DELETE FROM note
        WHERE entity_type = 'deal' AND entity_id = ?
      `).run(dealId);
      return result.changes;
    } catch (error) {
      console.error('Error deleting notes by deal ID from database:', error);
      return 0;
    }
  }

  /**
   * Get conversation threads for an entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @returns Array of conversation threads
   */
  getConversationThreads(entityType: string, entityId: string): ConversationThread[] {
    try {
      const threads = this.db.prepare(`
        SELECT 
          thread_id,
          entity_type,
          entity_id,
          MIN(content) as topic,
          COUNT(DISTINCT participants) as participant_count,
          COUNT(*) as message_count,
          MAX(status) as status,
          MAX(conversation_type) as conversation_type,
          MAX(created_at) as last_activity,
          MIN(created_at) as created_at
        FROM note
        WHERE entity_type = ? AND entity_id = ? AND thread_id IS NOT NULL AND deleted_at IS NULL
        GROUP BY thread_id
        ORDER BY last_activity DESC
      `).all(entityType, entityId) as any[];

      return threads.map(thread => ({
        threadId: thread.thread_id,
        entityType: thread.entity_type,
        entityId: thread.entity_id,
        topic: thread.topic,
        participantCount: thread.participant_count || 0,
        messageCount: thread.message_count,
        status: thread.status || 'open',
        conversationType: thread.conversation_type,
        lastActivity: thread.last_activity,
        createdAt: thread.created_at
      }));
    } catch (error) {
      console.error('Error fetching conversation threads:', error);
      return [];
    }
  }

  /**
   * Get notes in a thread
   * @param threadId Thread ID
   * @returns Array of threaded notes with hierarchy
   */
  getThreadNotes(threadId: string): ThreadedNote[] {
    try {
      const notes = this.db.prepare(`
        SELECT
          id,
          entity_id as dealId,
          entity_type,
          content,
          parent_note_id as parentNoteId,
          thread_id as threadId,
          participants,
          conversation_type as conversationType,
          status,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE thread_id = ? AND deleted_at IS NULL
        ORDER BY created_at ASC
      `).all(threadId) as any[];

      // Parse participants and build hierarchy
      const notesMap = new Map<string, ThreadedNote>();
      const rootNotes: ThreadedNote[] = [];

      notes.forEach(note => {
        const threadedNote: ThreadedNote = {
          ...note,
          participants: note.participants ? JSON.parse(note.participants) : [],
          replies: []
        };
        notesMap.set(note.id, threadedNote);
      });

      // Build hierarchy
      notes.forEach(note => {
        const threadedNote = notesMap.get(note.id)!;
        if (note.parentNoteId) {
          const parent = notesMap.get(note.parentNoteId);
          if (parent) {
            parent.replies = parent.replies || [];
            parent.replies.push(threadedNote);
          }
        } else {
          rootNotes.push(threadedNote);
        }
      });

      return rootNotes;
    } catch (error) {
      console.error('Error fetching thread notes:', error);
      return [];
    }
  }

  /**
   * Create a threaded note
   * @param noteData Note data with threading information
   * @returns Created threaded note
   */
  createThreadedNote(noteData: ThreadedNoteCreate): ThreadedNote {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();
      
      // Generate thread ID if this is a new thread
      const threadId = noteData.threadId || (noteData.parentNoteId ? null : uuidv4());

      // If this is a reply, get the thread ID from the parent
      let finalThreadId = threadId;
      if (noteData.parentNoteId && !threadId) {
        const parent = this.db.prepare(`
          SELECT thread_id FROM note WHERE id = ? AND deleted_at IS NULL
        `).get(noteData.parentNoteId) as { thread_id: string } | undefined;
        
        if (parent) {
          finalThreadId = parent.thread_id;
        }
      }

      this.db.prepare(`
        INSERT INTO note (
          id,
          entity_type,
          entity_id,
          content,
          parent_note_id,
          thread_id,
          participants,
          conversation_type,
          status,
          created_at,
          created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        'deal', // Default to deal type for backward compatibility
        noteData.dealId,
        noteData.content,
        noteData.parentNoteId || null,
        finalThreadId,
        noteData.participants ? JSON.stringify(noteData.participants) : null,
        noteData.conversationType || null,
        noteData.status || 'open',
        now,
        noteData.createdBy || 'system'
      );

      return this.getThreadedNoteById(id) as ThreadedNote;
    } catch (error) {
      console.error('Error creating threaded note:', error);
      throw error;
    }
  }

  /**
   * Get a threaded note by ID
   * @param id Note ID
   * @returns Threaded note or null
   */
  getThreadedNoteById(id: string): ThreadedNote | null {
    try {
      const note = this.db.prepare(`
        SELECT
          id,
          entity_id as dealId,
          entity_type,
          content,
          parent_note_id as parentNoteId,
          thread_id as threadId,
          participants,
          conversation_type as conversationType,
          status,
          created_at as createdAt,
          created_by as createdBy
        FROM note
        WHERE id = ? AND deleted_at IS NULL
      `).get(id) as any;

      if (!note) return null;

      return {
        ...note,
        participants: note.participants ? JSON.parse(note.participants) : []
      };
    } catch (error) {
      console.error('Error fetching threaded note by ID:', error);
      return null;
    }
  }

  /**
   * Update note status
   * @param id Note ID
   * @param status New status
   * @returns Updated note
   */
  updateNoteStatus(id: string, status: NoteStatus): ThreadedNote | null {
    try {
      this.db.prepare(`
        UPDATE note
        SET status = ?
        WHERE id = ?
      `).run(status, id);

      // If updating a thread root, update all notes in thread
      const note = this.getThreadedNoteById(id);
      if (note && note.threadId && !note.parentNoteId) {
        this.db.prepare(`
          UPDATE note
          SET status = ?
          WHERE thread_id = ?
        `).run(status, note.threadId);
      }

      return this.getThreadedNoteById(id);
    } catch (error) {
      console.error('Error updating note status:', error);
      return null;
    }
  }
}
