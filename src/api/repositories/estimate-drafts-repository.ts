import db from "../services/db-service";
import { BaseRepositoryEnhanced, EnhancedQueryOptions } from "./base-repository-enhanced";
import {
  DraftEstimate,
  DraftEstimateCreate,
  DraftEstimateUpdate,
  DraftEstimateSummary,
  DraftEstimateAllocation,
  DraftEstimateTimeAllocation
} from "../../types/api";
import { v4 as uuidv4 } from "uuid";
import { calculateTotalFeesWithBilling } from "../utils/billing-calculations";

/**
 * Get ISO week number from a date
 */
function getWeekNumber(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
}

/**
 * Repository for managing draft estimates in SQLite
 */
export class EstimateDraftsRepository extends BaseRepositoryEnhanced {
  // Using the actual SQLite database mounted at /disk for all environments
  
  constructor() {
    super('estimate');
  }

  /**
   * Get all draft estimates (visible to all authenticated users)
   * @param options Query options including soft delete handling
   */
  findAll(options: EnhancedQueryOptions = {}): DraftEstimateSummary[] {
    try {
      const { includeDeleted = false } = options;
      
      const drafts = this.db.prepare(`
        SELECT id as uuid, client_name as clientName, project_name as projectName,
               start_date as startDate, end_date as endDate,
               updated_at as updatedAt, status, harvest_estimate_id as harvestEstimateId,
               created_by as userId, invoice_frequency as invoiceFrequency,
               payment_terms as paymentTerms, billing_type as billingType,
               hours_per_day as hoursPerDay
        FROM estimate
        WHERE status != 'external_placeholder'
        AND (notes IS NULL OR notes != '__PLACEHOLDER_FOR_HARVEST_ESTIMATE__')
        ${includeDeleted ? '' : 'AND deleted_at IS NULL'}
        ORDER BY updated_at DESC
      `).all() as DraftEstimateSummary[];

      // Calculate totalFees for each draft
      return drafts.map(draft => {
        // Get time allocations with rates from the join
        const timeAllocations = this.db.prepare(`
          SELECT 
            eta.days,
            ea.proposed_rate_daily as rateProposedDaily
          FROM estimate_time_allocation eta
          JOIN estimate_allocation ea ON eta.allocation_id = ea.id
          WHERE ea.estimate_id = ?
        `).all(draft.uuid) as { days: number; rateProposedDaily: number }[];

        // Calculate total fees
        let totalFees = 0;
        timeAllocations.forEach(ta => {
          const days = ta.days || 0;
          const rate = ta.rateProposedDaily || 0;
          const lineTotal = days * rate;
          totalFees += lineTotal;
        });

        console.log(`EstimateDraftsRepository.findAll - Calculated totalFees for ${draft.uuid}: ${totalFees} (${timeAllocations.length} time allocations)`);

        return {
          ...draft,
          totalFees
        };
      });
    } catch (error) {
      console.error('Error fetching all draft estimates from database:', error);
      // Return empty array instead of crashing
      return [];
    }
  }

  /**
   * Get a specific draft estimate by UUID with all allocations and time allocations
   * @param uuid Estimate UUID
   * @param options Query options including soft delete handling
   */
  findByUuid(uuid: string, options: EnhancedQueryOptions = {}): DraftEstimate | null {
    try {
      const { includeDeleted = false } = options;
      
      // Use a transaction to ensure atomicity, but don't return the result directly
      let result: DraftEstimate | null = null;

      this.db.transaction(() => {
        // Get the draft estimate
        const draft = this.db.prepare(`
          SELECT
            e.id as uuid, 
            e.company_id as companyId, 
            e.client_name as clientName,
            e.project_name as projectName, 
            e.start_date as startDate,
            e.end_date as endDate, 
            e.created_at as createdAt,
            e.updated_at as updatedAt, 
            e.harvest_estimate_id as harvestEstimateId,
            e.created_by as userId, 
            e.notes, 
            e.status,
            COALESCE(e.discount_type, 'none') as discountType,
            COALESCE(e.discount_value, 0) as discountValue,
            e.invoice_frequency as invoiceFrequency,
            e.payment_terms as paymentTerms,
            COALESCE(e.billing_type, 'daily') as billingType,
            COALESCE(e.hours_per_day, 8.0) as hoursPerDay
          FROM estimate e
          WHERE e.id = ?
            AND e.deleted_at IS NULL
        `).get(uuid) as DraftEstimate | undefined;

        if (!draft) return;

        // Get allocations for this draft
        const allocations = this.db.prepare(`
          SELECT
            id as internalId, harvest_user_id as harvestUserId,
            first_name as firstName, last_name as lastName,
            project_role as projectRole, level,
            cost_rate_daily as onbordCostRateDaily,
            target_rate_daily as onbordTargetRateDaily,
            proposed_rate_daily as rateProposedDaily,
            rate_type as rateType,
            rate_as_entered as rateAsEntered
          FROM estimate_allocation
          WHERE estimate_id = ?
        `).all(uuid) as Omit<DraftEstimateAllocation, 'timeAllocations'>[];

        // For each allocation, get its time allocations
        const allocationWithTimeAllocations: DraftEstimateAllocation[] = allocations.map(allocation => {
          const timeAllocations = this.db.prepare(`
            SELECT week_identifier as weekIdentifier, days
            FROM estimate_time_allocation
            WHERE allocation_id = ?
          `).all(allocation.internalId) as DraftEstimateTimeAllocation[];

          return {
            ...allocation,
            timeAllocations
          };
        });

        // Calculate total fees using billing-aware calculation
        let totalFees = 0;
        for (const allocation of allocationWithTimeAllocations) {
          // Calculate total days for this allocation
          const totalDays = allocation.timeAllocations.reduce(
            (sum, ta) => sum + ta.days,
            0
          );

          // Calculate fees based on billing type
          const fees = calculateTotalFeesWithBilling(
            allocation.rateProposedDaily,
            totalDays,
            draft.billingType as 'daily' | 'hourly',
            draft.hoursPerDay,
            (allocation as any).rateType,
            (allocation as any).rateAsEntered
          );
          totalFees += fees;
        }

        // Add allocations and total fees to the draft estimate
        result = {
          ...draft,
          allocations: allocationWithTimeAllocations,
          totalFees
        };
      })();

      return result;
    } catch (error) {
      console.error('Error fetching draft estimate by UUID from database:', error);
      return null;
    }
  }

  /**
   * Create a new draft estimate
   */
  create(draftData: DraftEstimateCreate): DraftEstimate {
    const uuid = draftData.uuid || uuidv4();
    const now = new Date().toISOString();

    try {
      // Validate required fields
      if (!draftData.companyId || !draftData.clientName || !draftData.startDate || !draftData.endDate || !draftData.userId) {
        console.error('Cannot create draft estimate: missing required fields');
        throw new Error('Missing required fields for draft estimate creation');
      }

      // Use a transaction to ensure atomicity
      this.db.transaction(() => {
        // Verify the company exists (optional validation)
        const companyExists = this.db.prepare(`
          SELECT id FROM company WHERE id = ?
        `).get(draftData.companyId);
        
        if (!companyExists) {
          console.error('Cannot create draft estimate: company not found');
          throw new Error('Company not found');
        }

        // Insert into the estimate table
        this.db.prepare(`
          INSERT INTO estimate (
            id, company_id, client_name, project_name,
            start_date, end_date, created_at, updated_at,
            harvest_estimate_id, created_by, updated_by, notes, status,
            discount_type, discount_value,
            invoice_frequency, payment_terms,
            billing_type, hours_per_day
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          uuid,
          draftData.companyId,
          draftData.clientName,
          draftData.projectName || '', // Ensure not null
          draftData.startDate,
          draftData.endDate,
          now,
          now,
          null, // harvest_estimate_id starts as null
          draftData.userId,
          draftData.userId,
          draftData.notes || null,
          'draft',
          draftData.discountType || 'none',
          draftData.discountValue || 0,
          draftData.invoiceFrequency || null,
          draftData.paymentTerms || null,
          draftData.billingType || 'daily', // default to daily billing
          draftData.hoursPerDay || 7.5 // default to 7.5 hours per day (Australian standard)
        );

        // Insert allocations and their time allocations
        for (const allocation of draftData.allocations) {
          const allocationId = allocation.internalId || uuidv4();

          // Validate required fields for allocation
          if (!allocation.harvestUserId || !allocation.firstName ||
              allocation.onbordTargetRateDaily === undefined ||
              allocation.onbordCostRateDaily === undefined ||
              allocation.rateProposedDaily === undefined) {
            console.error('Cannot create allocation: missing required fields');
            throw new Error('Missing required fields for allocation creation');
          }

          // Insert the allocation
          this.db.prepare(`
            INSERT INTO estimate_allocation (
              id, estimate_id, harvest_user_id,
              first_name, last_name, project_role, level,
              target_rate_daily, cost_rate_daily, proposed_rate_daily,
              created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            allocationId,
            uuid,
            allocation.harvestUserId,
            allocation.firstName,
            allocation.lastName || '',
            allocation.projectRole || null,
            allocation.level || null,
            allocation.onbordTargetRateDaily,
            allocation.onbordCostRateDaily,
            allocation.rateProposedDaily,
            now,
            now
          );

          // Insert time allocations for this allocation
          for (const [weekId, days] of Object.entries(allocation.weeklyAllocation)) {
            if (days > 0) {
              const timeAllocationId = uuidv4();
              
              // Week identifier is already in the correct format from frontend (YYYY-WW)
              let weekIdentifier = weekId;
              
              this.db.prepare(`
                INSERT INTO estimate_time_allocation (
                  id, allocation_id, week_identifier, days
                ) VALUES (?, ?, ?, ?)
              `).run(
                timeAllocationId,
                allocationId,
                weekIdentifier,
                days
              );
            }
          }
        }
      })();

      // After the transaction completes, fetch the created draft
      return this.findByUuid(uuid);
    } catch (error) {
      console.error('Error creating draft estimate in database:', error);
      throw new Error(`Database error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update an existing draft estimate
   */
  update(uuid: string, draftData: DraftEstimateUpdate): DraftEstimate | null {
    const now = new Date().toISOString();

    try {
      // Start by checking if the draft exists
      const existingDraft = this.findByUuid(uuid);
      if (!existingDraft) return null;

      // Use a transaction to ensure atomicity
      this.db.transaction(() => {
        // Build SET clause and parameters for the update
        const updateFields: string[] = ['updated_at = ?', 'updated_by = ?'];
        const updateParams: (string | number | null)[] = [now, draftData.userId || existingDraft.userId];

        if (draftData.companyId !== undefined) {
          // Verify the company exists (optional validation)
          const companyExists = this.db.prepare(`
            SELECT id FROM company WHERE id = ?
          `).get(draftData.companyId);
          
          if (!companyExists) {
            console.error('Cannot update draft estimate: company not found');
            throw new Error('Company not found');
          }
          
          updateFields.push('company_id = ?');
          updateParams.push(draftData.companyId);
        }

        if (draftData.clientName !== undefined) {
          updateFields.push('client_name = ?');
          updateParams.push(draftData.clientName);
        }

        if (draftData.projectName !== undefined) {
          updateFields.push('project_name = ?');
          updateParams.push(draftData.projectName || null);
        }

        if (draftData.startDate !== undefined) {
          updateFields.push('start_date = ?');
          updateParams.push(draftData.startDate);
        }

        if (draftData.endDate !== undefined) {
          updateFields.push('end_date = ?');
          updateParams.push(draftData.endDate);
        }

        if (draftData.notes !== undefined) {
          updateFields.push('notes = ?');
          updateParams.push(draftData.notes || null);
        }

        if (draftData.harvestEstimateId !== undefined) {
          updateFields.push('harvest_estimate_id = ?');
          updateParams.push(draftData.harvestEstimateId);
        }

        if (draftData.status !== undefined) {
          updateFields.push('status = ?');
          updateParams.push(draftData.status);
        }

        if (draftData.discountType !== undefined) {
          updateFields.push('discount_type = ?');
          updateParams.push(draftData.discountType);
        }

        if (draftData.discountValue !== undefined) {
          updateFields.push('discount_value = ?');
          updateParams.push(draftData.discountValue);
        }

        if (draftData.invoiceFrequency !== undefined) {
          updateFields.push('invoice_frequency = ?');
          updateParams.push(draftData.invoiceFrequency);
        }

        if (draftData.paymentTerms !== undefined) {
          updateFields.push('payment_terms = ?');
          updateParams.push(draftData.paymentTerms);
        }

        if (draftData.billingType !== undefined) {
          updateFields.push('billing_type = ?');
          updateParams.push(draftData.billingType);
        }

        if (draftData.hoursPerDay !== undefined) {
          updateFields.push('hours_per_day = ?');
          updateParams.push(draftData.hoursPerDay);
        }

        // Add the UUID as the last parameter for the WHERE clause
        updateParams.push(uuid);

        // Update the draft estimate
        if (updateFields.length > 2) { // More than just updated_at and updated_by
          this.db.prepare(`
            UPDATE estimate
            SET ${updateFields.join(', ')}
            WHERE id = ?
          `).run(...updateParams);
        }

        // Handle allocations if provided
        if (draftData.allocations) {
          // Get the current allocations to determine what to delete/update/insert
          const currentAllocationIds = existingDraft.allocations.map(a => a.internalId);
          const newAllocationIds = draftData.allocations.map(a => a.internalId);

          // Delete allocations that are no longer needed (cascades to time_allocations due to foreign key)
          const toDelete = currentAllocationIds.filter(id => !newAllocationIds.includes(id));
          for (const id of toDelete) {
            this.db.prepare(`
              DELETE FROM estimate_allocation
              WHERE id = ?
            `).run(id);
          }

          // Update or insert allocations
          for (const allocation of draftData.allocations) {
            const exists = currentAllocationIds.includes(allocation.internalId);

            if (exists) {
              // Update existing allocation
              this.db.prepare(`
                UPDATE estimate_allocation
                SET
                  harvest_user_id = ?,
                  first_name = ?,
                  last_name = ?,
                  project_role = ?,
                  level = ?,
                  target_rate_daily = ?,
                  cost_rate_daily = ?,
                  proposed_rate_daily = ?,
                  rate_type = ?,
                  rate_as_entered = ?,
                  updated_at = ?
                WHERE id = ?
              `).run(
                allocation.harvestUserId,
                allocation.firstName,
                allocation.lastName || null,
                allocation.projectRole || null,
                allocation.level || null,
                allocation.onbordTargetRateDaily,
                allocation.onbordCostRateDaily,
                allocation.rateProposedDaily,
                allocation.rateType || 'daily',
                allocation.rateAsEntered || allocation.rateProposedDaily,
                now,
                allocation.internalId
              );

              // Delete existing time allocations for this allocation
              this.db.prepare(`
                DELETE FROM estimate_time_allocation
                WHERE allocation_id = ?
              `).run(allocation.internalId);
            } else {
              // Insert new allocation
              const allocationId = allocation.internalId || uuidv4();

              // Validate required fields for allocation
              if (!allocation.harvestUserId || !allocation.firstName ||
                  allocation.onbordTargetRateDaily === undefined ||
                  allocation.onbordCostRateDaily === undefined ||
                  allocation.rateProposedDaily === undefined) {
                console.error('Cannot create allocation: missing required fields');
                throw new Error('Missing required fields for allocation creation');
              }

              this.db.prepare(`
                INSERT INTO estimate_allocation (
                  id, estimate_id, harvest_user_id,
                  first_name, last_name, project_role, level,
                  target_rate_daily, cost_rate_daily, proposed_rate_daily,
                  rate_type, rate_as_entered,
                  created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `).run(
                allocationId,
                uuid,
                allocation.harvestUserId,
                allocation.firstName,
                allocation.lastName || '',
                allocation.projectRole || null,
                allocation.level || null,
                allocation.onbordTargetRateDaily,
                allocation.onbordCostRateDaily,
                allocation.rateProposedDaily,
                allocation.rateType || 'daily',
                allocation.rateAsEntered || allocation.rateProposedDaily,
                now,
                now
              );

              // Update the allocation.internalId for time allocations if it was generated
              if (!allocation.internalId) {
                allocation.internalId = allocationId;
              }
            }

            // Insert new time allocations
            for (const [weekId, days] of Object.entries(allocation.weeklyAllocation)) {
              if (days > 0) {
                const timeAllocationId = uuidv4();
                this.db.prepare(`
                  INSERT INTO estimate_time_allocation (
                    id, allocation_id, week_identifier, days
                  ) VALUES (?, ?, ?, ?)
                `).run(
                  timeAllocationId,
                  allocation.internalId,
                  weekId,
                  days
                );
              }
            }
          }
        }
      })();

      // After the transaction completes, fetch the updated draft
      return this.findByUuid(uuid);
    } catch (error) {
      console.error('Error updating draft estimate in database:', error);
      return null;
    }
  }

  /**
   * Delete a draft estimate
   */
  delete(uuid: string): boolean {
    try {
      // Use soft delete instead of hard delete
      const now = new Date().toISOString();
      
      // Start a transaction to soft delete everything atomically
      this.db.transaction(() => {
        // Soft delete the estimate
        this.db.prepare(`
          UPDATE estimate 
          SET deleted_at = ?, updated_at = ? 
          WHERE id = ? AND deleted_at IS NULL
        `).run(now, now, uuid);
        
        // Soft delete all allocations for this estimate
        this.db.prepare(`
          UPDATE estimate_allocation 
          SET deleted_at = ?, updated_at = ? 
          WHERE estimate_id = ? AND deleted_at IS NULL
        `).run(now, now, uuid);
        
        // Get all allocation IDs to soft delete their time allocations
        const allocationIds = this.db.prepare(`
          SELECT id FROM estimate_allocation 
          WHERE estimate_id = ? AND deleted_at IS NULL
        `).all(uuid).map((row: any) => row.id);
        
        // Soft delete all time allocations for these allocations
        if (allocationIds.length > 0) {
          const placeholders = allocationIds.map(() => '?').join(',');
          this.db.prepare(`
            UPDATE estimate_time_allocation 
            SET deleted_at = ?, updated_at = ? 
            WHERE allocation_id IN (${placeholders}) AND deleted_at IS NULL
          `).run(now, now, ...allocationIds);
        }
        
        // Soft delete any deal_estimate links
        this.db.prepare(`
          UPDATE deal_estimate 
          SET deleted_at = ?, updated_at = ? 
          WHERE estimate_id = ? AND deleted_at IS NULL
        `).run(now, now, uuid);
      })();
      
      return true;
    } catch (error) {
      console.error('Error soft deleting draft estimate from database:', error);
      return false;
    }
  }

  /**
   * Mark a draft as published when saved to Harvest
   */
  markAsPublished(uuid: string, harvestEstimateId: number): DraftEstimate | null {
    return this.update(uuid, {
      harvestEstimateId,
      status: "published"
    });
  }

  /**
   * Get a draft estimate by UUID (synchronous version for internal use)
   * @deprecated Use the async findByUuid method instead
   */
  getDraftEstimateById(uuid: string): DraftEstimate | null {
    try {
      // Get the draft estimate using the new schema
      // This is a synchronous version of findByUuid for internal use
      let result: DraftEstimate | null = null;

      this.db.transaction(() => {
        // Get the draft estimate
        const draft = this.db.prepare(`
          SELECT
            e.id as uuid, 
            e.company_id as companyId, 
            e.client_name as clientName,
            e.project_name as projectName, 
            e.start_date as startDate,
            e.end_date as endDate, 
            e.created_at as createdAt,
            e.updated_at as updatedAt, 
            e.harvest_estimate_id as harvestEstimateId,
            e.created_by as userId, 
            e.notes, 
            e.status,
            COALESCE(e.discount_type, 'none') as discountType,
            COALESCE(e.discount_value, 0) as discountValue,
            e.invoice_frequency as invoiceFrequency,
            e.payment_terms as paymentTerms,
            COALESCE(e.billing_type, 'daily') as billingType,
            COALESCE(e.hours_per_day, 8.0) as hoursPerDay
          FROM estimate e
          WHERE e.id = ?
            AND e.deleted_at IS NULL
        `).get(uuid) as DraftEstimate | undefined;

        if (!draft) return;

        // Get allocations for this draft
        const allocations = this.db.prepare(`
          SELECT
            id as internalId, harvest_user_id as harvestUserId,
            first_name as firstName, last_name as lastName,
            project_role as projectRole, level,
            cost_rate_daily as onbordCostRateDaily,
            target_rate_daily as onbordTargetRateDaily,
            proposed_rate_daily as rateProposedDaily,
            rate_type as rateType,
            rate_as_entered as rateAsEntered
          FROM estimate_allocation
          WHERE estimate_id = ?
        `).all(uuid) as Omit<DraftEstimateAllocation, 'timeAllocations'>[];

        // For each allocation, get its time allocations
        const allocationWithTimeAllocations = allocations.map(allocation => {
          const timeAllocations = this.db.prepare(`
            SELECT week_identifier as weekIdentifier, days
            FROM estimate_time_allocation
            WHERE allocation_id = ?
          `).all(allocation.internalId) as DraftEstimateTimeAllocation[];

          return {
            ...allocation,
            timeAllocations
          };
        });

        // Calculate total fees using billing-aware calculation
        let totalFees = 0;
        for (const allocation of allocationWithTimeAllocations) {
          // Calculate total days for this allocation
          const totalDays = allocation.timeAllocations.reduce(
            (sum, ta) => sum + ta.days,
            0
          );

          // Calculate fees based on billing type
          const fees = calculateTotalFeesWithBilling(
            allocation.rateProposedDaily,
            totalDays,
            draft.billingType as 'daily' | 'hourly',
            draft.hoursPerDay,
            (allocation as any).rateType,
            (allocation as any).rateAsEntered
          );
          totalFees += fees;
        }

        // Add allocations and total fees to the draft estimate
        result = {
          ...draft,
          allocations: allocationWithTimeAllocations,
          totalFees
        };
      })();

      return result;
    } catch (error) {
      console.error('Error fetching draft estimate by UUID from database:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const estimateDraftsRepository = new EstimateDraftsRepository();
