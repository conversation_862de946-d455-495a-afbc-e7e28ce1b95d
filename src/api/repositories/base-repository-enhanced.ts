/**
 * Base Repository Enhanced
 * 
 * This class extends BaseRepository to provide enhanced functionality including:
 * - Automatic soft delete filtering by default
 * - Support for including/excluding soft-deleted records
 * - Enhanced query methods with soft delete awareness
 * - Backward compatible with existing repositories
 */

import { BaseRepository, BaseEntity } from "./base-repository";

/**
 * Options for enhanced query methods
 */
export interface EnhancedQueryOptions {
  includeDeleted?: boolean;
}

/**
 * Enhanced base repository class with automatic soft delete filtering
 */
export class BaseRepositoryEnhanced extends BaseRepository {
  /**
   * Constructor
   * @param tableName The name of the table this repository manages
   */
  constructor(tableName: string) {
    super(tableName);
  }

  /**
   * Get all records from the table with soft delete filtering
   * @param conditions Object mapping column names to values for WHERE clause
   * @param options Query options including soft delete handling
   * @returns Array of records
   */
  protected getAllEnhanced<T extends BaseEntity>(
    conditions?: Record<string, any>,
    options: EnhancedQueryOptions = {}
  ): T[] {
    try {
      const { includeDeleted = false } = options;
      
      // Add soft delete filter to conditions if not including deleted records
      if (!includeDeleted && this.columnExists('deleted_at')) {
        conditions = {
          ...conditions,
          deleted_at: null
        };
      }
      
      return this.getAll<T>(conditions);
    } catch (error) {
      console.error(`Failed to get enhanced records from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get a record by ID with soft delete filtering
   * @param id Record ID
   * @param options Query options including soft delete handling
   * @returns Record or null if not found or soft deleted
   */
  protected getByIdEnhanced<T extends BaseEntity>(
    id: string,
    options: EnhancedQueryOptions = {}
  ): T | null {
    try {
      const { includeDeleted = false } = options;
      
      const record = this.getById<T>(id);
      
      // If record not found, return null
      if (!record) return null;
      
      // If not including deleted records and record is soft deleted, return null
      if (!includeDeleted && this.columnExists('deleted_at') && record.deletedAt) {
        return null;
      }
      
      return record;
    } catch (error) {
      console.error(`Failed to get enhanced record by ID from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get records by a specific field with soft delete filtering
   * @param field Field name to search by
   * @param value Value to search for
   * @param options Query options including soft delete handling
   * @returns Array of records
   */
  protected getByFieldEnhanced<T extends BaseEntity>(
    field: string,
    value: any,
    options: EnhancedQueryOptions = {}
  ): T[] {
    try {
      const { includeDeleted = false } = options;
      
      const conditions: Record<string, any> = {
        [field]: value
      };
      
      // Add soft delete filter if not including deleted records
      if (!includeDeleted && this.columnExists('deleted_at')) {
        conditions.deleted_at = null;
      }
      
      return this.getAll<T>(conditions);
    } catch (error) {
      console.error(`Failed to get enhanced records by field from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get soft-deleted records only
   * @param conditions Additional conditions for the query
   * @returns Array of soft-deleted records
   */
  protected getDeletedRecords<T extends BaseEntity>(
    conditions?: Record<string, any>
  ): T[] {
    try {
      if (!this.columnExists('deleted_at')) {
        return [];
      }
      
      let query = `SELECT * FROM ${this.tableName} WHERE deleted_at IS NOT NULL`;
      const params: any[] = [];
      
      if (conditions && Object.keys(conditions).length > 0) {
        const validColumns = this.getColumns();
        const whereClauses: string[] = [];
        
        for (const [column, value] of Object.entries(conditions)) {
          // Skip deleted_at as we're already filtering for it
          if (column === 'deleted_at') continue;
          
          // Validate column name exists
          if (!validColumns.includes(column)) {
            throw new Error(`Invalid column name: ${column}`);
          }
          
          if (value === null) {
            whereClauses.push(`${column} IS NULL`);
          } else if (value === undefined) {
            // Skip undefined values
            continue;
          } else {
            whereClauses.push(`${column} = ?`);
            params.push(value);
          }
        }
        
        if (whereClauses.length > 0) {
          query += ` AND ${whereClauses.join(' AND ')}`;
        }
      }
      
      const statement = this.db.prepare(query);
      
      if (params.length > 0) {
        return statement.all(...params) as T[];
      }
      
      return statement.all() as T[];
    } catch (error) {
      console.error(`Failed to get deleted records from ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Restore a soft-deleted record
   * @param id Record ID
   * @param restoredBy User who restored the record
   * @returns Boolean indicating success
   */
  protected restore(id: string, restoredBy: string = 'system'): boolean {
    try {
      if (!this.columnExists('deleted_at')) {
        throw new Error(`Table ${this.tableName} does not support soft delete`);
      }
      
      const now = new Date().toISOString();
      
      const query = `
        UPDATE ${this.tableName} 
        SET deleted_at = NULL${this.columnExists('updated_at') ? ", updated_at = ?' : ""}${this.columnExists('updated_by') ? ", updated_by = ?" : ""}
        WHERE id = ? AND deleted_at IS NOT NULL
      `;
      
      const params: any[] = [];
      if (this.columnExists('updated_at')) params.push(now);
      if (this.columnExists('updated_by')) params.push(restoredBy);
      params.push(id);
      
      const result = this.db.prepare(query).run(...params);
      
      return result.changes > 0;
    } catch (error) {
      console.error(`Failed to restore record in ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Count records with soft delete filtering
   * @param conditions Conditions for counting
   * @param options Query options including soft delete handling
   * @returns Count of records
   */
  protected countEnhanced(
    conditions?: Record<string, any>,
    options: EnhancedQueryOptions = {}
  ): number {
    try {
      const { includeDeleted = false } = options;
      
      // Add soft delete filter if not including deleted records
      if (!includeDeleted && this.columnExists('deleted_at')) {
        conditions = {
          ...conditions,
          deleted_at: null
        };
      }
      
      let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      const params: any[] = [];
      
      if (conditions && Object.keys(conditions).length > 0) {
        const validColumns = this.getColumns();
        const whereClauses: string[] = [];
        
        for (const [column, value] of Object.entries(conditions)) {
          // Validate column name exists
          if (!validColumns.includes(column)) {
            throw new Error(`Invalid column name: ${column}`);
          }
          
          if (value === null) {
            whereClauses.push(`${column} IS NULL`);
          } else if (value === undefined) {
            // Skip undefined values
            continue;
          } else {
            whereClauses.push(`${column} = ?`);
            params.push(value);
          }
        }
        
        if (whereClauses.length > 0) {
          query += ` WHERE ${whereClauses.join(' AND ')}`;
        }
      }
      
      const statement = this.db.prepare(query);
      const result = params.length > 0 ? statement.get(...params) : statement.get();
      
      return result?.count || 0;
    } catch (error) {
      console.error(`Failed to count records in ${this.tableName}:`, error);
      throw error;
    }
  }
}