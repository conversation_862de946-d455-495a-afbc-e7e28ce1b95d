/**
 * Repository for managing HubSpot settings
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Interface for HubSpot settings entity
 */
export interface HubSpotSetting {
  id: string;
  key: string;
  value?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new HubSpot setting
 */
export interface HubSpotSettingCreate {
  key: string;
  value?: string;
}

/**
 * Interface for updating a HubSpot setting
 */
export interface HubSpotSettingUpdate {
  value?: string;
}

/**
 * Repository for managing HubSpot settings
 */
export class HubSpotSettingsRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super("hubspot_settings");
  }

  /**
   * Get all HubSpot settings
   * @returns Array of settings
   */
  getAllSettings(): HubSpotSetting[] {
    try {
      const settings = this.db
        .prepare(
          `
        SELECT 
          id,
          key,
          value,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        ORDER BY key
      `,
        )
        .all() as HubSpotSetting[];

      return settings;
    } catch (error) {
      console.error("Error fetching all HubSpot settings:", error);
      return [];
    }
  }

  /**
   * Get setting by key
   * @param key Setting key
   * @returns Setting or null if not found
   */
  getSettingByKey(key: string): HubSpotSetting | null {
    try {
      const setting = this.db
        .prepare(
          `
        SELECT 
          id,
          key,
          value,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE key = ?
      `,
        )
        .get(key) as HubSpotSetting | undefined;

      return setting || null;
    } catch (error) {
      console.error(`Error fetching HubSpot setting by key ${key}:`, error);
      return null;
    }
  }

  /**
   * Get setting value by key
   * @param key Setting key
   * @returns Setting value or null if not found
   */
  getSettingValue(key: string): string | null {
    try {
      const setting = this.getSettingByKey(key);
      return setting?.value || null;
    } catch (error) {
      console.error(
        `Error fetching HubSpot setting value for key ${key}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Create a new HubSpot setting
   * @param settingData Setting data
   * @returns Created setting
   */
  createSetting(settingData: HubSpotSettingCreate): HubSpotSetting {
    try {
      // Check if setting with this key already exists
      const existing = this.getSettingByKey(settingData.key);
      if (existing) {
        throw new Error(
          `HubSpot setting with key '${settingData.key}' already exists`,
        );
      }

      const id = uuidv4();
      const now = new Date().toISOString();

      this.db
        .prepare(
          `
        INSERT INTO ${this.tableName} (
          id,
          key,
          value,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?)
      `,
        )
        .run(id, settingData.key, settingData.value || null, now, now);

      return this.getSettingById(id) as HubSpotSetting;
    } catch (error) {
      console.error("Error creating HubSpot setting:", error);
      throw error;
    }
  }

  /**
   * Update an existing HubSpot setting
   * @param key Setting key
   * @param updateData Update data
   * @returns Updated setting or null if not found
   */
  updateSetting(
    key: string,
    updateData: HubSpotSettingUpdate,
  ): HubSpotSetting | null {
    try {
      const existing = this.getSettingByKey(key);
      if (!existing) {
        return null;
      }

      const now = new Date().toISOString();

      this.db
        .prepare(
          `
        UPDATE ${this.tableName}
        SET
          value = ?,
          updated_at = ?
        WHERE key = ?
      `,
        )
        .run(
          updateData.value !== undefined ? updateData.value : existing.value,
          now,
          key,
        );

      return this.getSettingByKey(key);
    } catch (error) {
      console.error(`Error updating HubSpot setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Set a setting value (create if doesn't exist, update if exists)
   * @param key Setting key
   * @param value Setting value
   * @returns Setting
   */
  setSetting(key: string, value: string): HubSpotSetting {
    try {
      const existing = this.getSettingByKey(key);

      if (existing) {
        return this.updateSetting(key, { value }) as HubSpotSetting;
      } else {
        return this.createSetting({ key, value });
      }
    } catch (error) {
      console.error(`Error setting HubSpot setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Delete a HubSpot setting
   * @param key Setting key
   * @returns Boolean indicating success
   */
  deleteSetting(key: string): boolean {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE key = ?
      `,
        )
        .run(key);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting HubSpot setting ${key}:`, error);
      return false;
    }
  }

  /**
   * Get multiple settings by keys
   * @param keys Array of setting keys
   * @returns Array of settings
   */
  getSettingsByKeys(keys: string[]): HubSpotSetting[] {
    try {
      if (keys.length === 0) {
        return [];
      }

      const placeholders = keys.map(() => "?").join(",");
      const settings = this.db
        .prepare(
          `
        SELECT 
          id,
          key,
          value,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE key IN (${placeholders})
        ORDER BY key
      `,
        )
        .all(...keys) as HubSpotSetting[];

      return settings;
    } catch (error) {
      console.error("Error fetching HubSpot settings by keys:", error);
      return [];
    }
  }

  /**
   * Get settings as key-value map
   * @param keys Optional array of keys to filter by
   * @returns Object with settings as key-value pairs
   */
  getSettingsMap(keys?: string[]): Record<string, string> {
    try {
      const settings = keys
        ? this.getSettingsByKeys(keys)
        : this.getAllSettings();
      const map: Record<string, string> = {};

      settings.forEach((setting) => {
        if (setting.value !== null && setting.value !== undefined) {
          map[setting.key] = setting.value;
        }
      });

      return map;
    } catch (error) {
      console.error("Error creating HubSpot settings map:", error);
      return {};
    }
  }

  /**
   * Bulk set multiple settings
   * @param settings Object with key-value pairs
   * @returns Array of updated/created settings
   */
  bulkSetSettings(settings: Record<string, string>): HubSpotSetting[] {
    try {
      const results: HubSpotSetting[] = [];

      const transaction = this.db.transaction(() => {
        for (const [key, value] of Object.entries(settings)) {
          const setting = this.setSetting(key, value);
          results.push(setting);
        }
      });

      transaction();
      return results;
    } catch (error) {
      console.error("Error bulk setting HubSpot settings:", error);
      throw error;
    }
  }

  /**
   * Check if a setting exists
   * @param key Setting key
   * @returns Boolean indicating if setting exists
   */
  settingExists(key: string): boolean {
    try {
      const count = this.db
        .prepare(
          `
        SELECT COUNT(*) as count
        FROM ${this.tableName}
        WHERE key = ?
      `,
        )
        .get(key) as { count: number };

      return count.count > 0;
    } catch (error) {
      console.error(`Error checking if HubSpot setting ${key} exists:`, error);
      return false;
    }
  }

  /**
   * Get setting by ID
   * @param id Setting ID
   * @returns Setting or null if not found
   */
  private getSettingById(id: string): HubSpotSetting | null {
    try {
      const setting = this.db
        .prepare(
          `
        SELECT 
          id,
          key,
          value,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE id = ?
      `,
        )
        .get(id) as HubSpotSetting | undefined;

      return setting || null;
    } catch (error) {
      console.error(`Error fetching HubSpot setting by ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Get common access token settings (helper method)
   * @returns Object with access token and refresh token
   */
  getAccessTokens(): {
    accessToken: string | null;
    refreshToken: string | null;
  } {
    try {
      const settings = this.getSettingsByKeys([
        "access_token",
        "refresh_token",
      ]);
      const accessToken =
        settings.find((s) => s.key === "access_token")?.value || null;
      const refreshToken =
        settings.find((s) => s.key === "refresh_token")?.value || null;

      return { accessToken, refreshToken };
    } catch (error) {
      console.error("Error fetching HubSpot access tokens:", error);
      return { accessToken: null, refreshToken: null };
    }
  }

  /**
   * Set access token settings (helper method)
   * @param accessToken Access token
   * @param refreshToken Refresh token
   * @returns Boolean indicating success
   */
  setAccessTokens(accessToken: string, refreshToken: string): boolean {
    try {
      this.bulkSetSettings({
        access_token: accessToken,
        refresh_token: refreshToken,
      });
      return true;
    } catch (error) {
      console.error("Error setting HubSpot access tokens:", error);
      return false;
    }
  }
}
