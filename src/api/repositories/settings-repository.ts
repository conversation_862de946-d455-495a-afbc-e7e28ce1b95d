/**
 * Repository for managing application settings
 */

import { BaseRepository } from "./base-repository";

/**
 * Interface for settings entity
 */
export interface Setting {
  key: string;
  value: string;
  updatedAt: string;
  updatedBy?: string;
}

/**
 * Interface for creating a new setting
 */
export interface SettingCreate {
  key: string;
  value: string;
  updatedBy?: string;
}

/**
 * Interface for updating a setting
 */
export interface SettingUpdate {
  value?: string;
  updatedBy?: string;
}

/**
 * Repository for managing application settings
 */
export class SettingsRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('settings');
  }

  /**
   * Get all settings
   * @returns Array of settings
   */
  getAllSettings(): Setting[] {
    try {
      const settings = this.db.prepare(`
        SELECT 
          key,
          value,
          updated_at as updatedAt,
          updated_by as updatedBy
        FROM ${this.tableName}
        ORDER BY key
      `).all() as Setting[];

      return settings;
    } catch (error) {
      console.error('Error fetching all settings:', error);
      return [];
    }
  }

  /**
   * Get setting by key
   * @param key Setting key
   * @returns Setting or null if not found
   */
  getSettingByKey(key: string): Setting | null {
    try {
      const setting = this.db.prepare(`
        SELECT 
          key,
          value,
          updated_at as updatedAt,
          updated_by as updatedBy
        FROM ${this.tableName}
        WHERE key = ?
      `).get(key) as Setting | undefined;

      return setting || null;
    } catch (error) {
      console.error(`Error fetching setting by key ${key}:`, error);
      return null;
    }
  }

  /**
   * Get setting value by key
   * @param key Setting key
   * @returns Setting value or null if not found
   */
  getSettingValue(key: string): string | null {
    try {
      const setting = this.getSettingByKey(key);
      return setting?.value || null;
    } catch (error) {
      console.error(`Error fetching setting value for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Create a new setting
   * @param settingData Setting data
   * @returns Created setting
   */
  createSetting(settingData: SettingCreate): Setting {
    try {
      // Check if setting with this key already exists
      const existing = this.getSettingByKey(settingData.key);
      if (existing) {
        throw new Error(`Setting with key '${settingData.key}' already exists`);
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO ${this.tableName} (
          key,
          value,
          updated_at,
          updated_by
        ) VALUES (?, ?, ?, ?)
      `).run(
        settingData.key,
        settingData.value,
        now,
        settingData.updatedBy || null
      );

      return this.getSettingByKey(settingData.key) as Setting;
    } catch (error) {
      console.error('Error creating setting:', error);
      throw error;
    }
  }

  /**
   * Update an existing setting
   * @param key Setting key
   * @param updateData Update data
   * @returns Updated setting or null if not found
   */
  updateSetting(key: string, updateData: SettingUpdate): Setting | null {
    try {
      const existing = this.getSettingByKey(key);
      if (!existing) {
        return null;
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE ${this.tableName}
        SET
          value = ?,
          updated_at = ?,
          updated_by = ?
        WHERE key = ?
      `).run(
        updateData.value !== undefined ? updateData.value : existing.value,
        now,
        updateData.updatedBy !== undefined ? updateData.updatedBy : existing.updatedBy,
        key
      );

      return this.getSettingByKey(key);
    } catch (error) {
      console.error(`Error updating setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Set a setting value (create if doesn't exist, update if exists)
   * @param key Setting key
   * @param value Setting value
   * @param updatedBy User making the change
   * @returns Setting
   */
  setSetting(key: string, value: string, updatedBy?: string): Setting {
    try {
      const existing = this.getSettingByKey(key);
      
      if (existing) {
        return this.updateSetting(key, { value, updatedBy }) as Setting;
      } else {
        return this.createSetting({ key, value, updatedBy });
      }
    } catch (error) {
      console.error(`Error setting value for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Delete a setting
   * @param key Setting key
   * @returns Boolean indicating success
   */
  deleteSetting(key: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE key = ?
      `).run(key);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting setting ${key}:`, error);
      return false;
    }
  }

  /**
   * Get multiple settings by keys
   * @param keys Array of setting keys
   * @returns Array of settings
   */
  getSettingsByKeys(keys: string[]): Setting[] {
    try {
      if (keys.length === 0) {
        return [];
      }

      const placeholders = keys.map(() => '?').join(',');
      const settings = this.db.prepare(`
        SELECT 
          key,
          value,
          updated_at as updatedAt,
          updated_by as updatedBy
        FROM ${this.tableName}
        WHERE key IN (${placeholders})
        ORDER BY key
      `).all(...keys) as Setting[];

      return settings;
    } catch (error) {
      console.error('Error fetching settings by keys:', error);
      return [];
    }
  }

  /**
   * Get settings as key-value map
   * @param keys Optional array of keys to filter by
   * @returns Object with settings as key-value pairs
   */
  getSettingsMap(keys?: string[]): Record<string, string> {
    try {
      const settings = keys ? this.getSettingsByKeys(keys) : this.getAllSettings();
      const map: Record<string, string> = {};
      
      settings.forEach(setting => {
        map[setting.key] = setting.value;
      });

      return map;
    } catch (error) {
      console.error('Error creating settings map:', error);
      return {};
    }
  }

  /**
   * Bulk set multiple settings
   * @param settings Object with key-value pairs
   * @param updatedBy User making the changes
   * @returns Array of updated/created settings
   */
  bulkSetSettings(settings: Record<string, string>, updatedBy?: string): Setting[] {
    try {
      const results: Setting[] = [];
      
      const transaction = this.db.transaction(() => {
        for (const [key, value] of Object.entries(settings)) {
          const setting = this.setSetting(key, value, updatedBy);
          results.push(setting);
        }
      });

      transaction();
      return results;
    } catch (error) {
      console.error('Error bulk setting values:', error);
      throw error;
    }
  }

  /**
   * Check if a setting exists
   * @param key Setting key
   * @returns Boolean indicating if setting exists
   */
  settingExists(key: string): boolean {
    try {
      const count = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM ${this.tableName}
        WHERE key = ?
      `).get(key) as { count: number };

      return count.count > 0;
    } catch (error) {
      console.error(`Error checking if setting ${key} exists:`, error);
      return false;
    }
  }

  /**
   * Get settings by prefix
   * @param prefix Key prefix to search for
   * @returns Array of settings matching the prefix
   */
  getSettingsByPrefix(prefix: string): Setting[] {
    try {
      const settings = this.db.prepare(`
        SELECT 
          key,
          value,
          updated_at as updatedAt,
          updated_by as updatedBy
        FROM ${this.tableName}
        WHERE key LIKE ?
        ORDER BY key
      `).all(`${prefix}%`) as Setting[];

      return settings;
    } catch (error) {
      console.error(`Error fetching settings by prefix ${prefix}:`, error);
      return [];
    }
  }

  /**
   * Get settings statistics
   * @returns Settings statistics
   */
  getSettingsStatistics(): {
    totalSettings: number;
    settingsByUpdater: Record<string, number>;
    oldestSetting: string | null;
    newestSetting: string | null;
    averageValueLength: number;
  } {
    try {
      // Get basic statistics
      const basicStats = this.db.prepare(`
        SELECT 
          COUNT(*) as totalSettings,
          MIN(updated_at) as oldestSetting,
          MAX(updated_at) as newestSetting,
          ROUND(AVG(LENGTH(value)), 2) as averageValueLength
        FROM ${this.tableName}
      `).get() as any;

      // Get settings by updater
      const updaterStats = this.db.prepare(`
        SELECT 
          COALESCE(updated_by, 'Unknown') as updater,
          COUNT(*) as count
        FROM ${this.tableName}
        GROUP BY updated_by
        ORDER BY count DESC
      `).all() as Array<{ updater: string; count: number }>;

      const settingsByUpdater: Record<string, number> = {};
      updaterStats.forEach(stat => {
        settingsByUpdater[stat.updater] = stat.count;
      });

      return {
        totalSettings: basicStats.totalSettings || 0,
        settingsByUpdater,
        oldestSetting: basicStats.oldestSetting || null,
        newestSetting: basicStats.newestSetting || null,
        averageValueLength: basicStats.averageValueLength || 0
      };
    } catch (error) {
      console.error('Error fetching settings statistics:', error);
      return {
        totalSettings: 0,
        settingsByUpdater: {},
        oldestSetting: null,
        newestSetting: null,
        averageValueLength: 0
      };
    }
  }

  /**
   * Clear all settings (use with caution)
   * @returns Number of deleted settings
   */
  clearAllSettings(): number {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
      `).run();

      return result.changes || 0;
    } catch (error) {
      console.error('Error clearing all settings:', error);
      return 0;
    }
  }

  /**
   * Delete settings by prefix
   * @param prefix Key prefix to delete
   * @returns Number of deleted settings
   */
  deleteSettingsByPrefix(prefix: string): number {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE key LIKE ?
      `).run(`${prefix}%`);

      return result.changes || 0;
    } catch (error) {
      console.error(`Error deleting settings by prefix ${prefix}:`, error);
      return 0;
    }
  }

  /**
   * Export all settings as JSON
   * @returns JSON string of all settings
   */
  exportSettings(): string {
    try {
      const settings = this.getAllSettings();
      const settingsMap = this.getSettingsMap();
      
      const exportData = {
        exportedAt: new Date().toISOString(),
        totalSettings: settings.length,
        settings: settingsMap,
        metadata: settings.map(s => ({
          key: s.key,
          updatedAt: s.updatedAt,
          updatedBy: s.updatedBy
        }))
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Error exporting settings:', error);
      throw error;
    }
  }

  /**
   * Import settings from JSON
   * @param jsonData JSON string containing settings
   * @param updatedBy User performing the import
   * @returns Number of imported settings
   */
  importSettings(jsonData: string, updatedBy?: string): number {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.settings || typeof importData.settings !== 'object') {
        throw new Error('Invalid settings import format');
      }

      let imported = 0;
      
      const transaction = this.db.transaction(() => {
        for (const [key, value] of Object.entries(importData.settings)) {
          this.setSetting(key, String(value), updatedBy);
          imported++;
        }
      });

      transaction();
      return imported;
    } catch (error) {
      console.error('Error importing settings:', error);
      throw error;
    }
  }
}