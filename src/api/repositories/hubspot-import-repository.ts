/**
 * Repository for managing HubSpot import records
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Interface for HubSpot import entity
 */
export interface HubSpotImport {
  id: string;
  importDate: string;
  status: "pending" | "completed" | "failed";
  dealsCount?: number;
  contactsCount?: number;
  companiesCount?: number;
  errorMessage?: string;
  createdAt: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new HubSpot import record
 */
export interface HubSpotImportCreate {
  importDate: string;
  status: "pending" | "completed" | "failed";
  dealsCount?: number;
  contactsCount?: number;
  companiesCount?: number;
  errorMessage?: string;
}

/**
 * Interface for updating a HubSpot import record
 */
export interface HubSpotImportUpdate {
  status?: "pending" | "completed" | "failed";
  dealsCount?: number;
  contactsCount?: number;
  companiesCount?: number;
  errorMessage?: string;
}

/**
 * Repository for managing HubSpot import records
 */
export class HubSpotImportRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('hubspot_import');
  }

  /**
   * Get all import records
   * @param limit Optional limit for results
   * @returns Array of import records
   */
  getAllImports(limit?: number): HubSpotImport[] {
    try {
      let query = `
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        ORDER BY created_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const imports = this.db.prepare(query).all() as HubSpotImport[];
      return imports;
    } catch (error) {
      console.error('Error fetching all HubSpot imports:', error);
      return [];
    }
  }

  /**
   * Get import record by ID
   * @param id Import ID
   * @returns Import record or null if not found
   */
  getImportById(id: string): HubSpotImport | null {
    try {
      const importRecord = this.db.prepare(`
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE id = ?
      `).get(id) as HubSpotImport | undefined;

      return importRecord || null;
    } catch (error) {
      console.error(`Error fetching HubSpot import by ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Get import records by status
   * @param status Import status
   * @param limit Optional limit for results
   * @returns Array of import records
   */
  getImportsByStatus(status: "pending" | "completed" | "failed", limit?: number): HubSpotImport[] {
    try {
      let query = `
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE status = ?
        ORDER BY created_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const imports = this.db.prepare(query).all(status) as HubSpotImport[];
      return imports;
    } catch (error) {
      console.error(`Error fetching HubSpot imports by status ${status}:`, error);
      return [];
    }
  }

  /**
   * Get import records by date range
   * @param startDate Start date (ISO string)
   * @param endDate End date (ISO string)
   * @returns Array of import records
   */
  getImportsByDateRange(startDate: string, endDate: string): HubSpotImport[] {
    try {
      const imports = this.db.prepare(`
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE import_date >= ? AND import_date <= ?
        ORDER BY import_date DESC
      `).all(startDate, endDate) as HubSpotImport[];

      return imports;
    } catch (error) {
      console.error(`Error fetching HubSpot imports for date range ${startDate} to ${endDate}:`, error);
      return [];
    }
  }

  /**
   * Get latest import record
   * @returns Latest import record or null if none exist
   */
  getLatestImport(): HubSpotImport | null {
    try {
      const importRecord = this.db.prepare(`
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        ORDER BY created_at DESC
        LIMIT 1
      `).get() as HubSpotImport | undefined;

      return importRecord || null;
    } catch (error) {
      console.error('Error fetching latest HubSpot import:', error);
      return null;
    }
  }

  /**
   * Get latest successful import record
   * @returns Latest successful import record or null if none exist
   */
  getLatestSuccessfulImport(): HubSpotImport | null {
    try {
      const importRecord = this.db.prepare(`
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE status = 'completed'
        ORDER BY created_at DESC
        LIMIT 1
      `).get() as HubSpotImport | undefined;

      return importRecord || null;
    } catch (error) {
      console.error('Error fetching latest successful HubSpot import:', error);
      return null;
    }
  }

  /**
   * Create a new import record
   * @param importData Import data
   * @returns Created import record
   */
  createImport(importData: HubSpotImportCreate): HubSpotImport {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO ${this.tableName} (
          id,
          import_date,
          status,
          deals_count,
          contacts_count,
          companies_count,
          error_message,
          created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        importData.importDate,
        importData.status,
        importData.dealsCount || null,
        importData.contactsCount || null,
        importData.companiesCount || null,
        importData.errorMessage || null,
        now
      );

      return this.getImportById(id) as HubSpotImport;
    } catch (error) {
      console.error('Error creating HubSpot import record:', error);
      throw error;
    }
  }

  /**
   * Update an existing import record
   * @param id Import ID
   * @param updateData Update data
   * @returns Updated import record or null if not found
   */
  updateImport(id: string, updateData: HubSpotImportUpdate): HubSpotImport | null {
    try {
      const existing = this.getImportById(id);
      if (!existing) {
        return null;
      }

      this.db.prepare(`
        UPDATE ${this.tableName}
        SET
          status = ?,
          deals_count = ?,
          contacts_count = ?,
          companies_count = ?,
          error_message = ?
        WHERE id = ?
      `).run(
        updateData.status !== undefined ? updateData.status : existing.status,
        updateData.dealsCount !== undefined ? updateData.dealsCount : existing.dealsCount,
        updateData.contactsCount !== undefined ? updateData.contactsCount : existing.contactsCount,
        updateData.companiesCount !== undefined ? updateData.companiesCount : existing.companiesCount,
        updateData.errorMessage !== undefined ? updateData.errorMessage : existing.errorMessage,
        id
      );

      return this.getImportById(id);
    } catch (error) {
      console.error(`Error updating HubSpot import ${id}:`, error);
      throw error;
    }
  }

  /**
   * Mark import as completed
   * @param id Import ID
   * @param counts Object with count data
   * @returns Updated import record or null if not found
   */
  markImportCompleted(
    id: string,
    counts: {
      dealsCount?: number;
      contactsCount?: number;
      companiesCount?: number;
    }
  ): HubSpotImport | null {
    try {
      return this.updateImport(id, {
        status: "completed",
        dealsCount: counts.dealsCount,
        contactsCount: counts.contactsCount,
        companiesCount: counts.companiesCount,
        errorMessage: undefined // Clear any previous error message
      });
    } catch (error) {
      console.error(`Error marking HubSpot import ${id} as completed:`, error);
      throw error;
    }
  }

  /**
   * Mark import as failed
   * @param id Import ID
   * @param errorMessage Error message
   * @returns Updated import record or null if not found
   */
  markImportFailed(id: string, errorMessage: string): HubSpotImport | null {
    try {
      return this.updateImport(id, {
        status: "failed",
        errorMessage
      });
    } catch (error) {
      console.error(`Error marking HubSpot import ${id} as failed:`, error);
      throw error;
    }
  }

  /**
   * Delete an import record
   * @param id Import ID
   * @returns Boolean indicating success
   */
  deleteImport(id: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE id = ?
      `).run(id);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting HubSpot import ${id}:`, error);
      return false;
    }
  }

  /**
   * Delete old import records
   * @param days Number of days to keep (default: 30)
   * @returns Number of deleted records
   */
  deleteOldImports(days: number = 30): number {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      const cutoffIso = cutoffDate.toISOString();

      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE created_at < ?
      `).run(cutoffIso);

      return result.changes || 0;
    } catch (error) {
      console.error(`Error deleting old HubSpot imports (older than ${days} days):`, error);
      return 0;
    }
  }

  /**
   * Get import statistics
   * @returns Import statistics
   */
  getImportStatistics(): {
    totalImports: number;
    completedImports: number;
    failedImports: number;
    pendingImports: number;
    successRate: number;
    totalDeals: number;
    totalContacts: number;
    totalCompanies: number;
    averageDealsPerImport: number;
    averageContactsPerImport: number;
    averageCompaniesPerImport: number;
    oldestImport: string | null;
    newestImport: string | null;
  } {
    try {
      // Get basic statistics
      const basicStats = this.db.prepare(`
        SELECT 
          COUNT(*) as totalImports,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedImports,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failedImports,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pendingImports,
          SUM(COALESCE(deals_count, 0)) as totalDeals,
          SUM(COALESCE(contacts_count, 0)) as totalContacts,
          SUM(COALESCE(companies_count, 0)) as totalCompanies,
          MIN(created_at) as oldestImport,
          MAX(created_at) as newestImport
        FROM ${this.tableName}
      `).get() as any;

      // Calculate averages for completed imports only
      const averageStats = this.db.prepare(`
        SELECT 
          COUNT(*) as completedCount,
          AVG(COALESCE(deals_count, 0)) as avgDeals,
          AVG(COALESCE(contacts_count, 0)) as avgContacts,
          AVG(COALESCE(companies_count, 0)) as avgCompanies
        FROM ${this.tableName}
        WHERE status = 'completed'
      `).get() as any;

      const successRate = basicStats.totalImports > 0 
        ? (basicStats.completedImports / basicStats.totalImports) * 100 
        : 0;

      return {
        totalImports: basicStats.totalImports || 0,
        completedImports: basicStats.completedImports || 0,
        failedImports: basicStats.failedImports || 0,
        pendingImports: basicStats.pendingImports || 0,
        successRate: Math.round(successRate * 100) / 100,
        totalDeals: basicStats.totalDeals || 0,
        totalContacts: basicStats.totalContacts || 0,
        totalCompanies: basicStats.totalCompanies || 0,
        averageDealsPerImport: Math.round((averageStats.avgDeals || 0) * 100) / 100,
        averageContactsPerImport: Math.round((averageStats.avgContacts || 0) * 100) / 100,
        averageCompaniesPerImport: Math.round((averageStats.avgCompanies || 0) * 100) / 100,
        oldestImport: basicStats.oldestImport || null,
        newestImport: basicStats.newestImport || null
      };
    } catch (error) {
      console.error('Error fetching HubSpot import statistics:', error);
      return {
        totalImports: 0,
        completedImports: 0,
        failedImports: 0,
        pendingImports: 0,
        successRate: 0,
        totalDeals: 0,
        totalContacts: 0,
        totalCompanies: 0,
        averageDealsPerImport: 0,
        averageContactsPerImport: 0,
        averageCompaniesPerImport: 0,
        oldestImport: null,
        newestImport: null
      };
    }
  }

  /**
   * Get recent import summary
   * @param days Number of days to look back (default: 7)
   * @returns Recent import summary
   */
  getRecentImportSummary(days: number = 7): {
    totalImports: number;
    completedImports: number;
    failedImports: number;
    pendingImports: number;
    totalRecords: number;
    mostRecentImport: HubSpotImport | null;
  } {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      const cutoffIso = cutoffDate.toISOString();

      const summary = this.db.prepare(`
        SELECT 
          COUNT(*) as totalImports,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedImports,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failedImports,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pendingImports,
          SUM(
            COALESCE(deals_count, 0) + 
            COALESCE(contacts_count, 0) + 
            COALESCE(companies_count, 0)
          ) as totalRecords
        FROM ${this.tableName}
        WHERE created_at >= ?
      `).get(cutoffIso) as any;

      const mostRecentImport = this.db.prepare(`
        SELECT 
          id,
          import_date as importDate,
          status,
          deals_count as dealsCount,
          contacts_count as contactsCount,
          companies_count as companiesCount,
          error_message as errorMessage,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE created_at >= ?
        ORDER BY created_at DESC
        LIMIT 1
      `).get(cutoffIso) as HubSpotImport | undefined;

      return {
        totalImports: summary.totalImports || 0,
        completedImports: summary.completedImports || 0,
        failedImports: summary.failedImports || 0,
        pendingImports: summary.pendingImports || 0,
        totalRecords: summary.totalRecords || 0,
        mostRecentImport: mostRecentImport || null
      };
    } catch (error) {
      console.error(`Error fetching recent import summary (${days} days):`, error);
      return {
        totalImports: 0,
        completedImports: 0,
        failedImports: 0,
        pendingImports: 0,
        totalRecords: 0,
        mostRecentImport: null
      };
    }
  }

  /**
   * Check if there are any pending imports
   * @returns Boolean indicating if there are pending imports
   */
  hasPendingImports(): boolean {
    try {
      const count = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM ${this.tableName}
        WHERE status = 'pending'
      `).get() as { count: number };

      return count.count > 0;
    } catch (error) {
      console.error('Error checking for pending imports:', error);
      return false;
    }
  }

  /**
   * Start a new import (create with pending status)
   * @param importDate Import date
   * @returns Created import record
   */
  startNewImport(importDate: string = new Date().toISOString()): HubSpotImport {
    try {
      return this.createImport({
        importDate,
        status: "pending"
      });
    } catch (error) {
      console.error('Error starting new HubSpot import:', error);
      throw error;
    }
  }
}