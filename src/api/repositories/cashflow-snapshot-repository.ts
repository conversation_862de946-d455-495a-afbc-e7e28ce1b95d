/**
 * Repository for managing cashflow snapshots
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Interface for cashflow snapshot entity
 */
export interface CashflowSnapshot {
  id: string;
  date: string;
  tenantId: string;
  daysAhead: number;
  snapshotData: string; // JSON string
  createdAt: string;
  createdBy?: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new cashflow snapshot
 */
export interface CashflowSnapshotCreate {
  date: string;
  tenantId: string;
  daysAhead: number;
  snapshotData: any; // Will be JSON stringified
  createdBy?: string;
}

/**
 * Repository for managing cashflow snapshots
 */
export class CashflowSnapshotRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super("cashflow_snapshot");
  }

  /**
   * Get all snapshots for a tenant
   * @param tenantId Xero tenant ID
   * @param limit Optional limit for results
   * @returns Array of snapshots
   */
  getSnapshotsByTenant(tenantId: string, limit?: number): CashflowSnapshot[] {
    try {
      const conditions = { tenant_id: tenantId };
      let query = `
        SELECT 
          id,
          date,
          tenant_id as tenantId,
          days_ahead as daysAhead,
          snapshot_data as snapshotData,
          created_at as createdAt,
          created_by as createdBy
        FROM ${this.tableName}
        WHERE tenant_id = ?
        ORDER BY date DESC, created_at DESC
      `;

      if (limit) {
        query += ` LIMIT ${limit}`;
      }

      const snapshots = this.db
        .prepare(query)
        .all(tenantId) as CashflowSnapshot[];
      return snapshots;
    } catch (error) {
      console.error(`Error fetching snapshots for tenant ${tenantId}:`, error);
      return [];
    }
  }

  /**
   * Get snapshot by date and tenant
   * @param date ISO date string
   * @param tenantId Xero tenant ID
   * @param daysAhead Number of days ahead for projection
   * @returns Snapshot or null if not found
   */
  getSnapshotByDate(
    date: string,
    tenantId: string,
    daysAhead: number = 90,
  ): CashflowSnapshot | null {
    try {
      const snapshot = this.db
        .prepare(
          `
        SELECT 
          id,
          date,
          tenant_id as tenantId,
          days_ahead as daysAhead,
          snapshot_data as snapshotData,
          created_at as createdAt,
          created_by as createdBy
        FROM ${this.tableName}
        WHERE date = ? AND tenant_id = ? AND days_ahead = ?
        ORDER BY created_at DESC
        LIMIT 1
      `,
        )
        .get(date, tenantId, daysAhead) as CashflowSnapshot | undefined;

      return snapshot || null;
    } catch (error) {
      console.error(`Error fetching snapshot for ${date}:`, error);
      return null;
    }
  }

  /**
   * Get latest snapshot for a tenant
   * @param tenantId Xero tenant ID
   * @param daysAhead Number of days ahead for projection
   * @returns Latest snapshot or null if not found
   */
  getLatestSnapshot(
    tenantId: string,
    daysAhead: number = 90,
  ): CashflowSnapshot | null {
    try {
      const snapshot = this.db
        .prepare(
          `
        SELECT 
          id,
          date,
          tenant_id as tenantId,
          days_ahead as daysAhead,
          snapshot_data as snapshotData,
          created_at as createdAt,
          created_by as createdBy
        FROM ${this.tableName}
        WHERE tenant_id = ? AND days_ahead = ?
        ORDER BY date DESC, created_at DESC
        LIMIT 1
      `,
        )
        .get(tenantId, daysAhead) as CashflowSnapshot | undefined;

      return snapshot || null;
    } catch (error) {
      console.error(
        `Error fetching latest snapshot for tenant ${tenantId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Create a new cashflow snapshot
   * @param snapshotData Snapshot data
   * @returns Created snapshot
   */
  createSnapshot(snapshotData: CashflowSnapshotCreate): CashflowSnapshot {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      // First check if a snapshot for this date/tenant/daysAhead already exists
      const existing = this.getSnapshotByDate(
        snapshotData.date,
        snapshotData.tenantId,
        snapshotData.daysAhead,
      );
      if (existing) {
        // Update existing snapshot instead of creating duplicate
        return this.updateSnapshot(existing.id, {
          snapshotData: snapshotData.snapshotData,
          createdBy: snapshotData.createdBy,
        });
      }

      this.db
        .prepare(
          `
        INSERT INTO ${this.tableName} (
          id,
          date,
          tenant_id,
          days_ahead,
          snapshot_data,
          created_at,
          created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `,
        )
        .run(
          id,
          snapshotData.date,
          snapshotData.tenantId,
          snapshotData.daysAhead,
          JSON.stringify(snapshotData.snapshotData),
          now,
          snapshotData.createdBy || "system",
        );

      return this.getSnapshotById(id) as CashflowSnapshot;
    } catch (error) {
      console.error("Error creating cashflow snapshot:", error);
      throw error;
    }
  }

  /**
   * Update an existing cashflow snapshot
   * @param id Snapshot ID
   * @param updateData Update data
   * @returns Updated snapshot
   */
  updateSnapshot(
    id: string,
    updateData: Partial<CashflowSnapshotCreate>,
  ): CashflowSnapshot {
    try {
      const existing = this.getSnapshotById(id);
      if (!existing) {
        throw new Error(`Cashflow snapshot with ID ${id} not found`);
      }

      const now = new Date().toISOString();

      this.db
        .prepare(
          `
        UPDATE ${this.tableName}
        SET
          snapshot_data = ?,
          created_at = ?,
          created_by = ?
        WHERE id = ?
      `,
        )
        .run(
          updateData.snapshotData
            ? JSON.stringify(updateData.snapshotData)
            : existing.snapshotData,
          now,
          updateData.createdBy || existing.createdBy,
          id,
        );

      return this.getSnapshotById(id) as CashflowSnapshot;
    } catch (error) {
      console.error(`Error updating cashflow snapshot ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete old snapshots to maintain storage efficiency
   * @param tenantId Xero tenant ID
   * @param keepDays Number of days of snapshots to keep (default: 30)
   * @returns Number of deleted snapshots
   */
  deleteOldSnapshots(tenantId: string, keepDays: number = 30): number {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - keepDays);
      const cutoffIso = cutoffDate.toISOString().split("T")[0]; // YYYY-MM-DD format

      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE tenant_id = ? AND date < ?
      `,
        )
        .run(tenantId, cutoffIso);

      return result.changes || 0;
    } catch (error) {
      console.error(
        `Error deleting old snapshots for tenant ${tenantId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Get snapshot statistics for a tenant
   * @param tenantId Xero tenant ID
   * @returns Snapshot statistics
   */
  getSnapshotStats(tenantId: string): {
    totalSnapshots: number;
    oldestSnapshot: string | null;
    newestSnapshot: string | null;
    avgSizeKB: number;
  } {
    try {
      const stats = this.db
        .prepare(
          `
        SELECT 
          COUNT(*) as totalSnapshots,
          MIN(date) as oldestSnapshot,
          MAX(date) as newestSnapshot,
          ROUND(AVG(LENGTH(snapshot_data)) / 1024.0, 2) as avgSizeKB
        FROM ${this.tableName}
        WHERE tenant_id = ?
      `,
        )
        .get(tenantId) as any;

      return {
        totalSnapshots: stats.totalSnapshots || 0,
        oldestSnapshot: stats.oldestSnapshot || null,
        newestSnapshot: stats.newestSnapshot || null,
        avgSizeKB: stats.avgSizeKB || 0,
      };
    } catch (error) {
      console.error(
        `Error fetching snapshot stats for tenant ${tenantId}:`,
        error,
      );
      return {
        totalSnapshots: 0,
        oldestSnapshot: null,
        newestSnapshot: null,
        avgSizeKB: 0,
      };
    }
  }

  /**
   * Get snapshot by ID
   * @param id Snapshot ID
   * @returns Snapshot or null if not found
   */
  private getSnapshotById(id: string): CashflowSnapshot | null {
    try {
      const snapshot = this.db
        .prepare(
          `
        SELECT 
          id,
          date,
          tenant_id as tenantId,
          days_ahead as daysAhead,
          snapshot_data as snapshotData,
          created_at as createdAt,
          created_by as createdBy
        FROM ${this.tableName}
        WHERE id = ?
      `,
        )
        .get(id) as CashflowSnapshot | undefined;

      return snapshot || null;
    } catch (error) {
      console.error(`Error fetching snapshot by ID ${id}:`, error);
      return null;
    }
  }
}
