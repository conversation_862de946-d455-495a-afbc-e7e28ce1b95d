/**
 * Repository for managing team contact coverage
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Relationship strength levels
 */
export type RelationshipStrength = 'primary' | 'secondary' | 'minimal';

/**
 * Team contact coverage interface
 */
export interface TeamContactCoverage {
  id: string;
  contactId: string;
  teamMemberId: string; // Harvest user ID
  relationshipStrength: RelationshipStrength;
  lastInteractionDate?: string;
  lastInteractionType?: string;
  notes?: string;
  createdAt: string;
  updatedAt?: string;
  deletedAt?: string;
}

/**
 * Team coverage with details
 */
export interface TeamCoverageWithDetails extends TeamContactCoverage {
  contact?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    jobTitle?: string;
    companyId?: string;
    companyName?: string;
  };
  teamMember?: {
    id: string;
    name: string;
    email?: string;
  };
}

/**
 * Coverage summary for a company
 */
export interface CompanyCoverageSummary {
  companyId: string;
  companyName: string;
  totalContacts: number;
  coveredContacts: number;
  coverage: {
    uncovered: Array<{
      id: string;
      name: string;
      jobTitle?: string;
    }>;
    singleThreaded: Array<{
      id: string;
      name: string;
      jobTitle?: string;
      coveredBy: string;
    }>;
    wellCovered: Array<{
      id: string;
      name: string;
      jobTitle?: string;
      teamMembers: string[];
    }>;
  };
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
}

/**
 * Repository for managing team contact coverage
 */
export class TeamCoverageRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('team_contact_coverage');
  }

  /**
   * Create or update team coverage for a contact
   * @param data Coverage data
   * @returns Created/updated coverage
   */
  upsertCoverage(data: {
    contactId: string;
    teamMemberId: string;
    relationshipStrength: RelationshipStrength;
    lastInteractionDate?: string;
    lastInteractionType?: string;
    notes?: string;
  }): TeamContactCoverage {
    try {
      const existing = this.db.prepare(`
        SELECT id FROM team_contact_coverage 
        WHERE contact_id = ? AND team_member_id = ?
      `).get(data.contactId, data.teamMemberId) as { id: string } | undefined;

      const now = new Date().toISOString();

      if (existing) {
        // Update existing
        this.db.prepare(`
          UPDATE team_contact_coverage
          SET relationship_strength = ?,
              last_interaction_date = ?,
              last_interaction_type = ?,
              notes = ?,
              updated_at = ?
          WHERE id = ?
        `).run(
          data.relationshipStrength,
          data.lastInteractionDate || null,
          data.lastInteractionType || null,
          data.notes || null,
          now,
          existing.id
        );

        return this.getCoverageById(existing.id)!;
      } else {
        // Create new
        const id = uuidv4();
        
        this.db.prepare(`
          INSERT INTO team_contact_coverage (
            id, contact_id, team_member_id, relationship_strength,
            last_interaction_date, last_interaction_type, notes,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          id,
          data.contactId,
          data.teamMemberId,
          data.relationshipStrength,
          data.lastInteractionDate || null,
          data.lastInteractionType || null,
          data.notes || null,
          now,
          now
        );

        return {
          id,
          contactId: data.contactId,
          teamMemberId: data.teamMemberId,
          relationshipStrength: data.relationshipStrength,
          lastInteractionDate: data.lastInteractionDate,
          lastInteractionType: data.lastInteractionType,
          notes: data.notes,
          createdAt: now,
          updatedAt: now
        };
      }
    } catch (error) {
      console.error('Error upserting team coverage:', error);
      throw error;
    }
  }

  /**
   * Get coverage for a specific contact
   * @param contactId Contact ID
   * @returns Array of team coverage records
   */
  getContactCoverage(contactId: string): TeamCoverageWithDetails[] {
    try {
      const coverage = this.db.prepare(`
        SELECT 
          tc.*,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          c.job_title as contact_job_title,
          cc.company_id as contact_company_id,
          comp.name as contact_company_name
        FROM team_contact_coverage tc
        JOIN contact c ON tc.contact_id = c.id
        LEFT JOIN contact_company cc ON c.id = cc.contact_id AND cc.is_primary = 1
        LEFT JOIN company comp ON cc.company_id = comp.id
        WHERE tc.contact_id = ? AND c.deleted_at IS NULL
        ORDER BY tc.relationship_strength = 'primary' DESC, tc.updated_at DESC
      `).all(contactId) as any[];

      return coverage.map(row => ({
        id: row.id,
        contactId: row.contact_id,
        teamMemberId: row.team_member_id,
        relationshipStrength: row.relationship_strength,
        lastInteractionDate: row.last_interaction_date,
        lastInteractionType: row.last_interaction_type,
        notes: row.notes,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        contact: {
          id: row.contact_id,
          firstName: row.contact_first_name,
          lastName: row.contact_last_name,
          email: row.contact_email,
          jobTitle: row.contact_job_title,
          companyId: row.contact_company_id,
          companyName: row.contact_company_name
        }
      }));
    } catch (error) {
      console.error('Error fetching contact coverage:', error);
      return [];
    }
  }

  /**
   * Get all coverage for a team member
   * @param teamMemberId Team member ID (Harvest user ID)
   * @returns Array of team coverage records
   */
  getTeamMemberCoverage(teamMemberId: string): TeamCoverageWithDetails[] {
    try {
      const coverage = this.db.prepare(`
        SELECT 
          tc.*,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          c.job_title as contact_job_title,
          cc.company_id as contact_company_id,
          comp.name as contact_company_name
        FROM team_contact_coverage tc
        JOIN contact c ON tc.contact_id = c.id
        LEFT JOIN contact_company cc ON c.id = cc.contact_id AND cc.is_primary = 1
        LEFT JOIN company comp ON cc.company_id = comp.id
        WHERE tc.team_member_id = ? AND c.deleted_at IS NULL
        ORDER BY tc.updated_at DESC
      `).all(teamMemberId) as any[];

      return coverage.map(row => ({
        id: row.id,
        contactId: row.contact_id,
        teamMemberId: row.team_member_id,
        relationshipStrength: row.relationship_strength,
        lastInteractionDate: row.last_interaction_date,
        lastInteractionType: row.last_interaction_type,
        notes: row.notes,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        contact: {
          id: row.contact_id,
          firstName: row.contact_first_name,
          lastName: row.contact_last_name,
          email: row.contact_email,
          jobTitle: row.contact_job_title,
          companyId: row.contact_company_id,
          companyName: row.contact_company_name
        }
      }));
    } catch (error) {
      console.error('Error fetching team member coverage:', error);
      return [];
    }
  }

  /**
   * Get company coverage summary
   * @param companyId Company ID
   * @returns Company coverage summary with risk assessment
   */
  getCompanyCoverage(companyId: string): CompanyCoverageSummary | null {
    try {
      // Get company details
      const company = this.db.prepare(`
        SELECT id, name FROM company WHERE id = ? AND deleted_at IS NULL
      `).get(companyId) as { id: string; name: string } | undefined;

      if (!company) return null;

      // Get all contacts at the company with their coverage
      const contacts = this.db.prepare(`
        SELECT 
          c.id,
          c.first_name,
          c.last_name,
          c.job_title,
          COUNT(tc.id) as coverage_count,
          GROUP_CONCAT(tc.team_member_id) as team_members
        FROM contact c
        JOIN contact_company cc ON c.id = cc.contact_id
        LEFT JOIN team_contact_coverage tc ON c.id = tc.contact_id
        WHERE cc.company_id = ? AND c.deleted_at IS NULL
        GROUP BY c.id
      `).all(companyId) as any[];

      const totalContacts = contacts.length;
      const uncovered: any[] = [];
      const singleThreaded: any[] = [];
      const wellCovered: any[] = [];

      contacts.forEach(contact => {
        const name = `${contact.first_name} ${contact.last_name}`;
        
        if (contact.coverage_count === 0) {
          uncovered.push({
            id: contact.id,
            name,
            jobTitle: contact.job_title
          });
        } else if (contact.coverage_count === 1) {
          singleThreaded.push({
            id: contact.id,
            name,
            jobTitle: contact.job_title,
            coveredBy: contact.team_members
          });
        } else {
          wellCovered.push({
            id: contact.id,
            name,
            jobTitle: contact.job_title,
            teamMembers: contact.team_members?.split(',') || []
          });
        }
      });

      const coveredContacts = singleThreaded.length + wellCovered.length;
      const coveragePercentage = totalContacts > 0 ? (coveredContacts / totalContacts) * 100 : 0;

      // Determine risk level
      let riskLevel: 'low' | 'medium' | 'high' = 'low';
      const recommendations: string[] = [];

      if (uncovered.length > totalContacts * 0.5) {
        riskLevel = 'high';
        recommendations.push(`${uncovered.length} contacts have no relationship owner`);
      } else if (uncovered.length > totalContacts * 0.25) {
        riskLevel = 'medium';
        recommendations.push(`Assign team members to ${uncovered.length} uncovered contacts`);
      }

      if (singleThreaded.length > totalContacts * 0.75) {
        riskLevel = riskLevel === 'high' ? 'high' : 'medium';
        recommendations.push('Most relationships are single-threaded - add backup coverage');
      }

      // Look for key roles without coverage
      const keyRoles = ['CEO', 'CTO', 'CFO', 'Director', 'VP', 'President'];
      const uncoveredKeyRoles = uncovered.filter(c => 
        c.jobTitle && keyRoles.some(role => c.jobTitle.includes(role))
      );
      
      if (uncoveredKeyRoles.length > 0) {
        riskLevel = 'high';
        recommendations.push(`Key decision makers without coverage: ${uncoveredKeyRoles.map(c => c.name).join(', ')}`);
      }

      return {
        companyId: company.id,
        companyName: company.name,
        totalContacts,
        coveredContacts,
        coverage: {
          uncovered,
          singleThreaded,
          wellCovered
        },
        riskLevel,
        recommendations
      };
    } catch (error) {
      console.error('Error fetching company coverage:', error);
      return null;
    }
  }

  /**
   * Get stale relationships (no interaction in 30+ days)
   * @param days Number of days to consider stale (default: 30)
   * @returns Array of stale coverage records
   */
  getStaleRelationships(days: number = 30): TeamCoverageWithDetails[] {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      const cutoffDateStr = cutoffDate.toISOString();

      const stale = this.db.prepare(`
        SELECT 
          tc.*,
          c.first_name as contact_first_name,
          c.last_name as contact_last_name,
          c.email as contact_email,
          c.job_title as contact_job_title,
          cc.company_id as contact_company_id,
          comp.name as contact_company_name
        FROM team_contact_coverage tc
        JOIN contact c ON tc.contact_id = c.id
        LEFT JOIN contact_company cc ON c.id = cc.contact_id AND cc.is_primary = 1
        LEFT JOIN company comp ON cc.company_id = comp.id
        WHERE (tc.last_interaction_date IS NULL OR tc.last_interaction_date < ?)
          AND c.deleted_at IS NULL
        ORDER BY tc.last_interaction_date ASC NULLS FIRST
      `).all(cutoffDateStr) as any[];

      return stale.map(row => ({
        id: row.id,
        contactId: row.contact_id,
        teamMemberId: row.team_member_id,
        relationshipStrength: row.relationship_strength,
        lastInteractionDate: row.last_interaction_date,
        lastInteractionType: row.last_interaction_type,
        notes: row.notes,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        contact: {
          id: row.contact_id,
          firstName: row.contact_first_name,
          lastName: row.contact_last_name,
          email: row.contact_email,
          jobTitle: row.contact_job_title,
          companyId: row.contact_company_id,
          companyName: row.contact_company_name
        }
      }));
    } catch (error) {
      console.error('Error fetching stale relationships:', error);
      return [];
    }
  }

  /**
   * Delete team coverage
   * @param id Coverage ID
   * @returns Number of affected rows
   */
  deleteCoverage(id: string): number {
    try {
      return this.db.prepare(`
        DELETE FROM team_contact_coverage WHERE id = ?
      `).run(id).changes;
    } catch (error) {
      console.error('Error deleting team coverage:', error);
      throw error;
    }
  }

  /**
   * Get coverage by ID
   * @param id Coverage ID
   * @returns Team contact coverage or null
   */
  private getCoverageById(id: string): TeamContactCoverage | null {
    try {
      const coverage = this.db.prepare(`
        SELECT * FROM team_contact_coverage WHERE id = ?
      `).get(id) as any;

      if (!coverage) return null;

      return {
        id: coverage.id,
        contactId: coverage.contact_id,
        teamMemberId: coverage.team_member_id,
        relationshipStrength: coverage.relationship_strength,
        lastInteractionDate: coverage.last_interaction_date,
        lastInteractionType: coverage.last_interaction_type,
        notes: coverage.notes,
        createdAt: coverage.created_at,
        updatedAt: coverage.updated_at
      };
    } catch (error) {
      console.error('Error fetching coverage by ID:', error);
      return null;
    }
  }
}