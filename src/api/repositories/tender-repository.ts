/**
 * Repository for managing tenders
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";
import { BaseTender, TenderQualificationStatus, BaseCompany } from "../../types/shared-types";

// Extended tender interface for internal use
interface Tender extends BaseTender {
  company?: BaseCompany;
}

// Database row type
interface TenderRow {
  id: string;
  request_no: string;
  status: string;
  type: string;
  summary: string;
  issued_by: string;
  unspsc: string | null;
  closing_date: string;
  source_email: string | null;
  tender_url: string | null;
  additional_info: string | null;
  qualification_status: string;
  qualification_reason: string | null;
  qualified_by: string | null;
  qualified_at: string | null;
  company_id: string | null;
  deal_id: string | null;
  tags: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  updated_by: string | null;
  deleted_at: string | null;
}

// Create and update types
export interface TenderCreate {
  requestNo: string;
  status?: string;
  type?: string;
  summary: string;
  issuedBy: string;
  unspsc?: string;
  closingDate: string;
  sourceEmail?: string;
  tenderUrl?: string;
  additionalInfo?: string;
  qualificationStatus?: TenderQualificationStatus;
  qualificationReason?: string;
  companyId?: string;
  tags?: string[];
  notes?: string;
}

export interface TenderUpdate {
  summary?: string;
  closingDate?: string;
  additionalInfo?: string;
  qualificationStatus?: TenderQualificationStatus;
  qualificationReason?: string;
  qualifiedBy?: string;
  qualifiedAt?: string;
  companyId?: string;
  dealId?: string;
  tags?: string[];
  notes?: string;
}

/**
 * Repository for managing tenders
 */
export class TenderRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('tender');
  }

  /**
   * Convert database row to tender object
   */
  private rowToTender(row: TenderRow): Tender {
    return {
      id: row.id,
      requestNo: row.request_no,
      status: row.status as any,
      type: row.type,
      summary: row.summary,
      issuedBy: row.issued_by,
      unspsc: row.unspsc || undefined,
      closingDate: row.closing_date,
      sourceEmail: row.source_email || undefined,
      tenderUrl: row.tender_url || undefined,
      additionalInfo: row.additional_info || undefined,
      qualificationStatus: row.qualification_status as TenderQualificationStatus,
      qualificationReason: row.qualification_reason || undefined,
      qualifiedBy: row.qualified_by || undefined,
      qualifiedAt: row.qualified_at || undefined,
      companyId: row.company_id || undefined,
      dealId: row.deal_id || undefined,
      tags: row.tags ? JSON.parse(row.tags) : undefined,
      notes: row.notes || undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: row.created_by || undefined,
      updatedBy: row.updated_by || undefined,
      deletedAt: row.deleted_at || undefined
    };
  }

  /**
   * Get all tenders with optional filtering
   */
  getAllTenders(filters?: {
    qualificationStatus?: TenderQualificationStatus;
    includeDeleted?: boolean;
  }): Tender[] {
    try {
      let query = `
        SELECT 
          t.*,
          c.id as company_id,
          c.name as company_name,
          c.industry as company_industry
        FROM tender t
        LEFT JOIN company c ON t.company_id = c.id
        WHERE 1=1
      `;
      
      const params: any[] = [];
      
      if (!filters?.includeDeleted) {
        query += ' AND t.deleted_at IS NULL';
      }
      
      if (filters?.qualificationStatus) {
        query += ' AND t.qualification_status = ?';
        params.push(filters.qualificationStatus);
      }
      
      query += " ORDER BY t.closing_date ASC, t.created_at DESC";
      
      const rows = this.db.prepare(query).all(...params) as (TenderRow & {
        company_name?: string;
        company_industry?: string;
      })[];
      
      return rows.map(row => {
        const tender = this.rowToTender(row);
        
        // Add company info if available
        if (row.company_id && row.company_name) {
          tender.company = {
            id: row.company_id,
            name: row.company_name,
            industry: row.company_industry || undefined,
            createdAt: "",
            updatedAt: ""
          };
        }
        
        return tender;
      });
    } catch (error) {
      console.error('Error fetching tenders:", error);
      return [];
    }
  }

  /**
   * Get tender by ID
   */
  getTenderById(id: string): Tender | null {
    try {
      const query = `
        SELECT 
          t.*,
          c.id as company_id,
          c.name as company_name,
          c.industry as company_industry
        FROM tender t
        LEFT JOIN company c ON t.company_id = c.id
        WHERE t.id = ?
      `;
      
      const row = this.db.prepare(query).get(id) as (TenderRow & {
        company_name?: string;
        company_industry?: string;
      }) | undefined;
      
      if (!row) return null;
      
      const tender = this.rowToTender(row);
      
      // Add company info if available
      if (row.company_id && row.company_name) {
        tender.company = {
          id: row.company_id,
          name: row.company_name,
          industry: row.company_industry || undefined,
          createdAt: "",
          updatedAt: ""
        };
      }
      
      return tender;
    } catch (error) {
      console.error('Error fetching tender by ID:", error);
      return null;
    }
  }

  /**
   * Get tender by request number
   */
  getTenderByRequestNo(requestNo: string): Tender | null {
    try {
      const query = `
        SELECT * FROM tender 
        WHERE request_no = ? AND deleted_at IS NULL
      `;
      
      const row = this.db.prepare(query).get(requestNo) as TenderRow | undefined;
      
      if (!row) return null;
      
      return this.rowToTender(row);
    } catch (error) {
      console.error("Error fetching tender by request number:", error);
      return null;
    }
  }

  /**
   * Create a new tender
   */
  createTender(data: TenderCreate): Tender | null {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();
      
      const insertData = {
        id,
        request_no: data.requestNo,
        status: data.status || "Current",
        type: data.type || "Tender",
        summary: data.summary,
        issued_by: data.issuedBy,
        unspsc: data.unspsc || null,
        closing_date: data.closingDate,
        source_email: data.sourceEmail || null,
        tender_url: data.tenderUrl || null,
        additional_info: data.additionalInfo || null,
        qualification_status: data.qualificationStatus || "new",
        qualification_reason: data.qualificationReason || null,
        company_id: data.companyId || null,
        tags: data.tags ? JSON.stringify(data.tags) : null,
        notes: data.notes || null,
        created_at: now,
        updated_at: now,
        created_by: "email_ingestion"
      };
      
      const stmt = this.db.prepare(`
        INSERT INTO tender (
          id, request_no, status, type, summary, issued_by, unspsc,
          closing_date, source_email, tender_url, additional_info,
          qualification_status, qualification_reason, company_id,
          tags, notes, created_at, updated_at, created_by
        ) VALUES (
          :id, :request_no, :status, :type, :summary, :issued_by, :unspsc,
          :closing_date, :source_email, :tender_url, :additional_info,
          :qualification_status, :qualification_reason, :company_id,
          :tags, :notes, :created_at, :updated_at, :created_by
        )
      `);
      
      stmt.run(insertData);
      
      return this.getTenderById(id);
    } catch (error) {
      console.error('Error creating tender:', error);
      return null;
    }
  }

  /**
   * Update a tender
   */
  updateTender(id: string, data: TenderUpdate): Tender | null {
    try {
      const now = new Date().toISOString();
      const updates: any = { updated_at: now };
      const updateFields: string[] = ['updated_at = :updated_at'];
      
      // Build dynamic update query
      if (data.summary !== undefined) {
        updates.summary = data.summary;
        updateFields.push('summary = :summary');
      }
      
      if (data.closingDate !== undefined) {
        updates.closing_date = data.closingDate;
        updateFields.push('closing_date = :closing_date');
      }
      
      if (data.additionalInfo !== undefined) {
        updates.additional_info = data.additionalInfo;
        updateFields.push('additional_info = :additional_info');
      }
      
      if (data.qualificationStatus !== undefined) {
        updates.qualification_status = data.qualificationStatus;
        updateFields.push('qualification_status = :qualification_status');
      }
      
      if (data.qualificationReason !== undefined) {
        updates.qualification_reason = data.qualificationReason;
        updateFields.push('qualification_reason = :qualification_reason');
      }
      
      if (data.qualifiedBy !== undefined) {
        updates.qualified_by = data.qualifiedBy;
        updateFields.push('qualified_by = :qualified_by');
      }
      
      if (data.qualifiedAt !== undefined) {
        updates.qualified_at = data.qualifiedAt;
        updateFields.push('qualified_at = :qualified_at');
      }
      
      if (data.companyId !== undefined) {
        updates.company_id = data.companyId;
        updateFields.push('company_id = :company_id');
      }
      
      if (data.dealId !== undefined) {
        updates.deal_id = data.dealId;
        updateFields.push('deal_id = :deal_id');
      }
      
      if (data.tags !== undefined) {
        updates.tags = JSON.stringify(data.tags);
        updateFields.push('tags = :tags');
      }
      
      if (data.notes !== undefined) {
        updates.notes = data.notes;
        updateFields.push('notes = :notes');
      }
      
      updates.id = id;
      
      const stmt = this.db.prepare(`
        UPDATE tender
        SET ${updateFields.join(', ')}
        WHERE id = :id
      `);
      
      const result = stmt.run(updates);
      
      if (result.changes === 0) {
        return null;
      }
      
      return this.getTenderById(id);
    } catch (error) {
      console.error('Error updating tender:", error);
      return null;
    }
  }

  /**
   * Soft delete a tender
   */
  deleteTender(id: string): boolean {
    try {
      const now = new Date().toISOString();
      
      const stmt = this.db.prepare(`
        UPDATE tender
        SET deleted_at = :deleted_at, updated_at = :updated_at
        WHERE id = :id
      `);
      
      const result = stmt.run({ id, deleted_at: now, updated_at: now });
      
      return result.changes > 0;
    } catch (error) {
      console.error("Error deleting tender:", error);
      return false;
    }
  }

  /**
   * Move tender to a new qualification status
   */
  qualifyTender(
    id: string, 
    status: TenderQualificationStatus, 
    reason?: string,
    qualifiedBy?: string
  ): Tender | null {
    try {
      const now = new Date().toISOString();
      
      const updates: TenderUpdate = {
        qualificationStatus: status,
        qualificationReason: reason,
        qualifiedBy: qualifiedBy || "system",
        qualifiedAt: now
      };
      
      return this.updateTender(id, updates);
    } catch (error) {
      console.error('Error qualifying tender:', error);
      return null;
    }
  }

  /**
   * Get tenders by company
   */
  getTendersByCompany(companyId: string): Tender[] {
    try {
      const query = `
        SELECT * FROM tender
        WHERE company_id = ? AND deleted_at IS NULL
        ORDER BY closing_date ASC
      `;
      
      const rows = this.db.prepare(query).all(companyId) as TenderRow[];
      
      return rows.map(row => this.rowToTender(row));
    } catch (error) {
      console.error('Error fetching tenders by company:', error);
      return [];
    }
  }

  /**
   * Get tenders closing soon (within specified days)
   */
  getTendersClosingSoon(days: number = 7): Tender[] {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() + days);
      
      const query = `
        SELECT 
          t.*,
          c.id as company_id,
          c.name as company_name,
          c.industry as company_industry
        FROM tender t
        LEFT JOIN company c ON t.company_id = c.id
        WHERE t.deleted_at IS NULL
          AND t.qualification_status IN ('new", 'reviewing')
          AND date(t.closing_date) <= date(?)
          AND date(t.closing_date) >= date('now")
        ORDER BY t.closing_date ASC
      `;
      
      const rows = this.db.prepare(query).all(cutoffDate.toISOString()) as (TenderRow & {
        company_name?: string;
        company_industry?: string;
      })[];
      
      return rows.map(row => {
        const tender = this.rowToTender(row);
        
        // Add company info if available
        if (row.company_id && row.company_name) {
          tender.company = {
            id: row.company_id,
            name: row.company_name,
            industry: row.company_industry || undefined,
            createdAt: "",
            updatedAt: ""
          };
        }
        
        return tender;
      });
    } catch (error) {
      console.error('Error fetching tenders closing soon:", error);
      return [];
    }
  }
}