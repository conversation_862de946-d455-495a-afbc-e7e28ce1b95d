/**
 * Repository for managing Harvest invoice cache
 */

import { BaseRepository } from "./base-repository";

/**
 * Interface for Harvest invoice cache entity
 */
export interface HarvestInvoiceCache {
  harvestClientId: number;
  totalInvoiced: number;
  invoiceCount: number;
  lastUpdated: string;
  createdAt: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new cache entry
 */
export interface HarvestInvoiceCacheCreate {
  harvestClientId: number;
  totalInvoiced: number;
  invoiceCount: number;
}

/**
 * Interface for updating a cache entry
 */
export interface HarvestInvoiceCacheUpdate {
  totalInvoiced?: number;
  invoiceCount?: number;
}

/**
 * Repository for managing Harvest invoice cache
 */
export class HarvestInvoiceCacheRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super("harvest_invoice_cache");
  }

  /**
   * Get all cache entries
   * @returns Array of cache entries
   */
  getAllCacheEntries(): HarvestInvoiceCache[] {
    try {
      const entries = this.db
        .prepare(
          `
        SELECT 
          harvest_client_id as harvestClientId,
          total_invoiced as totalInvoiced,
          invoice_count as invoiceCount,
          last_updated as lastUpdated,
          created_at as createdAt
        FROM ${this.tableName}
        ORDER BY last_updated DESC
      `,
        )
        .all() as HarvestInvoiceCache[];

      return entries;
    } catch (error) {
      console.error("Error fetching all Harvest invoice cache entries:", error);
      return [];
    }
  }

  /**
   * Get cache entry by Harvest client ID
   * @param harvestClientId Harvest client ID
   * @returns Cache entry or null if not found
   */
  getCacheEntry(harvestClientId: number): HarvestInvoiceCache | null {
    try {
      const entry = this.db
        .prepare(
          `
        SELECT 
          harvest_client_id as harvestClientId,
          total_invoiced as totalInvoiced,
          invoice_count as invoiceCount,
          last_updated as lastUpdated,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE harvest_client_id = ?
      `,
        )
        .get(harvestClientId) as HarvestInvoiceCache | undefined;

      return entry || null;
    } catch (error) {
      console.error(
        `Error fetching cache entry for client ${harvestClientId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Create a new cache entry
   * @param cacheData Cache data
   * @returns Created cache entry
   */
  createCacheEntry(cacheData: HarvestInvoiceCacheCreate): HarvestInvoiceCache {
    try {
      // Check if entry already exists
      const existing = this.getCacheEntry(cacheData.harvestClientId);
      if (existing) {
        throw new Error(
          `Cache entry for Harvest client ${cacheData.harvestClientId} already exists`,
        );
      }

      const now = new Date().toISOString();

      this.db
        .prepare(
          `
        INSERT INTO ${this.tableName} (
          harvest_client_id,
          total_invoiced,
          invoice_count,
          last_updated,
          created_at
        ) VALUES (?, ?, ?, ?, ?)
      `,
        )
        .run(
          cacheData.harvestClientId,
          cacheData.totalInvoiced,
          cacheData.invoiceCount,
          now,
          now,
        );

      return this.getCacheEntry(
        cacheData.harvestClientId,
      ) as HarvestInvoiceCache;
    } catch (error) {
      console.error("Error creating Harvest invoice cache entry:", error);
      throw error;
    }
  }

  /**
   * Update an existing cache entry
   * @param harvestClientId Harvest client ID
   * @param updateData Update data
   * @returns Updated cache entry or null if not found
   */
  updateCacheEntry(
    harvestClientId: number,
    updateData: HarvestInvoiceCacheUpdate,
  ): HarvestInvoiceCache | null {
    try {
      const existing = this.getCacheEntry(harvestClientId);
      if (!existing) {
        return null;
      }

      const now = new Date().toISOString();

      this.db
        .prepare(
          `
        UPDATE ${this.tableName}
        SET
          total_invoiced = ?,
          invoice_count = ?,
          last_updated = ?
        WHERE harvest_client_id = ?
      `,
        )
        .run(
          updateData.totalInvoiced !== undefined
            ? updateData.totalInvoiced
            : existing.totalInvoiced,
          updateData.invoiceCount !== undefined
            ? updateData.invoiceCount
            : existing.invoiceCount,
          now,
          harvestClientId,
        );

      return this.getCacheEntry(harvestClientId);
    } catch (error) {
      console.error(
        `Error updating cache entry for client ${harvestClientId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Upsert cache entry (create if doesn't exist, update if exists)
   * @param harvestClientId Harvest client ID
   * @param totalInvoiced Total invoiced amount
   * @param invoiceCount Number of invoices
   * @returns Cache entry
   */
  upsertCacheEntry(
    harvestClientId: number,
    totalInvoiced: number,
    invoiceCount: number,
  ): HarvestInvoiceCache {
    try {
      const existing = this.getCacheEntry(harvestClientId);

      if (existing) {
        return this.updateCacheEntry(harvestClientId, {
          totalInvoiced,
          invoiceCount,
        }) as HarvestInvoiceCache;
      } else {
        return this.createCacheEntry({
          harvestClientId,
          totalInvoiced,
          invoiceCount,
        });
      }
    } catch (error) {
      console.error(
        `Error upserting cache entry for client ${harvestClientId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Delete a cache entry
   * @param harvestClientId Harvest client ID
   * @returns Boolean indicating success
   */
  deleteCacheEntry(harvestClientId: number): boolean {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE harvest_client_id = ?
      `,
        )
        .run(harvestClientId);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(
        `Error deleting cache entry for client ${harvestClientId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get cache entries by multiple client IDs
   * @param harvestClientIds Array of Harvest client IDs
   * @returns Array of cache entries
   */
  getCacheEntriesByClientIds(
    harvestClientIds: number[],
  ): HarvestInvoiceCache[] {
    try {
      if (harvestClientIds.length === 0) {
        return [];
      }

      const placeholders = harvestClientIds.map(() => "?").join(",");
      const entries = this.db
        .prepare(
          `
        SELECT 
          harvest_client_id as harvestClientId,
          total_invoiced as totalInvoiced,
          invoice_count as invoiceCount,
          last_updated as lastUpdated,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE harvest_client_id IN (${placeholders})
        ORDER BY last_updated DESC
      `,
        )
        .all(...harvestClientIds) as HarvestInvoiceCache[];

      return entries;
    } catch (error) {
      console.error("Error fetching cache entries by client IDs:", error);
      return [];
    }
  }

  /**
   * Get cache entries that are stale (need updating)
   * @param maxAgeHours Maximum age in hours before considering stale (default: 24)
   * @returns Array of stale cache entries
   */
  getStaleCacheEntries(maxAgeHours: number = 24): HarvestInvoiceCache[] {
    try {
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - maxAgeHours);
      const cutoffIso = cutoffDate.toISOString();

      const entries = this.db
        .prepare(
          `
        SELECT 
          harvest_client_id as harvestClientId,
          total_invoiced as totalInvoiced,
          invoice_count as invoiceCount,
          last_updated as lastUpdated,
          created_at as createdAt
        FROM ${this.tableName}
        WHERE last_updated < ?
        ORDER BY last_updated ASC
      `,
        )
        .all(cutoffIso) as HarvestInvoiceCache[];

      return entries;
    } catch (error) {
      console.error("Error fetching stale cache entries:", error);
      return [];
    }
  }

  /**
   * Get cache statistics
   * @returns Cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    totalInvoiced: number;
    totalInvoices: number;
    oldestEntry: string | null;
    newestEntry: string | null;
    averageInvoiceValue: number;
  } {
    try {
      const stats = this.db
        .prepare(
          `
        SELECT 
          COUNT(*) as totalEntries,
          SUM(total_invoiced) as totalInvoiced,
          SUM(invoice_count) as totalInvoices,
          MIN(last_updated) as oldestEntry,
          MAX(last_updated) as newestEntry
        FROM ${this.tableName}
      `,
        )
        .get() as any;

      const averageInvoiceValue =
        stats.totalInvoices > 0
          ? (stats.totalInvoiced || 0) / stats.totalInvoices
          : 0;

      return {
        totalEntries: stats.totalEntries || 0,
        totalInvoiced: stats.totalInvoiced || 0,
        totalInvoices: stats.totalInvoices || 0,
        oldestEntry: stats.oldestEntry || null,
        newestEntry: stats.newestEntry || null,
        averageInvoiceValue: Math.round(averageInvoiceValue * 100) / 100,
      };
    } catch (error) {
      console.error("Error fetching cache statistics:", error);
      return {
        totalEntries: 0,
        totalInvoiced: 0,
        totalInvoices: 0,
        oldestEntry: null,
        newestEntry: null,
        averageInvoiceValue: 0,
      };
    }
  }

  /**
   * Clear all cache entries
   * @returns Number of deleted entries
   */
  clearAllCache(): number {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
      `,
        )
        .run();

      return result.changes || 0;
    } catch (error) {
      console.error("Error clearing all cache entries:", error);
      return 0;
    }
  }

  /**
   * Clear stale cache entries
   * @param maxAgeHours Maximum age in hours before considering stale (default: 24)
   * @returns Number of deleted entries
   */
  clearStaleCache(maxAgeHours: number = 24): number {
    try {
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - maxAgeHours);
      const cutoffIso = cutoffDate.toISOString();

      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE last_updated < ?
      `,
        )
        .run(cutoffIso);

      return result.changes || 0;
    } catch (error) {
      console.error("Error clearing stale cache entries:", error);
      return 0;
    }
  }

  /**
   * Check if cache entry exists for a client
   * @param harvestClientId Harvest client ID
   * @returns Boolean indicating if entry exists
   */
  cacheEntryExists(harvestClientId: number): boolean {
    try {
      const count = this.db
        .prepare(
          `
        SELECT COUNT(*) as count
        FROM ${this.tableName}
        WHERE harvest_client_id = ?
      `,
        )
        .get(harvestClientId) as { count: number };

      return count.count > 0;
    } catch (error) {
      console.error(
        `Error checking if cache entry exists for client ${harvestClientId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Bulk upsert cache entries (efficient for updating many entries)
   * @param entries Array of cache entries to upsert
   * @returns Number of entries processed
   */
  bulkUpsertCacheEntries(entries: HarvestInvoiceCacheCreate[]): number {
    try {
      let processed = 0;

      const transaction = this.db.transaction(() => {
        for (const entry of entries) {
          this.upsertCacheEntry(
            entry.harvestClientId,
            entry.totalInvoiced,
            entry.invoiceCount,
          );
          processed++;
        }
      });

      transaction();
      return processed;
    } catch (error) {
      console.error("Error bulk upserting cache entries:", error);
      throw error;
    }
  }
}
