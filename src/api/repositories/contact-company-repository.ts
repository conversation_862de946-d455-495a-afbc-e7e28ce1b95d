/**
 * Repository for managing contact-company relationships
 */

// Using any type for Database to avoid TypeScript namespace issues

// Define the contact-company relationship interface
export interface ContactCompanyRelationship {
  contact_id: string;
  company_id: string;
  role?: string;
  is_primary: boolean;
  created_at: string;
  created_by: string;
  deleted_at?: string;
}

// Define the repository class
export class ContactCompanyRepository {
  private db: any;

  constructor(db: any) {
    this.db = db;
  }

  /**
   * Associate a contact with a company
   * @param contactId The contact ID
   * @param companyId The company ID
   * @param isPrimary Whether this is the primary company for this contact
   * @param role Optional role of the contact at the company
   * @param createdBy The user who created the relationship
   * @returns The created relationship
   */
  associateContactWithCompany(
    contactId: string,
    companyId: string,
    isPrimary: boolean = false,
    role?: string,
    createdBy: string = "system",
  ): ContactCompanyRelationship {
    // Verify that both contact and company exist
    const contactExists = this.db
      .prepare("SELECT id FROM contact WHERE id = ?")
      .get(contactId);
    const companyExists = this.db
      .prepare("SELECT id FROM company WHERE id = ?")
      .get(companyId);

    if (!contactExists) {
      throw new Error(`Contact with ID ${contactId} not found`);
    }

    if (!companyExists) {
      throw new Error(`Company with ID ${companyId} not found`);
    }

    // Check if the relationship already exists
    const existingRelationship = this.db
      .prepare(
        "SELECT * FROM contact_company WHERE contact_id = ? AND company_id = ?",
      )
      .get(contactId, companyId);

    if (existingRelationship) {
      throw new Error("This contact is already associated with this company");
    }

    // If this is marked as primary, ensure any existing primary is demoted
    if (isPrimary) {
      this.db
        .prepare(
          `
        UPDATE contact_company
        SET is_primary = 0
        WHERE contact_id = ? AND is_primary = 1
      `,
        )
        .run(contactId);
    }

    const now = new Date().toISOString();

    const relationship: ContactCompanyRelationship = {
      contact_id: contactId,
      company_id: companyId,
      role,
      is_primary: isPrimary,
      created_at: now,
      created_by: createdBy,
    };

    this.db
      .prepare(
        `
      INSERT INTO contact_company (
        contact_id,
        company_id,
        role,
        is_primary,
        created_at,
        created_by
      ) VALUES (?, ?, ?, ?, ?, ?)
    `,
      )
      .run(
        relationship.contact_id,
        relationship.company_id,
        relationship.role,
        relationship.is_primary ? 1 : 0,
        relationship.created_at,
        relationship.created_by,
      );

    // Now also ensure the company_id in the contact table is set to the primary company
    if (isPrimary) {
      this.db
        .prepare(
          `
        UPDATE contact
        SET company_id = ?
        WHERE id = ?
      `,
        )
        .run(companyId, contactId);
    }

    return relationship;
  }

  /**
   * Remove an association between a contact and company
   * @param contactId The contact ID
   * @param companyId The company ID
   * @returns Success flag
   */
  disassociateContactFromCompany(
    contactId: string,
    companyId: string,
  ): boolean {
    // Check if this is the primary company
    const relationship = this.db
      .prepare(
        "SELECT is_primary FROM contact_company WHERE contact_id = ? AND company_id = ?",
      )
      .get(contactId, companyId);

    const result = this.db
      .prepare(
        `
      DELETE FROM contact_company
      WHERE contact_id = ? AND company_id = ?
    `,
      )
      .run(contactId, companyId);

    // If this was the primary company, find another company to set as primary
    if (relationship && relationship.is_primary === 1) {
      const nextCompany = this.db
        .prepare(
          `
        SELECT company_id FROM contact_company
        WHERE contact_id = ?
        LIMIT 1
      `,
        )
        .get(contactId);

      if (nextCompany) {
        // Set the next company as primary
        this.db
          .prepare(
            `
          UPDATE contact_company
          SET is_primary = 1
          WHERE contact_id = ? AND company_id = ?
        `,
          )
          .run(contactId, nextCompany.company_id);

        // Update the contact's company_id
        this.db
          .prepare(
            `
          UPDATE contact
          SET company_id = ?
          WHERE id = ?
        `,
          )
          .run(nextCompany.company_id, contactId);
      } else {
        // No more companies, set contact's company_id to null
        this.db
          .prepare(
            `
          UPDATE contact
          SET company_id = NULL
          WHERE id = ?
        `,
          )
          .run(contactId);
      }
    }

    return result.changes > 0;
  }

  /**
   * Update a contact's role at a company
   * @param contactId The contact ID
   * @param companyId The company ID
   * @param role The new role
   * @returns Success flag
   */
  updateContactRole(
    contactId: string,
    companyId: string,
    role?: string,
  ): boolean {
    const result = this.db
      .prepare(
        `
      UPDATE contact_company
      SET role = ?
      WHERE contact_id = ? AND company_id = ?
    `,
      )
      .run(role, contactId, companyId);

    return result.changes > 0;
  }

  /**
   * Set a contact's primary company
   * @param contactId The contact ID
   * @param companyId The company ID to set as primary
   * @returns Success flag
   */
  setContactPrimaryCompany(contactId: string, companyId: string): boolean {
    // First check if the relationship exists
    const relationshipExists = this.db
      .prepare(
        "SELECT 1 FROM contact_company WHERE contact_id = ? AND company_id = ?",
      )
      .get(contactId, companyId);

    if (!relationshipExists) {
      throw new Error("This contact is not associated with this company");
    }

    // Start a transaction
    this.db.prepare("BEGIN TRANSACTION").run();

    try {
      // Remove primary status from all other companies
      this.db
        .prepare(
          `
        UPDATE contact_company
        SET is_primary = 0
        WHERE contact_id = ?
      `,
        )
        .run(contactId);

      // Set this company as primary
      this.db
        .prepare(
          `
        UPDATE contact_company
        SET is_primary = 1
        WHERE contact_id = ? AND company_id = ?
      `,
        )
        .run(contactId, companyId);

      // Update the contact's company_id
      this.db
        .prepare(
          `
        UPDATE contact
        SET company_id = ?
        WHERE id = ?
      `,
        )
        .run(companyId, contactId);

      this.db.prepare("COMMIT").run();
      return true;
    } catch (error) {
      this.db.prepare("ROLLBACK").run();
      throw error;
    }
  }

  /**
   * Get all companies associated with a contact
   * @param contactId The contact ID
   * @returns Array of companies with relationship details
   */
  getContactCompanies(contactId: string): Array<any> {
    return this.db
      .prepare(
        `
      SELECT c.*, cc.role, cc.is_primary
      FROM company c
      JOIN contact_company cc ON c.id = cc.company_id
      WHERE cc.contact_id = ?
      ORDER BY cc.is_primary DESC, c.name
    `,
      )
      .all(contactId);
  }

  /**
   * Get all contacts associated with a company
   * @param companyId The company ID
   * @returns Array of contacts with relationship details
   */
  getCompanyContacts(companyId: string): Array<any> {
    return this.db
      .prepare(
        `
      SELECT c.*, cc.role, cc.is_primary
      FROM contact c
      JOIN contact_company cc ON c.id = cc.contact_id
      WHERE cc.company_id = ? AND c.deleted_at IS NULL
      ORDER BY c.first_name, c.last_name
    `,
      )
      .all(companyId);
  }

  /**
   * Get primary contacts for a company
   * @param companyId The company ID
   * @returns Array of primary contacts
   */
  getCompanyPrimaryContacts(companyId: string): Array<any> {
    return this.db
      .prepare(
        `
      SELECT c.*
      FROM contact c
      JOIN contact_company cc ON c.id = cc.contact_id
      WHERE cc.company_id = ? AND cc.is_primary = 1 AND c.deleted_at IS NULL
      ORDER BY c.first_name, c.last_name
    `,
      )
      .all(companyId);
  }

  /**
   * Check if a contact is associated with a company
   * @param contactId The contact ID
   * @param companyId The company ID
   * @returns Boolean indicating if the association exists
   */
  isContactAssociatedWithCompany(
    contactId: string,
    companyId: string,
  ): boolean {
    const relationship = this.db
      .prepare(
        `
      SELECT 1 FROM contact_company
      WHERE contact_id = ? AND company_id = ?
    `,
      )
      .get(contactId, companyId);

    return !!relationship;
  }

  /**
   * Get all contact-company relationships
   * @returns Array of all contact-company relationships
   */
  getAllRelationships(): Array<{
    contact_id: string;
    company_id: string;
    role: string;
    is_primary: boolean;
    created_at: string;
    created_by: string;
  }> {
    try {
      if (!this.db) {
        console.error(
          "ContactCompanyRepository: Database instance is undefined in getAllRelationships",
        );
        return [];
      }

      return this.db
        .prepare(
          `
        SELECT contact_id, company_id, role, is_primary, created_at, created_by
        FROM contact_company
        ORDER BY created_at DESC
      `,
        )
        .all();
    } catch (error) {
      console.error("Error fetching all contact-company relationships:", error);
      return [];
    }
  }

  /**
   * Get contacts by company
   * @param companyId The company ID
   * @returns Array of contacts with relationship details
   */
  getContactsByCompany(companyId: string): Array<{
    contact_id: string;
    company_id: string;
    role: string;
    is_primary: boolean;
  }> {
    try {
      return this.db
        .prepare(
          `
        SELECT contact_id, company_id, role, is_primary
        FROM contact_company
        WHERE company_id = ?
        ORDER BY is_primary DESC, role
      `,
        )
        .all(companyId);
    } catch (error) {
      console.error("Error fetching contacts by company:", error);
      return [];
    }
  }

  /**
   * Get companies by contact
   * @param contactId The contact ID
   * @returns Array of companies with relationship details
   */
  getCompaniesByContact(contactId: string): Array<{
    contact_id: string;
    company_id: string;
    role: string;
    is_primary: boolean;
  }> {
    try {
      return this.db
        .prepare(
          `
        SELECT contact_id, company_id, role, is_primary
        FROM contact_company
        WHERE contact_id = ?
        ORDER BY is_primary DESC, role
      `,
        )
        .all(contactId);
    } catch (error) {
      console.error("Error fetching companies by contact:", error);
      return [];
    }
  }
}

// Export a factory function to create the repository
export function createContactCompanyRepository(
  db: any,
): ContactCompanyRepository {
  return new ContactCompanyRepository(db);
}
