import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";
import { db } from "../services/db-service";
import { logActivity } from "../services/activity-service";
import { 
  Project, 
  ProjectContact, 
  ProjectDependency,
  CreateProjectInput,
  UpdateProjectInput 
} from "../../types/project-types";

// Re-export types for backward compatibility
export { Project, ProjectContact, ProjectDependency } from "../../types/project-types";

export class ProjectRepository extends BaseRepository {
  constructor() {
    super('project');
    this.checkTableExists();
  }

  /**
   * Check if project tables exist (for production safety)
   */
  private checkTableExists(): boolean {
    try {
      const result = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      `).get();
      
      if (!result) {
        console.warn('Project table does not exist. Run migrations to create it.');
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error checking project table existence:", error);
      return false;
    }
  }

  /**
   * Create a new project
   */
  createProject(projectData: CreateProjectInput & { created_by?: string }): Project | null {
    if (!this.checkTableExists()) {
      console.error('Cannot create project: table does not exist");
      return null;
    }
    const id = uuidv4();
    const now = new Date().toISOString();

    const project: Project = {
      id,
      ...projectData,
      tags: projectData.tags ? JSON.stringify(projectData.tags) : null,
      custom_fields: projectData.custom_fields ? JSON.stringify(projectData.custom_fields) : null,
      created_at: now,
      updated_at: now
    };

    const stmt = this.db.prepare(`
      INSERT INTO project (
        id, name, description, status, project_type,
        start_date, end_date, budget, spent, currency,
        harvest_project_id, company_id, deal_id,
        tags, custom_fields,
        created_at, updated_at, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      project.id,
      project.name,
      project.description,
      project.status,
      project.project_type,
      project.start_date,
      project.end_date,
      project.budget,
      project.spent,
      project.currency || "AUD",
      project.harvest_project_id,
      project.company_id,
      project.deal_id,
      project.tags,
      project.custom_fields,
      project.created_at,
      project.updated_at,
      project.created_by,
      project.updated_by
    );

    // Log activity
    logActivity({
      action: "project_created",
      actor: project.created_by || "system",
      target: project.id,
      targetType: "project",
      metadata: {
        projectName: project.name,
        companyId: project.company_id,
        status: project.status
      }
    });

    return this.getProjectById(id)!;
  }

  /**
   * Get project by ID
   */
  getProjectById(id: string): Project | null {
    if (!this.checkTableExists()) return null;
    const stmt = this.db.prepare(`
      SELECT * FROM project WHERE id = ? AND deleted_at IS NULL
    `);
    
    const row = stmt.get(id);
    if (!row) return null;

    return this.mapRowToProject(row);
  }

  /**
   * Get project by Harvest ID
   */
  getProjectByHarvestId(harvestId: string): Project | null {
    const stmt = this.db.prepare(`
      SELECT * FROM project WHERE harvest_project_id = ? AND deleted_at IS NULL
    `);
    
    const row = stmt.get(harvestId);
    if (!row) return null;

    return this.mapRowToProject(row);
  }

  /**
   * Get all projects for a company
   */
  getProjectsByCompany(companyId: string): Project[] {
    const stmt = this.db.prepare(`
      SELECT * FROM project 
      WHERE company_id = ? AND deleted_at IS NULL
      ORDER BY created_at DESC
    `);
    
    const rows = stmt.all(companyId);
    return rows.map(row => this.mapRowToProject(row));
  }

  /**
   * Get all projects
   */
  getAllProjects(): Project[] {
    const stmt = this.db.prepare(`
      SELECT * FROM project 
      WHERE deleted_at IS NULL
      ORDER BY created_at DESC
    `);
    
    const rows = stmt.all();
    return rows.map(row => this.mapRowToProject(row));
  }

  /**
   * Update a project
   */
  updateProject(id: string, updates: Partial<Project>): Project | null {
    const existing = this.getProjectById(id);
    if (!existing) return null;

    const now = new Date().toISOString();
    const updatedProject = {
      ...existing,
      ...updates,
      id, // Ensure ID doesn"t change
      updated_at: now
    };

    // Convert arrays/objects to JSON strings
    if (updates.tags) {
      updatedProject.tags = JSON.stringify(updates.tags);
    }
    if (updates.custom_fields) {
      updatedProject.custom_fields = JSON.stringify(updates.custom_fields);
    }

    const stmt = this.db.prepare(`
      UPDATE project SET
        name = ?, description = ?, status = ?, project_type = ?,
        start_date = ?, end_date = ?, budget = ?, spent = ?, currency = ?,
        harvest_project_id = ?, company_id = ?, deal_id = ?,
        tags = ?, custom_fields = ?,
        updated_at = ?, updated_by = ?
      WHERE id = ?
    `);

    stmt.run(
      updatedProject.name,
      updatedProject.description,
      updatedProject.status,
      updatedProject.project_type,
      updatedProject.start_date,
      updatedProject.end_date,
      updatedProject.budget,
      updatedProject.spent,
      updatedProject.currency,
      updatedProject.harvest_project_id,
      updatedProject.company_id,
      updatedProject.deal_id,
      updatedProject.tags,
      updatedProject.custom_fields,
      updatedProject.updated_at,
      updatedProject.updated_by,
      id
    );

    // Log activity
    logActivity({
      action: "project_updated",
      actor: updatedProject.updated_by || "system",
      target: id,
      targetType: "project",
      metadata: {
        projectName: updatedProject.name,
        changes: Object.keys(updates)
      }
    });

    return this.getProjectById(id);
  }

  /**
   * Soft delete a project
   */
  deleteProject(id: string, deletedBy?: string): boolean {
    const stmt = this.db.prepare(`
      UPDATE project SET deleted_at = ?, updated_by = ? WHERE id = ?
    `);
    
    const result = stmt.run(new Date().toISOString(), deletedBy, id);
    
    if (result.changes > 0) {
      logActivity({
        action: "project_deleted",
        actor: deletedBy || "system",
        target: id,
        targetType: "project"
      });
    }
    
    return result.changes > 0;
  }

  /**
   * Add contact to project
   */
  addProjectContact(data: Omit<ProjectContact, 'created_at" | "updated_at">): ProjectContact {
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO project_contact (
        project_id, contact_id, role, allocation_percentage,
        start_date, end_date, created_at, created_by, updated_at, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT(project_id, contact_id) DO UPDATE SET
        role = excluded.role,
        allocation_percentage = excluded.allocation_percentage,
        start_date = excluded.start_date,
        end_date = excluded.end_date,
        updated_at = excluded.updated_at,
        updated_by = excluded.updated_by
    `);

    stmt.run(
      data.project_id,
      data.contact_id,
      data.role,
      data.allocation_percentage,
      data.start_date,
      data.end_date,
      now,
      data.created_by,
      now,
      data.updated_by
    );

    return {
      ...data,
      created_at: now,
      updated_at: now
    };
  }

  /**
   * Get project contacts
   */
  getProjectContacts(projectId: string): ProjectContact[] {
    const stmt = this.db.prepare(`
      SELECT * FROM project_contact WHERE project_id = ?
    `);
    
    return stmt.all(projectId);
  }

  /**
   * Remove contact from project
   */
  removeProjectContact(projectId: string, contactId: string): boolean {
    const stmt = this.db.prepare(`
      DELETE FROM project_contact WHERE project_id = ? AND contact_id = ?
    `);
    
    const result = stmt.run(projectId, contactId);
    return result.changes > 0;
  }

  /**
   * Add project dependency
   */
  addProjectDependency(data: Omit<ProjectDependency, 'id" | "created_at">): ProjectDependency {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO project_dependency (
        id, predecessor_project_id, successor_project_id,
        dependency_type, lag_days, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      data.predecessor_project_id,
      data.successor_project_id,
      data.dependency_type,
      data.lag_days || 0,
      now,
      data.created_by
    );

    return {
      id,
      ...data,
      created_at: now
    };
  }

  /**
   * Get project dependencies
   */
  getProjectDependencies(projectId: string): {
    predecessors: ProjectDependency[];
    successors: ProjectDependency[];
  } {
    const predecessorStmt = this.db.prepare(`
      SELECT * FROM project_dependency WHERE successor_project_id = ?
    `);
    
    const successorStmt = this.db.prepare(`
      SELECT * FROM project_dependency WHERE predecessor_project_id = ?
    `);
    
    return {
      predecessors: predecessorStmt.all(projectId),
      successors: successorStmt.all(projectId)
    };
  }

  /**
   * Helper to map database row to Project object
   */
  private mapRowToProject(row: any): Project {
    return {
      ...row,
      tags: row.tags ? JSON.parse(row.tags) : [],
      custom_fields: row.custom_fields ? JSON.parse(row.custom_fields) : {}
    };
  }

  /**
   * Get projects for knowledge graph
   */
  getProjectsForKnowledgeGraph(): Array<{
    id: string;
    name: string;
    type: "project";
    status: string;
    companyId: string;
    dealId?: string;
    harvestProjectId?: string;
  }> {
    if (!this.checkTableExists()) {
      console.warn('Project table does not exist, returning empty array');
      return [];
    }

    try {
      const stmt = this.db.prepare(`
        SELECT id, name, status, company_id, deal_id, harvest_project_id
        FROM project 
        WHERE deleted_at IS NULL
      `);
      
      const rows = stmt.all();
      return rows.map(row => ({
        id: row.id,
        name: row.name,
        type: "project" as const,
        status: row.status,
        companyId: row.company_id,
        dealId: row.deal_id,
        harvestProjectId: row.harvest_project_id
      }));
    } catch (error) {
      console.error('Error fetching projects for knowledge graph:", error);
      return [];
    }
  }

  /**
   * Get project relationships for knowledge graph
   */
  getProjectRelationshipsForKnowledgeGraph(): Array<{
    source: string;
    target: string;
    type: string;
    strength: number;
  }> {
    if (!this.checkTableExists()) {
      console.warn('Project table does not exist, returning empty relationships");
      return [];
    }

    const relationships: Array<{
      source: string;
      target: string;
      type: string;
      strength: number;
    }> = [];

    try {
      // Project to company relationships
      const projectCompanyStmt = this.db.prepare(`
        SELECT id as project_id, company_id
        FROM project 
        WHERE deleted_at IS NULL AND company_id IS NOT NULL
      `);
      
      const projectCompanyRows = projectCompanyStmt.all();
      projectCompanyRows.forEach(row => {
        relationships.push({
          source: row.project_id,
          target: row.company_id,
          type: "belongs_to",
          strength: 5
        });
      });

      // Project to deal relationships
      const projectDealStmt = this.db.prepare(`
        SELECT id as project_id, deal_id
        FROM project 
        WHERE deleted_at IS NULL AND deal_id IS NOT NULL
      `);
      
      const projectDealRows = projectDealStmt.all();
      projectDealRows.forEach(row => {
        relationships.push({
          source: row.project_id,
          target: row.deal_id,
          type: "originated_from",
          strength: 4
        });
      });

      // Project to contact relationships (check if table exists)
      try {
        const projectContactStmt = this.db.prepare(`
          SELECT project_id, contact_id, role
          FROM project_contact
        `);
        
        const projectContactRows = projectContactStmt.all();
        projectContactRows.forEach(row => {
          relationships.push({
            source: row.project_id,
            target: row.contact_id,
            type: row.role,
            strength: 3
          });
        });
      } catch (error) {
        console.warn('project_contact table does not exist, skipping project-contact relationships');
      }

      // Project dependencies (check if table exists)
      try {
        const dependencyStmt = this.db.prepare(`
          SELECT predecessor_project_id, successor_project_id, dependency_type
          FROM project_dependency
        `);
        
        const dependencyRows = dependencyStmt.all();
        dependencyRows.forEach(row => {
          relationships.push({
            source: row.predecessor_project_id,
            target: row.successor_project_id,
            type: row.dependency_type,
            strength: 4
          });
        });
      } catch (error) {
        console.warn('project_dependency table does not exist, skipping project dependencies');
      }
    } catch (error) {
      console.error('Error fetching project relationships for knowledge graph:", error);
    }

    return relationships;
  }
}