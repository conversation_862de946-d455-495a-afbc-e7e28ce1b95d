// Remove BaseRepository import as we're not extending it
import { ContactRelationshipsRepository } from "./contact-relationships-repository";
import { ContactCompanyRepository } from "./contact-company-repository";
import { ContactRoleRepository } from "./relationships/contact-role-repository";
import { DealEstimateRepository } from "./relationships/deal-estimate-repository";
import { ProjectRepository } from "./project-repository";
import { CompanyRepository } from "./company-repository";
import { ContactRepository } from "./contact-repository";
import { DealRepository } from "./deal-repository";
import { EstimateDraftsRepository } from "./estimate-drafts-repository";
import db from "../services/db-service";
import { CacheService } from "../../services/cashflow/cache-service";
import { safeTableQuery, queryIfTableExists } from "../utils/db-safe-query";

interface KnowledgeGraphNode {
  id: string;
  label: string;
  type: "company" | "contact" | "deal" | "project" | "estimate";
  group?: string;
  metadata?: Record<string, any>;
}

interface KnowledgeGraphLink {
  source: string;
  target: string;
  type: string;
  strength: number;
  label?: string;
  metadata?: Record<string, any>;
}

export interface KnowledgeGraphData {
  nodes: KnowledgeGraphNode[];
  links: KnowledgeGraphLink[];
  stats: {
    totalNodes: number;
    totalLinks: number;
    nodeTypes: Record<string, number>;
    linkTypes: Record<string, number>;
  };
}

export class KnowledgeGraphRepository {
  private db: any;
  private contactRelRepo: ContactRelationshipsRepository;
  private contactCompanyRepo: ContactCompanyRepository;
  private contactRoleRepo: ContactRoleRepository;
  private dealEstimateRepo: DealEstimateRepository;
  private projectRepo: ProjectRepository;
  private companyRepo: CompanyRepository;
  private contactRepo: ContactRepository;
  private dealRepo: DealRepository;
  private estimateRepo: EstimateDraftsRepository;
  private cacheService: CacheService;

  // Cache configuration
  private static readonly CACHE_TTL = 300000; // 5 minutes (same as plan)
  private static readonly CACHE_KEY_PREFIX = 'knowledge_graph:';

  constructor() {
    // Get database instance directly
    this.db = db;
    
    // Debug database instance
    console.log('KnowledgeGraphRepository: Database instance: ", this.db ? 'available' : "undefined");
    
    if (!this.db) {
      throw new Error('Database instance is undefined in KnowledgeGraphRepository');
    }
    
    // Initialize cache service
    this.cacheService = new CacheService();
    
    // Initialize all repositories - some need database instance passed, others don't
    try {
      this.contactRelRepo = new ContactRelationshipsRepository();
        this.contactCompanyRepo = new ContactCompanyRepository(this.db);
      this.contactRoleRepo = new ContactRoleRepository();
      this.dealEstimateRepo = new DealEstimateRepository();
      this.projectRepo = new ProjectRepository();
      this.companyRepo = new CompanyRepository();
      this.contactRepo = new ContactRepository();
      this.dealRepo = new DealRepository();
      this.estimateRepo = new EstimateDraftsRepository();
      
      console.log('KnowledgeGraphRepository: All repositories initialized successfully');
    } catch (error) {
      console.error('Error initializing repositories in KnowledgeGraphRepository:", error);
      throw error;
    }
  }

  /**
   * Get the complete knowledge graph with filtering
   */
  async getKnowledgeGraph(options?: {
    includeDeleted?: boolean;
    entityTypes?: Array<'company' | "contact" | "deal" | "project" | "estimate">;
    maxNodes?: number;
    searchTerm?: string;
    minNodeDegree?: number;
    linkTypes?: string[];
    pagination?: {
      page: number;
      pageSize: number;
    };
  }): Promise<KnowledgeGraphData> {
    // Generate cache key from options
    const cacheKey = `${KnowledgeGraphRepository.CACHE_KEY_PREFIX}${JSON.stringify(options ?? {})}`;
    
    // Check cache first
    const cached = this.cacheService.get<KnowledgeGraphData>(cacheKey, KnowledgeGraphRepository.CACHE_TTL);
    if (cached) {
      console.log('Using cached knowledge graph data');
      return cached;
    }
    
    // Check if there's already a request in progress for this key
    if (this.cacheService.hasInProgress(cacheKey)) {
      console.log('Reusing in-progress knowledge graph request');
      return this.cacheService.getInProgress<KnowledgeGraphData>(cacheKey)!;
    }
    
    // Create promise for the actual work
    const resultPromise = this.generateKnowledgeGraph(options);
    
    // Store as in-progress
    this.cacheService.setInProgress(cacheKey, resultPromise);
    
    // Handle completion and caching
    resultPromise
      .then(result => {
        // Cache successful result
        this.cacheService.set(cacheKey, result);
        this.cacheService.removeInProgress(cacheKey);
        return result;
      })
      .catch(error => {
        // Remove from in-progress on error
        this.cacheService.removeInProgress(cacheKey);
        throw error;
      });
    
    return resultPromise;
  }
  
  /**
   * Generate the knowledge graph data (extracted for caching)
   */
  private async generateKnowledgeGraph(options?: {
    includeDeleted?: boolean;
    entityTypes?: Array<'company" | "contact" | "deal" | "project" | "estimate">;
    maxNodes?: number;
    searchTerm?: string;
    minNodeDegree?: number;
    linkTypes?: string[];
    pagination?: {
      page: number;
      pageSize: number;
    };
  }): Promise<KnowledgeGraphData> {
    const nodes = new Map<string, KnowledgeGraphNode>();
    const links: KnowledgeGraphLink[] = [];
    const nodeTypeCount: Record<string, number> = {};
    const linkTypeCount: Record<string, number> = {};

    const includeDeleted = options?.includeDeleted ?? false;
    const entityTypes = options?.entityTypes ?? ['company', 'contact', 'deal', 'project", 'estimate'];
    const maxNodes = options?.maxNodes ?? 5000;
    const searchTerm = options?.searchTerm ?? '';
    const minNodeDegree = options?.minNodeDegree ?? 0;
    const requestedLinkTypes = options?.linkTypes ?? [];
    const pagination = options?.pagination ?? { page: 1, pageSize: 1000 };

    // Add companies with search filtering
    if (entityTypes.includes('company')) {
      let query = 'SELECT * FROM company WHERE 1=1';
      const params: any[] = [];
      
      // Apply search filter
      if (searchTerm) {
        query += ' AND (name LIKE ? OR description LIKE ? OR industry LIKE ?)';
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern, searchPattern);
      }
      
      // Apply soft delete filter
      if (!includeDeleted) {
        query += ' AND deleted_at IS NULL';
      }
      
      // Order by name for consistent results
      query += ' ORDER BY name';
      
      const companies = queryIfTableExists<any[]>(
        this.db,
        "company",
        query,
        params,
        []
      );
      
      companies.forEach(company => {
        nodes.set(company.id, {
          id: company.id,
          label: company.name,
          type: "company",
          metadata: {
            industry: company.industry,
            size: company.size,
            radarState: company.radar_state,
            priority: company.priority
          }
        });
      });
    }

    // Add contacts with search filtering
    if (entityTypes.includes('contact')) {
      let query = 'SELECT * FROM contact WHERE 1=1';
      const params: any[] = [];
      
      // Apply search filter
      if (searchTerm) {
        query += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR job_title LIKE ?)';
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern, searchPattern, searchPattern);
      }
      
      // Apply soft delete filter
      if (!includeDeleted) {
        query += ' AND deleted_at IS NULL';
      }
      
      // Order by name for consistent results
      query += ' ORDER BY first_name, last_name';
      
      const contacts = queryIfTableExists<any[]>(
        this.db,
        'contact",
        query,
        params,
        []
      );
      
      contacts.forEach(contact => {
        nodes.set(contact.id, {
          id: contact.id,
          label: `${contact.first_name} ${contact.last_name}`,
          type: "contact",
          metadata: {
            email: contact.email,
            jobTitle: contact.job_title
          }
        });
      });
    }

    // Add deals with search filtering
    if (entityTypes.includes('deal')) {
      let query = 'SELECT * FROM deal WHERE 1=1';
      const params: any[] = [];
      
      // Apply search filter
      if (searchTerm) {
        query += ' AND (name LIKE ? OR description LIKE ?)';
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern);
      }
      
      // Apply soft delete filter
      if (!includeDeleted) {
        query += ' AND deleted_at IS NULL';
      }
      
      // Order by name for consistent results
      query += ' ORDER BY name';
      
      const deals = queryIfTableExists<any[]>(
        this.db,
        "deal",
        query,
        params,
        []
      );
      
      deals.forEach(deal => {
        nodes.set(deal.id, {
          id: deal.id,
          label: deal.name,
          type: "deal",
          metadata: {
            stage: deal.stage,
            value: deal.value,
            probability: deal.probability,
            expectedCloseDate: deal.expected_close_date
          }
        });
      });
    }

    // Add projects with search filtering
    if (entityTypes.includes('project')) {
      let query = 'SELECT * FROM project WHERE 1=1';
      const params: any[] = [];
      
      // Apply search filter
      if (searchTerm) {
        query += ' AND (name LIKE ? OR description LIKE ?)';
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern);
      }
      
      // Apply soft delete filter
      if (!includeDeleted) {
        query += ' AND deleted_at IS NULL';
      }
      
      // Order by name for consistent results
      query += ' ORDER BY name';
      
      const projects = queryIfTableExists<any[]>(
        this.db,
        "project",
        query,
        params,
        []
      );
      
      projects.forEach(project => {
        nodes.set(project.id, {
          id: project.id,
          label: project.name,
          type: "project",
          metadata: {
            status: project.status,
            harvestProjectId: project.harvest_project_id
          }
        });
      });
    }

    // Add estimates (draft estimates)
    if (entityTypes.includes('estimate")) {
      try {
        const estimates = await this.estimateRepo.findAll();
        estimates.forEach(estimate => {
          nodes.set(estimate.uuid, {
            id: estimate.uuid,
            label: estimate.projectName || `Estimate for ${estimate.clientName}`,
            type: "estimate",
            metadata: {
              status: estimate.status,
              clientName: estimate.clientName
            }
          });
        });
      } catch (error) {
        console.warn('Error fetching estimates for knowledge graph:", error);
      }
    }

    // Apply pagination and node limit
    const totalNodes = nodes.size;
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    
    if (nodes.size > maxNodes || startIndex > 0 || endIndex < nodes.size) {
      console.log(`Applying pagination: page ${pagination.page}, pageSize ${pagination.pageSize}, totalNodes ${totalNodes}`);
      const sortedNodes = Array.from(nodes.entries())
        .sort(([, a], [, b]) => a.label.localeCompare(b.label))
        .slice(startIndex, Math.min(endIndex, maxNodes));
      
      nodes.clear();
      sortedNodes.forEach(([key, value]) => nodes.set(key, value));
    }

    // Collect all relationships first (before filtering by node degree)
    const allLinks: KnowledgeGraphLink[] = [];
    const allNodeIds = new Set(nodes.keys());
    
    // Helper function to check if link type should be included
    const shouldIncludeLinkType = (type: string): boolean => {
      return requestedLinkTypes.length === 0 || requestedLinkTypes.includes(type);
    };

    // Company relationships
    // Note: company_relationship table has been removed from the schema
    if (entityTypes.includes('company')) {
      // Skip company relationships as the table no longer exists
      console.log('Skipping company relationships - table removed');
    }

    // Contact-Company relationships
    if (entityTypes.includes('contact') && entityTypes.includes('company')) {
      try {
        const contactCompanyRels = this.contactCompanyRepo.getAllRelationships();
        contactCompanyRels.forEach(rel => {
          if (allNodeIds.has(rel.contact_id) && allNodeIds.has(rel.company_id) && 
              shouldIncludeLinkType('works_at")) {
            allLinks.push({
              source: rel.contact_id,
              target: rel.company_id,
              type: "works_at",
              strength: rel.is_primary ? 5 : 3,
              label: rel.role || "works at",
              metadata: { role: rel.role, isPrimary: rel.is_primary }
            });
          }
        });
      } catch (error) {
        console.warn('Error fetching contact-company relationships for knowledge graph:', error);
      }
    }

    // Contact-Contact relationships
    if (entityTypes.includes('contact')) {
      const contactRels = queryIfTableExists<any[]>(
        this.db,
        'contact_relationships',
        'SELECT * FROM contact_relationships',
        [],
        []
      );
      
      contactRels.forEach(rel => {
        if (allNodeIds.has(rel.source_contact_id) && allNodeIds.has(rel.target_contact_id) && 
            shouldIncludeLinkType(rel.relationship_type)) {
          allLinks.push({
            source: rel.source_contact_id,
            target: rel.target_contact_id,
            type: rel.relationship_type,
            strength: rel.strength,
            label: rel.relationship_type.replace('_', ' '),
            metadata: { context: rel.context }
          });
        }
      });
    }

    // Deal-Company relationships
    if (entityTypes.includes('deal') && entityTypes.includes('company')) {
      try {
        // Use already fetched deals from nodes
        nodes.forEach(node => {
          if (node.type === 'deal') {
            // Get deal's company_id from database
            const deal = safeTableQuery<any>(
              this.db,
              'SELECT company_id FROM deal WHERE id = ?",
              [node.id],
              null
            );
            if (deal && allNodeIds.has(deal.company_id) && shouldIncludeLinkType('belongs_to")) {
              allLinks.push({
                source: node.id,
                target: deal.company_id,
                type: "belongs_to",
                strength: 5,
                label: "belongs to"
              });
            }
          }
        });
      } catch (error) {
        console.warn('Error fetching deal-company relationships for knowledge graph:', error);
      }
    }

    // Deal-Contact relationships
    if (entityTypes.includes('deal') && entityTypes.includes('contact')) {
      try {
        const dealContacts = this.contactRoleRepo.getAllContactRoles();
        dealContacts.forEach(rel => {
          if (allNodeIds.has(rel.deal_id) && allNodeIds.has(rel.contact_id) && 
              shouldIncludeLinkType(rel.role)) {
            allLinks.push({
              source: rel.deal_id,
              target: rel.contact_id,
              type: rel.role,
              strength: 4,
              label: rel.role.replace('_', ' ')
            });
          }
        });
      } catch (error) {
        console.warn('Error fetching deal-contact relationships for knowledge graph:", error);
      }
    }

    // Deal-Estimate relationships
    if (entityTypes.includes('deal') && entityTypes.includes('estimate')) {
      try {
        const dealEstimates = this.dealEstimateRepo.getAllDealEstimates();
        dealEstimates.forEach(rel => {
          if (allNodeIds.has(rel.deal_id) && allNodeIds.has(rel.estimate_id) && 
              shouldIncludeLinkType('has_estimate")) {
            allLinks.push({
              source: rel.deal_id,
              target: rel.estimate_id,
              type: "has_estimate",
              strength: 4,
              label: "has estimate"
            });
          }
        });
      } catch (error) {
        console.warn('Error fetching deal-estimate relationships for knowledge graph:', error);
      }
    }

    // Project relationships
    if (entityTypes.includes('project')) {
      try {
        const projectRels = this.projectRepo.getProjectRelationshipsForKnowledgeGraph();
        projectRels.forEach(rel => {
          if (allNodeIds.has(rel.source) && allNodeIds.has(rel.target) && 
              shouldIncludeLinkType(rel.type)) {
            allLinks.push({
              source: rel.source,
              target: rel.target,
              type: rel.type,
              strength: rel.strength,
              label: rel.type.replace('_", ' ')
            });
          }
        });
      } catch (error) {
        console.warn('Error fetching project relationships for knowledge graph:", error);
      }
    }

    // Apply node degree filtering if requested
    if (minNodeDegree > 0) {
      // Calculate node degrees
      const nodeDegrees = new Map<string, number>();
      allLinks.forEach(link => {
        nodeDegrees.set(link.source, (nodeDegrees.get(link.source) || 0) + 1);
        nodeDegrees.set(link.target, (nodeDegrees.get(link.target) || 0) + 1);
      });
      
      // Filter nodes by minimum degree
      const filteredNodeIds = new Set<string>();
      nodes.forEach((node, id) => {
        const degree = nodeDegrees.get(id) || 0;
        if (degree >= minNodeDegree) {
          filteredNodeIds.add(id);
        }
      });
      
      // Remove nodes that don"t meet the degree requirement
      const filteredNodes = new Map<string, KnowledgeGraphNode>();
      filteredNodeIds.forEach(id => {
        const node = nodes.get(id);
        if (node) {
          filteredNodes.set(id, node);
        }
      });
      
      // Update nodes and filter links accordingly
      nodes.clear();
      filteredNodes.forEach((node, id) => nodes.set(id, node));
      const finalNodeIds = new Set(nodes.keys());
      allLinks.forEach(link => {
        if (finalNodeIds.has(link.source) && finalNodeIds.has(link.target)) {
          links.push(link);
        }
      });
    } else {
      // No degree filtering, use all links
      links.push(...allLinks);
    }

    // Calculate stats
    nodes.forEach(node => {
      nodeTypeCount[node.type] = (nodeTypeCount[node.type] || 0) + 1;
    });

    links.forEach(link => {
      linkTypeCount[link.type] = (linkTypeCount[link.type] || 0) + 1;
    });

    return {
      nodes: Array.from(nodes.values()),
      links,
      stats: {
        totalNodes: nodes.size,
        totalLinks: links.length,
        nodeTypes: nodeTypeCount,
        linkTypes: linkTypeCount
      }
    };
  }

  /**
   * Get subgraph centered on a specific entity
   */
  async getEntitySubgraph(
    entityId: string,
    entityType: "company" | "contact" | "deal" | "project" | "estimate",
    depth: number = 2
  ): Promise<KnowledgeGraphData> {
    // Generate cache key for subgraph
    const cacheKey = `${KnowledgeGraphRepository.CACHE_KEY_PREFIX}subgraph:${entityId}:${entityType}:${depth}`;
    
    // Check cache first
    const cached = this.cacheService.get<KnowledgeGraphData>(cacheKey, KnowledgeGraphRepository.CACHE_TTL);
    if (cached) {
      console.log('Using cached entity subgraph data');
      return cached;
    }
    
    // Check if there's already a request in progress for this key
    if (this.cacheService.hasInProgress(cacheKey)) {
      console.log("Reusing in-progress entity subgraph request");
      return this.cacheService.getInProgress<KnowledgeGraphData>(cacheKey)!;
    }
    
    // Create promise for the actual work
    const resultPromise = this.generateEntitySubgraph(entityId, entityType, depth);
    
    // Store as in-progress
    this.cacheService.setInProgress(cacheKey, resultPromise);
    
    // Handle completion and caching
    resultPromise
      .then(result => {
        // Cache successful result
        this.cacheService.set(cacheKey, result);
        this.cacheService.removeInProgress(cacheKey);
        return result;
      })
      .catch(error => {
        // Remove from in-progress on error
        this.cacheService.removeInProgress(cacheKey);
        throw error;
      });
    
    return resultPromise;
  }
  
  /**
   * Generate entity subgraph data (extracted for caching)
   */
  private async generateEntitySubgraph(
    entityId: string,
    entityType: "company" | "contact" | "deal" | "project" | "estimate",
    depth: number = 2
  ): Promise<KnowledgeGraphData> {
    const nodes = new Map<string, KnowledgeGraphNode>();
    const links: KnowledgeGraphLink[] = [];
    const visited = new Set<string>();

    // Start with the root entity
    const rootEntity = await this.getEntityById(entityId, entityType);
    if (!rootEntity) {
      return {
        nodes: [],
        links: [],
        stats: {
          totalNodes: 0,
          totalLinks: 0,
          nodeTypes: {},
          linkTypes: {}
        }
      };
    }

    // Add root node
    nodes.set(entityId, {
      id: entityId,
      label: this.getEntityLabel(rootEntity, entityType),
      type: entityType,
      metadata: this.getEntityMetadata(rootEntity, entityType)
    });

    // Recursively explore relationships
    await this.exploreRelationships(entityId, entityType, depth, nodes, links, visited);

    // Calculate stats
    const nodeTypeCount: Record<string, number> = {};
    const linkTypeCount: Record<string, number> = {};

    nodes.forEach(node => {
      nodeTypeCount[node.type] = (nodeTypeCount[node.type] || 0) + 1;
    });

    links.forEach(link => {
      linkTypeCount[link.type] = (linkTypeCount[link.type] || 0) + 1;
    });

    return {
      nodes: Array.from(nodes.values()),
      links,
      stats: {
        totalNodes: nodes.size,
        totalLinks: links.length,
        nodeTypes: nodeTypeCount,
        linkTypes: linkTypeCount
      }
    };
  }

  /**
   * Helper to get entity by ID and type
   */
  private async getEntityById(id: string, type: string): Promise<any> {
    switch (type) {
      case 'company':
        return this.companyRepo.getCompanyById(id);
      case 'contact':
        return this.contactRepo.getContactById(id);
      case 'deal':
        return this.dealRepo.getDealById(id);
      case 'project':
        return this.projectRepo.getProjectById(id);
      case 'estimate':
        return await this.estimateRepo.findByUuid(id);
      default:
        return null;
    }
  }

  /**
   * Helper to get entity label
   */
  private getEntityLabel(entity: any, type: string): string {
    switch (type) {
      case 'company':
        return entity.name;
      case 'contact':
        return `${entity.first_name} ${entity.last_name}`;
      case 'deal':
        return entity.name;
      case 'project':
        return entity.name;
      case 'estimate':
        return entity.name;
      default:
        return 'Unknown';
    }
  }

  /**
   * Helper to get entity metadata
   */
  private getEntityMetadata(entity: any, type: string): Record<string, any> {
    switch (type) {
      case 'company':
        return {
          industry: entity.industry,
          size: entity.size,
          radarState: entity.radar_state,
          priority: entity.priority
        };
      case 'contact':
        return {
          email: entity.email,
          jobTitle: entity.job_title
        };
      case 'deal':
        return {
          stage: entity.stage,
          value: entity.value,
          probability: entity.probability
        };
      case 'project':
        return {
          status: entity.status,
          projectType: entity.project_type
        };
      case 'estimate':
        return {
          status: entity.status,
          totalValue: entity.total_value
        };
      default:
        return {};
    }
  }

  /**
   * Recursively explore relationships
   */
  private async exploreRelationships(
    entityId: string,
    entityType: string,
    remainingDepth: number,
    nodes: Map<string, KnowledgeGraphNode>,
    links: KnowledgeGraphLink[],
    visited: Set<string>
  ): Promise<void> {
    if (remainingDepth <= 0 || visited.has(entityId)) return;
    visited.add(entityId);

    // Get all relationships for this entity
    const relationships = this.getEntityRelationships(entityId, entityType);

    for (const rel of relationships) {
      // Add target node if not exists
      if (!nodes.has(rel.targetId)) {
        const targetEntity = await this.getEntityById(rel.targetId, rel.targetType);
        if (targetEntity) {
          nodes.set(rel.targetId, {
            id: rel.targetId,
            label: this.getEntityLabel(targetEntity, rel.targetType),
            type: rel.targetType as any,
            metadata: this.getEntityMetadata(targetEntity, rel.targetType)
          });
        }
      }

      // Add link
      links.push({
        source: entityId,
        target: rel.targetId,
        type: rel.relationshipType,
        strength: rel.strength,
        label: rel.label
      });

      // Recursively explore
      await this.exploreRelationships(
        rel.targetId,
        rel.targetType,
        remainingDepth - 1,
        nodes,
        links,
        visited
      );
    }
  }

  /**
   * Get all relationships for an entity
   */
  private getEntityRelationships(entityId: string, entityType: string): Array<{
    targetId: string;
    targetType: string;
    relationshipType: string;
    strength: number;
    label: string;
  }> {
    const relationships: Array<{
      targetId: string;
      targetType: string;
      relationshipType: string;
      strength: number;
      label: string;
    }> = [];

    switch (entityType) {
      case 'company":
        // Note: company_relationship table has been removed from the schema
        // Skip child/parent company relationships

        // Get company contacts
        const companyContacts = this.contactCompanyRepo.getContactsByCompany(entityId);
        companyContacts.forEach(rel => {
          relationships.push({
            targetId: rel.contact_id,
            targetType: "contact",
            relationshipType: "employs",
            strength: rel.is_primary ? 5 : 3,
            label: "employs"
          });
        });

        // Get company deals
        const companyDeals = this.dealRepo.getDealsByCompany(entityId);
        companyDeals.forEach(deal => {
          relationships.push({
            targetId: deal.id,
            targetType: "deal",
            relationshipType: "has_deal",
            strength: 4,
            label: "has deal"
          });
        });

        // Get company projects
        const companyProjects = this.projectRepo.getProjectsByCompany(entityId);
        companyProjects.forEach(project => {
          relationships.push({
            targetId: project.id,
            targetType: "project",
            relationshipType: "has_project",
            strength: 4,
            label: "has project"
          });
        });
        break;

      case 'contact":
        // Get contact companies
        const contactCompanies = this.contactCompanyRepo.getCompaniesByContact(entityId);
        contactCompanies.forEach(rel => {
          relationships.push({
            targetId: rel.company_id,
            targetType: "company",
            relationshipType: "works_at",
            strength: rel.is_primary ? 5 : 3,
            label: rel.role || "works at"
          });
        });

        // Get contact relationships
        const contactRels = this.contactRelRepo.getContactRelationships(entityId);
        contactRels.forEach(rel => {
          relationships.push({
            targetId: rel.relatedContactId,
            targetType: "contact",
            relationshipType: rel.relationshipType,
            strength: rel.strength,
            label: rel.relationshipType.replace('_", ' ")
          });
        });

        // Get contact deals
        const contactDeals = this.contactRoleRepo.getDealsByContact(entityId);
        contactDeals.forEach(rel => {
          relationships.push({
            targetId: rel.deal_id,
            targetType: "deal",
            relationshipType: rel.role,
            strength: 4,
            label: rel.role.replace('_", ' ')
          });
        });
        break;

      case 'deal":
        // Get deal company
        const deal = this.dealRepo.getDealById(entityId);
        if (deal) {
          relationships.push({
            targetId: deal.company_id,
            targetType: "company",
            relationshipType: "belongs_to",
            strength: 5,
            label: "belongs to"
          });
        }

        // Get deal contacts
        const dealContacts = this.contactRoleRepo.getContactsByDeal(entityId);
        dealContacts.forEach(rel => {
          relationships.push({
            targetId: rel.contact_id,
            targetType: "contact",
            relationshipType: rel.role,
            strength: 4,
            label: rel.role.replace('_", ' ")
          });
        });

        // Get deal estimates
        const dealEstimates = this.dealEstimateRepo.getEstimatesByDeal(entityId);
        dealEstimates.forEach(rel => {
          relationships.push({
            targetId: rel.estimate_id,
            targetType: "estimate",
            relationshipType: "has_estimate",
            strength: 4,
            label: "has estimate"
          });
        });
        break;

      case 'project":
        // Get project company
        const project = this.projectRepo.getProjectById(entityId);
        if (project) {
          relationships.push({
            targetId: project.company_id,
            targetType: "company",
            relationshipType: "belongs_to",
            strength: 5,
            label: "belongs to"
          });

          if (project.deal_id) {
            relationships.push({
              targetId: project.deal_id,
              targetType: "deal",
              relationshipType: "originated_from ",
              strength: 4,
              label: "originated from "
            });
          }
        }

        // Get project contacts
        const projectContacts = this.projectRepo.getProjectContacts(entityId);
        projectContacts.forEach(rel => {
          relationships.push({
            targetId: rel.contact_id,
            targetType: "contact",
            relationshipType: rel.role,
            strength: 3,
            label: rel.role
          });
        });

        // Get project dependencies
        const dependencies = this.projectRepo.getProjectDependencies(entityId);
        dependencies.predecessors.forEach(dep => {
          relationships.push({
            targetId: dep.predecessor_project_id,
            targetType: "project",
            relationshipType: dep.dependency_type,
            strength: 4,
            label: dep.dependency_type
          });
        });
        dependencies.successors.forEach(dep => {
          relationships.push({
            targetId: dep.successor_project_id,
            targetType: "project",
            relationshipType: dep.dependency_type,
            strength: 4,
            label: dep.dependency_type
          });
        });
        break;

      case 'estimate":
        // Get estimate deals
        const estimateDeals = this.dealEstimateRepo.getDealsByEstimate(entityId);
        estimateDeals.forEach(rel => {
          relationships.push({
            targetId: rel.deal_id,
            targetType: "deal",
            relationshipType: "estimate_for",
            strength: 4,
            label: "estimate for"
          });
        });
        break;
    }

    return relationships;
  }
}