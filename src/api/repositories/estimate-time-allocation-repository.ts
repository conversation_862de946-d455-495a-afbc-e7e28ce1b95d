/**
 * Repository for managing estimate time allocations
 */

import { v4 as uuidv4 } from "uuid";
import { BaseRepository } from "./base-repository";

/**
 * Interface for estimate time allocation entity
 */
export interface EstimateTimeAllocation {
  id: string;
  allocationId: string;
  weekIdentifier: string;
  days: number;
  deletedAt?: string;
}

/**
 * Interface for creating a new estimate time allocation
 */
export interface EstimateTimeAllocationCreate {
  allocationId: string;
  weekIdentifier: string;
  days: number;
}

/**
 * Interface for updating an estimate time allocation
 */
export interface EstimateTimeAllocationUpdate {
  days?: number;
}

/**
 * Interface for time allocation with allocation details
 */
export interface EstimateTimeAllocationWithDetails
  extends EstimateTimeAllocation {
  estimateId: string;
  harvestUserId: number;
  firstName: string;
  lastName?: string;
  projectRole?: string;
  level?: string;
}

/**
 * Repository for managing estimate time allocations
 */
export class EstimateTimeAllocationRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super("estimate_time_allocation");
  }

  /**
   * Get all time allocations for an allocation
   * @param allocationId Allocation ID
   * @returns Array of time allocations
   */
  getTimeAllocationsByAllocation(
    allocationId: string,
  ): EstimateTimeAllocation[] {
    try {
      const timeAllocations = this.db
        .prepare(
          `
        SELECT 
          id,
          allocation_id as allocationId,
          week_identifier as weekIdentifier,
          days
        FROM ${this.tableName}
        WHERE allocation_id = ?
        ORDER BY week_identifier
      `,
        )
        .all(allocationId) as EstimateTimeAllocation[];

      return timeAllocations;
    } catch (error) {
      console.error(
        `Error fetching time allocations for allocation ${allocationId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get all time allocations for an estimate
   * @param estimateId Estimate ID
   * @returns Array of time allocations with allocation details
   */
  getTimeAllocationsByEstimate(
    estimateId: string,
  ): EstimateTimeAllocationWithDetails[] {
    try {
      const timeAllocations = this.db
        .prepare(
          `
        SELECT 
          eta.id,
          eta.allocation_id as allocationId,
          eta.week_identifier as weekIdentifier,
          eta.days,
          ea.estimate_id as estimateId,
          ea.harvest_user_id as harvestUserId,
          ea.first_name as firstName,
          ea.last_name as lastName,
          ea.project_role as projectRole,
          ea.level
        FROM ${this.tableName} eta
        JOIN estimate_allocation ea ON eta.allocation_id = ea.id
        WHERE ea.estimate_id = ?
        ORDER BY ea.first_name, ea.last_name, eta.week_identifier
      `,
        )
        .all(estimateId) as EstimateTimeAllocationWithDetails[];

      return timeAllocations;
    } catch (error) {
      console.error(
        `Error fetching time allocations for estimate ${estimateId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get time allocation by ID
   * @param id Time allocation ID
   * @returns Time allocation or null if not found
   */
  getTimeAllocationById(id: string): EstimateTimeAllocation | null {
    try {
      const timeAllocation = this.db
        .prepare(
          `
        SELECT 
          id,
          allocation_id as allocationId,
          week_identifier as weekIdentifier,
          days
        FROM ${this.tableName}
        WHERE id = ?
      `,
        )
        .get(id) as EstimateTimeAllocation | undefined;

      return timeAllocation || null;
    } catch (error) {
      console.error(`Error fetching time allocation by ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Get time allocation by allocation and week
   * @param allocationId Allocation ID
   * @param weekIdentifier Week identifier (ISO week format)
   * @returns Time allocation or null if not found
   */
  getTimeAllocationByAllocationAndWeek(
    allocationId: string,
    weekIdentifier: string,
  ): EstimateTimeAllocation | null {
    try {
      const timeAllocation = this.db
        .prepare(
          `
        SELECT 
          id,
          allocation_id as allocationId,
          week_identifier as weekIdentifier,
          days
        FROM ${this.tableName}
        WHERE allocation_id = ? AND week_identifier = ?
      `,
        )
        .get(allocationId, weekIdentifier) as
        | EstimateTimeAllocation
        | undefined;

      return timeAllocation || null;
    } catch (error) {
      console.error(
        `Error fetching time allocation for allocation ${allocationId} and week ${weekIdentifier}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Get time allocations by week identifier across all estimates
   * @param weekIdentifier Week identifier (ISO week format)
   * @returns Array of time allocations with details
   */
  getTimeAllocationsByWeek(
    weekIdentifier: string,
  ): EstimateTimeAllocationWithDetails[] {
    try {
      const timeAllocations = this.db
        .prepare(
          `
        SELECT 
          eta.id,
          eta.allocation_id as allocationId,
          eta.week_identifier as weekIdentifier,
          eta.days,
          ea.estimate_id as estimateId,
          ea.harvest_user_id as harvestUserId,
          ea.first_name as firstName,
          ea.last_name as lastName,
          ea.project_role as projectRole,
          ea.level
        FROM ${this.tableName} eta
        JOIN estimate_allocation ea ON eta.allocation_id = ea.id
        WHERE eta.week_identifier = ?
        ORDER BY ea.first_name, ea.last_name
      `,
        )
        .all(weekIdentifier) as EstimateTimeAllocationWithDetails[];

      return timeAllocations;
    } catch (error) {
      console.error(
        `Error fetching time allocations for week ${weekIdentifier}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Create a new time allocation
   * @param timeAllocationData Time allocation data
   * @returns Created time allocation
   */
  createTimeAllocation(
    timeAllocationData: EstimateTimeAllocationCreate,
  ): EstimateTimeAllocation {
    try {
      // Check if time allocation for this allocation and week already exists
      const existing = this.getTimeAllocationByAllocationAndWeek(
        timeAllocationData.allocationId,
        timeAllocationData.weekIdentifier,
      );
      if (existing) {
        throw new Error(
          `Time allocation for allocation ${timeAllocationData.allocationId} and week ${timeAllocationData.weekIdentifier} already exists`,
        );
      }

      const id = uuidv4();

      this.db
        .prepare(
          `
        INSERT INTO ${this.tableName} (
          id,
          allocation_id,
          week_identifier,
          days
        ) VALUES (?, ?, ?, ?)
      `,
        )
        .run(
          id,
          timeAllocationData.allocationId,
          timeAllocationData.weekIdentifier,
          timeAllocationData.days,
        );

      return this.getTimeAllocationById(id) as EstimateTimeAllocation;
    } catch (error) {
      console.error("Error creating time allocation:", error);
      throw error;
    }
  }

  /**
   * Update an existing time allocation
   * @param id Time allocation ID
   * @param updateData Update data
   * @returns Updated time allocation or null if not found
   */
  updateTimeAllocation(
    id: string,
    updateData: EstimateTimeAllocationUpdate,
  ): EstimateTimeAllocation | null {
    try {
      const existing = this.getTimeAllocationById(id);
      if (!existing) {
        return null;
      }

      this.db
        .prepare(
          `
        UPDATE ${this.tableName}
        SET days = ?
        WHERE id = ?
      `,
        )
        .run(
          updateData.days !== undefined ? updateData.days : existing.days,
          id,
        );

      return this.getTimeAllocationById(id);
    } catch (error) {
      console.error(`Error updating time allocation ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a time allocation
   * @param id Time allocation ID
   * @returns Boolean indicating success
   */
  deleteTimeAllocation(id: string): boolean {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE id = ?
      `,
        )
        .run(id);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting time allocation ${id}:`, error);
      return false;
    }
  }

  /**
   * Delete all time allocations for an allocation
   * @param allocationId Allocation ID
   * @returns Number of deleted time allocations
   */
  deleteTimeAllocationsByAllocation(allocationId: string): number {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE allocation_id = ?
      `,
        )
        .run(allocationId);

      return result.changes || 0;
    } catch (error) {
      console.error(
        `Error deleting time allocations for allocation ${allocationId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Delete all time allocations for an estimate
   * @param estimateId Estimate ID
   * @returns Number of deleted time allocations
   */
  deleteTimeAllocationsByEstimate(estimateId: string): number {
    try {
      const result = this.db
        .prepare(
          `
        DELETE FROM ${this.tableName}
        WHERE allocation_id IN (
          SELECT id FROM estimate_allocation WHERE estimate_id = ?
        )
      `,
        )
        .run(estimateId);

      return result.changes || 0;
    } catch (error) {
      console.error(
        `Error deleting time allocations for estimate ${estimateId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Upsert time allocation (create if doesn't exist, update if exists)
   * @param timeAllocationData Time allocation data
   * @returns Time allocation
   */
  upsertTimeAllocation(
    timeAllocationData: EstimateTimeAllocationCreate,
  ): EstimateTimeAllocation {
    try {
      const existing = this.getTimeAllocationByAllocationAndWeek(
        timeAllocationData.allocationId,
        timeAllocationData.weekIdentifier,
      );

      if (existing) {
        return this.updateTimeAllocation(existing.id, {
          days: timeAllocationData.days,
        }) as EstimateTimeAllocation;
      } else {
        return this.createTimeAllocation(timeAllocationData);
      }
    } catch (error) {
      console.error("Error upserting time allocation:", error);
      throw error;
    }
  }

  /**
   * Bulk create/update time allocations for an allocation
   * @param allocationId Allocation ID
   * @param timeAllocations Array of time allocation data
   * @returns Array of created/updated time allocations
   */
  bulkUpsertTimeAllocations(
    allocationId: string,
    timeAllocations: { weekIdentifier: string; days: number }[],
  ): EstimateTimeAllocation[] {
    try {
      const results: EstimateTimeAllocation[] = [];

      const transaction = this.db.transaction(() => {
        for (const timeAllocation of timeAllocations) {
          const result = this.upsertTimeAllocation({
            allocationId,
            weekIdentifier: timeAllocation.weekIdentifier,
            days: timeAllocation.days,
          });
          results.push(result);
        }
      });

      transaction();
      return results;
    } catch (error) {
      console.error(
        `Error bulk upserting time allocations for allocation ${allocationId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get time allocation summary for an allocation
   * @param allocationId Allocation ID
   * @returns Time allocation summary
   */
  getTimeAllocationSummary(allocationId: string): {
    totalWeeks: number;
    totalDays: number;
    averageDaysPerWeek: number;
    minDaysPerWeek: number;
    maxDaysPerWeek: number;
    firstWeek: string | null;
    lastWeek: string | null;
  } {
    try {
      const summary = this.db
        .prepare(
          `
        SELECT 
          COUNT(*) as totalWeeks,
          SUM(days) as totalDays,
          AVG(days) as averageDaysPerWeek,
          MIN(days) as minDaysPerWeek,
          MAX(days) as maxDaysPerWeek,
          MIN(week_identifier) as firstWeek,
          MAX(week_identifier) as lastWeek
        FROM ${this.tableName}
        WHERE allocation_id = ?
      `,
        )
        .get(allocationId) as any;

      return {
        totalWeeks: summary.totalWeeks || 0,
        totalDays: summary.totalDays || 0,
        averageDaysPerWeek:
          Math.round((summary.averageDaysPerWeek || 0) * 100) / 100,
        minDaysPerWeek: summary.minDaysPerWeek || 0,
        maxDaysPerWeek: summary.maxDaysPerWeek || 0,
        firstWeek: summary.firstWeek || null,
        lastWeek: summary.lastWeek || null,
      };
    } catch (error) {
      console.error(
        `Error fetching time allocation summary for allocation ${allocationId}:`,
        error,
      );
      return {
        totalWeeks: 0,
        totalDays: 0,
        averageDaysPerWeek: 0,
        minDaysPerWeek: 0,
        maxDaysPerWeek: 0,
        firstWeek: null,
        lastWeek: null,
      };
    }
  }

  /**
   * Get time allocation summary for an estimate
   * @param estimateId Estimate ID
   * @returns Time allocation summary
   */
  getEstimateTimeAllocationSummary(estimateId: string): {
    totalWeeks: number;
    totalDays: number;
    totalAllocations: number;
    averageDaysPerWeek: number;
    firstWeek: string | null;
    lastWeek: string | null;
  } {
    try {
      const summary = this.db
        .prepare(
          `
        SELECT 
          COUNT(DISTINCT eta.week_identifier) as totalWeeks,
          SUM(eta.days) as totalDays,
          COUNT(DISTINCT eta.allocation_id) as totalAllocations,
          AVG(eta.days) as averageDaysPerWeek,
          MIN(eta.week_identifier) as firstWeek,
          MAX(eta.week_identifier) as lastWeek
        FROM ${this.tableName} eta
        JOIN estimate_allocation ea ON eta.allocation_id = ea.id
        WHERE ea.estimate_id = ?
      `,
        )
        .get(estimateId) as any;

      return {
        totalWeeks: summary.totalWeeks || 0,
        totalDays: summary.totalDays || 0,
        totalAllocations: summary.totalAllocations || 0,
        averageDaysPerWeek:
          Math.round((summary.averageDaysPerWeek || 0) * 100) / 100,
        firstWeek: summary.firstWeek || null,
        lastWeek: summary.lastWeek || null,
      };
    } catch (error) {
      console.error(
        `Error fetching time allocation summary for estimate ${estimateId}:`,
        error,
      );
      return {
        totalWeeks: 0,
        totalDays: 0,
        totalAllocations: 0,
        averageDaysPerWeek: 0,
        firstWeek: null,
        lastWeek: null,
      };
    }
  }

  /**
   * Get time allocations for a date range
   * @param startWeek Start week identifier (ISO week format)
   * @param endWeek End week identifier (ISO week format)
   * @returns Array of time allocations with details
   */
  getTimeAllocationsByDateRange(
    startWeek: string,
    endWeek: string,
  ): EstimateTimeAllocationWithDetails[] {
    try {
      const timeAllocations = this.db
        .prepare(
          `
        SELECT 
          eta.id,
          eta.allocation_id as allocationId,
          eta.week_identifier as weekIdentifier,
          eta.days,
          ea.estimate_id as estimateId,
          ea.harvest_user_id as harvestUserId,
          ea.first_name as firstName,
          ea.last_name as lastName,
          ea.project_role as projectRole,
          ea.level
        FROM ${this.tableName} eta
        JOIN estimate_allocation ea ON eta.allocation_id = ea.id
        WHERE eta.week_identifier >= ? AND eta.week_identifier <= ?
        ORDER BY eta.week_identifier, ea.first_name, ea.last_name
      `,
        )
        .all(startWeek, endWeek) as EstimateTimeAllocationWithDetails[];

      return timeAllocations;
    } catch (error) {
      console.error(
        `Error fetching time allocations for date range ${startWeek} to ${endWeek}:`,
        error,
      );
      return [];
    }
  }
}
