/**
 * Harvest API Client with Circuit Breaker
 */

import { BaseApiClient } from "./base-api-client";
import { HARVEST_CIRCUIT_BREAKER_CONFIG } from "../../services/xero/circuit-breaker-config";

export class HarvestApiClient extends BaseApiClient {
  private accountId: string;
  private accessToken: string;

  constructor(accountId: string, accessToken: string) {
    super({
      baseURL: "https://api.harvestapp.com/v2",
      circuitBreakerName: "HarvestAPI",
      timeout: 30000,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Harvest-Account-Id': accountId,
        'User-Agent': "Onbord Financial Dashboard"
      },
      retryConfig: {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2,
        retryableStatuses: [408, 429, 500, 502, 503, 504],
        retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'ENETUNREACH']
      },
      interceptors: {
        response: [
          // Handle Harvest rate limiting
          async (response) => {
            if (response.status === 429) {
              const retryAfter = response.headers['retry-after'];
              if (retryAfter) {
                console.log(`Harvest rate limit hit. Retry after ${retryAfter} seconds`);
              }
            }
            return response;
          }
        ]
      }
    });

    this.accountId = accountId;
    this.accessToken = accessToken;
  }

  /**
   * Get current user
   */
  async getCurrentUser() {
    return this.get('/users/me');
  }

  /**
   * Get all users
   */
  async getUsers(params?: { is_active?: boolean; page?: number; per_page?: number }) {
    return this.get('/users', params);
  }

  /**
   * Get clients
   */
  async getClients(params?: { is_active?: boolean; page?: number; per_page?: number }) {
    return this.get('/clients', params);
  }

  /**
   * Get projects
   */
  async getProjects(params?: {
    is_active?: boolean;
    client_id?: number;
    updated_since?: string;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/projects', params);
  }

  /**
   * Get invoices
   */
  async getInvoices(params?: {
    client_id?: number;
    project_id?: number;
    updated_since?: string;
    from?: string;
    to?: string;
    state?: string;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/invoices', params);
  }

  /**
   * Get estimates
   */
  async getEstimates(params?: {
    client_id?: number;
    updated_since?: string;
    from?: string;
    to?: string;
    state?: string;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/estimates', params);
  }

  /**
   * Get time entries
   */
  async getTimeEntries(params?: {
    user_id?: number;
    client_id?: number;
    project_id?: number;
    is_billed?: boolean;
    is_running?: boolean;
    updated_since?: string;
    from?: string;
    to?: string;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/time_entries', params);
  }

  /**
   * Get expenses
   */
  async getExpenses(params?: {
    user_id?: number;
    client_id?: number;
    project_id?: number;
    is_billed?: boolean;
    updated_since?: string;
    from?: string;
    to?: string;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/expenses', params);
  }

  /**
   * Get project budget report
   */
  async getProjectBudgetReport(params?: {
    is_active?: boolean;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/reports/project_budget', params);
  }

  /**
   * Get uninvoiced report
   */
  async getUninvoicedReport(params?: {
    from?: string;
    to?: string;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/reports/uninvoiced', params);
  }

  /**
   * Get time report
   */
  async getTimeReport(params?: {
    from?: string;
    to?: string;
    client_id?: number;
    project_id?: number;
    page?: number;
    per_page?: number;
  }) {
    return this.get('/reports/time', params);
  }
}

// Factory function to create Harvest API client
export function createHarvestApiClient(accountId: string, accessToken: string): HarvestApiClient {
  return new HarvestApiClient(accountId, accessToken);
}