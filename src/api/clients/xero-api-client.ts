/**
 * Xero API Client with Circuit Breaker
 */

import { BaseApiClient } from "./base-api-client";
import { XERO_CIRCUIT_BREAKER_CONFIG } from "../../services/xero/circuit-breaker-config";
import { getXeroService } from "../../services/xero";

export class XeroApiClient extends BaseApiClient {
  constructor() {
    super({
      baseURL: "https://api.xero.com/api.xro/2.0",
      circuitBreakerName: "XeroAPI",
      timeout: 30000,
      retryConfig: {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2,
        retryableStatuses: [408, 429, 500, 502, 503, 504],
        retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'ENETUNREACH']
      },
      interceptors: {
        request: [
          // Add Xero authentication headers
          async (request) => {
            const xeroService = getXeroService();
            const tenantId = xeroService.getActiveTenantId();
            
            if (!tenantId) {
              throw new Error('No active Xero tenant');
            }

            // Get fresh access token
            const accessToken = await xeroService.getAccessToken();
            
            return {
              ...request,
              headers: {
                ...request.headers,
                'Authorization': `Bearer ${accessToken}`,
                'xero-tenant-id': tenantId,
                'Accept': "application/json"
              }
            };
          }
        ],
        response: [
          // Handle Xero-specific error responses
          async (response) => {
            if (response.status === 401) {
              // Token might be expired, try to refresh
              const xeroService = getXeroService();
              await xeroService.refreshToken();
              throw new Error('Token refreshed, please retry');
            }
            return response;
          }
        ]
      }
    });
  }

  /**
   * Get organization info
   */
  async getOrganization(tenantId: string) {
    return this.get('/Organisation', { tenantId });
  }

  /**
   * Get invoices
   */
  async getInvoices(params?: {
    where?: string;
    order?: string;
    page?: number;
    includeArchived?: boolean;
  }) {
    return this.get('/Invoices', params as any);
  }

  /**
   * Get bills (accounts payable invoices)
   */
  async getBills(params?: {
    where?: string;
    order?: string;
    page?: number;
  }) {
    const where = params?.where || 'Type=="ACCPAY"';
    return this.get('/Invoices', { ...params, where });
  }

  /**
   * Get bank transactions
   */
  async getBankTransactions(params?: {
    where?: string;
    order?: string;
    page?: number;
    fromDate?: string;
    toDate?: string;
  }) {
    return this.get('/BankTransactions', params as any);
  }

  /**
   * Get reports
   */
  async getReport(reportType: string, params?: Record<string, any>) {
    return this.get(`/Reports/${reportType}`, params);
  }

  /**
   * Get balance sheet
   */
  async getBalanceSheet(date?: string, periods?: number, timeframe?: string) {
    return this.getReport('BalanceSheet', { date, periods, timeframe });
  }

  /**
   * Get profit and loss
   */
  async getProfitAndLoss(fromDate?: string, toDate?: string, periods?: number) {
    return this.getReport('ProfitAndLoss', { fromDate, toDate, periods });
  }

  /**
   * Get bank summary
   */
  async getBankSummary(fromDate?: string, toDate?: string) {
    return this.getReport('BankSummary', { fromDate, toDate });
  }
}

// Export singleton instance
export const xeroApiClient = new XeroApiClient();