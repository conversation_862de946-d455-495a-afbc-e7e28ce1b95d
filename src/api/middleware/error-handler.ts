/**
 * Standardized Error Response Middleware
 * 
 * Provides consistent error format across all API endpoints
 */

import { Request, Response, NextFunction } from "express";
import { CircuitBreakerError } from "../../utils/circuit-breaker";

// TypeScript interfaces for error handling
type ErrorDetails = Record<string, unknown> | string | number | boolean | null | undefined;

interface ErrorWithStatus extends Error {
  status?: number;
  statusCode?: number;
  code?: string;
}

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: unknown;
  };
  correlationId?: string;
}

type AsyncRouteHandler = (req: Request, res: Response, next: NextFunction) => Promise<void | Response>;

/**
 * Application Error Types
 */
export enum ErrorType {
  // Client Errors (4xx)
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  BAD_REQUEST = 'BAD_REQUEST',
  CONFLICT = 'CONFLICT',
  
  // Server Errors (5xx)
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  CIRCUIT_BREAKER_OPEN = 'CIRCUIT_BREAKER_OPEN',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // Business Logic Errors
  INVALID_DATE_RANGE = 'INVALID_DATE_RANGE',
  DATA_INCONSISTENCY = 'DATA_INCONSISTENCY',
  PROJECTION_ERROR = 'PROJECTION_ERROR',
  SYNC_FAILED = 'SYNC_FAILED',
  MISSING_CONFIGURATION = 'MISSING_CONFIGURATION',
  
  // Integration Errors
  XERO_API_ERROR = 'XERO_API_ERROR',
  HARVEST_API_ERROR = 'HARVEST_API_ERROR',
  HUBSPOT_API_ERROR = 'HUBSPOT_API_ERROR',
  
  // Unknown
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Standard Error Response Format
 */
export interface ErrorResponse {
  error: {
    type: ErrorType;
    message: string;
    code?: string;
    details?: ErrorDetails;
    timestamp: string;
    path: string;
    method: string;
    correlationId?: string;
  };
  success: false;
}

/**
 * Application Error Class
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly code?: string;
  public readonly details?: ErrorDetails;
  public readonly isOperational: boolean;

  constructor(
    type: ErrorType,
    message: string,
    statusCode: number,
    code?: string,
    details?: ErrorDetails,
    isOperational = true
  ) {
    super(message);
    this.type = type;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }

  static validation(message: string, details?: ErrorDetails): AppError {
    return new AppError(ErrorType.VALIDATION_ERROR, message, 400, 'VALIDATION_FAILED', details);
  }

  static authentication(message: string = 'Authentication required'): AppError {
    return new AppError(ErrorType.AUTHENTICATION_ERROR, message, 401, 'AUTH_REQUIRED');
  }

  static authorization(message: string = 'Insufficient permissions'): AppError {
    return new AppError(ErrorType.AUTHORIZATION_ERROR, message, 403, 'FORBIDDEN');
  }

  static notFound(resource: string): AppError {
    return new AppError(ErrorType.NOT_FOUND, `${resource} not found`, 404, 'NOT_FOUND');
  }

  static conflict(message: string, details?: ErrorDetails): AppError {
    return new AppError(ErrorType.CONFLICT, message, 409, 'CONFLICT', details);
  }

  static internal(message: string = 'Internal server error', details?: ErrorDetails): AppError {
    return new AppError(ErrorType.INTERNAL_ERROR, message, 500, 'INTERNAL_ERROR', details, false);
  }

  static database(message: string, details?: ErrorDetails): AppError {
    return new AppError(ErrorType.DATABASE_ERROR, message, 500, 'DB_ERROR', details);
  }

  static external(service: string, message: string, details?: ErrorDetails): AppError {
    const type = `${service.toUpperCase()}_API_ERROR` as ErrorType;
    return new AppError(type, message, 502, 'EXTERNAL_SERVICE_ERROR', details);
  }

  static timeout(message: string = 'Request timeout'): AppError {
    return new AppError(ErrorType.TIMEOUT_ERROR, message, 504, 'TIMEOUT');
  }

  static circuitBreaker(service: string): AppError {
    return new AppError(
      ErrorType.CIRCUIT_BREAKER_OPEN,
      `${service} is temporarily unavailable. Please try again later.`,
      503,
      'SERVICE_UNAVAILABLE'
    );
  }
}

/**
 * Error Handler Middleware
 */
export function errorHandler(
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Default error values
  let type = ErrorType.UNKNOWN_ERROR;
  let statusCode = 500;
  let message = 'An unexpected error occurred';
  let code: string | undefined;
  let details: ErrorDetails;

  // Handle different error types
  if (err instanceof AppError) {
    type = err.type;
    statusCode = err.statusCode;
    message = err.message;
    code = err.code;
    details = err.details;
  } else if (err instanceof CircuitBreakerError) {
    type = ErrorType.CIRCUIT_BREAKER_OPEN;
    statusCode = 503;
    message = err.message;
    code = err.code;
  } else if (err.name === 'ValidationError') {
    type = ErrorType.VALIDATION_ERROR;
    statusCode = 400;
    message = 'Validation failed';
    details = err.message;
  } else if (err.name === 'UnauthorizedError') {
    type = ErrorType.AUTHENTICATION_ERROR;
    statusCode = 401;
    message = 'Authentication failed';
  } else if ((err as ErrorWithStatus).status || (err as ErrorWithStatus).statusCode) {
    statusCode = (err as ErrorWithStatus).status || (err as ErrorWithStatus).statusCode;
    message = err.message;
    
    // Map status codes to error types
    if (statusCode === 400) type = ErrorType.BAD_REQUEST;
    else if (statusCode === 401) type = ErrorType.AUTHENTICATION_ERROR;
    else if (statusCode === 403) type = ErrorType.AUTHORIZATION_ERROR;
    else if (statusCode === 404) type = ErrorType.NOT_FOUND;
    else if (statusCode === 429) type = ErrorType.RATE_LIMIT_EXCEEDED;
    else if (statusCode === 503) type = ErrorType.SERVICE_UNAVAILABLE;
  }

  // Check for specific error patterns
  if (err.message?.includes('rate limit')) {
    type = ErrorType.RATE_LIMIT_EXCEEDED;
    statusCode = 429;
  } else if (err.message?.includes('timeout')) {
    type = ErrorType.TIMEOUT_ERROR;
    statusCode = 504;
  } else if (err.message?.includes('SQLITE') || err.message?.includes('database')) {
    type = ErrorType.DATABASE_ERROR;
    statusCode = 500;
  }

  // Log error
  const logError = {
    type,
    message: err.message,
    statusCode,
    stack: err.stack,
    path: req.path,
    method: req.method,
    body: req.body,
    query: req.query,
    user: (req as AuthenticatedRequest).user?.id
  };

  if (statusCode >= 500 || !((err as AppError).isOperational)) {
    console.error('Server Error:', logError);
  } else {
    console.warn('Client Error:', logError);
  }

  // Prepare error response
  const errorResponse: ErrorResponse = {
    error: {
      type,
      message,
      code,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    },
    success: false
  };

  // Include details in development mode
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.details = details || err.stack;
  }

  // Add correlation ID if available
  if ((req as AuthenticatedRequest).correlationId) {
    errorResponse.error.correlationId = (req as AuthenticatedRequest).correlationId;
  }

  // Send response
  res.status(statusCode).json(errorResponse);
}

/**
 * Not Found Handler
 */
export function notFoundHandler(req: Request, res: Response): void {
  const error = AppError.notFound('Endpoint');
  errorHandler(error, req, res, () => {});
}

/**
 * Async Error Wrapper
 */
export function asyncHandler(
  fn: AsyncRouteHandler
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}