import rateLimit from "express-rate-limit";
import { Request, Response } from "express";

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Create different rate limiters for different endpoints
export const createRateLimiter = (options: {
  windowMs?: number;
  max?: number;
  message?: string;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes default
    max: options.max || 100, // Limit each IP to 100 requests per windowMs
    message: options.message || 'Too many requests from this IP, please try again later.',
    standardHeaders: options.standardHeaders !== false, // Return rate limit info in `RateLimit-*` headers
    legacyHeaders: options.legacyHeaders !== false, // Disable `X-RateLimit-*` headers
    // Skip successful requests from rate limit counts
    skipSuccessfulRequests: false,
    // Skip failed requests from rate limit counts
    skipFailedRequests: false,
    // Handler for when rate limit is exceeded
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        error: options.message || 'Too many requests from this IP, please try again later.',
        type: 'RATE_LIMIT_EXCEEDED',
        retryAfter: res.getHeader('Retry-After'),
        timestamp: new Date().toISOString()
      });
    }
  });
};

// General API rate limiter - 600 requests per 15 minutes (more lenient in dev)
export const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000,
  max: isDevelopment ? 2000 : 600,
  message: 'Too many API requests, please try again later.'
});

// Strict rate limiter for auth endpoints - 5 requests per 15 minutes
export const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000,
  max: 5,
  message: 'Too many authentication attempts, please try again later.'
});

// External API rate limiter - 45 requests per minute (to prevent hitting external API limits)
export const externalApiLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 45,
  message: 'Too many requests to external APIs, please slow down.'
});

// Data-intensive endpoint limiter - 50 requests per 5 minutes
export const dataIntensiveLimiter = createRateLimiter({
  windowMs: 5 * 60 * 1000,
  max: 50,
  message: 'Too many data-intensive requests, please try again later.'
});

// File upload limiter - 15 requests per hour
export const uploadLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000,
  max: 15,
  message: 'Too many file uploads, please try again later.'
});

// Batch API limiter - 180 requests per minute (for batch operations, increased for chunking)
export const batchApiLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: isDevelopment ? 450 : 180, // Higher limit to handle chunked requests
  message: 'Too many batch API requests, please slow down.'
});

// Estimates API limiter - 100 requests per 5 minutes (more lenient in dev)
export const estimatesLimiter = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: isDevelopment ? 400 : 100,
  message: 'Too many estimate operations, please slow down.'
});

// API-specific rate limiters for better control
// Xero has the most restrictive limit, so we keep it conservative
export const xeroSpecificLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 50, // 83% of Xero's 60/min limit
  message: 'Too many Xero API requests, please slow down.'
});

// HubSpot can handle much more traffic
export const hubspotSpecificLimiter = createRateLimiter({
  windowMs: 10 * 1000, // 10 seconds (matching their window)
  max: isDevelopment ? 100 : 75, // 50% of their 150/10s limit
  message: 'Too many HubSpot API requests, please slow down.'
});

// Harvest has moderate limits
export const harvestSpecificLimiter = createRateLimiter({
  windowMs: 15 * 1000, // 15 seconds (matching their window)
  max: isDevelopment ? 75 : 50, // 50% of their 100/15s limit
  message: 'Too many Harvest API requests, please slow down.'
});

// Request body size limits
export const bodySizeLimit = {
  json: '10mb', // Default JSON body limit
  urlencoded: '10mb', // URL-encoded body limit
  raw: '10mb', // Raw body limit
  text: '10mb' // Text body limit
};

// Specific limits for different endpoints
export const endpointBodyLimits = {
  '/api/estimates': '5mb',
  '/api/expenses': '2mb',
  '/api/feedback': '1mb',
  '/api/crm': '5mb',
  '/api/activity': '1mb'
};