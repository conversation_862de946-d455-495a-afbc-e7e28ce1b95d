/**
 * Mock Authentication Middleware
 *
 * This middleware bypasses Xero OAuth authentication for local development and testing.
 * It should ONLY be used in non-production environments when USE_MOCK_AUTH=true.
 *
 * SECURITY WARNING: This middleware must never be active in production!
 */

import { Request, Response, NextFunction } from "express";

interface MockRequest extends Request {
  session?: any;
}

export function mockAuthMiddleware(
  req: MockRequest,
  res: Response,
  next: NextFunction,
) {
  // Safety check - never run in production
  if (
    process.env.NODE_ENV === "production" ||
    process.env.USE_MOCK_AUTH !== "true"
  ) {
    return next();
  }

  // Only apply to API routes
  if (!req.path.startsWith("/api/")) {
    return next();
  }

  // Skip if session already has auth
  if (req.session?.tokenSet?.access_token) {
    return next();
  }

  // Inject mock session data
  console.log(`[MOCK AUTH] Injecting mock session for ${req.path}`);

  if (!req.session) {
    req.session = {};
  }

  req.session.tokenSet = {
    access_token: "mock-access-token",
    refresh_token: "mock-refresh-token",
    expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    scope:
      "accounting.transactions accounting.contacts accounting.settings offline_access",
  };

  req.session.user = {
    email: "<EMAIL>",
    name: "Test User",
    id: "mock-user-123",
  };

  req.session.tenants = [
    {
      tenantId: "mock-tenant-123",
      tenantName: "Mock Test Company",
      tenantType: "ORGANISATION",
    },
  ];

  next();
}
