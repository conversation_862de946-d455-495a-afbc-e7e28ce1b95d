#!/bin/bash

# Fix mixed quotes in TypeScript files

echo "Fixing mixed quote issues in TypeScript files..."

# Find all files with mixed quotes in imports
find src -name "*.ts" -o -name "*.tsx" | while read file; do
    # Check if file has mixed quotes issues
    if grep -E "'[^']*\"|\"[^\"]*'" "$file" >/dev/null 2>&1; then
        echo "Fixing: $file"
        
        # Create a temporary file
        temp_file="${file}.tmp"
        
        # Fix patterns like 'something" to 'something'
        sed -E "s/'([^']*)\"/'\\1'/g" "$file" > "$temp_file"
        
        # Fix patterns like "something' to "something"
        sed -E "s/\"([^\"]*)'\"$/\"\\1\"/g" "$temp_file" > "${file}.tmp2"
        
        # Move the fixed file back
        mv "${file}.tmp2" "$file"
        rm -f "$temp_file"
    fi
done

echo "Mixed quote fixes complete!"