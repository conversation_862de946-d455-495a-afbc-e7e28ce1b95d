#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Common problematic patterns and their fixes
const fixes = [
  // Fix malformed string comparisons
  { pattern: /e\.target\.value\s*===\s*""\s*'/g, replacement: 'e.target.value === ""' },
  { pattern: /e\.target\.value\s*===\s*''\s*"/g, replacement: 'e.target.value === ""' },
  
  // Fix malformed function calls with mixed quotes
  { pattern: /handleSettingChange\([^,]+,\s*"([^"]+)'\s*,/g, replacement: 'handleSettingChange($1, "$1",' },
  { pattern: /handleSettingChange\([^,]+,\s*'([^']+)"\s*,/g, replacement: 'handleSettingChange($1, "$1",' },
  
  // Fix case statements with mixed quotes
  { pattern: /case\s*"([^"]+)'\s*:/g, replacement: 'case "$1":' },
  { pattern: /case\s*'([^']+)"\s*:/g, replacement: 'case "$1":' },
  
  // Fix return statements with mixed quotes
  { pattern: /return\s*'([^']+)"\s*;/g, replacement: 'return "$1";' },
  { pattern: /return\s*"([^"]+)'\s*;/g, replacement: 'return "$1";' },
  
  // Fix template literals with mixed quotes
  { pattern: /`([^`]*)'([^`]*)"([^`]*)`/g, replacement: '`$1"$2"$3`' },
  
  // Fix array elements with mixed quotes
  { pattern: /'([^']+)"\s*,/g, replacement: '"$1",' },
  { pattern: /"([^"]+)'\s*,/g, replacement: '"$1",' },
  
  // Fix object property values with mixed quotes
  { pattern: /:\s*'([^']+)"\s*[,}]/g, replacement: ': "$1"$2' },
  { pattern: /:\s*"([^"]+)'\s*[,}]/g, replacement: ': "$1"$2' },
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        changed = true;
      }
    });
    
    if (changed) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Find all TypeScript/TSX files
function findTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
const srcDir = path.join(__dirname, 'src');
const files = findTsFiles(srcDir);

console.log(`Found ${files.length} TypeScript files`);

let fixedCount = 0;
files.forEach(file => {
  if (fixFile(file)) {
    fixedCount++;
  }
});

console.log(`Fixed ${fixedCount} files`);
