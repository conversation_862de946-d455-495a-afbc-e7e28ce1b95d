{"permissions": {"allow": ["Bash(grep:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(find:*)", "mcp__playwright__browser_click", "mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "Bash(find:*)", "Bash(ls:*)", "mcp__playwright__browser_drag", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "Bash(npm run lint:*)", "Bash(rg:*)", "<PERSON><PERSON>(sed:*)", "mcp__context7__resolve-library-id", "Bash(npm test:*)", "mcp__context7__get-library-docs", "Bash(npm test:*)", "<PERSON><PERSON>(diff:*)", "Bash(rm:*)", "Bash(sqlite3:*)", "Bash(npm run build:*)", "WebFetch(domain:help.getharvest.com)", "WebFetch(domain:github.com)", "Bash(npm run kill-ports:*)", "mcp__zen__thinkdeep", "mcp__zen__chat", "mcp__zen__thinkdeep", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__playwright__browser_snapshot", "<PERSON><PERSON>(jq:*)", "Bash(npm run audit:ui:*)", "Bash(node:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run test:visual:*)", "<PERSON><PERSON>(timeout 60 npm run test:visual:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(npm run test:ui:*)", "Bash(./migrate-colors.sh:*)", "Bash(./check-colors.sh:*)", "Bash(for file in src/frontend/components/CRM/pipeline/DealCalendarView.tsx src/frontend/components/ForwardProjection/ProjectionAudit/ReconciliationCards/UninvoicedWorkReconciliation.tsx src/frontend/components/CRM/DealEdit/DealHeaderCard.tsx src/frontend/components/CRM/DealEdit/DealContactsSection.tsx)", "Bash(do)", "Bash(done)", "Bash(for:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/tmp/final-quote-fix.sh:*)"], "deny": []}}