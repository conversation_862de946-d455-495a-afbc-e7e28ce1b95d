# HubSpot Integration Documentation

## 1. Overview

This document provides detailed information about the HubSpot integration in the Upstream Financial Dashboard. It covers authentication, data synchronization, field mapping, and best practices for working with the HubSpot API.

The HubSpot integration allows Upstream to import and synchronize CRM data, including deals, contacts, and companies. This data is used for the CRM system and deal management features.

## 2. Authentication

### 2.1 Authentication Method

Upstream uses API Key authentication for HubSpot integration:

1. **API Key**: A static API key is used for all HubSpot API requests
2. **Configuration**: The API key is set as an environment variable (`HUBSPOT_API_KEY`)

### 2.2 Implementation

```typescript
// Example of HubSpot API request with API key authentication
async function fetchHubSpotData(endpoint: string): Promise<any> {
  const response = await fetch(`https://api.hubapi.com/${endpoint}?hapikey=${process.env.HUBSPOT_API_KEY}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`HubSpot API request failed: ${response.statusText}`);
  }

  return await response.json();
}
```

### 2.3 Future Enhancement

A planned enhancement is to implement full OAuth 2.0 authentication for improved security:

1. **Authorization**: User will be redirected to HubSpot's authorization page
2. **Consent**: User will grant permission to Upstream
3. **Code Exchange**: Application will exchange authorization code for access token
4. **Token Refresh**: Application will use refresh token to obtain new access tokens when needed

## 3. Data Synchronization

### 3.1 Synchronization Process

The HubSpot integration follows a **strict sequential import process** with **association validation**:

1. **Initialization**: User triggers synchronization using "Import All Data" button
2. **Sequential Import**: Data is imported in dependency order to maintain relationships:
   - **Companies first** - Establishes the foundation for all relationships
   - **Deals second** - Links to companies via HubSpot associations API
   - **Contacts third** - Links to companies and deals
   - **Notes/Activities fourth** (optional) - Imports HubSpot engagements (notes, emails, calls, meetings, tasks)
   - **Associations fifth** (optional) - Imports contact-company and deal-contact relationships
3. **Association Validation**:
   - All deals **must** have valid company associations in HubSpot
   - Deals without company associations are **skipped** with error messages
   - No fallback company creation - maintains data integrity
4. **Data Transformation**: HubSpot data is transformed to internal format
5. **Field Mapping**: HubSpot fields are mapped to internal fields
6. **Data Storage**: Transformed data is stored in the local database
7. **Field Ownership**: Field ownership is set to HubSpot for imported fields
8. **Change Logging**: Changes are logged in the change log table

### 3.2 Import Order and Dependencies

**Critical**: The import process follows a strict order to ensure proper relationships:

```typescript
// Import sequence (enforced by /api/hubspot/import/all endpoint)
1. Companies → 2. Deals → 3. Contacts → 4. Notes/Activities → 5. Associations
```

**Why this order matters**:
- Deals require existing companies to link to via HubSpot associations
- Contacts can reference both companies and deals
- Notes/Activities require entities to exist before they can be linked
- Associations require both sides of the relationship to exist
- Real HubSpot relationships are preserved (no artificial company creation)

### 3.3 Association Validation

The system uses HubSpot's associations API to maintain real relationships:

```typescript
// Get company association for a deal
async function getDealCompanyAssociation(dealId: string): Promise<string | null> {
  const url = `https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/companies`;
  const response = await fetch(url, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });

  if (response.ok) {
    const data = await response.json();
    return data.results?.[0]?.id || null;
  }

  return null;
}
```

**Validation Rules**:
- Deals without company associations are **skipped** (not imported)
- Company must exist in local database before deal import
- No artificial company creation from deal names

### 3.4 Synchronized Entities

The following entities are synchronized from HubSpot:

#### Deals

**Important**: Deals are imported with **strict company association validation**. Only deals with valid company associations in HubSpot are imported.

```typescript
// Import deals with company association validation
async function importDealsFromHubSpot(): Promise<{ success: boolean; count: number }> {
  // 1. Fetch deals from HubSpot
  const response = await fetch(
    `https://api.hubapi.com/crm/v3/objects/deals?properties=dealname,dealstage,amount,closedate,description,hubspot_owner_id,chance_of_win`,
    {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    }
  );

  const data = await response.json();
  let importedCount = 0;

  for (const deal of data.results) {
    // 2. Get company association for this deal
    const companyId = await getDealCompanyAssociation(deal.id);

    if (!companyId) {
      console.log(`Skipping deal "${deal.properties.dealname}" - no company association found in HubSpot`);
      continue;
    }

    // 3. Verify company exists in local database
    const existingCompany = companyRepository.getCompanyByHubspotId(companyId);

    if (!existingCompany) {
      console.log(`Skipping deal "${deal.properties.dealname}" - company with HubSpot ID ${companyId} not found in database`);
      continue;
    }

    // 4. Create deal with proper company association
    const dealData = {
      id: generateUuid(),
      name: deal.properties.dealname,
      stage: mapHubSpotStage(deal.properties.dealstage),
      value: parseFloat(deal.properties.amount) || 0,
      currency: 'AUD',
      probability: parseFloat(deal.properties.chance_of_win) || 0,
      expectedCloseDate: deal.properties.closedate,
      description: deal.properties.description,
      owner: deal.properties.hubspot_owner_id,
      hubspotId: deal.id,
      companyId: existingCompany.id, // Link to real company
      source: 'HubSpot',
      includeInProjections: true
    };

    dealRepository.createDeal(dealData, 'HubSpot');
    importedCount++;
  }

  return { success: true, count: importedCount };
}
```

**Key Changes from Previous Implementation**:

- **No fallback company creation** - deals without associations are skipped
- **Real company relationships** - uses HubSpot associations API
- **Data integrity** - ensures all deals have valid company links
- **Clear error logging** - explains why deals are skipped

#### Contacts

```typescript
// Fetch contacts from HubSpot
async function getHubSpotContacts(): Promise<Contact[]> {
  const response = await fetch(
    `https://api.hubapi.com/crm/v3/objects/contacts?properties=firstname,lastname,email,phone,jobtitle&hapikey=${process.env.HUBSPOT_API_KEY}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch HubSpot contacts: ${response.statusText}`);
  }

  const data = await response.json();

  // Transform HubSpot contacts to internal format
  return data.results.map((contact: any) => ({
    id: generateUuid(), // Generate a new UUID for internal use
    firstName: contact.properties.firstname,
    lastName: contact.properties.lastname,
    email: contact.properties.email,
    phone: contact.properties.phone,
    jobTitle: contact.properties.jobtitle,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    hubspotId: contact.id
  }));
}
```

#### Companies

```typescript
// Fetch companies from HubSpot
async function getHubSpotCompanies(): Promise<Company[]> {
  const response = await fetch(
    `https://api.hubapi.com/crm/v3/objects/companies?properties=name,industry,website,address&hapikey=${process.env.HUBSPOT_API_KEY}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch HubSpot companies: ${response.statusText}`);
  }

  const data = await response.json();

  // Transform HubSpot companies to internal format
  return data.results.map((company: any) => ({
    id: generateUuid(), // Generate a new UUID for internal use
    name: company.properties.name,
    industry: company.properties.industry,
    website: company.properties.website,
    address: company.properties.address,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    hubspotId: company.id
  }));
}
```

**Company Deletion Behavior**:
- Unlike deals, companies are **NOT** automatically deleted when removed from HubSpot
- This preserves relationships with deals, contacts, estimates, and projects
- Soft-deleted companies in Upstream can be restored by syncing from HubSpot
- The `getCompanyByHubspotId()` method properly filters out soft-deleted records

#### Notes and Activities

The system imports HubSpot engagements as notes in the internal system:

```typescript
// Supported engagement types
const engagementTypes = {
  'note': 'general',
  'email': 'email',
  'call': 'call',
  'meeting': 'meeting',
  'task': 'task',
  'incoming_email': 'email',
  'forwarded_email': 'email'
};

// Notes are imported for all entities with HubSpot IDs
// Stored in the polymorphic 'note' table with entity_type and entity_id
```

**Key Features**:
- Imports all HubSpot engagements (notes, emails, calls, meetings, tasks)
- Maps engagement types to internal note types
- Preserves original timestamps and content
- Links notes to correct entities (companies, contacts, deals)
- Supports threaded conversations

### 3.5 Association Import

The integration imports two types of associations:

#### Contact-Company Associations

```typescript
// Imports contact-company relationships
// Uses the contact_company junction table
// Preserves role information and primary status from HubSpot
```

#### Deal-Contact Associations

```typescript
// Imports deal-contact relationships
// Uses the contact_role table
// Preserves role information and primary status from HubSpot
```

**Association Features**:
- Imports after all entities are created
- Preserves HubSpot relationship metadata
- Handles roles and primary contact designation
- Prevents duplicate associations

## 4. Field Mapping

### 4.1 Deal Field Mapping

The system maps HubSpot deal fields to internal fields:

| HubSpot Field | Internal Field |
|---------------|----------------|
| dealname | name |
| dealstage | stage |
| amount | value |
| closedate | expectedCloseDate |
| description | description |
| hubspot_owner_id | owner |
| chance_of_win (custom) | probability |

### 4.2 Contact Field Mapping

The system maps HubSpot contact fields to internal fields:

| HubSpot Field | Internal Field |
|---------------|----------------|
| firstname | firstName |
| lastname | lastName |
| email | email |
| phone | phone |
| jobtitle | jobTitle |

### 4.3 Company Field Mapping

The system maps HubSpot company fields to internal fields:

| HubSpot Field | Internal Field |
|---------------|----------------|
| name | name |
| industry | industry |
| website | website |
| address | address |

### 4.4 Note/Engagement Field Mapping

The system maps HubSpot engagement fields to internal note fields:

| HubSpot Field | Internal Field |
|---------------|----------------|
| hs_engagement_type | noteType (mapped via engagementTypes) |
| hs_engagement_body | content |
| hs_engagement_subject | content (if body is empty) |
| hs_timestamp | createdAt |
| hs_created_by | createdBy (set to 'HubSpot Import') |

### 4.5 Association Field Mapping

| HubSpot Association Data | Internal Field |
|-------------------------|----------------|
| associationTypes[].label | role |
| associationTypes[].isPrimary | isPrimary |
| association timestamp | createdAt |

## 5. Field Ownership

### 5.1 Field Ownership Rules

When data is imported from HubSpot, field ownership is set to HubSpot for standard fields. However, the deal name field is never locked by other systems (e.g., estimates) to ensure HubSpot can always update it:

```typescript
// Set field ownership for HubSpot-imported deal
function setHubSpotFieldOwnership(dealId: string): void {
  const fields = [
    'name',
    'stage',
    'value',
    'currency',
    'probability',
    'expectedCloseDate',
    'description',
    'owner'
  ];

  for (const field of fields) {
    db.prepare(`
      INSERT OR REPLACE INTO crm_deal_field_ownership (
        deal_id, field_name, owner_source, last_updated
      ) VALUES (?, ?, 'HubSpot', ?)
    `).run(dealId, field, new Date().toISOString());
  }
}
```

**Important Note**: The deal name field is always editable by users and can always be updated by HubSpot, even when the deal is linked to an estimate. This ensures smooth synchronization with external systems.

### 5.2 Update Rules

When HubSpot data is re-imported, only HubSpot-owned fields are updated:

```typescript
// Update a deal with HubSpot data
function updateDealWithHubSpotData(dealId: string, hubspotDeal: any): void {
  // Get current field ownership
  const fieldOwnership = getFieldOwnership(dealId);

  // Fields to update
  const updatesData = {
    name: hubspotDeal.properties.dealname,
    stage: hubspotDeal.properties.dealstage,
    value: parseFloat(hubspotDeal.properties.amount) || 0,
    expected_close_date: hubspotDeal.properties.closedate,
    description: hubspotDeal.properties.description,
    owner: hubspotDeal.properties.hubspot_owner_id,
    probability: parseFloat(hubspotDeal.properties.chance_of_win) || 0
  };

  // Update each field if allowed by ownership rules
  for (const [field, value] of Object.entries(updatesData)) {
    if (!value) continue;

    const currentOwner = fieldOwnership[field];

    // Only update if field is owned by HubSpot or has no owner
    if (!currentOwner || currentOwner === 'HubSpot') {
      // Update the field
      updateDealField(dealId, field, value, 'HubSpot');

      // Set field ownership to HubSpot if not already set
      if (!currentOwner) {
        setFieldOwnership(dealId, field, 'HubSpot');
      }
    }
  }
}
```

## 6. Error Handling

### 6.1 Error Handling Strategy

The HubSpot integration includes robust error handling:

1. **Rate Limiting**: Respects HubSpot's rate limits
2. **Pagination**: Properly handles pagination for large datasets
3. **Partial Results**: Returns partial data when some API calls fail
4. **Field Validation**: Validates fields before processing

### 6.2 Implementation

```typescript
async function makeHubSpotRequest(endpoint: string): Promise<any> {
  try {
    const response = await fetch(`https://api.hubapi.com/${endpoint}?hapikey=${process.env.HUBSPOT_API_KEY}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 429) {
      // Rate limit exceeded, wait and retry
      const retryAfter = parseInt(response.headers.get('Retry-After') || '10', 10);
      await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
      return makeHubSpotRequest(endpoint);
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`HubSpot API error (${response.status}): ${errorData.message || response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    // Log the error
    console.error('HubSpot API request failed:', error);

    // Return partial data or throw, depending on the context
    if (error.message.includes('Rate limit exceeded')) {
      return { partial: true, message: 'Rate limit exceeded, showing partial data' };
    }

    throw error;
  }
}
```

## 7. Real-Time Progress Tracking

### 7.1 Overview

The HubSpot integration includes real-time progress tracking for import operations using Socket.IO. This provides users with live updates during the import process, including:

- **Step-by-step progress** through companies, deals, contacts, notes/activities, and associations
- **Real-time item counts** and current processing status
- **Error tracking** with detailed error messages and affected items
- **Update tracking** showing what fields were changed during import
- **Creation tracking** showing newly created records
- **Notes import progress** showing engagement type and content preview
- **Association import progress** showing relationship creation

### 7.2 Technical Implementation

The progress tracking system uses Socket.IO for real-time communication between the server and client:

```typescript
// Server-side: HubSpot service emits progress events
export class HubSpotService extends EventEmitter {
  private socketIO: any = null;

  setSocketIO(io: any) {
    this.socketIO = io;
  }

  emit(event: string, ...args: any[]): boolean {
    const result = super.emit(event, ...args);

    // Also emit to Socket.IO if available
    if (this.socketIO && event === 'progress') {
      this.socketIO.emit('hubspot-import-progress', args[0]);
    }

    return result;
  }
}
```

```typescript
// Client-side: React component listens for progress updates
const socket = io(socketUrl, {
  reconnection: true,
  reconnectionAttempts: 3,
  reconnectionDelay: 1000,
  timeout: 5000,
  transports: ['polling', 'websocket']
});

socket.on('hubspot-import-progress', (progressData: ImportProgress) => {
  setProgress(progressData);
});
```

### 7.3 Progress Data Structure

```typescript
interface ImportProgress {
  step: 'companies' | 'deals' | 'contacts' | 'notes' | 'contact-company associations' | 'deal-contact associations';
  current: number;
  total: number;
  currentItem?: string;
  errors: Array<{ item: string; error: string }>;
}

interface ImportResult {
  success: boolean;
  count: number;
  errors: Array<{ item: string; error: string }>;
  updates: Array<{ item: string; changes: string[] }>;
  created: Array<{ item: string }>;
  error?: string;
}
```

### 7.4 Error Handling and Graceful Degradation

The system is designed to work even when Socket.IO connections fail:

- **Connection Error Handling**: Shows warning message but continues import
- **Fallback UI**: Displays generic progress when real-time updates unavailable
- **Timeout Protection**: Automatically disconnects after reasonable timeout
- **Reconnection Logic**: Attempts to reconnect with exponential backoff

### 7.5 CORS Configuration

Socket.IO is configured with flexible CORS settings for deployment:

```typescript
export const io = new SocketIOServer(httpServer, {
  cors: {
    origin: function(origin, callback) {
      if (!origin ||
          origin.includes('onbord') ||
          origin.includes('localhost') ||
          origin.includes('render.com')) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST']
  }
});
```

## 8. Best Practices

When working with the HubSpot API in Upstream, follow these best practices:

1. **Pagination Handling**:
   - Always handle pagination for endpoints that return large datasets
   - Use the `limit` and `after` parameters to control pagination
   - Check for the `paging.next.after` field in the response to determine if more pages exist

2. **Rate Limit Management**:
   - Implement request throttling
   - Use caching to reduce API calls
   - Batch requests where possible
   - Implement exponential backoff for retries

3. **Error Handling**:
   - Implement robust error handling
   - Provide meaningful error messages
   - Handle partial data scenarios
   - Log errors for troubleshooting

4. **Data Validation**:
   - Validate API responses before processing
   - Implement type checking for API responses
   - Handle missing or null data gracefully

5. **Performance Optimization**:
   - Use the batch API for fetching multiple records
   - Limit properties to only those needed
   - Cache frequently accessed data

6. **Real-Time Features**:
   - Implement graceful degradation for Socket.IO connections
   - Provide fallback UI when real-time updates are unavailable
   - Use appropriate timeout and reconnection settings
   - Handle connection errors without breaking the import process
