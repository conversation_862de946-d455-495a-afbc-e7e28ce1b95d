# Company Linking API

This document describes the API endpoints for managing company linking between external systems (HubSpot and Harvest).

## Overview

The Company Linking API allows you to manage connections between companies in the upstream database and external systems. This enables proper association of companies across HubSpot and Harvest while maintaining the upstream database as the source of truth.

## Authentication

All endpoints require authentication via session cookies. Users must be logged in to access these endpoints.

## Endpoints

### Get Companies with Linking Status

Retrieve all companies with their external system linking status.

```http
GET /api/crm/companies/linking-status
```

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "company-uuid",
      "name": "Example Company Ltd",
      "hubspotId": "12345678",
      "harvestId": 789,
      "source": "HubSpot",
      "linkingStatus": "both",
      "industry": "Technology",
      "website": "https://example.com",
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-01-15T12:30:00.000Z"
    }
  ]
}
```

**Linking Status Values:**
- `both`: Linked to both HubSpot and Harvest
- `hubspot_only`: Only linked to HubSpot
- `harvest_only`: Only linked to Harvest  
- `none`: Manually created, no external links

### Get Linked Harvest IDs

Retrieve all Harvest IDs that are already linked to companies in the system.

```http
GET /api/crm/companies/linked-harvest-ids
```

**Response:**

```json
{
  "success": true,
  "data": [789, 1234, 5678]
}
```

**Notes:**
- Returns only IDs from non-deleted companies
- Used by the Data Management UI to prevent duplicate linking
- Empty array if no companies are linked to Harvest

### Link Company to HubSpot

Link an existing company to a HubSpot company record.

```http
POST /api/crm/companies/:id/link-hubspot
```

**Parameters:**
- `id` (string): Company UUID

**Request Body:**

```json
{
  "hubspotId": "12345678"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "company-uuid",
    "name": "Example Company Ltd",
    "hubspotId": "12345678",
    "harvestId": null,
    "linkingStatus": "hubspot_only"
  },
  "message": "Company successfully linked to HubSpot"
}
```

**Error Responses:**

```json
{
  "success": false,
  "error": "Conflict",
  "message": "HubSpot ID 12345678 is already linked to company: Another Company"
}
```

### Link Company to Harvest

Link an existing company to a Harvest client record.

```http
POST /api/crm/companies/:id/link-harvest
```

**Parameters:**
- `id` (string): Company UUID

**Request Body:**

```json
{
  "harvestId": 789
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "company-uuid",
    "name": "Example Company Ltd",
    "hubspotId": null,
    "harvestId": 789,
    "linkingStatus": "harvest_only"
  },
  "message": "Company successfully linked to Harvest"
}
```

### Unlink Company from HubSpot

Remove the HubSpot link from a company.

```http
DELETE /api/crm/companies/:id/link-hubspot
```

**Parameters:**
- `id` (string): Company UUID

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "company-uuid",
    "name": "Example Company Ltd",
    "hubspotId": null,
    "harvestId": 789,
    "linkingStatus": "harvest_only"
  },
  "message": "Company successfully unlinked from HubSpot"
}
```

### Unlink Company from Harvest

Remove the Harvest link from a company.

```http
DELETE /api/crm/companies/:id/link-harvest
```

**Parameters:**
- `id` (string): Company UUID

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "company-uuid",
    "name": "Example Company Ltd",
    "hubspotId": "12345678",
    "harvestId": null,
    "linkingStatus": "hubspot_only"
  },
  "message": "Company successfully unlinked from Harvest"
}
```

### Merge Companies

Merge two company records, typically used when the same company exists as separate records from different sources.

```http
POST /api/crm/companies/:sourceId/merge/:targetId
```

**Parameters:**
- `sourceId` (string): UUID of company to merge from (will be deleted)
- `targetId` (string): UUID of company to merge to (will be updated)

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "target-company-uuid",
    "name": "Merged Company Ltd",
    "hubspotId": "12345678",
    "harvestId": 789,
    "linkingStatus": "both"
  },
  "message": "Companies successfully merged"
}
```

**Merge Process:**
1. External IDs from source company are added to target company
2. Related records (deals, contacts) are transferred to target company
3. Source company is soft deleted
4. All changes are logged in audit trail

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error Type",
  "message": "Detailed error message"
}
```

**Common Error Types:**
- `Missing required field` (400): Required request parameters missing
- `Company not found` (404): Company ID doesn't exist
- `Conflict` (409): External ID already linked to another company
- `Failed to [operation]` (500): Server error during operation

## Audit Trail

All linking operations are automatically logged in the audit trail with:

- Entity type and ID
- Field name changed
- Old and new values
- Change source ('Manual')
- User who made the change
- Timestamp

## Usage Examples

### Frontend Integration

```typescript
import { 
  getCompaniesWithLinkingStatus,
  linkCompanyToHubSpot,
  linkCompanyToHarvest,
  mergeCompanies 
} from './api/crm';

// Get all companies with linking status
const companies = await getCompaniesWithLinkingStatus();

// Link a company to HubSpot
await linkCompanyToHubSpot('company-uuid', 'hubspot-id');

// Merge duplicate companies
await mergeCompanies('source-uuid', 'target-uuid');
```

### Data Management Workflow

1. **List All Companies**: Use `/companies/linking-status` to view all companies
2. **Identify Duplicates**: Look for companies with similar names but different sources
3. **Link or Merge**: Either link companies to external systems or merge duplicates
4. **Verify Results**: Check that linking status is updated correctly

## Best Practices

1. **Check for Duplicates**: Before linking, ensure the external ID isn't already used
2. **Merge Similar Companies**: If the same company exists from multiple sources, merge them
3. **Maintain Audit Trail**: All operations are logged automatically for compliance
4. **Use Transactions**: The API uses database transactions to ensure data consistency
5. **Handle Errors**: Always check response status and handle conflicts appropriately

## Integration Notes

- **HubSpot IDs**: String format, typically 8-11 digits
- **Harvest IDs**: Integer format, typically 4-7 digits  
- **Company Names**: Should match between systems for easier identification
- **Source Tracking**: Companies maintain their original source even after linking
- **Soft Deletes**: Merged companies are soft deleted, not permanently removed