# Quote Mismatch Fixer Scripts

This directory contains scripts to fix quote mismatch issues in console statements throughout the codebase.

## Problem

The codebase has several instances of mismatched quotes in console statements, such as:
```javascript
console.log("text: ', object);  // Should be: console.log('text: ', object);
console.error("error: ', error); // Should be: console.error('error: ', error);
```

## Solutions

### 1. Regex-based Script (`fix-quote-mismatches.js`)

A lightweight script that uses regular expressions to find and fix quote mismatches.

**Pros:**
- No additional dependencies required (only needs `glob`)
- Fast execution
- Works on all file types

**Cons:**
- Less accurate than AST parsing
- May have edge cases with complex string patterns

### 2. AST-based Script (`fix-quote-mismatches-ast.js`)

An advanced script that uses Babel AST parsing for more accurate fixes.

**Pros:**
- More accurate parsing
- Understands code structure
- Safer for complex cases

**Cons:**
- Requires Babel dependencies
- Slower execution
- Falls back to regex if parsing fails

## Installation

```bash
cd scripts
npm install
```

## Usage

### Dry Run (Preview Changes)

```bash
# Using regex script
npm run fix-quotes:dry

# Using AST script
npm run fix-quotes:ast:dry

# Or directly
node fix-quote-mismatches.js --dry-run
node fix-quote-mismatches-ast.js --dry-run
```

### Apply Fixes

```bash
# Using regex script (recommended for first pass)
npm run fix-quotes

# Using AST script (for complex cases)
npm run fix-quotes:ast

# Or directly
node fix-quote-mismatches.js
node fix-quote-mismatches-ast.js
```

### Verbose Mode

```bash
# See detailed output
npm run fix-quotes:verbose

# Or
node fix-quote-mismatches.js --verbose
node fix-quote-mismatches-ast.js --verbose
```

### Options

- `--dry-run`: Preview changes without modifying files
- `--verbose`: Show detailed information about each fix
- `--regex`: Force regex mode in AST script (if AST parsing fails)

## What Gets Fixed

1. **Mismatched quotes in console statements:**
   ```javascript
   console.log("text: ', object) → console.log('text: ', object)
   ```

2. **Strings with apostrophes:**
   ```javascript
   console.log("it's broken: ', obj) → console.log("it's broken: ", obj)
   ```

3. **Complex patterns:**
   ```javascript
   console.error("Error in 'module': ", err) → console.error("Error in 'module': ", err)
   ```

## File Coverage

The scripts scan all TypeScript and JavaScript files:
- `*.ts`
- `*.tsx`
- `*.js`
- `*.jsx`

Excluding:
- `node_modules/`
- `dist/`
- `.next/`
- `build/`
- `coverage/`

## Safety

- Both scripts are safe to run multiple times
- Original quotes are preserved when they contain special characters
- Escaped quotes are handled properly
- Files are only modified if changes are needed

## Recommendations

1. Run the regex script first with `--dry-run` to preview changes
2. Review the output
3. Run without `--dry-run` to apply fixes
4. Use the AST script for any remaining complex cases
5. Run your test suite to ensure no functionality was broken