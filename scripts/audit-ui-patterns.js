const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;

class UIPatternAuditor {
  constructor() {
    this.report = {
      summary: {
        totalFiles: 0,
        totalComponents: 0,
        componentsWithIssues: 0,
        hardcodedColors: 0,
        inlineStyles: 0,
      },
      components: {},
      rawHtmlElements: {
        button: [],
        input: [],
        select: [],
        textarea: [],
      },
      globalIssues: {
        allHardcodedColors: [],
        tailwindColorUtilities: {},
      },
    };

    this.componentNames = ['Button', 'Alert', 'Card', 'Badge', 'Input', 'Select', 'Textarea', 'Modal'];
    this.colorRegex = {
      hex: /#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\b/g,
      rgb: /rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/g,
      tailwindColor: /\b(bg|text|border|ring|outline)-(red|blue|green|yellow|orange|purple|pink|gray|indigo|cyan|emerald|teal|lime|amber|rose|violet|fuchsia|sky)-(\d{50,950})\b/g,
      cssVar: /var\(--[\w-]+\)/g,
    };
  }

  async auditCodebase(srcPath) {
    const files = glob.sync(`${srcPath}/**/*.{tsx,jsx}`, {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**'],
    });

    console.log(`Found ${files.length} files to audit...`);
    this.report.summary.totalFiles = files.length;

    for (const file of files) {
      await this.auditFile(file);
    }

    this.calculateSummary();
    return this.report;
  }

  async auditFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const ast = parse(content, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript'],
      });

      const relativeFile = path.relative(process.cwd(), filePath);

      traverse(ast, {
        JSXElement: (nodePath) => {
          const node = nodePath.node;
          const opening = node.openingElement;
          
          if (opening.name.type === 'JSXIdentifier') {
            const componentName = opening.name.name;
            
            // Check if it's one of our UI components
            if (this.componentNames.includes(componentName)) {
              this.auditComponent(componentName, node, relativeFile, content);
            }
            
            // Check for raw HTML elements
            if (['button', 'input', 'select', 'textarea'].includes(componentName)) {
              this.auditRawHtmlElement(componentName, node, relativeFile, content);
            }
          }
        },
        // Also check for hardcoded colors in any string literals
        StringLiteral: (nodePath) => {
          const value = nodePath.node.value;
          this.detectHardcodedColors(value, relativeFile, nodePath.node.loc?.start.line || 0);
        },
      });
    } catch (error) {
      console.error(`Error auditing file ${filePath}:`, error);
    }
  }

  auditComponent(componentName, node, file, content) {
    if (!this.report.components[componentName]) {
      this.report.components[componentName] = {
        totalUsages: 0,
        variants: {},
        classPatterns: {},
        issues: {
          hardcodedColors: [],
          inlineStyles: [],
          nonSemanticClasses: [],
        },
        usages: [],
      };
    }

    const component = this.report.components[componentName];
    component.totalUsages++;

    const props = this.extractProps(node.openingElement);
    const location = node.loc?.start || { line: 0, column: 0 };
    
    const usage = {
      file,
      line: location.line,
      column: location.column,
      props,
      rawCode: this.extractCodeSnippet(content, location.line),
    };

    // Extract variant
    if (props.variant) {
      usage.variant = props.variant;
      component.variants[props.variant] = (component.variants[props.variant] || 0) + 1;
    }

    // Extract className
    if (props.className) {
      usage.className = props.className;
      this.analyzeClassName(props.className, component, usage);
    }

    // Check for inline styles
    if (props.style) {
      usage.inlineStyle = props.style;
      component.issues.inlineStyles.push(usage);
      this.report.summary.inlineStyles++;
    }

    component.usages.push(usage);
  }

  extractProps(opening) {
    const props = {};

    opening.attributes.forEach((attr) => {
      if (attr.type === 'JSXAttribute' && attr.name.type === 'JSXIdentifier') {
        const name = attr.name.name;
        
        if (!attr.value) {
          props[name] = true;
        } else if (attr.value.type === 'StringLiteral') {
          props[name] = attr.value.value;
        } else if (attr.value.type === 'JSXExpressionContainer') {
          // Handle expressions like {true}, {false}, {variable}, etc.
          if (attr.value.expression.type === 'BooleanLiteral') {
            props[name] = attr.value.expression.value;
          } else if (attr.value.expression.type === 'StringLiteral') {
            props[name] = attr.value.expression.value;
          } else if (attr.value.expression.type === 'ObjectExpression') {
            // Parse inline style objects
            props[name] = this.parseObjectExpression(attr.value.expression);
          } else {
            // Store as string representation for other expressions
            props[name] = '<expression>';
          }
        }
      }
    });

    return props;
  }

  parseObjectExpression(obj) {
    const result = {};
    
    obj.properties.forEach((prop) => {
      if (prop.type === 'ObjectProperty' && prop.key.type === 'Identifier') {
        const key = prop.key.name;
        if (prop.value.type === 'StringLiteral') {
          result[key] = prop.value.value;
        } else if (prop.value.type === 'NumericLiteral') {
          result[key] = prop.value.value;
        }
      }
    });

    return result;
  }

  analyzeClassName(className, component, usage) {
    // Track patterns
    const patterns = className.split(/\s+/);
    patterns.forEach((pattern) => {
      component.classPatterns[pattern] = (component.classPatterns[pattern] || 0) + 1;

      // Check for Tailwind color utilities
      if (this.colorRegex.tailwindColor.test(pattern)) {
        component.issues.nonSemanticClasses.push(usage);
        this.report.globalIssues.tailwindColorUtilities[pattern] = 
          (this.report.globalIssues.tailwindColorUtilities[pattern] || 0) + 1;
      }
    });

    // Check for hardcoded colors in className
    this.detectHardcodedColors(className, usage.file, usage.line);
  }

  detectHardcodedColors(value, file, line) {
    // Check for hex colors
    const hexMatches = value.match(this.colorRegex.hex);
    if (hexMatches) {
      hexMatches.forEach((color) => {
        this.report.globalIssues.allHardcodedColors.push({
          file,
          line,
          color,
          type: 'hex',
          context: value,
        });
        this.report.summary.hardcodedColors++;
      });
    }

    // Check for rgb colors
    const rgbMatches = value.match(this.colorRegex.rgb);
    if (rgbMatches) {
      rgbMatches.forEach((color) => {
        this.report.globalIssues.allHardcodedColors.push({
          file,
          line,
          color,
          type: 'rgb',
          context: value,
        });
        this.report.summary.hardcodedColors++;
      });
    }
  }

  auditRawHtmlElement(elementName, node, file, content) {
    const location = node.loc?.start || { line: 0, column: 0 };
    const props = this.extractProps(node.openingElement);
    
    const usage = {
      file,
      line: location.line,
      column: location.column,
      props,
      className: props.className,
      rawCode: this.extractCodeSnippet(content, location.line),
    };

    this.report.rawHtmlElements[elementName].push(usage);
  }

  extractCodeSnippet(content, line) {
    const lines = content.split('\n');
    const startLine = Math.max(0, line - 2);
    const endLine = Math.min(lines.length, line + 1);
    return lines.slice(startLine, endLine).join('\n');
  }

  calculateSummary() {
    // Count components with issues
    Object.values(this.report.components).forEach((component) => {
      if (
        component.issues.hardcodedColors.length > 0 ||
        component.issues.inlineStyles.length > 0 ||
        component.issues.nonSemanticClasses.length > 0
      ) {
        this.report.summary.componentsWithIssues++;
      }
      this.report.summary.totalComponents += component.totalUsages;
    });
  }

  generateReport() {
    console.log('\n=== UI Pattern Audit Report ===\n');
    
    console.log('Summary:');
    console.log(`- Total files scanned: ${this.report.summary.totalFiles}`);
    console.log(`- Total component usages: ${this.report.summary.totalComponents}`);
    console.log(`- Components with issues: ${this.report.summary.componentsWithIssues}`);
    console.log(`- Hardcoded colors found: ${this.report.summary.hardcodedColors}`);
    console.log(`- Inline styles found: ${this.report.summary.inlineStyles}`);

    console.log('\n=== Component Analysis ===\n');
    
    Object.entries(this.report.components).forEach(([name, data]) => {
      console.log(`\n${name} Component (${data.totalUsages} usages):`);
      
      if (Object.keys(data.variants).length > 0) {
        console.log('  Variants:');
        Object.entries(data.variants)
          .sort(([, a], [, b]) => b - a)
          .forEach(([variant, count]) => {
            console.log(`    - ${variant}: ${count}`);
          });
      }

      if (data.issues.inlineStyles.length > 0) {
        console.log(`  ⚠️  ${data.issues.inlineStyles.length} inline styles found`);
      }

      if (data.issues.nonSemanticClasses.length > 0) {
        console.log(`  ⚠️  ${data.issues.nonSemanticClasses.length} non-semantic classes found`);
      }
    });

    console.log('\n=== Raw HTML Elements ===\n');
    Object.entries(this.report.rawHtmlElements).forEach(([element, usages]) => {
      if (usages.length > 0) {
        console.log(`${element}: ${usages.length} instances that should use components`);
      }
    });

    console.log('\n=== Top Tailwind Color Utilities ===\n');
    Object.entries(this.report.globalIssues.tailwindColorUtilities)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .forEach(([utility, count]) => {
        console.log(`  ${utility}: ${count} usages`);
      });

    // Save detailed JSON report
    const reportPath = path.join(process.cwd(), 'ui-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
    console.log(`\n✅ Detailed report saved to: ${reportPath}`);
  }
}

// Run the audit
async function main() {
  const auditor = new UIPatternAuditor();
  const srcPath = path.join(process.cwd(), 'src/frontend');
  
  console.log('Starting UI pattern audit...');
  await auditor.auditCodebase(srcPath);
  auditor.generateReport();
}

main().catch(console.error);