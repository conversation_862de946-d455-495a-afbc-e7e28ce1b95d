#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// ANSI color codes for output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[36m',
  red: '\x1b[31m'
};

// Configuration
const config = {
  extensions: ['ts', 'tsx', 'js', 'jsx'],
  ignorePaths: ['node_modules', 'dist', '.next', 'build', 'coverage'],
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose')
};

// Statistics
let stats = {
  filesScanned: 0,
  filesModified: 0,
  issuesFixed: 0,
  errors: []
};

// Pattern to match console statements with mismatched quotes
// This regex captures:
// 1. The console method (log, error, warn, etc.)
// 2. Opening quote (single or double)
// 3. The text content
// 4. The mismatched closing pattern (: ', or : ",)
const consoleMismatchPattern = /console\.(log|error|warn|info|debug)\s*\(\s*(["'])((?:(?!\2).)*?):\s*(['"])\s*,/g;

// Function to fix quote mismatches in a string
function fixQuoteMismatches(content, filePath) {
  let modified = false;
  let fixedContent = content;
  let matches = [];

  // Find all matches first
  let match;
  while ((match = consoleMismatchPattern.exec(content)) !== null) {
    matches.push({
      fullMatch: match[0],
      method: match[1],
      openQuote: match[2],
      text: match[3],
      closeQuote: match[4],
      index: match.index
    });
  }

  // Process matches in reverse order to maintain indices
  for (let i = matches.length - 1; i >= 0; i--) {
    const m = matches[i];
    
    // Check if quotes are mismatched
    if (m.openQuote !== m.closeQuote) {
      const oldPattern = m.fullMatch;
      
      // Determine which quote to use based on content
      let preferredQuote = "'"; // Default to single quotes
      
      // If the text contains single quotes but no double quotes, use double quotes
      if (m.text.includes("'") && !m.text.includes('"')) {
        preferredQuote = '"';
      }
      // If the text contains double quotes but no single quotes, use single quotes
      else if (m.text.includes('"') && !m.text.includes("'")) {
        preferredQuote = "'";
      }
      // If it contains both or neither, prefer single quotes and escape as needed
      
      // Escape the text properly for the chosen quote
      let escapedText = m.text;
      if (preferredQuote === "'") {
        escapedText = m.text.replace(/'/g, "\\'");
      } else {
        escapedText = m.text.replace(/"/g, '\\"');
      }
      
      // Build the fixed pattern
      const newPattern = `console.${m.method}(${preferredQuote}${escapedText}: ${preferredQuote},`;
      
      // Replace in the content
      fixedContent = fixedContent.substring(0, m.index) + newPattern + fixedContent.substring(m.index + oldPattern.length);
      
      modified = true;
      stats.issuesFixed++;
      
      if (config.verbose) {
        console.log(`${colors.yellow}Fixed in ${filePath}:${colors.reset}`);
        console.log(`  ${colors.red}- ${oldPattern}${colors.reset}`);
        console.log(`  ${colors.green}+ ${newPattern}${colors.reset}`);
      }
    }
  }

  // Also fix patterns where the entire string might be using mismatched quotes
  // Pattern: "text with 'quotes' inside" → 'text with \'quotes\' inside' or vice versa
  const stringPattern = /console\.(log|error|warn|info|debug)\s*\(\s*["'].*?["']\s*[,)]/g;
  
  fixedContent = fixedContent.replace(stringPattern, (match) => {
    // Check if the string has balanced quotes
    const openQuoteMatch = match.match(/console\.\w+\s*\(\s*(["'])/);
    if (!openQuoteMatch) return match;
    
    const openQuote = openQuoteMatch[1];
    const beforeString = match.substring(0, match.indexOf(openQuote) + 1);
    const afterOpenQuote = match.substring(match.indexOf(openQuote) + 1);
    
    // Find the closing quote (considering escaped quotes)
    let closeIndex = -1;
    let escaped = false;
    for (let i = 0; i < afterOpenQuote.length; i++) {
      if (escaped) {
        escaped = false;
        continue;
      }
      if (afterOpenQuote[i] === '\\') {
        escaped = true;
        continue;
      }
      if (afterOpenQuote[i] === openQuote) {
        closeIndex = i;
        break;
      }
    }
    
    if (closeIndex === -1) {
      // Likely a mismatch - try to find any quote
      const anyQuoteMatch = afterOpenQuote.match(/["']\s*[,)]/);
      if (anyQuoteMatch) {
        const wrongQuote = anyQuoteMatch[0][0];
        if (wrongQuote !== openQuote) {
          // Found mismatched closing quote
          const stringContent = afterOpenQuote.substring(0, afterOpenQuote.indexOf(wrongQuote));
          const afterString = afterOpenQuote.substring(afterOpenQuote.indexOf(wrongQuote) + 1);
          
          // Determine best quote to use
          let bestQuote = "'";
          if (stringContent.includes("'") && !stringContent.includes('"')) {
            bestQuote = '"';
          }
          
          // Escape content for chosen quote
          let escapedContent = stringContent;
          if (bestQuote === "'") {
            escapedContent = stringContent.replace(/'/g, "\\'");
          } else {
            escapedContent = stringContent.replace(/"/g, '\\"');
          }
          
          const fixed = `console.${match.match(/\.(log|error|warn|info|debug)/)[1]}(${bestQuote}${escapedContent}${bestQuote}${afterString}`;
          
          if (fixed !== match) {
            modified = true;
            stats.issuesFixed++;
            if (config.verbose) {
              console.log(`${colors.yellow}Fixed string quotes in ${filePath}${colors.reset}`);
            }
          }
          
          return fixed;
        }
      }
    }
    
    return match;
  });

  return { content: fixedContent, modified };
}

// Function to process a single file
async function processFile(filePath) {
  try {
    stats.filesScanned++;
    
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, modified } = fixQuoteMismatches(content, filePath);
    
    if (modified) {
      stats.filesModified++;
      
      if (!config.dryRun) {
        fs.writeFileSync(filePath, fixedContent, 'utf8');
        console.log(`${colors.green}✓${colors.reset} Fixed ${filePath}`);
      } else {
        console.log(`${colors.blue}[DRY RUN]${colors.reset} Would fix ${filePath}`);
      }
    }
  } catch (error) {
    stats.errors.push({ file: filePath, error: error.message });
    console.error(`${colors.red}✗${colors.reset} Error processing ${filePath}: ${error.message}`);
  }
}

// Main function
async function main() {
  console.log(`${colors.bright}Quote Mismatch Fixer${colors.reset}`);
  console.log(`Mode: ${config.dryRun ? colors.blue + 'DRY RUN' : colors.green + 'LIVE'} ${colors.reset}`);
  console.log(`Verbose: ${config.verbose ? 'ON' : 'OFF'}\n`);

  // Build glob pattern
  const pattern = `**/*.{${config.extensions.join(',')}}`;
  const ignorePatterns = config.ignorePaths.map(p => `**/${p}/**`);
  
  try {
    // Find all matching files
    const files = await glob(pattern, {
      ignore: ignorePatterns,
      nodir: true
    });

    console.log(`Found ${files.length} files to scan...\n`);

    // Process each file
    for (const file of files) {
      await processFile(file);
    }

    // Print summary
    console.log(`\n${colors.bright}Summary:${colors.reset}`);
    console.log(`Files scanned: ${stats.filesScanned}`);
    console.log(`Files modified: ${stats.filesModified}`);
    console.log(`Issues fixed: ${stats.issuesFixed}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n${colors.red}Errors (${stats.errors.length}):${colors.reset}`);
      stats.errors.forEach(({ file, error }) => {
        console.log(`  ${file}: ${error}`);
      });
    }

    if (config.dryRun && stats.filesModified > 0) {
      console.log(`\n${colors.yellow}This was a dry run. Run without --dry-run to apply changes.${colors.reset}`);
    }

  } catch (error) {
    console.error(`${colors.red}Fatal error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error(`${colors.red}Unhandled error: ${error}${colors.reset}`);
  process.exit(1);
});