{"button": {"simple_patterns": {"save|submit|create|add|confirm|apply": "primary", "cancel|close|back|dismiss|return": "secondary", "delete|remove|destroy|trash": "danger", "edit|update|modify|change": "outline", "view|details|info|show|expand": "ghost", "success|done|complete|finish": "success", "warning|caution|alert": "warning"}, "className_patterns": {"btn-primary|primary-button": "primary", "btn-secondary|secondary-button": "secondary", "btn-danger|danger-button|delete": "danger", "btn-ghost|ghost-button": "ghost", "btn-outline|outline-button": "outline", "btn-success|success-button": "success", "btn-warning|warning-button": "warning"}, "skip_if_has": ["ref", "dangerouslySetInnerHTML", "style", "as"], "preserve_props": ["onClick", "onMouseEnter", "onMouseLeave", "onMouseDown", "onMouseUp", "onFocus", "onBlur", "disabled", "type", "className", "id", "data-testid", "aria-label", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-pressed", "role", "tabIndex", "title"]}, "input": {"simple_patterns": {"email|mail": "email", "password|pwd": "password", "search|find|query": "search", "number|amount|quantity|count": "number", "date|time|datetime": "date", "url|link|website": "url", "tel|phone|mobile": "tel"}, "skip_if_has": ["ref", "dangerouslySetInnerHTML", "style"], "preserve_props": ["onChange", "onFocus", "onBlur", "onKeyDown", "onKeyUp", "onKeyPress", "value", "defaultValue", "placeholder", "disabled", "readOnly", "required", "name", "id", "className", "type", "autoComplete", "autoFocus", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "min", "max", "step"]}, "select": {"skip_if_has": ["ref", "dangerouslySetInnerHTML", "style"], "preserve_props": ["onChange", "onFocus", "onBlur", "value", "defaultValue", "disabled", "required", "name", "id", "className", "multiple", "size"]}, "textarea": {"skip_if_has": ["ref", "dangerouslySetInnerHTML", "style"], "preserve_props": ["onChange", "onFocus", "onBlur", "onKeyDown", "onKeyUp", "value", "defaultValue", "placeholder", "disabled", "readOnly", "required", "name", "id", "className", "rows", "cols", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "wrap"]}}