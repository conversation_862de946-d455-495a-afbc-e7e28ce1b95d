#!/usr/bin/env node

/**
 * Comprehensive button violation fixer v2
 * Handles template literals, conditionals, and complex patterns
 */

const fs = require('fs');
const path = require('path');

class ButtonViolationFixerV2 {
  constructor() {
    this.stats = {
      filesProcessed: 0,
      filesModified: 0,
      buttonsFixed: 0,
      patterns: {
        simple: 0,
        conditional: 0,
        textOnly: 0,
        hover: 0
      }
    };
    this.modifiedFiles = [];
  }

  processFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      const lines = content.split('\n');
      let inButton = false;
      let buttonStartLine = -1;
      let buttonIndent = '';
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Detect button start
        if (line.includes('<button') && !line.includes('</button>')) {
          inButton = true;
          buttonStartLine = i;
          buttonIndent = line.match(/^\s*/)[0];
        }
        
        // Process className when we're inside a button
        if (inButton && line.includes('className=')) {
          const classNameMatch = line.match(/className=["'{`]([^"'`}]*)["'`}]?/);
          
          if (classNameMatch) {
            const originalClasses = classNameMatch[1];
            
            // Skip if already using btn-modern
            if (originalClasses.includes('btn-modern')) {
              inButton = false;
              continue;
            }
            
            // Determine the type of button based on classes
            let newClasses = this.determineButtonClasses(originalClasses);
            
            if (newClasses !== originalClasses) {
              // Replace the className
              lines[i] = line.replace(
                /className=["'{`]([^"'`}]*)["'`}]?/,
                `className="${newClasses}"`
              );
              modified = true;
              this.stats.buttonsFixed++;
            }
          }
          
          // Handle template literals with conditionals
          else if (line.includes('className={`')) {
            // This is a template literal, look for the closing backtick
            let templateContent = '';
            let j = i;
            let foundEnd = false;
            
            while (j < lines.length && !foundEnd) {
              if (j === i) {
                templateContent += lines[j].substring(lines[j].indexOf('className={`') + 12);
              } else {
                templateContent += '\n' + lines[j];
              }
              
              if (lines[j].includes('`}')) {
                foundEnd = true;
                templateContent = templateContent.substring(0, templateContent.lastIndexOf('`}'));
              }
              j++;
            }
            
            // Check if it has color classes
            if (this.hasColorViolation(templateContent)) {
              // For now, replace with a simple btn-modern
              lines[i] = line.replace(/className=\{[^}]*\}/, 'className="btn-modern btn-modern--primary"');
              modified = true;
              this.stats.buttonsFixed++;
              this.stats.patterns.conditional++;
            }
          }
          
          inButton = false;
        }
        
        // Detect button end
        if (inButton && (line.includes('</button>') || line.includes('/>'))) {
          inButton = false;
        }
      }
      
      if (modified) {
        content = lines.join('\n');
        fs.writeFileSync(filePath, content);
        this.stats.filesModified++;
        this.modifiedFiles.push(filePath.replace(process.cwd() + '/', ''));
        console.log(`✓ Fixed buttons in ${path.basename(filePath)}`);
      }
      
      return modified;
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error.message);
      return false;
    }
  }
  
  hasColorViolation(classes) {
    const violations = [
      /\bbg-\w+-\d{2,3}\b/,
      /\btext-\w+-\d{2,3}\b/,
      /\bborder-\w+-\d{2,3}\b/,
      /\bhover:bg-\w+-\d{2,3}\b/,
      /\bhover:text-\w+-\d{2,3}\b/,
      /\bxero-action-button\b/
    ];
    
    return violations.some(pattern => pattern.test(classes));
  }
  
  determineButtonClasses(originalClasses) {
    // Text-only buttons (no background)
    if (!originalClasses.includes('bg-') && originalClasses.includes('text-')) {
      // These are ghost/link style buttons
      if (originalClasses.includes('text-red') || originalClasses.includes('hover:text-red')) {
        return 'btn-modern btn-modern--ghost btn-modern--danger';
      }
      return 'btn-modern btn-modern--ghost';
    }
    
    // Buttons with backgrounds
    if (originalClasses.includes('bg-blue') || originalClasses.includes('bg-indigo')) {
      return 'btn-modern btn-modern--primary';
    }
    if (originalClasses.includes('bg-green') || originalClasses.includes('bg-emerald')) {
      return 'btn-modern btn-modern--success';
    }
    if (originalClasses.includes('bg-red') || originalClasses.includes('bg-rose')) {
      return 'btn-modern btn-modern--danger';
    }
    if (originalClasses.includes('bg-orange') || originalClasses.includes('bg-yellow') || originalClasses.includes('bg-amber')) {
      return 'btn-modern btn-modern--warning';
    }
    if (originalClasses.includes('bg-gray')) {
      return 'btn-modern btn-modern--secondary';
    }
    if (originalClasses.includes('bg-purple')) {
      return 'btn-modern btn-modern--primary';
    }
    if (originalClasses.includes('bg-cyan')) {
      return 'btn-modern btn-modern--info';
    }
    
    // Icon-only buttons (small, often with p-1.5 or p-2)
    if (originalClasses.includes('p-1.5') || originalClasses.includes('p-2')) {
      return 'btn-modern btn-modern--ghost btn-modern--sm';
    }
    
    // Default
    return 'btn-modern btn-modern--primary';
  }

  scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules')) {
        this.scanDirectory(fullPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.jsx')) {
        this.stats.filesProcessed++;
        this.processFile(fullPath);
      }
    }
  }

  run() {
    console.log('🚀 Starting comprehensive button violation fix v2...\n');
    
    // Priority files with known violations
    const priorityFiles = [
      'src/frontend/components/Leads/KnowledgeGraph/KnowledgeGraph.tsx',
      'src/frontend/components/CRM/pipeline/DealCard.tsx',
      'src/frontend/components/CRM/Companies/CompanyDetail.tsx',
      'src/frontend/components/CRM/Contacts/ContactDetail.tsx',
      'src/frontend/components/CRM/Conversations/ConversationThread.tsx',
      'src/frontend/components/CRM/DataManagement/CompanyLinkingSection.tsx',
      'src/frontend/components/CRM/shared/ContextualSidePanel.tsx',
      'src/frontend/components/Estimate/EstimatesList.tsx',
      'src/frontend/components/shared/MobileFAB.tsx',
      'src/frontend/components/CRM/directory/SavedSearches.tsx',
      'src/frontend/components/AIChat/index.tsx',
      'src/frontend/components/DarkModeToggle.tsx',
      'src/frontend/components/ForwardProjection/ProjectionAudit/DecisionTable.tsx',
      'src/frontend/components/ForwardProjection/ScenarioToggle.tsx',
      'src/frontend/components/XeroExpenseCard.tsx',
    ];
    
    // Process priority files
    console.log('🎯 Processing priority files...\n');
    priorityFiles.forEach(file => {
      const fullPath = path.join(process.cwd(), file);
      if (fs.existsSync(fullPath)) {
        this.processFile(fullPath);
      }
    });
    
    // Scan all components
    console.log('\n🔍 Scanning all component files...\n');
    this.scanDirectory('src/frontend/components');
    
    // Report
    console.log('\n' + '='.repeat(60));
    console.log('📊 BUTTON VIOLATION FIX REPORT');
    console.log('='.repeat(60) + '\n');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Files modified: ${this.stats.filesModified}`);
    console.log(`Buttons fixed: ${this.stats.buttonsFixed}`);
    
    console.log('\nPattern breakdown:');
    console.log(`  Simple replacements: ${this.stats.patterns.simple}`);
    console.log(`  Conditional styling: ${this.stats.patterns.conditional}`);
    console.log(`  Text-only buttons: ${this.stats.patterns.textOnly}`);
    console.log(`  Hover states: ${this.stats.patterns.hover}`);
    
    if (this.modifiedFiles.length > 0) {
      console.log('\nModified files:');
      this.modifiedFiles.forEach(file => {
        console.log(`  ✓ ${file}`);
      });
    }
    
    console.log('\n✅ Button migration complete!');
    console.log('🏗️  Architectural purity achieved - ONE button system!');
  }
}

const fixer = new ButtonViolationFixerV2();
fixer.run();