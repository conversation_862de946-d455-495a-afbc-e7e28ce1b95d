#!/usr/bin/env node

/**
 * Input Migration Codemod
 * 
 * Automatically migrates raw <input> elements to Input components
 * Focuses on simple, high-confidence cases first
 * 
 * Usage:
 *   node scripts/migrate-inputs.js [--dry-run] [--file path/to/file.tsx]
 *   node scripts/migrate-inputs.js --dry-run  # Preview changes
 *   node scripts/migrate-inputs.js            # Apply changes
 */

const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');
const glob = require('glob');

// Load migration configuration
const configPath = path.join(__dirname, 'migration-config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
const MIGRATION_CONFIG = config.input;

// Parse arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const specificFile = args.find(arg => arg.startsWith('--file='))?.split('=')[1];

// Migration statistics
let stats = {
  filesProcessed: 0,
  inputsFound: 0,
  inputsMigrated: 0,
  inputsSkipped: 0,
  errors: []
};

// Migration log
const migrationLog = {
  timestamp: new Date().toISOString(),
  isDryRun,
  migrations: [],
  skipped: [],
  errors: []
};

/**
 * Extract type from various patterns
 */
function extractInputType(node) {
  const attributes = node.openingElement.attributes || [];
  const typeAttr = attributes.find(attr => 
    t.isJSXAttribute(attr) && attr.name.name === 'type'
  );
  
  if (typeAttr) {
    if (t.isStringLiteral(typeAttr.value)) {
      return typeAttr.value.value;
    } else if (t.isJSXExpressionContainer(typeAttr.value) && t.isStringLiteral(typeAttr.value.expression)) {
      return typeAttr.value.expression.value;
    }
  }
  
  // Default to text if no type specified
  return 'text';
}

/**
 * Determine variant based on type and other attributes
 */
function determineVariant(type, attributes) {
  // Input component doesn't typically use variants like Button
  // But we might want to preserve certain styles
  return null;
}

/**
 * Check if input should be skipped
 */
function shouldSkipInput(node) {
  const attributes = node.openingElement.attributes || [];
  
  // Check for skip conditions
  for (const attr of attributes) {
    if (t.isJSXAttribute(attr)) {
      const propName = attr.name.name;
      if (MIGRATION_CONFIG.skip_if_has.includes(propName)) {
        return true;
      }
    }
  }
  
  // Skip hidden inputs
  const typeAttr = attributes.find(attr => 
    t.isJSXAttribute(attr) && attr.name.name === 'type'
  );
  if (typeAttr && t.isStringLiteral(typeAttr.value) && typeAttr.value.value === 'hidden') {
    return true;
  }
  
  // Skip checkboxes and radios (they might need different components)
  const type = extractInputType(node);
  if (['checkbox', 'radio', 'file', 'range', 'color'].includes(type)) {
    return true;
  }
  
  return false;
}

/**
 * Filter attributes to only preserve allowed ones
 */
function filterAttributes(attributes) {
  return attributes.filter(attr => {
    if (!t.isJSXAttribute(attr)) return true; // Keep spread attributes
    const propName = attr.name.name;
    return MIGRATION_CONFIG.preserve_props.includes(propName);
  });
}

/**
 * Transform input JSX element to Input component
 */
function transformInput(node, filePath) {
  if (shouldSkipInput(node)) {
    stats.inputsSkipped++;
    const type = extractInputType(node);
    migrationLog.skipped.push({
      file: filePath,
      reason: `Type '${type}' or complex props detected`,
      line: node.loc?.start.line
    });
    return false;
  }
  
  const type = extractInputType(node);
  
  // Transform to Input component
  node.openingElement.name.name = 'Input';
  if (node.closingElement) {
    node.closingElement.name.name = 'Input';
  }
  
  // Filter attributes to only keep the ones we want
  node.openingElement.attributes = filterAttributes(node.openingElement.attributes);
  
  stats.inputsMigrated++;
  migrationLog.migrations.push({
    file: filePath,
    line: node.loc?.start.line,
    type: type
  });
  
  return true;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const code = fs.readFileSync(filePath, 'utf8');
    
    // Parse the file
    const ast = parser.parse(code, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript'],
      errorRecovery: true
    });
    
    let hasInputs = false;
    let needsImport = false;
    let hasExistingImport = false;
    let inputImportIndex = -1;
    
    // First pass: check for existing Input import
    traverse(ast, {
      ImportDeclaration(path) {
        const source = path.node.source.value;
        if (source.includes('components/ui')) {
          const specifiers = path.node.specifiers;
          hasExistingImport = specifiers.some(spec => 
            t.isImportSpecifier(spec) && spec.imported.name === 'Input'
          );
          if (hasExistingImport) {
            inputImportIndex = path.parent.body.indexOf(path.node);
          }
        }
      }
    });
    
    // Second pass: transform inputs
    traverse(ast, {
      JSXElement(path) {
        const node = path.node;
        if (t.isJSXIdentifier(node.openingElement.name) && 
            node.openingElement.name.name === 'input') {
          hasInputs = true;
          stats.inputsFound++;
          
          if (transformInput(node, filePath)) {
            needsImport = true;
          }
        }
      }
    });
    
    // Add import if needed
    if (needsImport && !hasExistingImport) {
      const importDeclaration = t.importDeclaration(
        [t.importSpecifier(t.identifier('Input'), t.identifier('Input'))],
        t.stringLiteral('@/frontend/components/ui/Input')
      );
      
      // Find the last import statement
      let lastImportIndex = -1;
      ast.program.body.forEach((node, index) => {
        if (t.isImportDeclaration(node)) {
          lastImportIndex = index;
        }
      });
      
      // Insert after last import
      if (lastImportIndex >= 0) {
        ast.program.body.splice(lastImportIndex + 1, 0, importDeclaration);
      } else {
        ast.program.body.unshift(importDeclaration);
      }
    }
    
    // Generate code if changes were made
    if (needsImport || hasInputs) {
      const output = generate(ast, {
        retainLines: true,
        compact: false
      }, code);
      
      if (!isDryRun) {
        fs.writeFileSync(filePath, output.code);
      }
      
      stats.filesProcessed++;
      return true;
    }
    
    return false;
  } catch (error) {
    stats.errors.push({ file: filePath, error: error.message });
    migrationLog.errors.push({
      file: filePath,
      error: error.message,
      stack: error.stack
    });
    return false;
  }
}

/**
 * Find all TypeScript/TSX files
 */
function findFiles() {
  if (specificFile) {
    return [specificFile];
  }
  
  return glob.sync('src/frontend/**/*.{ts,tsx}', {
    ignore: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/*.test.{ts,tsx}',
      '**/*.spec.{ts,tsx}'
    ]
  });
}

/**
 * Main migration function
 */
function migrate() {
  console.log(`\n🔧 Input Migration Codemod ${isDryRun ? '(DRY RUN)' : ''}\n`);
  
  const files = findFiles();
  console.log(`Found ${files.length} files to process\n`);
  
  // Process files
  files.forEach((file, index) => {
    if (index % 10 === 0) {
      process.stdout.write(`Processing... ${index}/${files.length}\r`);
    }
    processFile(file);
  });
  
  // Save migration log
  const logPath = `migration-log-inputs-${Date.now()}.json`;
  fs.writeFileSync(logPath, JSON.stringify(migrationLog, null, 2));
  
  // Print summary
  console.log('\n\n📊 Migration Summary\n');
  console.log(`Files processed: ${stats.filesProcessed}`);
  console.log(`Inputs found: ${stats.inputsFound}`);
  console.log(`Inputs migrated: ${stats.inputsMigrated}`);
  console.log(`Inputs skipped: ${stats.inputsSkipped}`);
  console.log(`Errors: ${stats.errors.length}`);
  
  if (stats.inputsMigrated > 0) {
    console.log('\n✅ Sample migrations:');
    migrationLog.migrations.slice(0, 5).forEach(m => {
      console.log(`  - ${path.basename(m.file)}:${m.line} type="${m.type}"`);
    });
    if (migrationLog.migrations.length > 5) {
      console.log(`  ... and ${migrationLog.migrations.length - 5} more`);
    }
  }
  
  if (stats.inputsSkipped > 0) {
    console.log('\n⚠️  Skipped inputs (need manual review):');
    migrationLog.skipped.slice(0, 5).forEach(s => {
      console.log(`  - ${path.basename(s.file)}:${s.line} - ${s.reason}`);
    });
    if (migrationLog.skipped.length > 5) {
      console.log(`  ... and ${migrationLog.skipped.length - 5} more`);
    }
  }
  
  if (stats.errors.length > 0) {
    console.log('\n❌ Errors:');
    stats.errors.forEach(e => {
      console.log(`  - ${e.file}: ${e.error}`);
    });
  }
  
  console.log(`\n📄 Full log saved to: ${logPath}`);
  
  if (isDryRun) {
    console.log('\n⚡ This was a dry run. No files were modified.');
    console.log('Run without --dry-run to apply changes.\n');
  }
}

// Run migration
migrate();