#!/usr/bin/env node

/**
 * Count ACTUAL button elements in the codebase
 * This script only counts <button> elements and button-like components
 */

const fs = require('fs');
const path = require('path');

class ButtonCounter {
  constructor() {
    this.stats = {
      totalButtons: 0,
      btnModernButtons: 0,
      violationButtons: 0,
      fileCount: 0,
      filesWithButtons: 0,
      buttonsByFile: {},
      violationTypes: {
        inlineTailwind: 0,
        extendedTheme: 0,
        customClass: 0,
        noStyling: 0
      }
    };
  }

  isButtonElement(line) {
    // Match <button> elements or Button components
    return /<button|<Button/.test(line);
  }

  extractClassName(content, startIndex) {
    // Find className prop after button element
    const afterButton = content.substring(startIndex);
    const classMatch = afterButton.match(/className\s*=\s*{?["'`]([^"'`}]+)["'`]?}?/);
    return classMatch ? classMatch[1] : null;
  }

  analyzeButton(className) {
    if (!className) {
      this.stats.violationTypes.noStyling++;
      return 'no-styling';
    }

    if (className.includes('btn-modern')) {
      this.stats.btnModernButtons++;
      return 'btn-modern';
    }

    // Check for violations
    if (/bg-(blue|red|green|yellow|orange|gray|indigo|purple)-\d{3}/.test(className)) {
      this.stats.violationTypes.inlineTailwind++;
      this.stats.violationButtons++;
      return 'inline-tailwind';
    }

    if (/bg-(primary|secondary|success|warning|error)-\d{3}/.test(className)) {
      this.stats.violationTypes.extendedTheme++;
      this.stats.violationButtons++;
      return 'extended-theme';
    }

    if (/xero-action-button|custom-button/.test(className)) {
      this.stats.violationTypes.customClass++;
      this.stats.violationButtons++;
      return 'custom-class';
    }

    // If it has other classes but no violations
    return 'other';
  }

  processFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    let fileButtons = 0;
    let fileViolations = 0;

    lines.forEach((line, index) => {
      if (this.isButtonElement(line)) {
        fileButtons++;
        this.stats.totalButtons++;

        // Try to find className
        const remainingContent = lines.slice(index).join('\n');
        const className = this.extractClassName(remainingContent, 0);
        const buttonType = this.analyzeButton(className);

        if (buttonType !== 'btn-modern' && buttonType !== 'other' && buttonType !== 'no-styling') {
          fileViolations++;
        }
      }
    });

    if (fileButtons > 0) {
      this.stats.filesWithButtons++;
      this.stats.buttonsByFile[filePath.replace(process.cwd() + '/', '')] = {
        total: fileButtons,
        violations: fileViolations
      };
    }
  }

  scanDirectory(dir) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
        this.scanDirectory(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.jsx'))) {
        this.stats.fileCount++;
        this.processFile(fullPath);
      }
    }
  }

  printReport() {
    console.log('\n🔍 ACTUAL Button Count Analysis\n');
    console.log('📊 Summary:');
    console.log(`  Total files scanned: ${this.stats.fileCount}`);
    console.log(`  Files with buttons: ${this.stats.filesWithButtons}`);
    console.log(`  Total button elements: ${this.stats.totalButtons}`);
    console.log(`  Buttons using btn-modern: ${this.stats.btnModernButtons}`);
    console.log(`  Buttons with violations: ${this.stats.violationButtons}`);
    console.log(`  Compliance rate: ${((this.stats.btnModernButtons / this.stats.totalButtons) * 100).toFixed(1)}%`);

    console.log('\n📈 Violation Breakdown:');
    console.log(`  Inline Tailwind colors: ${this.stats.violationTypes.inlineTailwind}`);
    console.log(`  Extended theme colors: ${this.stats.violationTypes.extendedTheme}`);
    console.log(`  Custom button classes: ${this.stats.violationTypes.customClass}`);
    console.log(`  No styling: ${this.stats.violationTypes.noStyling}`);

    console.log('\n📁 Top files with most button violations:');
    const sortedFiles = Object.entries(this.stats.buttonsByFile)
      .filter(([_, data]) => data.violations > 0)
      .sort((a, b) => b[1].violations - a[1].violations)
      .slice(0, 10);

    sortedFiles.forEach(([file, data]) => {
      console.log(`  ${file}: ${data.violations}/${data.total} buttons`);
    });

    // Write detailed report
    const report = {
      summary: {
        totalButtons: this.stats.totalButtons,
        btnModernButtons: this.stats.btnModernButtons,
        violationButtons: this.stats.violationButtons,
        complianceRate: ((this.stats.btnModernButtons / this.stats.totalButtons) * 100).toFixed(1) + '%'
      },
      violations: this.stats.violationTypes,
      fileDetails: this.stats.buttonsByFile
    };

    fs.writeFileSync('button-count-report.json', JSON.stringify(report, null, 2));
    console.log('\n✅ Detailed report saved to button-count-report.json');
  }

  run() {
    console.log('🚀 Starting ACTUAL button count analysis...\n');
    this.scanDirectory('src/frontend');
    this.printReport();
  }
}

// Run the analysis
const counter = new ButtonCounter();
counter.run();