#!/usr/bin/env node

/**
 * Textarea Migration Codemod
 * 
 * Automatically migrates raw <textarea> elements to Textarea components
 * Focuses on simple, high-confidence cases first
 * 
 * Usage:
 *   node scripts/migrate-textareas.js [--dry-run] [--file path/to/file.tsx]
 *   node scripts/migrate-textareas.js --dry-run  # Preview changes
 *   node scripts/migrate-textareas.js            # Apply changes
 */

const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');
const glob = require('glob');

// Load migration configuration
const configPath = path.join(__dirname, 'migration-config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
const MIGRATION_CONFIG = config.textarea;

// Parse arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const specificFile = args.find(arg => arg.startsWith('--file='))?.split('=')[1];

// Migration statistics
let stats = {
  filesProcessed: 0,
  textareasFound: 0,
  textareasMigrated: 0,
  textareasSkipped: 0,
  errors: []
};

// Migration log
const migrationLog = {
  timestamp: new Date().toISOString(),
  isDryRun,
  migrations: [],
  skipped: [],
  errors: []
};

/**
 * Check if textarea should be skipped
 */
function shouldSkipTextarea(node) {
  const attributes = node.openingElement.attributes || [];
  
  // Check for skip conditions
  for (const attr of attributes) {
    if (t.isJSXAttribute(attr)) {
      const propName = attr.name.name;
      if (MIGRATION_CONFIG.skip_if_has.includes(propName)) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Filter attributes to only preserve allowed ones
 */
function filterAttributes(attributes) {
  return attributes.filter(attr => {
    if (!t.isJSXAttribute(attr)) return true; // Keep spread attributes
    const propName = attr.name.name;
    return MIGRATION_CONFIG.preserve_props.includes(propName);
  });
}

/**
 * Transform textarea JSX element to Textarea component
 */
function transformTextarea(node, filePath) {
  if (shouldSkipTextarea(node)) {
    stats.textareasSkipped++;
    migrationLog.skipped.push({
      file: filePath,
      reason: 'Complex props detected',
      line: node.loc?.start.line
    });
    return false;
  }
  
  // Transform to Textarea component
  node.openingElement.name.name = 'Textarea';
  if (node.closingElement) {
    node.closingElement.name.name = 'Textarea';
  }
  
  // Filter attributes to only keep the ones we want
  node.openingElement.attributes = filterAttributes(node.openingElement.attributes);
  
  stats.textareasMigrated++;
  migrationLog.migrations.push({
    file: filePath,
    line: node.loc?.start.line
  });
  
  return true;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const code = fs.readFileSync(filePath, 'utf8');
    
    // Parse the file
    const ast = parser.parse(code, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript'],
      errorRecovery: true
    });
    
    let hasTextareas = false;
    let needsImport = false;
    let hasExistingImport = false;
    let textareaImportIndex = -1;
    
    // First pass: check for existing Textarea import
    traverse(ast, {
      ImportDeclaration(path) {
        const source = path.node.source.value;
        if (source.includes('components/ui')) {
          const specifiers = path.node.specifiers;
          hasExistingImport = specifiers.some(spec => 
            t.isImportSpecifier(spec) && spec.imported.name === 'Textarea'
          );
          if (hasExistingImport) {
            textareaImportIndex = path.parent.body.indexOf(path.node);
          }
        }
      }
    });
    
    // Second pass: transform textareas
    traverse(ast, {
      JSXElement(path) {
        const node = path.node;
        if (t.isJSXIdentifier(node.openingElement.name) && 
            node.openingElement.name.name === 'textarea') {
          hasTextareas = true;
          stats.textareasFound++;
          
          if (transformTextarea(node, filePath)) {
            needsImport = true;
          }
        }
      }
    });
    
    // Add import if needed
    if (needsImport && !hasExistingImport) {
      const importDeclaration = t.importDeclaration(
        [t.importSpecifier(t.identifier('Textarea'), t.identifier('Textarea'))],
        t.stringLiteral('@/frontend/components/ui/Textarea')
      );
      
      // Find the last import statement
      let lastImportIndex = -1;
      ast.program.body.forEach((node, index) => {
        if (t.isImportDeclaration(node)) {
          lastImportIndex = index;
        }
      });
      
      // Insert after last import
      if (lastImportIndex >= 0) {
        ast.program.body.splice(lastImportIndex + 1, 0, importDeclaration);
      } else {
        ast.program.body.unshift(importDeclaration);
      }
    }
    
    // Generate code if changes were made
    if (needsImport || hasTextareas) {
      const output = generate(ast, {
        retainLines: true,
        compact: false
      }, code);
      
      if (!isDryRun) {
        fs.writeFileSync(filePath, output.code);
      }
      
      stats.filesProcessed++;
      return true;
    }
    
    return false;
  } catch (error) {
    stats.errors.push({ file: filePath, error: error.message });
    migrationLog.errors.push({
      file: filePath,
      error: error.message,
      stack: error.stack
    });
    return false;
  }
}

/**
 * Find all TypeScript/TSX files
 */
function findFiles() {
  if (specificFile) {
    return [specificFile];
  }
  
  return glob.sync('src/frontend/**/*.{ts,tsx}', {
    ignore: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/*.test.{ts,tsx}',
      '**/*.spec.{ts,tsx}'
    ]
  });
}

/**
 * Main migration function
 */
function migrate() {
  console.log(`\n🔧 Textarea Migration Codemod ${isDryRun ? '(DRY RUN)' : ''}\n`);
  
  const files = findFiles();
  console.log(`Found ${files.length} files to process\n`);
  
  // Process files
  files.forEach((file, index) => {
    if (index % 10 === 0) {
      process.stdout.write(`Processing... ${index}/${files.length}\r`);
    }
    processFile(file);
  });
  
  // Save migration log
  const logPath = `migration-log-textareas-${Date.now()}.json`;
  fs.writeFileSync(logPath, JSON.stringify(migrationLog, null, 2));
  
  // Print summary
  console.log('\n\n📊 Migration Summary\n');
  console.log(`Files processed: ${stats.filesProcessed}`);
  console.log(`Textareas found: ${stats.textareasFound}`);
  console.log(`Textareas migrated: ${stats.textareasMigrated}`);
  console.log(`Textareas skipped: ${stats.textareasSkipped}`);
  console.log(`Errors: ${stats.errors.length}`);
  
  if (stats.textareasMigrated > 0) {
    console.log('\n✅ Sample migrations:');
    migrationLog.migrations.slice(0, 5).forEach(m => {
      console.log(`  - ${path.basename(m.file)}:${m.line}`);
    });
    if (migrationLog.migrations.length > 5) {
      console.log(`  ... and ${migrationLog.migrations.length - 5} more`);
    }
  }
  
  if (stats.textareasSkipped > 0) {
    console.log('\n⚠️  Skipped textareas (need manual review):');
    migrationLog.skipped.slice(0, 5).forEach(s => {
      console.log(`  - ${path.basename(s.file)}:${s.line} - ${s.reason}`);
    });
    if (migrationLog.skipped.length > 5) {
      console.log(`  ... and ${migrationLog.skipped.length - 5} more`);
    }
  }
  
  if (stats.errors.length > 0) {
    console.log('\n❌ Errors:');
    stats.errors.forEach(e => {
      console.log(`  - ${e.file}: ${e.error}`);
    });
  }
  
  console.log(`\n📄 Full log saved to: ${logPath}`);
  
  if (isDryRun) {
    console.log('\n⚡ This was a dry run. No files were modified.');
    console.log('Run without --dry-run to apply changes.\n');
  }
}

// Run migration
migrate();