#!/usr/bin/env node

/**
 * Button Migration Codemod
 * 
 * Automatically migrates raw <button> elements to Button components
 * Focuses on simple, high-confidence cases first
 * 
 * Usage:
 *   node scripts/migrate-buttons.js [--dry-run] [--file path/to/file.tsx]
 *   node scripts/migrate-buttons.js --dry-run  # Preview changes
 *   node scripts/migrate-buttons.js            # Apply changes
 */

const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');
const glob = require('glob');

// Migration configuration
const MIGRATION_CONFIG = {
  button: {
    // Text patterns to variant mapping
    simple_patterns: {
      'save|submit|create|add|confirm': 'primary',
      'cancel|close|back|dismiss': 'secondary', 
      'delete|remove|destroy': 'danger',
      'edit|update|modify': 'outline',
      'view|details|info': 'ghost',
      'success|done|complete': 'success',
      'warning|caution': 'warning'
    },
    // Skip migration if these props are present (complex cases)
    skip_if_has: ['ref', 'dangerouslySetInnerHTML', 'style'],
    // Props to preserve during migration
    preserve_props: ['onClick', 'onMouseEnter', 'onMouseLeave', 'disabled', 'type', 'className', 'id', 'data-testid']
  }
};

// Parse arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const specificFile = args.find(arg => arg.startsWith('--file='))?.split('=')[1];

// Migration statistics
let stats = {
  filesProcessed: 0,
  buttonsFound: 0,
  buttonsMigrated: 0,
  buttonsSkipped: 0,
  errors: []
};

// Migration log
const migrationLog = {
  timestamp: new Date().toISOString(),
  isDryRun,
  migrations: [],
  skipped: [],
  errors: []
};

/**
 * Determine button variant based on text content or className
 */
function determineVariant(buttonText, className = '') {
  const text = buttonText.toLowerCase();
  const classLower = className.toLowerCase();
  
  // Check text patterns
  for (const [pattern, variant] of Object.entries(MIGRATION_CONFIG.button.simple_patterns)) {
    const regex = new RegExp(pattern);
    if (regex.test(text) || regex.test(classLower)) {
      return variant;
    }
  }
  
  // Check className patterns
  if (classLower.includes('danger') || classLower.includes('error')) return 'danger';
  if (classLower.includes('primary')) return 'primary';
  if (classLower.includes('secondary')) return 'secondary';
  if (classLower.includes('ghost')) return 'ghost';
  if (classLower.includes('outline')) return 'outline';
  
  // Default to primary for submit buttons, secondary for others
  return 'secondary';
}

/**
 * Extract text content from JSX element
 */
function extractButtonText(node) {
  let text = '';
  
  if (node.children) {
    node.children.forEach(child => {
      if (t.isJSXText(child)) {
        text += child.value.trim();
      } else if (t.isJSXExpressionContainer(child) && t.isStringLiteral(child.expression)) {
        text += child.expression.value;
      }
    });
  }
  
  return text;
}

/**
 * Check if button should be skipped
 */
function shouldSkipButton(node) {
  const attributes = node.openingElement.attributes || [];
  
  // Check for skip conditions
  for (const attr of attributes) {
    if (t.isJSXAttribute(attr)) {
      const propName = attr.name.name;
      if (MIGRATION_CONFIG.button.skip_if_has.includes(propName)) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Extract variant from btn-modern classes
 */
function extractVariantFromBtnModern(className) {
  const variantMap = {
    'btn-modern--primary': 'primary',
    'btn-modern--secondary': 'secondary',
    'btn-modern--danger': 'danger',
    'btn-modern--success': 'success',
    'btn-modern--warning': 'warning',
    'btn-modern--ghost': 'ghost',
    'btn-modern--outline': 'outline',
    'btn-modern--harvest': 'harvest',
    'btn-modern--xero': 'xero'
  };
  
  for (const [btnClass, variant] of Object.entries(variantMap)) {
    if (className.includes(btnClass)) {
      return variant;
    }
  }
  
  return null;
}

/**
 * Transform button JSX element to Button component
 */
function transformButton(node, filePath) {
  if (shouldSkipButton(node)) {
    stats.buttonsSkipped++;
    migrationLog.skipped.push({
      file: filePath,
      reason: 'Complex props detected',
      line: node.loc?.start.line
    });
    return false;
  }
  
  // Extract button text and className
  const buttonText = extractButtonText(node);
  let className = '';
  
  // Find className attribute
  const attributes = node.openingElement.attributes || [];
  const classAttr = attributes.find(attr => 
    t.isJSXAttribute(attr) && attr.name.name === 'className'
  );
  
  if (classAttr && t.isStringLiteral(classAttr.value)) {
    className = classAttr.value.value;
  }
  
  // Check if this is a btn-modern button
  let variant = null;
  let isBtnModern = false;
  
  if (className.includes('btn-modern')) {
    isBtnModern = true;
    variant = extractVariantFromBtnModern(className);
  }
  
  // If no variant from btn-modern, determine from text/class
  if (!variant) {
    variant = determineVariant(buttonText, className);
  }
  
  // Transform to Button component
  node.openingElement.name.name = 'Button';
  if (node.closingElement) {
    node.closingElement.name.name = 'Button';
  }
  
  // Add variant prop
  const variantAttr = t.jsxAttribute(
    t.jsxIdentifier('variant'),
    t.stringLiteral(variant)
  );
  
  // Insert variant as first attribute
  node.openingElement.attributes.unshift(variantAttr);
  
  // Clean up className if it was btn-modern
  if (isBtnModern && classAttr) {
    // Remove btn-modern classes
    const cleanedClasses = className
      .split(' ')
      .filter(cls => !cls.startsWith('btn-modern'))
      .join(' ')
      .trim();
    
    if (cleanedClasses) {
      // Update className with remaining classes
      classAttr.value = t.stringLiteral(cleanedClasses);
    } else {
      // Remove className attribute entirely if empty
      const attrIndex = node.openingElement.attributes.indexOf(classAttr);
      if (attrIndex >= 0) {
        node.openingElement.attributes.splice(attrIndex, 1);
      }
    }
  }
  
  stats.buttonsMigrated++;
  migrationLog.migrations.push({
    file: filePath,
    line: node.loc?.start.line,
    text: buttonText,
    variant: variant,
    className: className,
    wasBtnModern: isBtnModern
  });
  
  return true;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const code = fs.readFileSync(filePath, 'utf8');
    
    // Parse the file
    const ast = parser.parse(code, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript'],
      errorRecovery: true
    });
    
    let hasButtons = false;
    let needsImport = false;
    let hasExistingImport = false;
    let buttonImportIndex = -1;
    
    // First pass: check for existing Button import
    traverse(ast, {
      ImportDeclaration(path) {
        const source = path.node.source.value;
        if (source.includes('components/ui')) {
          const specifiers = path.node.specifiers;
          hasExistingImport = specifiers.some(spec => 
            t.isImportSpecifier(spec) && spec.imported.name === 'Button'
          );
          if (hasExistingImport) {
            buttonImportIndex = path.parent.body.indexOf(path.node);
          }
        }
      }
    });
    
    // Second pass: transform buttons
    traverse(ast, {
      JSXElement(path) {
        const node = path.node;
        if (t.isJSXIdentifier(node.openingElement.name) && 
            node.openingElement.name.name === 'button') {
          hasButtons = true;
          stats.buttonsFound++;
          
          if (transformButton(node, filePath)) {
            needsImport = true;
          }
        }
      }
    });
    
    // Add import if needed
    if (needsImport && !hasExistingImport) {
      const importDeclaration = t.importDeclaration(
        [t.importSpecifier(t.identifier('Button'), t.identifier('Button'))],
        t.stringLiteral('@/frontend/components/ui/Button')
      );
      
      // Find the last import statement
      let lastImportIndex = -1;
      ast.program.body.forEach((node, index) => {
        if (t.isImportDeclaration(node)) {
          lastImportIndex = index;
        }
      });
      
      // Insert after last import
      if (lastImportIndex >= 0) {
        ast.program.body.splice(lastImportIndex + 1, 0, importDeclaration);
      } else {
        ast.program.body.unshift(importDeclaration);
      }
    }
    
    // Generate code if changes were made
    if (needsImport || hasButtons) {
      const output = generate(ast, {
        retainLines: true,
        compact: false
      }, code);
      
      if (!isDryRun) {
        fs.writeFileSync(filePath, output.code);
      }
      
      stats.filesProcessed++;
      return true;
    }
    
    return false;
  } catch (error) {
    stats.errors.push({ file: filePath, error: error.message });
    migrationLog.errors.push({
      file: filePath,
      error: error.message,
      stack: error.stack
    });
    return false;
  }
}

/**
 * Find all TypeScript/TSX files
 */
function findFiles() {
  if (specificFile) {
    return [specificFile];
  }
  
  return glob.sync('src/frontend/**/*.{ts,tsx}', {
    ignore: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/*.test.{ts,tsx}',
      '**/*.spec.{ts,tsx}'
    ]
  });
}

/**
 * Main migration function
 */
function migrate() {
  console.log(`\n🔧 Button Migration Codemod ${isDryRun ? '(DRY RUN)' : ''}\n`);
  
  const files = findFiles();
  console.log(`Found ${files.length} files to process\n`);
  
  // Process files
  files.forEach((file, index) => {
    if (index % 10 === 0) {
      process.stdout.write(`Processing... ${index}/${files.length}\r`);
    }
    processFile(file);
  });
  
  // Save migration log
  const logPath = `migration-log-${Date.now()}.json`;
  fs.writeFileSync(logPath, JSON.stringify(migrationLog, null, 2));
  
  // Print summary
  console.log('\n\n📊 Migration Summary\n');
  console.log(`Files processed: ${stats.filesProcessed}`);
  console.log(`Buttons found: ${stats.buttonsFound}`);
  console.log(`Buttons migrated: ${stats.buttonsMigrated}`);
  console.log(`Buttons skipped: ${stats.buttonsSkipped}`);
  console.log(`Errors: ${stats.errors.length}`);
  
  if (stats.buttonsMigrated > 0) {
    console.log('\n✅ Sample migrations:');
    migrationLog.migrations.slice(0, 5).forEach(m => {
      console.log(`  - ${path.basename(m.file)}:${m.line} "${m.text}" → variant="${m.variant}"`);
    });
    if (migrationLog.migrations.length > 5) {
      console.log(`  ... and ${migrationLog.migrations.length - 5} more`);
    }
  }
  
  if (stats.buttonsSkipped > 0) {
    console.log('\n⚠️  Skipped buttons (need manual review):');
    migrationLog.skipped.slice(0, 5).forEach(s => {
      console.log(`  - ${path.basename(s.file)}:${s.line} - ${s.reason}`);
    });
    if (migrationLog.skipped.length > 5) {
      console.log(`  ... and ${migrationLog.skipped.length - 5} more`);
    }
  }
  
  if (stats.errors.length > 0) {
    console.log('\n❌ Errors:');
    stats.errors.forEach(e => {
      console.log(`  - ${e.file}: ${e.error}`);
    });
  }
  
  console.log(`\n📄 Full log saved to: ${logPath}`);
  
  if (isDryRun) {
    console.log('\n⚡ This was a dry run. No files were modified.');
    console.log('Run without --dry-run to apply changes.\n');
  }
}

// Run migration
migrate();