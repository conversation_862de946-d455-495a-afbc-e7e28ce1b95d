#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// ANSI color codes for output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[36m',
  red: '\x1b[31m'
};

// Configuration
const config = {
  extensions: ['ts', 'tsx', 'js', 'jsx'],
  ignorePaths: ['node_modules', 'dist', '.next', 'build', 'coverage'],
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  useRegex: process.argv.includes('--regex') // Fallback to regex mode
};

// Statistics
let stats = {
  filesScanned: 0,
  filesModified: 0,
  issuesFixed: 0,
  errors: []
};

// Parser options for different file types
function getParserOptions(filePath) {
  const ext = path.extname(filePath);
  const isTypeScript = ext === '.ts' || ext === '.tsx';
  const isJSX = ext === '.jsx' || ext === '.tsx';

  return {
    sourceType: 'module',
    plugins: [
      'decorators-legacy',
      'dynamicImport',
      'classProperties',
      'classPrivateProperties',
      'classPrivateMethods',
      'exportDefaultFrom',
      'exportNamespaceFrom',
      'nullishCoalescingOperator',
      'optionalChaining',
      'optionalCatchBinding',
      ...(isTypeScript ? ['typescript'] : []),
      ...(isJSX ? ['jsx'] : [])
    ]
  };
}

// Check if a string literal has mismatched quotes in its raw value
function hasMismatchedQuotes(node) {
  if (!t.isStringLiteral(node)) return false;
  
  const raw = node.extra?.raw || '';
  if (!raw) return false;
  
  // Check if it's a pattern like "text: ',
  const openQuote = raw[0];
  const lastChars = raw.slice(-2);
  
  // Common mismatch patterns
  if (openQuote === '"' && lastChars === "',") return true;
  if (openQuote === "'" && lastChars === '",') return true;
  
  // Check for unclosed quotes
  const content = raw.slice(1, -1);
  const hasUnescapedQuote = openQuote === '"' 
    ? content.match(/(?<!\\)'$/) 
    : content.match(/(?<!\\)"$/);
    
  return hasUnescapedQuote !== null;
}

// Fix quote mismatches using AST
function fixQuoteMismatchesAST(content, filePath) {
  let modified = false;
  let ast;

  try {
    // Parse the file
    ast = parser.parse(content, getParserOptions(filePath));
  } catch (parseError) {
    // If AST parsing fails, fall back to regex method
    if (config.verbose) {
      console.log(`${colors.yellow}AST parsing failed for ${filePath}, using regex fallback${colors.reset}`);
    }
    return fixQuoteMismatchesRegex(content, filePath);
  }

  // Traverse the AST
  traverse(ast, {
    CallExpression(path) {
      const node = path.node;
      
      // Check if it's a console method call
      if (
        t.isMemberExpression(node.callee) &&
        t.isIdentifier(node.callee.object, { name: 'console' }) &&
        ['log', 'error', 'warn', 'info', 'debug'].includes(node.callee.property.name)
      ) {
        // Check the first argument
        if (node.arguments.length > 0 && t.isStringLiteral(node.arguments[0])) {
          const stringNode = node.arguments[0];
          const value = stringNode.value;
          
          // Check if the string ends with ": " (common pattern)
          if (value.endsWith(': ')) {
            // Check the raw value for quote mismatches
            if (hasMismatchedQuotes(stringNode)) {
              // Fix the string
              const fixedValue = value.substring(0, value.length - 2); // Remove ": "
              stringNode.value = fixedValue + ': ';
              
              // Determine the best quote to use
              let quote = "'";
              if (fixedValue.includes("'") && !fixedValue.includes('"')) {
                quote = '"';
              }
              
              // Update the raw value
              if (stringNode.extra) {
                stringNode.extra.raw = quote + stringNode.value.replace(new RegExp(quote, 'g'), '\\' + quote) + quote;
                stringNode.extra.rawValue = stringNode.value;
              }
              
              modified = true;
              stats.issuesFixed++;
              
              if (config.verbose) {
                console.log(`${colors.yellow}Fixed console.${node.callee.property.name} in ${filePath}${colors.reset}`);
              }
            }
          }
        }
      }
    }
  });

  if (modified) {
    // Generate the fixed code
    const output = generate(ast, {
      retainLines: true,
      retainFunctionParens: true,
      comments: true
    }, content);
    
    return { content: output.code, modified: true };
  }

  return { content, modified: false };
}

// Regex-based fallback for fixing quote mismatches
function fixQuoteMismatchesRegex(content, filePath) {
  let modified = false;
  let fixedContent = content;

  // Pattern to match console statements with mismatched quotes
  const patterns = [
    // Pattern 1: "text: ', → 'text: ',
    {
      regex: /console\.(log|error|warn|info|debug)\s*\(\s*"([^"]*?):\s*',/g,
      fix: (match, method, text) => {
        const escaped = text.replace(/'/g, "\\'");
        return `console.${method}('${escaped}: ',`;
      }
    },
    // Pattern 2: 'text: ", → "text: ",
    {
      regex: /console\.(log|error|warn|info|debug)\s*\(\s*'([^']*?):\s*",/g,
      fix: (match, method, text) => {
        const escaped = text.replace(/"/g, '\\"');
        return `console.${method}("${escaped}: ",`;
      }
    },
    // Pattern 3: Fix unclosed quotes at end of strings
    {
      regex: /console\.(log|error|warn|info|debug)\s*\(\s*(["'])([^\2]*?)\2([^\2\s,)]*)(\s*[,)])/g,
      fix: (match, method, openQuote, content, trailing, end) => {
        if (trailing) {
          // There's content after the closing quote - likely a mismatch
          const fullContent = content + openQuote + trailing;
          let bestQuote = "'";
          if (fullContent.includes("'") && !fullContent.includes('"')) {
            bestQuote = '"';
          }
          
          let escaped = fullContent;
          if (bestQuote === "'") {
            escaped = fullContent.replace(/'/g, "\\'");
          } else {
            escaped = fullContent.replace(/"/g, '\\"');
          }
          
          stats.issuesFixed++;
          modified = true;
          return `console.${method}(${bestQuote}${escaped}${bestQuote}${end}`;
        }
        return match;
      }
    }
  ];

  // Apply each pattern
  patterns.forEach(({ regex, fix }) => {
    fixedContent = fixedContent.replace(regex, (...args) => {
      const result = fix(...args);
      if (result !== args[0]) {
        modified = true;
        if (config.verbose) {
          console.log(`${colors.yellow}Fixed pattern in ${filePath}:${colors.reset}`);
          console.log(`  ${colors.red}- ${args[0]}${colors.reset}`);
          console.log(`  ${colors.green}+ ${result}${colors.reset}`);
        }
      }
      return result;
    });
  });

  return { content: fixedContent, modified };
}

// Function to process a single file
async function processFile(filePath) {
  try {
    stats.filesScanned++;
    
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, modified } = config.useRegex 
      ? fixQuoteMismatchesRegex(content, filePath)
      : fixQuoteMismatchesAST(content, filePath);
    
    if (modified) {
      stats.filesModified++;
      
      if (!config.dryRun) {
        fs.writeFileSync(filePath, fixedContent, 'utf8');
        console.log(`${colors.green}✓${colors.reset} Fixed ${filePath}`);
      } else {
        console.log(`${colors.blue}[DRY RUN]${colors.reset} Would fix ${filePath}`);
      }
    }
  } catch (error) {
    stats.errors.push({ file: filePath, error: error.message });
    console.error(`${colors.red}✗${colors.reset} Error processing ${filePath}: ${error.message}`);
  }
}

// Main function
async function main() {
  console.log(`${colors.bright}Quote Mismatch Fixer (${config.useRegex ? 'Regex' : 'AST'} Mode)${colors.reset}`);
  console.log(`Mode: ${config.dryRun ? colors.blue + 'DRY RUN' : colors.green + 'LIVE'} ${colors.reset}`);
  console.log(`Verbose: ${config.verbose ? 'ON' : 'OFF'}\n`);

  // Build glob pattern
  const pattern = `**/*.{${config.extensions.join(',')}}`;
  const ignorePatterns = config.ignorePaths.map(p => `**/${p}/**`);
  
  try {
    // Find all matching files
    const files = await glob(pattern, {
      ignore: ignorePatterns,
      nodir: true
    });

    console.log(`Found ${files.length} files to scan...\n`);

    // Process each file
    for (const file of files) {
      await processFile(file);
    }

    // Print summary
    console.log(`\n${colors.bright}Summary:${colors.reset}`);
    console.log(`Files scanned: ${stats.filesScanned}`);
    console.log(`Files modified: ${stats.filesModified}`);
    console.log(`Issues fixed: ${stats.issuesFixed}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n${colors.red}Errors (${stats.errors.length}):${colors.reset}`);
      stats.errors.forEach(({ file, error }) => {
        console.log(`  ${file}: ${error}`);
      });
    }

    if (config.dryRun && stats.filesModified > 0) {
      console.log(`\n${colors.yellow}This was a dry run. Run without --dry-run to apply changes.${colors.reset}`);
    }

  } catch (error) {
    console.error(`${colors.red}Fatal error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Check if required dependencies are installed
function checkDependencies() {
  const required = ['glob'];
  const optionalAST = ['@babel/parser', '@babel/traverse', '@babel/generator', '@babel/types'];
  
  const missing = [];
  const missingAST = [];
  
  required.forEach(dep => {
    try {
      require.resolve(dep);
    } catch {
      missing.push(dep);
    }
  });
  
  if (!config.useRegex) {
    optionalAST.forEach(dep => {
      try {
        require.resolve(dep);
      } catch {
        missingAST.push(dep);
      }
    });
  }
  
  if (missing.length > 0) {
    console.error(`${colors.red}Missing required dependencies: ${missing.join(', ')}${colors.reset}`);
    console.error(`Run: npm install ${missing.join(' ')}`);
    process.exit(1);
  }
  
  if (missingAST.length > 0 && !config.useRegex) {
    console.log(`${colors.yellow}AST dependencies not found, switching to regex mode${colors.reset}`);
    console.log(`For AST mode, run: npm install ${missingAST.join(' ')}`);
    config.useRegex = true;
  }
}

// Run the script
checkDependencies();
main().catch(error => {
  console.error(`${colors.red}Unhandled error: ${error}${colors.reset}`);
  process.exit(1);
});