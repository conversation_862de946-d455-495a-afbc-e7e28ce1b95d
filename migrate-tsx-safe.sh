#!/bin/bash

# Safe TypeScript/React color migration script
# This script uses more precise patterns to avoid damaging files

echo "=== Safe TypeScript/React Color Migration Script ==="
echo "This script will process .ts, .tsx, and .js files with careful patterns"
echo ""

# Create backup directory
BACKUP_DIR="tsx-migration-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Function to backup a file
backup_file() {
    local file=$1
    local rel_path=${file#./}
    local backup_path="$BACKUP_DIR/$rel_path"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
}

# Find TypeScript/JavaScript files
echo "Analyzing TypeScript/React files..."
TSX_FILES=$(find ./src -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./dist/*")
TOTAL_FILES=$(echo "$TSX_FILES" | wc -l | tr -d ' ')

echo "Found $TOTAL_FILES TypeScript/React files to check"
echo ""

# Create migration patterns file
cat > migration-patterns.txt << 'EOF'
# Pattern file for safe TypeScript migrations
# Format: PATTERN|REPLACEMENT

# className string patterns (most common)
className="([^"]*\b)(bg|text|border)-gray-50(\b[^"]*)"$|className="$1$2-surface-alt$3"
className="([^"]*\b)(bg|text|border)-gray-100(\b[^"]*)"$|className="$1$2-surface-alt$3"
className="([^"]*\b)(bg|text|border)-gray-200(\b[^"]*)"$|className="$1$2-default$3"
className="([^"]*\b)(bg|text|border)-gray-300(\b[^"]*)"$|className="$1$2-subtle$3"
className="([^"]*\b)(bg|text|border)-gray-400(\b[^"]*)"$|className="$1$2-subtle$3"
className="([^"]*\b)(bg|text|border)-gray-500(\b[^"]*)"$|className="$1$2-muted$3"
className="([^"]*\b)(bg|text|border)-gray-600(\b[^"]*)"$|className="$1$2-secondary$3"
className="([^"]*\b)(bg|text|border)-gray-700(\b[^"]*)"$|className="$1$2-primary$3"
className="([^"]*\b)(bg|text|border)-gray-800(\b[^"]*)"$|className="$1$2-primary$3"
className="([^"]*\b)(bg|text|border)-gray-900(\b[^"]*)"$|className="$1$2-primary$3"

# Semantic colors in className
className="([^"]*\b)(bg|text|border)-(red|rose)-600(\b[^"]*)"$|className="$1$2-error$4"
className="([^"]*\b)(bg|text|border)-(red|rose)-100(\b[^"]*)"$|className="$1$2-error-light$4"
className="([^"]*\b)(bg|text|border)-(green|emerald)-600(\b[^"]*)"$|className="$1$2-success$4"
className="([^"]*\b)(bg|text|border)-(green|emerald)-100(\b[^"]*)"$|className="$1$2-success-light$4"
className="([^"]*\b)(bg|text|border)-(yellow|amber)-600(\b[^"]*)"$|className="$1$2-warning$4"
className="([^"]*\b)(bg|text|border)-(yellow|amber)-100(\b[^"]*)"$|className="$1$2-warning-light$4"
className="([^"]*\b)(bg|text|border)-(blue|indigo)-600(\b[^"]*)"$|className="$1$2-primary$4"
className="([^"]*\b)(bg|text|border)-(blue|indigo)-100(\b[^"]*)"$|className="$1$2-primary-light$4"

# Special case for white/black
className="([^"]*\b)bg-white(\b[^"]*)"$|className="$1bg-surface-card$2"
className="([^"]*\b)text-white(\b[^"]*)"$|className="$1text-inverse$2"
className="([^"]*\b)border-white(\b[^"]*)"$|className="$1border-subtle$2"
className="([^"]*\b)bg-black(\b[^"]*)"$|className="$1bg-primary$2"
className="([^"]*\b)text-black(\b[^"]*)"$|className="$1text-primary$2"
EOF

# Dry run first
echo "Running dry-run analysis..."
DRY_RUN_LOG="tsx-migration-dry-run.log"
echo "=== TypeScript Color Migration Dry Run - $(date) ===" > "$DRY_RUN_LOG"

PATTERNS_FOUND=0
for file in $TSX_FILES; do
    # Only process files that contain className with color patterns
    if grep -E 'className=.*\b(bg|text|border)-(gray|white|black|red|green|blue|yellow)' "$file" > /dev/null 2>&1; then
        MATCHES=$(grep -E 'className=.*\b(bg|text|border)-(gray|white|black|red|green|blue|yellow)' "$file" | wc -l | tr -d ' ')
        if [ $MATCHES -gt 0 ]; then
            echo "Would process: $file ($MATCHES patterns)" | tee -a "$DRY_RUN_LOG"
            ((PATTERNS_FOUND += MATCHES))
        fi
    fi
done

echo ""
echo "Dry run complete. Found $PATTERNS_FOUND patterns to migrate."
echo "Full analysis saved to: $DRY_RUN_LOG"
echo ""

# Show sample of what would be changed
echo "Sample changes that would be made:"
echo "  bg-gray-50 → bg-surface-alt"
echo "  text-gray-600 → text-secondary"
echo "  border-red-600 → border-error"
echo "  bg-white → bg-surface-card"
echo ""

# Ask for confirmation
read -p "Do you want to proceed with the migration? (y/N) " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Migration cancelled."
    rm migration-patterns.txt
    exit 0
fi

# Proceed with migration
echo ""
echo "Starting migration..."
PROCESSED=0
MODIFIED=0

# Process each file
for file in $TSX_FILES; do
    # Skip if no patterns found
    if ! grep -E 'className=.*\b(bg|text|border)-(gray|white|black|red|green|blue|yellow)' "$file" > /dev/null 2>&1; then
        continue
    fi
    
    echo "Processing: $file"
    backup_file "$file"
    
    # Create temp file for processing
    TEMP_FILE="${file}.tmp"
    cp "$file" "$TEMP_FILE"
    
    # Apply each pattern from the patterns file
    while IFS='|' read -r pattern replacement; do
        # Skip comments and empty lines
        [[ "$pattern" =~ ^#.*$ ]] && continue
        [[ -z "$pattern" ]] && continue
        
        # Apply the pattern using perl for better regex support
        perl -i -pe "s/$pattern/$replacement/g" "$TEMP_FILE"
    done < migration-patterns.txt
    
    # Check if file was actually modified
    if ! diff -q "$file" "$TEMP_FILE" > /dev/null; then
        mv "$TEMP_FILE" "$file"
        ((MODIFIED++))
        echo "  ✓ Modified"
    else
        rm "$TEMP_FILE"
        echo "  - No changes needed"
    fi
    
    ((PROCESSED++))
done

# Cleanup
rm migration-patterns.txt

echo ""
echo "=== Migration Complete ==="
echo "Files processed: $PROCESSED"
echo "Files modified: $MODIFIED"
echo "Backup created at: $BACKUP_DIR"
echo ""
echo "Next steps:"
echo "1. Review the changes with: git diff"
echo "2. Test your build: npm run build"
echo "3. If issues occur, restore from backup: cp -r $BACKUP_DIR/* ."
echo ""
echo "Note: This script only handles basic className patterns."
echo "Dynamic classes and complex conditionals need manual review."