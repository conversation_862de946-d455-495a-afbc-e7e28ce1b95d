#!/bin/bash

# Fix concatenated imports (where two imports are on the same line without proper separation)

echo "Fixing concatenated imports in TypeScript files..."

# Find all files with the ';import' pattern and fix them
find src -name "*.ts" -o -name "*.tsx" | while read file; do
    if grep -q ';import' "$file"; then
        echo "Fixing concatenated imports in: $file"
        # Replace ;import with ;\nimport (adding a newline)
        sed -i '' 's/;import/;\
import/g' "$file"
    fi
done

# Also fix any remaining quote mismatches in these specific patterns
find src -name "*.ts" -o -name "*.tsx" | while read file; do
    # Fix patterns where single quotes are mixed in imports
    if grep -E "from ['\"].*['\"].*['\"]" "$file" >/dev/null 2>&1; then
        echo "Fixing mixed quotes in: $file"
        # Fix any remaining single quotes at the end of imports
        sed -i '' "s/from '\([^']*\)'/from \"\1\"/g" "$file"
        sed -i '' "s/';$/\";/g" "$file"
    fi
done

echo "Concatenated import fixes complete!"