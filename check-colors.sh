#!/bin/bash

echo "=== Color Migration Progress Report ==="
echo ""

# Count hardcoded colors
echo "Hardcoded color patterns found:"
echo "--------------------------------"

echo -n "text-[color]-[number]: "
grep -r "text-\(red\|green\|blue\|yellow\|gray\|orange\|purple\|pink\|indigo\)-[0-9]00" src/frontend/components --include="*.tsx" --include="*.ts" | wc -l

echo -n "bg-[color]-[number]: "
grep -r "bg-\(red\|green\|blue\|yellow\|gray\|orange\|purple\|pink\|indigo\)-[0-9]00" src/frontend/components --include="*.tsx" --include="*.ts" | wc -l

echo -n "border-[color]-[number]: "
grep -r "border-\(red\|green\|blue\|yellow\|gray\|orange\|purple\|pink\|indigo\)-[0-9]00" src/frontend/components --include="*.tsx" --include="*.ts" | wc -l

echo -n "dark: variants: "
grep -r "dark:\(bg\|text\|border\)-" src/frontend/components --include="*.tsx" --include="*.ts" | wc -l

echo ""
echo "Top files with most hardcoded colors:"
echo "------------------------------------"
grep -r "\(text\|bg\|border\)-\(red\|green\|blue\|yellow\|gray\|orange\|purple\|pink\|indigo\)-[0-9]00\|dark:\(bg\|text\|border\)-" src/frontend/components --include="*.tsx" --include="*.ts" | cut -d: -f1 | sort | uniq -c | sort -nr | head -20