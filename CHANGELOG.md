# Changelog

All notable changes to the Upstream Financial Dashboard will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Data Management Enhancement** (June 2025)
  - New API endpoint `/api/crm/companies/linked-harvest-ids` to get all linked Harvest IDs
  - Harvest company deduplication in linking modal
  - User-friendly message when all Harvest companies are already linked
  - Submit button disabled when no companies available to link

### Fixed
- **HubSpot Company Sync** (June 2025)
  - Fixed bug where soft-deleted companies prevented recreation during sync
  - `getCompanyByHubspotId()` now properly filters out soft-deleted records
  - Companies deleted in Upstream can now be restored by syncing from HubSpot
  - Fixed Harvest ID duplicate check to exclude deleted companies

- **HubSpot Deal Sync** (June 2025)
  - Fixed "removed value" messages for deals with linked estimates
  - Field tracking now only includes fields actually being updated
  - Estimate-controlled fields are no longer reported as "removed"

- **Flexoki Theme Visual Issues** (December 2024)
  - Fixed card readability issues where text was too light on white backgrounds
  - Resolved "black box" issue in Transaction Filters component
  - Improved text contrast throughout the application
  - Updated card system to use proper Flexoki CSS variables instead of hardcoded colors
  - Enhanced Transaction Filters styling with proper Flexoki theming
  - All components now consistently use Flexoki color system

### Changed
- **Card System Overhaul**
  - Replaced hardcoded Tailwind color classes with Flexoki CSS variables
  - Updated text colors from `text-gray-700` to `text-gray-900` for better contrast
  - Improved subtitle text from `text-gray-500` to `text-gray-600`
  - All card variants now use consistent Flexoki theming

- **Component Styling Improvements**
  - Transaction Filters now use `var(--color-surface-alt)` instead of generic grays
  - Added proper hover states using Flexoki color variables
  - Form inputs and labels now use Flexoki CSS variables for consistency

### Technical
- **CSS Architecture**
  - Migrated card classes in `src/tailwind.css` to use CSS variables
  - Updated `card.css` and `foundation.css` to use Flexoki colors
  - Improved maintainability by centralizing color definitions

### Documentation
- Added comprehensive documentation for Flexoki theme fixes
- Updated color mapping documentation with troubleshooting section
- Created detailed fix documentation in `docs/flexoki-theme-fixes-december-2024.md`
- Updated README.md to reference theme documentation

## Previous Changes

*This changelog was created in December 2024. For historical changes, please refer to git commit history.*
