import { describe, it, expect, beforeEach, afterEach } from 'vitest';

describe('Design Tokens', () => {
  let styleElement: HTMLStyleElement;

  beforeEach(() => {
    // Create a style element with our CSS variables
    styleElement = document.createElement('style');
    styleElement.textContent = `
      :root {
        /* Button tokens */
        --btn-primary-bg: var(--color-primary);
        --btn-primary-hover: var(--color-primary-hover);
        --btn-primary-text: var(--color-bg);
        
        --btn-secondary-bg: var(--color-surface);
        --btn-secondary-hover: var(--color-surface-alt);
        --btn-secondary-text: var(--color-text);
        
        --btn-danger-bg: var(--color-danger);
        --btn-danger-hover: var(--color-danger-dark);
        --btn-danger-text: var(--color-white);
        
        --btn-ghost-bg: transparent;
        --btn-ghost-hover: var(--color-surface);
        --btn-ghost-text: var(--color-text);
        
        /* Input tokens */
        --input-bg: var(--color-bg);
        --input-border: var(--color-border);
        --input-focus-border: var(--color-primary);
        --input-text: var(--color-text);
        --input-placeholder: var(--color-text-muted);
        --input-error-border: var(--color-danger);
        
        /* Surface tokens */
        --surface-primary: var(--color-white);
        --surface-secondary: var(--color-gray-50);
        --surface-elevated: var(--color-white);
        
        /* Text tokens */
        --text-primary: var(--color-text);
        --text-secondary: var(--color-text-muted);
        --text-disabled: var(--color-gray-400);
        
        /* Base colors */
        --color-primary: #3b82f6;
        --color-primary-hover: #2563eb;
        --color-danger: #ef4444;
        --color-danger-dark: #dc2626;
        --color-white: #ffffff;
        --color-bg: #ffffff;
        --color-surface: #f9fafb;
        --color-surface-alt: #f3f4f6;
        --color-text: #111827;
        --color-text-muted: #6b7280;
        --color-border: #e5e7eb;
        --color-gray-50: #f9fafb;
        --color-gray-400: #9ca3af;
      }
      
      /* Dark mode overrides */
      :root.dark {
        --color-bg: #0f172a;
        --color-surface: #1e293b;
        --color-surface-alt: #334155;
        --color-text: #f1f5f9;
        --color-text-muted: #94a3b8;
        --color-border: #334155;
        --color-white: #ffffff;
        
        --surface-primary: var(--color-surface);
        --surface-secondary: var(--color-surface-alt);
        --surface-elevated: var(--color-surface-alt);
      }
    `;
    document.head.appendChild(styleElement);
  });

  afterEach(() => {
    // Clean up
    if (styleElement && styleElement.parentNode) {
      styleElement.parentNode.removeChild(styleElement);
    }
    document.documentElement.classList.remove('dark');
  });

  describe('Semantic Token Resolution', () => {
    it('all button tokens resolve to valid CSS values', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      const buttonTokens = [
        '--btn-primary-bg',
        '--btn-primary-hover',
        '--btn-primary-text',
        '--btn-secondary-bg',
        '--btn-secondary-hover',
        '--btn-secondary-text',
        '--btn-danger-bg',
        '--btn-danger-hover',
        '--btn-danger-text',
        '--btn-ghost-bg',
        '--btn-ghost-hover',
        '--btn-ghost-text',
      ];

      buttonTokens.forEach(token => {
        const value = computedStyle.getPropertyValue(token).trim();
        expect(value, `Token ${token} should have a value`).not.toBe('');
        expect(value, `Token ${token} should not be undefined`).not.toBe('undefined');
        expect(value, `Token ${token} should not contain var()`).not.toMatch(/var\(/);
      });
    });

    it('all input tokens resolve to valid CSS values', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      const inputTokens = [
        '--input-bg',
        '--input-border',
        '--input-focus-border',
        '--input-text',
        '--input-placeholder',
        '--input-error-border',
      ];

      inputTokens.forEach(token => {
        const value = computedStyle.getPropertyValue(token).trim();
        expect(value, `Token ${token} should have a value`).not.toBe('');
        expect(value, `Token ${token} should not be undefined`).not.toBe('undefined');
        expect(value, `Token ${token} should not contain var()`).not.toMatch(/var\(/);
      });
    });

    it('all surface tokens resolve to valid CSS values', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      const surfaceTokens = [
        '--surface-primary',
        '--surface-secondary',
        '--surface-elevated',
      ];

      surfaceTokens.forEach(token => {
        const value = computedStyle.getPropertyValue(token).trim();
        expect(value, `Token ${token} should have a value`).not.toBe('');
        expect(value, `Token ${token} should not be undefined`).not.toBe('undefined');
        expect(value, `Token ${token} should not contain var()`).not.toMatch(/var\(/);
      });
    });
  });

  describe('Theme Switching', () => {
    it('dark mode tokens override correctly', () => {
      const root = document.documentElement;
      
      // Get light mode values
      const lightBg = getComputedStyle(root).getPropertyValue('--color-bg').trim();
      const lightSurface = getComputedStyle(root).getPropertyValue('--surface-primary').trim();
      const lightText = getComputedStyle(root).getPropertyValue('--color-text').trim();
      
      // Switch to dark mode
      root.classList.add('dark');
      
      // Force recomputation
      const darkComputedStyle = getComputedStyle(root);
      const darkBg = darkComputedStyle.getPropertyValue('--color-bg').trim();
      const darkSurface = darkComputedStyle.getPropertyValue('--surface-primary').trim();
      const darkText = darkComputedStyle.getPropertyValue('--color-text').trim();
      
      // Verify values changed
      expect(darkBg).not.toBe(lightBg);
      expect(darkSurface).not.toBe(lightSurface);
      expect(darkText).not.toBe(lightText);
      
      // Verify specific dark mode values
      expect(darkBg).toBe('#0f172a');
      expect(darkText).toBe('#f1f5f9');
    });

    it('button tokens adapt to theme changes', () => {
      const root = document.documentElement;
      
      // Light mode
      const lightSecondaryBg = getComputedStyle(root).getPropertyValue('--btn-secondary-bg').trim();
      
      // Dark mode
      root.classList.add('dark');
      const darkSecondaryBg = getComputedStyle(root).getPropertyValue('--btn-secondary-bg').trim();
      
      // Secondary button bg should change with theme
      expect(darkSecondaryBg).not.toBe(lightSecondaryBg);
    });
  });

  describe('Token Consistency', () => {
    it('related tokens use consistent values', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      // Primary button text should match base background
      const btnPrimaryText = computedStyle.getPropertyValue('--btn-primary-text').trim();
      const baseBg = computedStyle.getPropertyValue('--color-bg').trim();
      expect(btnPrimaryText).toBe(baseBg);
      
      // Ghost button should have transparent background
      const btnGhostBg = computedStyle.getPropertyValue('--btn-ghost-bg').trim();
      expect(btnGhostBg).toBe('transparent');
      
      // Input background should match base background
      const inputBg = computedStyle.getPropertyValue('--input-bg').trim();
      expect(inputBg).toBe(baseBg);
    });

    it('hover states use appropriate darker/lighter values', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      // Primary hover should be different from primary
      const primary = computedStyle.getPropertyValue('--color-primary').trim();
      const primaryHover = computedStyle.getPropertyValue('--color-primary-hover').trim();
      expect(primaryHover).not.toBe(primary);
      
      // Danger hover should be different from danger
      const danger = computedStyle.getPropertyValue('--color-danger').trim();
      const dangerHover = computedStyle.getPropertyValue('--color-danger-dark').trim();
      expect(dangerHover).not.toBe(danger);
    });
  });

  describe('Color Values', () => {
    it('uses valid color formats', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      const colorTokens = [
        '--color-primary',
        '--color-danger',
        '--color-white',
        '--color-bg',
        '--color-text',
      ];
      
      const validColorRegex = /^(#[0-9a-f]{6}|#[0-9a-f]{3}|rgb|rgba|transparent)$/i;
      
      colorTokens.forEach(token => {
        const value = computedStyle.getPropertyValue(token).trim();
        expect(value).toMatch(validColorRegex);
      });
    });

    it('maintains color contrast requirements', () => {
      const root = document.documentElement;
      const computedStyle = getComputedStyle(root);
      
      // This is a simplified check - in production you'd use a proper contrast calculation
      const bg = computedStyle.getPropertyValue('--color-bg').trim();
      const text = computedStyle.getPropertyValue('--color-text').trim();
      
      // Light mode should have dark text on light background
      expect(bg).toBe('#ffffff');
      expect(text).toBe('#111827');
      
      // Dark mode should have light text on dark background
      root.classList.add('dark');
      const darkBg = getComputedStyle(root).getPropertyValue('--color-bg').trim();
      const darkText = getComputedStyle(root).getPropertyValue('--color-text').trim();
      
      expect(darkBg).toBe('#0f172a');
      expect(darkText).toBe('#f1f5f9');
    });
  });
});