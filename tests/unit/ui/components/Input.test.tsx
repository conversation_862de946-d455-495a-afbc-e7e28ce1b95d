import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Input } from '@/frontend/components/ui/Input';

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

describe('Input Component', () => {
  // 1. Default rendering
  it('renders with default props', () => {
    render(<Input />);
    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('type', 'text');
  });

  // 2. Label rendering
  it('renders with label', () => {
    render(<Input label="Email Address" />);
    const label = screen.getByText('Email Address');
    const input = screen.getByRole('textbox');
    
    expect(label).toBeInTheDocument();
    expect(input).toHaveAccessibleName('Email Address');
  });

  // 3. Type variations
  describe.each([
    ['text', 'textbox'],
    ['email', 'textbox'],
    ['password', null], // password inputs don't have a role
    ['number', 'spinbutton'],
    ['tel', 'textbox'],
    ['url', 'textbox'],
  ])('type: %s', (type, expectedRole) => {
    it(`renders ${type} input correctly`, () => {
      render(<Input type={type as any} data-testid="input" />);
      
      if (expectedRole) {
        const input = screen.getByRole(expectedRole);
        expect(input).toHaveAttribute('type', type);
      } else {
        const input = screen.getByTestId('input');
        expect(input).toHaveAttribute('type', type);
      }
    });
  });

  // 4. Placeholder
  it('renders with placeholder', () => {
    render(<Input placeholder="Enter your name" />);
    const input = screen.getByPlaceholderText('Enter your name');
    expect(input).toBeInTheDocument();
  });

  // 5. Value and onChange (controlled)
  it('works as a controlled component', async () => {
    const handleChange = vi.fn();
    const user = userEvent.setup();
    
    const { rerender } = render(
      <Input value="initial" onChange={handleChange} />
    );
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveValue('initial');
    
    await user.type(input, 'a');
    expect(handleChange).toHaveBeenCalled();
    
    // Simulate controlled update
    rerender(<Input value="initial + a" onChange={handleChange} />);
    expect(input).toHaveValue('initial + a');
  });

  // 6. Uncontrolled with defaultValue
  it('works as an uncontrolled component', async () => {
    const user = userEvent.setup();
    render(<Input defaultValue="default" />);
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveValue('default');
    
    await user.clear(input);
    await user.type(input, 'new value');
    expect(input).toHaveValue('new value');
  });

  // 7. Disabled state
  it('handles disabled state correctly', async () => {
    const handleChange = vi.fn();
    const user = userEvent.setup();
    
    render(<Input disabled onChange={handleChange} />);
    const input = screen.getByRole('textbox');
    
    expect(input).toBeDisabled();
    await user.type(input, 'test');
    expect(handleChange).not.toHaveBeenCalled();
    expect(input).toHaveValue('');
  });

  // 8. Error state
  it('shows error state correctly', () => {
    render(
      <Input 
        label="Email" 
        error="Please enter a valid email address"
        aria-invalid="true"
      />
    );
    
    const input = screen.getByRole('textbox');
    const error = screen.getByText('Please enter a valid email address');
    
    expect(error).toBeInTheDocument();
    expect(input).toHaveAttribute('aria-invalid', 'true');
    expect(input).toHaveAccessibleDescription('Please enter a valid email address');
  });

  // 9. Helper text
  it('shows helper text', () => {
    render(
      <Input 
        label="Password" 
        helperText="Must be at least 8 characters"
      />
    );
    
    const helper = screen.getByText('Must be at least 8 characters');
    expect(helper).toBeInTheDocument();
  });

  // 10. Required field
  it('marks field as required', () => {
    render(<Input label="Name" required />);
    const input = screen.getByRole('textbox');
    
    expect(input).toBeRequired();
    expect(input).toHaveAttribute('aria-required', 'true');
  });

  // 11. Custom className
  it('accepts custom className', () => {
    render(<Input className="custom-input" data-testid="input" />);
    const wrapper = screen.getByTestId('input').parentElement;
    expect(wrapper).toHaveClass('custom-input');
  });

  // 12. Ref forwarding
  it('forwards ref correctly', () => {
    const ref = vi.fn();
    render(<Input ref={ref} />);
    
    expect(ref).toHaveBeenCalled();
    expect(ref.mock.calls[0][0]).toBeInstanceOf(HTMLInputElement);
  });

  // 13. onBlur and onFocus
  it('handles focus events', async () => {
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();
    const user = userEvent.setup();
    
    render(
      <Input 
        onFocus={handleFocus} 
        onBlur={handleBlur}
      />
    );
    
    const input = screen.getByRole('textbox');
    
    await user.click(input);
    expect(handleFocus).toHaveBeenCalledTimes(1);
    
    await user.tab();
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });

  // 14. Accessibility testing
  it('has no accessibility violations', async () => {
    const { container } = render(
      <div>
        <Input label="Default input" />
        <Input label="Required input" required />
        <Input label="Disabled input" disabled />
        <Input label="Error input" error="Error message" />
        <Input label="Input with helper" helperText="Helper text" />
      </div>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 15. Keyboard navigation
  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <>
        <Input data-testid="input1" />
        <Input data-testid="input2" />
      </>
    );
    
    const input1 = screen.getByTestId('input1');
    const input2 = screen.getByTestId('input2');
    
    // Focus first input
    await user.click(input1);
    expect(input1).toHaveFocus();
    
    // Tab to next input
    await user.tab();
    expect(input2).toHaveFocus();
    
    // Shift+Tab back
    await user.tab({ shift: true });
    expect(input1).toHaveFocus();
  });

  // 16. Max length
  it('respects maxLength attribute', async () => {
    const user = userEvent.setup();
    render(<Input maxLength={5} />);
    
    const input = screen.getByRole('textbox');
    await user.type(input, 'abcdefgh');
    
    expect(input).toHaveValue('abcde');
  });

  // 17. Pattern validation
  it('accepts pattern attribute', () => {
    render(<Input pattern="[0-9]*" />);
    const input = screen.getByRole('textbox');
    
    expect(input).toHaveAttribute('pattern', '[0-9]*');
  });

  // 18. Autocomplete
  it('supports autocomplete attribute', () => {
    render(<Input type="email" autoComplete="email" />);
    const input = screen.getByRole('textbox');
    
    expect(input).toHaveAttribute('autocomplete', 'email');
  });

  // 19. Size variants
  describe.each([
    ['sm', 'small'],
    ['md', 'medium'],
    ['lg', 'large'],
  ])('size: %s', (size, description) => {
    it(`renders ${description} size correctly`, () => {
      render(<Input size={size as any} data-testid="input" />);
      const input = screen.getByTestId('input');
      expect(input).toBeInTheDocument();
      // Size styling is verified through visual tests
    });
  });

  // 20. Success state variant
  it('shows success state correctly', () => {
    render(<Input variant="success" data-testid="input" />);
    const input = screen.getByTestId('input');
    expect(input).toBeInTheDocument();
    // Success styling is verified through visual tests
  });
});