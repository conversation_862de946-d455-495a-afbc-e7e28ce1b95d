import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Button } from '@/frontend/components/ui/Button';

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

describe('Button Component', () => {
  // 1. Default rendering
  it('renders with default props', () => {
    render(<Button>Click me</Button>);
    const button = screen.getByRole('button', { name: 'Click me' });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Click me');
  });

  // 2. Variant testing
  describe.each([
    ['primary', 'primary'],
    ['secondary', 'secondary'],
    ['danger', 'danger'],
    ['ghost', 'ghost'],
    ['outline', 'outline'],
    ['success', 'success'],
    ['warning', 'warning'],
  ])('variant: %s', (variant, expected) => {
    it(`renders ${variant} variant correctly`, () => {
      render(<Button variant={variant as any}>Test</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAccessibleName('Test');
      // Verify button is rendered - specific styling is handled by visual tests
    });
  });

  // 3. Size testing
  describe.each([
    ['sm', 'small'],
    ['md', 'medium'],
    ['lg', 'large'],
  ])('size: %s', (size, description) => {
    it(`renders ${description} size correctly`, () => {
      render(<Button size={size as any}>Test</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });
  });

  // 4. Interaction testing
  it('calls onClick when clicked', async () => {
    const handleClick = vi.fn();
    const user = userEvent.setup();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    const button = screen.getByRole('button');
    
    await user.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not call onClick when disabled', async () => {
    const handleClick = vi.fn();
    const user = userEvent.setup();
    
    render(<Button onClick={handleClick} disabled>Click me</Button>);
    const button = screen.getByRole('button');
    
    expect(button).toBeDisabled();
    await user.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  // 5. Loading state
  it('shows loading state correctly', () => {
    render(<Button loading>Click me</Button>);
    const button = screen.getByRole('button');
    
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('aria-busy', 'true');
  });

  // 6. Type prop
  it('renders as different button types', () => {
    const { rerender } = render(<Button type="submit">Submit</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'submit');

    rerender(<Button type="button">Button</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'button');

    rerender(<Button type="reset">Reset</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'reset');
  });

  // 7. Custom className
  it('accepts custom className', () => {
    render(<Button className="custom-class">Test</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  // 8. Ref forwarding
  it('forwards ref correctly', () => {
    const ref = vi.fn();
    render(<Button ref={ref}>Test</Button>);
    expect(ref).toHaveBeenCalled();
    expect(ref.mock.calls[0][0]).toBeInstanceOf(HTMLButtonElement);
  });

  // 9. Accessibility testing
  it('has no accessibility violations', async () => {
    const { container } = render(
      <div>
        <Button>Default</Button>
        <Button variant="primary">Primary</Button>
        <Button disabled>Disabled</Button>
        <Button loading>Loading</Button>
      </div>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 10. Keyboard interaction
  it('can be activated with keyboard', async () => {
    const handleClick = vi.fn();
    const user = userEvent.setup();
    
    render(<Button onClick={handleClick}>Press me</Button>);
    const button = screen.getByRole('button');
    
    // Focus the button
    button.focus();
    expect(button).toHaveFocus();
    
    // Press Enter
    await user.keyboard('{Enter}');
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    // Press Space
    await user.keyboard(' ');
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  // 11. Children rendering
  it('renders complex children', () => {
    render(
      <Button>
        <span data-testid="icon">🚀</span>
        <span>Launch</span>
      </Button>
    );
    
    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(screen.getByText('Launch')).toBeInTheDocument();
  });

  // 12. Full width prop
  it('renders full width when specified', () => {
    render(<Button fullWidth>Full Width</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-full');
  });
});