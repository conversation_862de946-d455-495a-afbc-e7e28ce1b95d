import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Select } from '@/frontend/components/ui/Select';

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

describe('Select Component', () => {
  const mockOptions = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'orange', label: 'Orange' },
  ];

  // 1. Default rendering
  it('renders with default props', () => {
    render(
      <Select>
        <option value="">Choose an option</option>
        <option value="1">Option 1</option>
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();
    expect(select).toHaveValue('');
  });

  // 2. Label rendering
  it('renders with label', () => {
    render(
      <Select label="Fruit Selection">
        <option value="">Choose a fruit</option>
        {mockOptions.map(opt => (
          <option key={opt.value} value={opt.value}>{opt.label}</option>
        ))}
      </Select>
    );
    
    const label = screen.getByText('Fruit Selection');
    const select = screen.getByRole('combobox');
    
    expect(label).toBeInTheDocument();
    expect(select).toHaveAccessibleName('Fruit Selection');
  });

  // 3. Controlled component
  it('works as a controlled component', async () => {
    const handleChange = vi.fn();
    const user = userEvent.setup();
    
    const { rerender } = render(
      <Select value="apple" onChange={handleChange}>
        {mockOptions.map(opt => (
          <option key={opt.value} value={opt.value}>{opt.label}</option>
        ))}
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    expect(select).toHaveValue('apple');
    
    await user.selectOptions(select, 'banana');
    expect(handleChange).toHaveBeenCalled();
    
    // Simulate controlled update
    rerender(
      <Select value="banana" onChange={handleChange}>
        {mockOptions.map(opt => (
          <option key={opt.value} value={opt.value}>{opt.label}</option>
        ))}
      </Select>
    );
    expect(select).toHaveValue('banana');
  });

  // 4. Uncontrolled with defaultValue
  it('works as an uncontrolled component', async () => {
    const user = userEvent.setup();
    
    render(
      <Select defaultValue="banana">
        {mockOptions.map(opt => (
          <option key={opt.value} value={opt.value}>{opt.label}</option>
        ))}
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    expect(select).toHaveValue('banana');
    
    await user.selectOptions(select, 'orange');
    expect(select).toHaveValue('orange');
  });

  // 5. Disabled state
  it('handles disabled state correctly', async () => {
    const handleChange = vi.fn();
    const user = userEvent.setup();
    
    render(
      <Select disabled onChange={handleChange}>
        {mockOptions.map(opt => (
          <option key={opt.value} value={opt.value}>{opt.label}</option>
        ))}
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    expect(select).toBeDisabled();
    
    // Try to change value
    await user.selectOptions(select, 'banana');
    expect(handleChange).not.toHaveBeenCalled();
  });

  // 6. Error state
  it('shows error state correctly', () => {
    render(
      <Select 
        label="Country" 
        error="Please select a country"
        aria-invalid="true"
      >
        <option value="">Choose a country</option>
        <option value="us">United States</option>
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    const error = screen.getByText('Please select a country');
    
    expect(error).toBeInTheDocument();
    expect(select).toHaveAttribute('aria-invalid', 'true');
    expect(select).toHaveAccessibleDescription('Please select a country');
  });

  // 7. Helper text
  it('shows helper text', () => {
    render(
      <Select 
        label="Size" 
        helperText="Select your preferred size"
      >
        <option value="s">Small</option>
        <option value="m">Medium</option>
        <option value="l">Large</option>
      </Select>
    );
    
    const helper = screen.getByText('Select your preferred size');
    expect(helper).toBeInTheDocument();
  });

  // 8. Required field
  it('marks field as required', () => {
    render(
      <Select label="Priority" required>
        <option value="">Choose priority</option>
        <option value="high">High</option>
        <option value="low">Low</option>
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    expect(select).toBeRequired();
    expect(select).toHaveAttribute('aria-required', 'true');
  });

  // 9. Multiple selection
  it('supports multiple selection', async () => {
    const handleChange = vi.fn();
    const user = userEvent.setup();
    
    render(
      <Select multiple onChange={handleChange} data-testid="multi-select">
        {mockOptions.map(opt => (
          <option key={opt.value} value={opt.value}>{opt.label}</option>
        ))}
      </Select>
    );
    
    const select = screen.getByTestId('multi-select');
    
    // Select multiple options
    await user.selectOptions(select, ['apple', 'banana']);
    expect(handleChange).toHaveBeenCalled();
  });

  // 10. Custom className
  it('accepts custom className', () => {
    render(
      <Select className="custom-select" data-testid="select">
        <option value="1">Option 1</option>
      </Select>
    );
    
    const wrapper = screen.getByTestId('select').parentElement;
    expect(wrapper).toHaveClass('custom-select');
  });

  // 11. Ref forwarding
  it('forwards ref correctly', () => {
    const ref = vi.fn();
    
    render(
      <Select ref={ref}>
        <option value="1">Option 1</option>
      </Select>
    );
    
    expect(ref).toHaveBeenCalled();
    expect(ref.mock.calls[0][0]).toBeInstanceOf(HTMLSelectElement);
  });

  // 12. onBlur and onFocus
  it('handles focus events', async () => {
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();
    const user = userEvent.setup();
    
    render(
      <Select 
        onFocus={handleFocus} 
        onBlur={handleBlur}
      >
        <option value="1">Option 1</option>
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    
    await user.click(select);
    expect(handleFocus).toHaveBeenCalledTimes(1);
    
    await user.tab();
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });

  // 13. Accessibility testing
  it('has no accessibility violations', async () => {
    const { container } = render(
      <div>
        <Select label="Default select">
          <option value="">Choose</option>
          <option value="1">Option 1</option>
        </Select>
        
        <Select label="Required select" required>
          <option value="">Choose</option>
          <option value="1">Option 1</option>
        </Select>
        
        <Select label="Disabled select" disabled>
          <option value="1">Option 1</option>
        </Select>
        
        <Select label="Error select" error="Error message">
          <option value="">Choose</option>
        </Select>
      </div>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 14. Keyboard navigation
  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <Select>
        <option value="">Choose</option>
        <option value="1">First</option>
        <option value="2">Second</option>
        <option value="3">Third</option>
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    
    // Focus and navigate with keyboard
    await user.click(select);
    expect(select).toHaveFocus();
    
    // Arrow down should change selection
    await user.keyboard('{ArrowDown}');
    expect(select).toHaveValue('1');
    
    await user.keyboard('{ArrowDown}');
    expect(select).toHaveValue('2');
    
    await user.keyboard('{ArrowUp}');
    expect(select).toHaveValue('1');
  });

  // 15. Option groups
  it('renders option groups correctly', () => {
    render(
      <Select label="Grouped Options">
        <optgroup label="Fruits">
          <option value="apple">Apple</option>
          <option value="banana">Banana</option>
        </optgroup>
        <optgroup label="Vegetables">
          <option value="carrot">Carrot</option>
          <option value="lettuce">Lettuce</option>
        </optgroup>
      </Select>
    );
    
    // Check that optgroups are rendered
    expect(screen.getByRole('group', { name: 'Fruits' })).toBeInTheDocument();
    expect(screen.getByRole('group', { name: 'Vegetables' })).toBeInTheDocument();
    
    // Check that options are within groups
    expect(screen.getByRole('option', { name: 'Apple' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'Carrot' })).toBeInTheDocument();
  });

  // 16. Disabled options
  it('handles disabled options', async () => {
    const user = userEvent.setup();
    
    render(
      <Select defaultValue="1">
        <option value="1">Active Option</option>
        <option value="2" disabled>Disabled Option</option>
        <option value="3">Another Active</option>
      </Select>
    );
    
    const select = screen.getByRole('combobox');
    const disabledOption = screen.getByRole('option', { name: 'Disabled Option' });
    
    expect(disabledOption).toBeDisabled();
    
    // Try to select disabled option
    await user.selectOptions(select, '2');
    // Should remain on original value
    expect(select).toHaveValue('1');
  });

  // 17. Size variants
  describe.each([
    ['sm', 'small'],
    ['md', 'medium'],
    ['lg', 'large'],
  ])('size: %s', (size, description) => {
    it(`renders ${description} size correctly`, () => {
      render(
        <Select size={size as any} data-testid="select">
          <option value="1">Option</option>
        </Select>
      );
      
      const select = screen.getByTestId('select');
      expect(select).toBeInTheDocument();
      // Size styling is verified through visual tests
    });
  });
});