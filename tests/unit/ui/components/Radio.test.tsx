import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Radio } from '@/frontend/components/ui/Radio';

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

describe('Radio Component', () => {
  // 1. Default rendering
  it('renders with default props', () => {
    render(<Radio name="test" value="option1" />);
    const radio = screen.getByRole('radio');
    expect(radio).toBeInTheDocument();
    expect(radio).not.toBeChecked();
  });

  // 2. Label rendering
  it('renders with label', () => {
    render(<Radio name="test" value="option1" label="Option 1" />);
    const radio = screen.getByRole('radio', { name: 'Option 1' });
    const label = screen.getByText('Option 1');
    expect(radio).toBeInTheDocument();
    expect(label).toBeInTheDocument();
  });

  // 3. Controlled component
  it('works as controlled component', () => {
    const handleChange = vi.fn();
    const { rerender } = render(
      <Radio 
        name="controlled" 
        value="option1" 
        checked={false} 
        onChange={handleChange} 
        label="Controlled" 
      />
    );
    
    const radio = screen.getByRole('radio');
    expect(radio).not.toBeChecked();
    
    rerender(
      <Radio 
        name="controlled" 
        value="option1" 
        checked={true} 
        onChange={handleChange} 
        label="Controlled" 
      />
    );
    expect(radio).toBeChecked();
  });

  // 4. Radio group behavior
  it('works within a radio group', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    
    render(
      <div>
        <Radio 
          name="group" 
          value="option1" 
          label="Option 1" 
          onChange={handleChange}
        />
        <Radio 
          name="group" 
          value="option2" 
          label="Option 2" 
          onChange={handleChange}
        />
        <Radio 
          name="group" 
          value="option3" 
          label="Option 3" 
          onChange={handleChange}
        />
      </div>
    );
    
    const option1 = screen.getByRole('radio', { name: 'Option 1' });
    const option2 = screen.getByRole('radio', { name: 'Option 2' });
    const option3 = screen.getByRole('radio', { name: 'Option 3' });
    
    await user.click(option1);
    expect(option1).toBeChecked();
    expect(option2).not.toBeChecked();
    expect(option3).not.toBeChecked();
    
    await user.click(option2);
    expect(option1).not.toBeChecked();
    expect(option2).toBeChecked();
    expect(option3).not.toBeChecked();
  });

  // 5. onChange handler
  it('calls onChange when clicked', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(
      <Radio 
        name="test" 
        value="option1" 
        onChange={handleChange} 
        label="Click me" 
      />
    );
    
    const radio = screen.getByRole('radio');
    await user.click(radio);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: 'option1',
          checked: true,
          type: 'radio'
        })
      })
    );
  });

  // 6. Disabled state
  it('handles disabled state', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(
      <Radio 
        name="test" 
        value="option1" 
        disabled 
        onChange={handleChange} 
        label="Disabled radio" 
      />
    );
    
    const radio = screen.getByRole('radio');
    expect(radio).toBeDisabled();
    
    await user.click(radio);
    expect(handleChange).not.toHaveBeenCalled();
  });

  // 7. Error state
  it('applies error styles', () => {
    const { container } = render(
      <Radio name="test" value="option1" error label="Error radio" />
    );
    const radio = screen.getByRole('radio');
    
    expect(radio.className).toContain('border-red-500');
  });

  // 8. Custom className
  it('accepts custom className', () => {
    const { container } = render(
      <Radio 
        name="test" 
        value="option1" 
        className="custom-class" 
        label="Custom" 
      />
    );
    const radio = screen.getByRole('radio');
    
    expect(radio.className).toContain('custom-class');
  });

  // 9. ID handling
  it('uses provided id', () => {
    render(<Radio name="test" value="option1" id="custom-radio" label="Custom ID" />);
    const radio = screen.getByRole('radio');
    
    expect(radio).toHaveAttribute('id', 'custom-radio');
  });

  it('generates unique id when not provided', () => {
    const { container } = render(
      <Radio name="test" value="option1" label="Auto ID" />
    );
    const radio = screen.getByRole('radio');
    const label = container.querySelector('label');
    
    expect(radio.id).toBeTruthy();
    expect(label).toHaveAttribute('for', radio.id);
  });

  // 10. Label interaction
  it('selects when label is clicked', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(
      <Radio 
        name="test" 
        value="option1" 
        onChange={handleChange} 
        label="Click label" 
      />
    );
    
    const label = screen.getByText('Click label');
    await user.click(label);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  // 11. Required name and value props
  it('requires name and value props', () => {
    render(<Radio name="required-test" value="required-value" />);
    const radio = screen.getByRole('radio');
    
    expect(radio).toHaveAttribute('name', 'required-test');
    expect(radio).toHaveAttribute('value', 'required-value');
  });

  // 12. Keyboard interaction
  it('supports keyboard navigation in radio group', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    
    render(
      <div>
        <Radio 
          name="keyboard" 
          value="option1" 
          label="Option 1" 
          onChange={handleChange}
        />
        <Radio 
          name="keyboard" 
          value="option2" 
          label="Option 2" 
          onChange={handleChange}
        />
        <Radio 
          name="keyboard" 
          value="option3" 
          label="Option 3" 
          onChange={handleChange}
        />
      </div>
    );
    
    const option1 = screen.getByRole('radio', { name: 'Option 1' });
    const option2 = screen.getByRole('radio', { name: 'Option 2' });
    
    // Focus first radio
    option1.focus();
    
    // Arrow down should move to next radio
    await user.keyboard('{ArrowDown}');
    expect(document.activeElement).toBe(option2);
  });

  // 13. Accessibility
  it('has no accessibility violations', async () => {
    const { container } = render(
      <fieldset>
        <legend>Choose an option</legend>
        <Radio name="a11y" value="option1" label="Option 1" />
        <Radio name="a11y" value="option2" label="Option 2" defaultChecked />
        <Radio name="a11y" value="option3" label="Option 3" disabled />
      </fieldset>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 14. Without label
  it('renders without label', () => {
    render(
      <Radio name="test" value="option1" aria-label="Hidden label radio" />
    );
    const radio = screen.getByRole('radio', { name: 'Hidden label radio' });
    
    expect(radio).toBeInTheDocument();
    expect(screen.queryByText('Hidden label radio')).not.toBeInTheDocument();
  });

  // 15. Additional props pass-through
  it('passes through additional props', () => {
    render(
      <Radio
        name="test"
        value="option1"
        data-testid="custom-radio"
        aria-describedby="helper-text"
        label="Props test"
      />
    );
    
    const radio = screen.getByTestId('custom-radio');
    expect(radio).toHaveAttribute('aria-describedby', 'helper-text');
  });

  // 16. Ref forwarding
  it('forwards ref to input element', () => {
    const ref = vi.fn();
    render(<Radio name="test" value="option1" ref={ref} label="Ref test" />);
    
    expect(ref).toHaveBeenCalledWith(
      expect.objectContaining({
        tagName: 'INPUT',
        type: 'radio'
      })
    );
  });

  // 17. DefaultChecked uncontrolled
  it('works with defaultChecked for uncontrolled usage', async () => {
    const user = userEvent.setup();
    render(
      <div>
        <Radio name="uncontrolled" value="option1" label="Option 1" />
        <Radio name="uncontrolled" value="option2" label="Option 2" defaultChecked />
      </div>
    );
    
    const option1 = screen.getByRole('radio', { name: 'Option 1' });
    const option2 = screen.getByRole('radio', { name: 'Option 2' });
    
    expect(option1).not.toBeChecked();
    expect(option2).toBeChecked();
    
    await user.click(option1);
    expect(option1).toBeChecked();
    expect(option2).not.toBeChecked();
  });
});