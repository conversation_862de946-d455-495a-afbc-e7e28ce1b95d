import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { FileInput } from '@/frontend/components/ui/FileInput';

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

// Mock file for testing
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

describe('FileInput Component', () => {
  // 1. Default rendering
  it('renders with default props', () => {
    render(<FileInput />);
    const input = screen.getByRole('button', { name: /choose file/i });
    expect(input).toBeInTheDocument();
    expect(screen.getByText(/drag and drop/i)).toBeInTheDocument();
  });

  // 2. Label rendering
  it('renders with label', () => {
    render(<FileInput label="Upload Document" />);
    expect(screen.getByText('Upload Document')).toBeInTheDocument();
  });

  // 3. Helper text
  it('renders with helper text', () => {
    render(<FileInput helperText="PDF files only, max 5MB" />);
    expect(screen.getByText('PDF files only, max 5MB')).toBeInTheDocument();
  });

  // 4. Single file selection
  it('handles single file selection', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<FileInput onChange={handleChange} />);
    
    const file = createMockFile('document.pdf', 1024, 'application/pdf');
    const input = screen.getByLabelText(/choose file/i);
    
    await user.upload(input, file);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          files: expect.any(FileList)
        })
      })
    );
  });

  // 5. Multiple file selection
  it('handles multiple file selection', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<FileInput multiple onChange={handleChange} />);
    
    const files = [
      createMockFile('doc1.pdf', 1024, 'application/pdf'),
      createMockFile('doc2.pdf', 2048, 'application/pdf')
    ];
    const input = screen.getByLabelText(/choose files/i);
    
    await user.upload(input, files);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
    const event = handleChange.mock.calls[0][0];
    expect(event.target.files).toHaveLength(2);
  });

  // 6. Accept attribute
  it('applies accept attribute correctly', () => {
    render(<FileInput accept=".pdf,.doc,.docx" />);
    const input = screen.getByLabelText(/choose file/i);
    
    expect(input).toHaveAttribute('accept', '.pdf,.doc,.docx');
  });

  // 7. Drag and drop - drag enter
  it('shows drag state on drag enter', async () => {
    render(<FileInput />);
    const dropZone = screen.getByText(/drag and drop/i).closest('label');
    
    fireEvent.dragEnter(dropZone!);
    
    await waitFor(() => {
      expect(dropZone).toHaveClass('border-primary-500');
    });
  });

  // 8. Drag and drop - drag leave
  it('removes drag state on drag leave', async () => {
    render(<FileInput />);
    const dropZone = screen.getByText(/drag and drop/i).closest('label');
    
    fireEvent.dragEnter(dropZone!);
    fireEvent.dragLeave(dropZone!);
    
    await waitFor(() => {
      expect(dropZone).not.toHaveClass('border-primary-500');
    });
  });

  // 9. Drag and drop - file drop
  it('handles file drop', async () => {
    const handleChange = vi.fn();
    render(<FileInput onChange={handleChange} />);
    const dropZone = screen.getByText(/drag and drop/i).closest('label');
    
    const file = createMockFile('dropped.pdf', 1024, 'application/pdf');
    const dataTransfer = {
      files: [file],
      items: [{ kind: 'file', type: 'application/pdf', getAsFile: () => file }],
      types: ['Files']
    };
    
    fireEvent.dragOver(dropZone!, { dataTransfer });
    fireEvent.drop(dropZone!, { dataTransfer });
    
    expect(handleChange).toHaveBeenCalled();
  });

  // 10. Disabled state
  it('handles disabled state', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<FileInput disabled onChange={handleChange} />);
    
    const input = screen.getByLabelText(/choose file/i);
    const dropZone = screen.getByText(/drag and drop/i).closest('label');
    
    expect(input).toBeDisabled();
    expect(dropZone).toHaveClass('cursor-not-allowed');
    
    // Try to upload file
    const file = createMockFile('test.pdf', 1024, 'application/pdf');
    await user.upload(input, file);
    
    expect(handleChange).not.toHaveBeenCalled();
  });

  // 11. Custom className
  it('accepts custom className', () => {
    const { container } = render(<FileInput className="custom-class" />);
    const input = container.querySelector('input[type="file"]');
    
    expect(input).toHaveClass('custom-class');
  });

  // 12. ID handling
  it('uses provided id', () => {
    render(<FileInput id="document-upload" label="Upload" />);
    const input = screen.getByLabelText('Upload');
    
    expect(input).toHaveAttribute('id', 'document-upload');
  });

  it('generates unique id when not provided', () => {
    render(<FileInput label="Auto ID" />);
    const input = screen.getByLabelText('Auto ID');
    
    expect(input.id).toBeTruthy();
    expect(input.id).toMatch(/^file-/);
  });

  // 13. Selected files display
  it('displays selected file name', async () => {
    const user = userEvent.setup();
    render(<FileInput />);
    
    const file = createMockFile('important-document.pdf', 1024, 'application/pdf');
    const input = screen.getByLabelText(/choose file/i);
    
    await user.upload(input, file);
    
    expect(screen.getByText('important-document.pdf')).toBeInTheDocument();
  });

  it('displays multiple selected file names', async () => {
    const user = userEvent.setup();
    render(<FileInput multiple />);
    
    const files = [
      createMockFile('file1.pdf', 1024, 'application/pdf'),
      createMockFile('file2.pdf', 2048, 'application/pdf'),
      createMockFile('file3.pdf', 3072, 'application/pdf')
    ];
    const input = screen.getByLabelText(/choose files/i);
    
    await user.upload(input, files);
    
    expect(screen.getByText('3 files selected')).toBeInTheDocument();
  });

  // 14. Accessibility
  it('has no accessibility violations', async () => {
    const { container } = render(
      <div>
        <FileInput label="Upload document" helperText="PDF or Word files" />
        <FileInput label="Upload multiple" multiple />
        <FileInput label="Disabled upload" disabled />
      </div>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 15. Additional props pass-through
  it('passes through additional props', () => {
    render(
      <FileInput
        data-testid="custom-file-input"
        aria-describedby="file-help"
        label="Props test"
      />
    );
    
    const input = screen.getByTestId('custom-file-input');
    expect(input).toHaveAttribute('aria-describedby', 'file-help');
  });

  // 16. Ref forwarding
  it('forwards ref to input element', () => {
    const ref = vi.fn();
    render(<FileInput ref={ref} label="Ref test" />);
    
    expect(ref).toHaveBeenCalledWith(
      expect.objectContaining({
        tagName: 'INPUT',
        type: 'file'
      })
    );
  });

  // 17. Clear selection
  it('allows clearing selected files', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<FileInput onChange={handleChange} />);
    
    const file = createMockFile('test.pdf', 1024, 'application/pdf');
    const input = screen.getByLabelText(/choose file/i);
    
    // Upload file
    await user.upload(input, file);
    expect(screen.getByText('test.pdf')).toBeInTheDocument();
    
    // Clear selection by uploading empty file list
    await user.upload(input, []);
    expect(screen.queryByText('test.pdf')).not.toBeInTheDocument();
  });

  // 18. Prevent default drag events
  it('prevents default browser drag behavior', () => {
    render(<FileInput />);
    const dropZone = screen.getByText(/drag and drop/i).closest('label');
    
    const dragOverEvent = new Event('dragover', { bubbles: true, cancelable: true });
    const spy = vi.spyOn(dragOverEvent, 'preventDefault');
    
    fireEvent(dropZone!, dragOverEvent);
    expect(spy).toHaveBeenCalled();
  });

  // 19. Error visual feedback (if applicable)
  it('shows error state when invalid files are selected', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    
    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <FileInput 
        accept=".pdf" 
        onChange={handleChange}
        label="PDF only"
      />
    );
    
    // This would typically be handled by browser validation
    // but we can test the visual state if an error prop is added
    const input = screen.getByLabelText(/choose file/i);
    const file = createMockFile('image.jpg', 1024, 'image/jpeg');
    
    await user.upload(input, file);
    
    // The actual validation would be done by the browser
    // This test ensures the component can handle file selection
    expect(handleChange).toHaveBeenCalled();
    
    consoleSpy.mockRestore();
  });

  // 20. Loading state (if applicable)
  it('can display loading state during upload', () => {
    // This would be implemented if the component supports a loading prop
    // For now, we ensure the component renders correctly
    render(<FileInput label="Upload with loading" />);
    expect(screen.getByText('Upload with loading')).toBeInTheDocument();
  });
});