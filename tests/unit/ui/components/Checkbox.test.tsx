import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Checkbox } from '@/frontend/components/ui/Checkbox';

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

describe('Checkbox Component', () => {
  // 1. Default rendering
  it('renders with default props', () => {
    render(<Checkbox />);
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();
  });

  // 2. Label rendering
  it('renders with label', () => {
    render(<Checkbox label="Accept terms" />);
    const checkbox = screen.getByRole('checkbox', { name: 'Accept terms' });
    const label = screen.getByText('Accept terms');
    expect(checkbox).toBeInTheDocument();
    expect(label).toBeInTheDocument();
  });

  // 3. Controlled component
  it('works as controlled component', () => {
    const handleChange = vi.fn();
    const { rerender } = render(
      <Checkbox checked={false} onChange={handleChange} label="Controlled" />
    );
    
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).not.toBeChecked();
    
    rerender(<Checkbox checked={true} onChange={handleChange} label="Controlled" />);
    expect(checkbox).toBeChecked();
  });

  // 4. Uncontrolled component
  it('works as uncontrolled component', async () => {
    const user = userEvent.setup();
    render(<Checkbox defaultChecked={false} label="Uncontrolled" />);
    
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).not.toBeChecked();
    
    await user.click(checkbox);
    expect(checkbox).toBeChecked();
    
    await user.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  // 5. onChange handler
  it('calls onChange when clicked', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<Checkbox onChange={handleChange} label="Click me" />);
    
    const checkbox = screen.getByRole('checkbox');
    await user.click(checkbox);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          checked: true,
          type: 'checkbox'
        })
      })
    );
  });

  // 6. Disabled state
  it('handles disabled state', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(
      <Checkbox disabled onChange={handleChange} label="Disabled checkbox" />
    );
    
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeDisabled();
    
    await user.click(checkbox);
    expect(handleChange).not.toHaveBeenCalled();
  });

  // 7. Error state
  it('applies error styles', () => {
    const { container } = render(<Checkbox error label="Error checkbox" />);
    const checkbox = screen.getByRole('checkbox');
    
    expect(checkbox.className).toContain('border-red-500');
  });

  // 8. Custom className
  it('accepts custom className', () => {
    const { container } = render(
      <Checkbox className="custom-class" label="Custom" />
    );
    const checkbox = screen.getByRole('checkbox');
    
    expect(checkbox.className).toContain('custom-class');
  });

  // 9. ID handling
  it('uses provided id', () => {
    render(<Checkbox id="terms-checkbox" label="Terms" />);
    const checkbox = screen.getByRole('checkbox');
    
    expect(checkbox).toHaveAttribute('id', 'terms-checkbox');
  });

  it('generates unique id when not provided', () => {
    const { container } = render(<Checkbox label="Auto ID" />);
    const checkbox = screen.getByRole('checkbox');
    const label = container.querySelector('label');
    
    expect(checkbox.id).toBeTruthy();
    expect(label).toHaveAttribute('for', checkbox.id);
  });

  // 10. Label interaction
  it('toggles when label is clicked', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<Checkbox onChange={handleChange} label="Click label" />);
    
    const label = screen.getByText('Click label');
    await user.click(label);
    
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  // 11. Keyboard interaction
  it('supports keyboard interaction', async () => {
    const user = userEvent.setup();
    const handleChange = vi.fn();
    render(<Checkbox onChange={handleChange} label="Keyboard test" />);
    
    const checkbox = screen.getByRole('checkbox');
    checkbox.focus();
    
    await user.keyboard(' ');
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(checkbox).toBeChecked();
  });

  // 12. Accessibility
  it('has no accessibility violations', async () => {
    const { container } = render(
      <div>
        <Checkbox label="Accessible checkbox" />
        <Checkbox label="Checked checkbox" defaultChecked />
        <Checkbox label="Disabled checkbox" disabled />
      </div>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // 13. Without label
  it('renders without label', () => {
    render(<Checkbox aria-label="Hidden label checkbox" />);
    const checkbox = screen.getByRole('checkbox', { name: 'Hidden label checkbox' });
    
    expect(checkbox).toBeInTheDocument();
    expect(screen.queryByText('Hidden label checkbox')).not.toBeInTheDocument();
  });

  // 14. Additional props pass-through
  it('passes through additional props', () => {
    render(
      <Checkbox
        data-testid="custom-checkbox"
        aria-describedby="helper-text"
        label="Props test"
      />
    );
    
    const checkbox = screen.getByTestId('custom-checkbox');
    expect(checkbox).toHaveAttribute('aria-describedby', 'helper-text');
  });

  // 15. Ref forwarding
  it('forwards ref to input element', () => {
    const ref = vi.fn();
    render(<Checkbox ref={ref} label="Ref test" />);
    
    expect(ref).toHaveBeenCalledWith(
      expect.objectContaining({
        tagName: 'INPUT',
        type: 'checkbox'
      })
    );
  });
});