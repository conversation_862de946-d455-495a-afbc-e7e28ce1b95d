Based on the web search results, I found:

1. **Tailwind v4 upgrade codemod** (@tailwindcss/upgrade@next) - This is the official Tailwind codemod, but it's for v3->v4 migration, not for migrating away from Tailwind colors to semantic tokens.

2. **No existing codemods** for specifically migrating from Tailwind utility classes to a custom semantic token system. The ember-tailwind-codemod does the opposite (CSS to Tailwind).

3. **Tailwind v4 native CSS variables** - Interestingly, Tailwind v4 now uses CSS variables by default and has a @theme directive that could potentially help.

Given this information and your jscodeshift recommendation, please:
1. Critically evaluate whether jscodeshift is still the best approach vs other options
2. Consider if we should leverage Tailwind v4's CSS variable system instead of fighting against Tailwind
3. Address the specific challenges in our codebase (dynamic classes, conditional rendering, cn/clsx utilities)
4. Provide a final recommendation that balances safety, speed, and maintainability