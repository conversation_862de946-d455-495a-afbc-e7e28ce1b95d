/** @type {import('tailwindcss').Config} */
const { generateTailwindColors } = require('./src/styles/flexoki-theme');

module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable dark mode with class strategy
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      // === FLEXOKI COLOR SYSTEM ===
      // REMOVED: generateTailwindColors() to enforce btn-modern usage
      // All button styling must use CSS variables through btn-modern classes
      colors: {
        // Keep only essential color references for non-button elements
        // These reference CSS variables, not hardcoded values
        current: 'currentColor',
        transparent: 'transparent',
        
        // PHASE 1 TEMPORARY: Full colors restored ONLY for @apply directives in CSS
        // The temporary-color-fix.css will override these at runtime
        // This allows the build to succeed without modifying source files
        ...generateTailwindColors(),
      },

      // === FLUID TYPOGRAPHY SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      fontSize: {
        'fluid-xs': 'clamp(0.65rem, 0.5vw + 0.5rem, 0.75rem)',
        'fluid-sm': 'clamp(0.75rem, 0.75vw + 0.5rem, 0.875rem)', 
        'fluid-base': 'clamp(0.875rem, 1vw + 0.5rem, 1rem)',
        'fluid-lg': 'clamp(1rem, 1.25vw + 0.5rem, 1.125rem)',
        'fluid-xl': 'clamp(1.125rem, 1.5vw + 0.5rem, 1.25rem)',
        'fluid-2xl': 'clamp(1.25rem, 2vw + 0.5rem, 1.5rem)',
      },

      // === FLUID SPACING SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      spacing: {
        'fluid-xs': 'clamp(0.25rem, 0.25vw + 0.25rem, 0.5rem)',
        'fluid-sm': 'clamp(0.5rem, 0.5vw + 0.25rem, 0.75rem)',
        'fluid-md': 'clamp(0.75rem, 0.75vw + 0.25rem, 1rem)', 
        'fluid-lg': 'clamp(1rem, 1vw + 0.5rem, 1.5rem)',
        'fluid-xl': 'clamp(1.5rem, 1.5vw + 0.5rem, 2rem)',
      },

      // === FLUID WIDTH SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      maxWidth: {
        'fluid-xs': 'clamp(16rem, 30vw, 20rem)',
        'fluid-sm': 'clamp(20rem, 40vw, 24rem)',
        'fluid-md': 'clamp(24rem, 60vw, 28rem)',
        'fluid-lg': 'clamp(28rem, 80vw, 32rem)',
        'fluid-xl': 'clamp(32rem, 90vw, 36rem)',
      },

      // === ENHANCED BOX SHADOWS ===
      boxShadow: {
        'xs': 'var(--shadow-xs, 0 1px 2px rgba(0, 0, 0, 0.05))',
        'sm': 'var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06))',
        'md': 'var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06))',
        'lg': 'var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05))',
        'xl': 'var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04))',
        '2xl': 'var(--shadow-2xl, 0 25px 50px -12px rgba(0, 0, 0, 0.25))',
        'inner': 'var(--shadow-inner, inset 0 2px 4px 0 rgba(0, 0, 0, 0.06))',
        // Legacy shadows for backwards compatibility
        card: 'var(--shadow-sm)',
        'card-hover': 'var(--shadow-lg)',
        'card-dark': 'var(--shadow-md)',
        'floating': '0 10px 25px rgba(0, 0, 0, 0.15)',
      },

      // === TYPOGRAPHY ===
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },

      // === ENHANCED ANIMATIONS ===
      animation: {
        fadeIn: 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out forwards',
        'slide-in-left': 'slideInLeft 0.3s ease-out forwards',
        'slide-out': 'slideOut 0.3s ease-in forwards',
        'slide-out-left': 'slideOutLeft 0.3s ease-in forwards',
        'spin-slow': 'spin 3s linear infinite',
        'spin-reverse': 'spin-reverse 2.5s linear infinite',
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'ellipsis1': 'ellipsis 1.4s infinite 0s',
        'ellipsis2': 'ellipsis 1.4s infinite 0.2s',
        'ellipsis3': 'ellipsis 1.4s infinite 0.4s',
        'progress-indeterminate': 'progress-indeterminate 1.5s ease-in-out infinite',
        // New smooth transitions
        'scale-in': 'scale-in 0.2s ease-out',
        'scale-out': 'scale-out 0.15s ease-in',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        slideIn: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOut: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        slideOutLeft: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        'spin-reverse': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(-360deg)' },
        },
        ellipsis: {
          '0%': { opacity: 0 },
          '50%': { opacity: 1 },
          '100%': { opacity: 0 },
        },
        'progress-indeterminate': {
          '0%': { transform: 'translateX(-100%)' },
          '50%': { transform: 'translateX(0%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.95)', opacity: 0 },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        'scale-out': {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '100%': { transform: 'scale(0.95)', opacity: 0 },
        },
      },

      // === ENHANCED TRANSITIONS ===
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
        'colors': 'color, background-color, border-color, text-decoration-color, fill, stroke',
        'opacity': 'opacity',
        'shadow': 'box-shadow',
        'transform': 'transform',
      },

      transitionDuration: {
        '250': '250ms',
        '400': '400ms',
      },

      transitionTimingFunction: {
        'out-expo': 'cubic-bezier(0.16, 1, 0.3, 1)',
        'in-expo': 'cubic-bezier(0.7, 0, 0.84, 0)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/container-queries'),
  ],
}